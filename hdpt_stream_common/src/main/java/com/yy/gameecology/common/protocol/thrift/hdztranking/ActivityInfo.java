/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import java.util.ArrayList;
import java.util.BitSet;
import java.util.Collections;
import java.util.EnumMap;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Generated;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;
import org.apache.thrift.scheme.TupleScheme;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-08-10")
public class ActivityInfo implements org.apache.thrift.TBase<ActivityInfo, ActivityInfo._Fields>, java.io.Serializable, Cloneable, Comparable<ActivityInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ActivityInfo");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField BUSI_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("busiId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField ACT_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("actName", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField ACT_BG_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("actBgUrl", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField DETAIL_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("detailUrl", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField BEGIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("beginTime", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField ACT_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("actType", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField RANK_END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("rankEndTime", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField BEGIN_TIME_SHOW_FIELD_DESC = new org.apache.thrift.protocol.TField("beginTimeShow", org.apache.thrift.protocol.TType.I64, (short)11);
  private static final org.apache.thrift.protocol.TField END_TIME_SHOW_FIELD_DESC = new org.apache.thrift.protocol.TField("endTimeShow", org.apache.thrift.protocol.TType.I64, (short)12);
  private static final org.apache.thrift.protocol.TField EXT_JSON_FIELD_DESC = new org.apache.thrift.protocol.TField("extJson", org.apache.thrift.protocol.TType.STRING, (short)13);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ActivityInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ActivityInfoTupleSchemeFactory());
  }

  public long actId; // required
  public long busiId; // required
  public String actName; // required
  public long status; // required
  public String actBgUrl; // required
  public String detailUrl; // required
  public long beginTime; // required
  public long endTime; // required
  public long actType; // required
  public long rankEndTime; // required
  public long beginTimeShow; // required
  public long endTimeShow; // required
  public String extJson; // optional
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    BUSI_ID((short)2, "busiId"),
    ACT_NAME((short)3, "actName"),
    STATUS((short)4, "status"),
    ACT_BG_URL((short)5, "actBgUrl"),
    DETAIL_URL((short)6, "detailUrl"),
    BEGIN_TIME((short)7, "beginTime"),
    END_TIME((short)8, "endTime"),
    ACT_TYPE((short)9, "actType"),
    RANK_END_TIME((short)10, "rankEndTime"),
    BEGIN_TIME_SHOW((short)11, "beginTimeShow"),
    END_TIME_SHOW((short)12, "endTimeShow"),
    EXT_JSON((short)13, "extJson"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // BUSI_ID
          return BUSI_ID;
        case 3: // ACT_NAME
          return ACT_NAME;
        case 4: // STATUS
          return STATUS;
        case 5: // ACT_BG_URL
          return ACT_BG_URL;
        case 6: // DETAIL_URL
          return DETAIL_URL;
        case 7: // BEGIN_TIME
          return BEGIN_TIME;
        case 8: // END_TIME
          return END_TIME;
        case 9: // ACT_TYPE
          return ACT_TYPE;
        case 10: // RANK_END_TIME
          return RANK_END_TIME;
        case 11: // BEGIN_TIME_SHOW
          return BEGIN_TIME_SHOW;
        case 12: // END_TIME_SHOW
          return END_TIME_SHOW;
        case 13: // EXT_JSON
          return EXT_JSON;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __BUSIID_ISSET_ID = 1;
  private static final int __STATUS_ISSET_ID = 2;
  private static final int __BEGINTIME_ISSET_ID = 3;
  private static final int __ENDTIME_ISSET_ID = 4;
  private static final int __ACTTYPE_ISSET_ID = 5;
  private static final int __RANKENDTIME_ISSET_ID = 6;
  private static final int __BEGINTIMESHOW_ISSET_ID = 7;
  private static final int __ENDTIMESHOW_ISSET_ID = 8;
  private short __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.EXT_JSON};
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BUSI_ID, new org.apache.thrift.meta_data.FieldMetaData("busiId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACT_NAME, new org.apache.thrift.meta_data.FieldMetaData("actName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACT_BG_URL, new org.apache.thrift.meta_data.FieldMetaData("actBgUrl", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.DETAIL_URL, new org.apache.thrift.meta_data.FieldMetaData("detailUrl", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BEGIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("beginTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACT_TYPE, new org.apache.thrift.meta_data.FieldMetaData("actType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(
        _Fields.RANK_END_TIME, new org.apache.thrift.meta_data.FieldMetaData("rankEndTime", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(
        _Fields.BEGIN_TIME_SHOW, new org.apache.thrift.meta_data.FieldMetaData("beginTimeShow", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(
        _Fields.END_TIME_SHOW, new org.apache.thrift.meta_data.FieldMetaData("endTimeShow", org.apache.thrift.TFieldRequirementType.DEFAULT,
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_JSON, new org.apache.thrift.meta_data.FieldMetaData("extJson", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ActivityInfo.class, metaDataMap);
  }

  public ActivityInfo() {
  }

  public ActivityInfo(
    long actId,
    long busiId,
    String actName,
    long status,
    String actBgUrl,
    String detailUrl,
    long beginTime,
    long endTime,
    long actType,
    long rankEndTime,
    long beginTimeShow,
    long endTimeShow,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.busiId = busiId;
    setBusiIdIsSet(true);
    this.actName = actName;
    this.status = status;
    setStatusIsSet(true);
    this.actBgUrl = actBgUrl;
    this.detailUrl = detailUrl;
    this.beginTime = beginTime;
    setBeginTimeIsSet(true);
    this.endTime = endTime;
    setEndTimeIsSet(true);
    this.actType = actType;
    setActTypeIsSet(true);
    this.rankEndTime = rankEndTime;
    setRankEndTimeIsSet(true);
    this.beginTimeShow = beginTimeShow;
    setBeginTimeShowIsSet(true);
    this.endTimeShow = endTimeShow;
    setEndTimeShowIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ActivityInfo(ActivityInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    this.busiId = other.busiId;
    if (other.isSetActName()) {
      this.actName = other.actName;
    }
    this.status = other.status;
    if (other.isSetActBgUrl()) {
      this.actBgUrl = other.actBgUrl;
    }
    if (other.isSetDetailUrl()) {
      this.detailUrl = other.detailUrl;
    }
    this.beginTime = other.beginTime;
    this.endTime = other.endTime;
    this.actType = other.actType;
    this.rankEndTime = other.rankEndTime;
    this.beginTimeShow = other.beginTimeShow;
    this.endTimeShow = other.endTimeShow;
    if (other.isSetExtJson()) {
      this.extJson = other.extJson;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public ActivityInfo deepCopy() {
    return new ActivityInfo(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    setBusiIdIsSet(false);
    this.busiId = 0;
    this.actName = null;
    setStatusIsSet(false);
    this.status = 0;
    this.actBgUrl = null;
    this.detailUrl = null;
    setBeginTimeIsSet(false);
    this.beginTime = 0;
    setEndTimeIsSet(false);
    this.endTime = 0;
    setActTypeIsSet(false);
    this.actType = 0;
    setRankEndTimeIsSet(false);
    this.rankEndTime = 0;
    setBeginTimeShowIsSet(false);
    this.beginTimeShow = 0;
    setEndTimeShowIsSet(false);
    this.endTimeShow = 0;
    this.extJson = null;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public ActivityInfo setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getBusiId() {
    return this.busiId;
  }

  public ActivityInfo setBusiId(long busiId) {
    this.busiId = busiId;
    setBusiIdIsSet(true);
    return this;
  }

  public void unsetBusiId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BUSIID_ISSET_ID);
  }

  /** Returns true if field busiId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusiId() {
    return EncodingUtils.testBit(__isset_bitfield, __BUSIID_ISSET_ID);
  }

  public void setBusiIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BUSIID_ISSET_ID, value);
  }

  public String getActName() {
    return this.actName;
  }

  public ActivityInfo setActName(String actName) {
    this.actName = actName;
    return this;
  }

  public void unsetActName() {
    this.actName = null;
  }

  /** Returns true if field actName is set (has been assigned a value) and false otherwise */
  public boolean isSetActName() {
    return this.actName != null;
  }

  public void setActNameIsSet(boolean value) {
    if (!value) {
      this.actName = null;
    }
  }

  public long getStatus() {
    return this.status;
  }

  public ActivityInfo setStatus(long status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  public String getActBgUrl() {
    return this.actBgUrl;
  }

  public ActivityInfo setActBgUrl(String actBgUrl) {
    this.actBgUrl = actBgUrl;
    return this;
  }

  public void unsetActBgUrl() {
    this.actBgUrl = null;
  }

  /** Returns true if field actBgUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetActBgUrl() {
    return this.actBgUrl != null;
  }

  public void setActBgUrlIsSet(boolean value) {
    if (!value) {
      this.actBgUrl = null;
    }
  }

  public String getDetailUrl() {
    return this.detailUrl;
  }

  public ActivityInfo setDetailUrl(String detailUrl) {
    this.detailUrl = detailUrl;
    return this;
  }

  public void unsetDetailUrl() {
    this.detailUrl = null;
  }

  /** Returns true if field detailUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetDetailUrl() {
    return this.detailUrl != null;
  }

  public void setDetailUrlIsSet(boolean value) {
    if (!value) {
      this.detailUrl = null;
    }
  }

  public long getBeginTime() {
    return this.beginTime;
  }

  public ActivityInfo setBeginTime(long beginTime) {
    this.beginTime = beginTime;
    setBeginTimeIsSet(true);
    return this;
  }

  public void unsetBeginTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
  }

  /** Returns true if field beginTime is set (has been assigned a value) and false otherwise */
  public boolean isSetBeginTime() {
    return EncodingUtils.testBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
  }

  public void setBeginTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BEGINTIME_ISSET_ID, value);
  }

  public long getEndTime() {
    return this.endTime;
  }

  public ActivityInfo setEndTime(long endTime) {
    this.endTime = endTime;
    setEndTimeIsSet(true);
    return this;
  }

  public void unsetEndTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
  public boolean isSetEndTime() {
    return EncodingUtils.testBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  public void setEndTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ENDTIME_ISSET_ID, value);
  }

  public long getActType() {
    return this.actType;
  }

  public ActivityInfo setActType(long actType) {
    this.actType = actType;
    setActTypeIsSet(true);
    return this;
  }

  public void unsetActType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTTYPE_ISSET_ID);
  }

  /** Returns true if field actType is set (has been assigned a value) and false otherwise */
  public boolean isSetActType() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTTYPE_ISSET_ID);
  }

  public void setActTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTTYPE_ISSET_ID, value);
  }

  public long getRankEndTime() {
    return this.rankEndTime;
  }

  public ActivityInfo setRankEndTime(long rankEndTime) {
    this.rankEndTime = rankEndTime;
    setRankEndTimeIsSet(true);
    return this;
  }

  public void unsetRankEndTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKENDTIME_ISSET_ID);
  }

  /** Returns true if field rankEndTime is set (has been assigned a value) and false otherwise */
  public boolean isSetRankEndTime() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKENDTIME_ISSET_ID);
  }

  public void setRankEndTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKENDTIME_ISSET_ID, value);
  }

  public long getBeginTimeShow() {
    return this.beginTimeShow;
  }

  public ActivityInfo setBeginTimeShow(long beginTimeShow) {
    this.beginTimeShow = beginTimeShow;
    setBeginTimeShowIsSet(true);
    return this;
  }

  public void unsetBeginTimeShow() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BEGINTIMESHOW_ISSET_ID);
  }

  /** Returns true if field beginTimeShow is set (has been assigned a value) and false otherwise */
  public boolean isSetBeginTimeShow() {
    return EncodingUtils.testBit(__isset_bitfield, __BEGINTIMESHOW_ISSET_ID);
  }

  public void setBeginTimeShowIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BEGINTIMESHOW_ISSET_ID, value);
  }

  public long getEndTimeShow() {
    return this.endTimeShow;
  }

  public ActivityInfo setEndTimeShow(long endTimeShow) {
    this.endTimeShow = endTimeShow;
    setEndTimeShowIsSet(true);
    return this;
  }

  public void unsetEndTimeShow() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ENDTIMESHOW_ISSET_ID);
  }

  /** Returns true if field endTimeShow is set (has been assigned a value) and false otherwise */
  public boolean isSetEndTimeShow() {
    return EncodingUtils.testBit(__isset_bitfield, __ENDTIMESHOW_ISSET_ID);
  }

  public void setEndTimeShowIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ENDTIMESHOW_ISSET_ID, value);
  }

  public String getExtJson() {
    return this.extJson;
  }

  public ActivityInfo setExtJson(String extJson) {
    this.extJson = extJson;
    return this;
  }

  public void unsetExtJson() {
    this.extJson = null;
  }

  /** Returns true if field extJson is set (has been assigned a value) and false otherwise */
  public boolean isSetExtJson() {
    return this.extJson != null;
  }

  public void setExtJsonIsSet(boolean value) {
    if (!value) {
      this.extJson = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public ActivityInfo setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case BUSI_ID:
      if (value == null) {
        unsetBusiId();
      } else {
        setBusiId((Long)value);
      }
      break;

    case ACT_NAME:
      if (value == null) {
        unsetActName();
      } else {
        setActName((String)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Long)value);
      }
      break;

    case ACT_BG_URL:
      if (value == null) {
        unsetActBgUrl();
      } else {
        setActBgUrl((String)value);
      }
      break;

    case DETAIL_URL:
      if (value == null) {
        unsetDetailUrl();
      } else {
        setDetailUrl((String)value);
      }
      break;

    case BEGIN_TIME:
      if (value == null) {
        unsetBeginTime();
      } else {
        setBeginTime((Long)value);
      }
      break;

    case END_TIME:
      if (value == null) {
        unsetEndTime();
      } else {
        setEndTime((Long)value);
      }
      break;

    case ACT_TYPE:
      if (value == null) {
        unsetActType();
      } else {
        setActType((Long)value);
      }
      break;

    case RANK_END_TIME:
      if (value == null) {
        unsetRankEndTime();
      } else {
        setRankEndTime((Long)value);
      }
      break;

    case BEGIN_TIME_SHOW:
      if (value == null) {
        unsetBeginTimeShow();
      } else {
        setBeginTimeShow((Long)value);
      }
      break;

    case END_TIME_SHOW:
      if (value == null) {
        unsetEndTimeShow();
      } else {
        setEndTimeShow((Long)value);
      }
      break;

    case EXT_JSON:
      if (value == null) {
        unsetExtJson();
      } else {
        setExtJson((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case BUSI_ID:
      return getBusiId();

    case ACT_NAME:
      return getActName();

    case STATUS:
      return getStatus();

    case ACT_BG_URL:
      return getActBgUrl();

    case DETAIL_URL:
      return getDetailUrl();

    case BEGIN_TIME:
      return getBeginTime();

    case END_TIME:
      return getEndTime();

    case ACT_TYPE:
      return getActType();

    case RANK_END_TIME:
      return getRankEndTime();

    case BEGIN_TIME_SHOW:
      return getBeginTimeShow();

    case END_TIME_SHOW:
      return getEndTimeShow();

    case EXT_JSON:
      return getExtJson();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case BUSI_ID:
      return isSetBusiId();
    case ACT_NAME:
      return isSetActName();
    case STATUS:
      return isSetStatus();
    case ACT_BG_URL:
      return isSetActBgUrl();
    case DETAIL_URL:
      return isSetDetailUrl();
    case BEGIN_TIME:
      return isSetBeginTime();
    case END_TIME:
      return isSetEndTime();
    case ACT_TYPE:
      return isSetActType();
    case RANK_END_TIME:
      return isSetRankEndTime();
    case BEGIN_TIME_SHOW:
      return isSetBeginTimeShow();
    case END_TIME_SHOW:
      return isSetEndTimeShow();
    case EXT_JSON:
      return isSetExtJson();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ActivityInfo)
      return this.equals((ActivityInfo)that);
    return false;
  }

  public boolean equals(ActivityInfo that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_busiId = true;
    boolean that_present_busiId = true;
    if (this_present_busiId || that_present_busiId) {
      if (!(this_present_busiId && that_present_busiId))
        return false;
      if (this.busiId != that.busiId)
        return false;
    }

    boolean this_present_actName = true && this.isSetActName();
    boolean that_present_actName = true && that.isSetActName();
    if (this_present_actName || that_present_actName) {
      if (!(this_present_actName && that_present_actName))
        return false;
      if (!this.actName.equals(that.actName))
        return false;
    }

    boolean this_present_status = true;
    boolean that_present_status = true;
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_actBgUrl = true && this.isSetActBgUrl();
    boolean that_present_actBgUrl = true && that.isSetActBgUrl();
    if (this_present_actBgUrl || that_present_actBgUrl) {
      if (!(this_present_actBgUrl && that_present_actBgUrl))
        return false;
      if (!this.actBgUrl.equals(that.actBgUrl))
        return false;
    }

    boolean this_present_detailUrl = true && this.isSetDetailUrl();
    boolean that_present_detailUrl = true && that.isSetDetailUrl();
    if (this_present_detailUrl || that_present_detailUrl) {
      if (!(this_present_detailUrl && that_present_detailUrl))
        return false;
      if (!this.detailUrl.equals(that.detailUrl))
        return false;
    }

    boolean this_present_beginTime = true;
    boolean that_present_beginTime = true;
    if (this_present_beginTime || that_present_beginTime) {
      if (!(this_present_beginTime && that_present_beginTime))
        return false;
      if (this.beginTime != that.beginTime)
        return false;
    }

    boolean this_present_endTime = true;
    boolean that_present_endTime = true;
    if (this_present_endTime || that_present_endTime) {
      if (!(this_present_endTime && that_present_endTime))
        return false;
      if (this.endTime != that.endTime)
        return false;
    }

    boolean this_present_actType = true;
    boolean that_present_actType = true;
    if (this_present_actType || that_present_actType) {
      if (!(this_present_actType && that_present_actType))
        return false;
      if (this.actType != that.actType)
        return false;
    }

    boolean this_present_rankEndTime = true;
    boolean that_present_rankEndTime = true;
    if (this_present_rankEndTime || that_present_rankEndTime) {
      if (!(this_present_rankEndTime && that_present_rankEndTime))
        return false;
      if (this.rankEndTime != that.rankEndTime)
        return false;
    }

    boolean this_present_beginTimeShow = true;
    boolean that_present_beginTimeShow = true;
    if (this_present_beginTimeShow || that_present_beginTimeShow) {
      if (!(this_present_beginTimeShow && that_present_beginTimeShow))
        return false;
      if (this.beginTimeShow != that.beginTimeShow)
        return false;
    }

    boolean this_present_endTimeShow = true;
    boolean that_present_endTimeShow = true;
    if (this_present_endTimeShow || that_present_endTimeShow) {
      if (!(this_present_endTimeShow && that_present_endTimeShow))
        return false;
      if (this.endTimeShow != that.endTimeShow)
        return false;
    }

    boolean this_present_extJson = true && this.isSetExtJson();
    boolean that_present_extJson = true && that.isSetExtJson();
    if (this_present_extJson || that_present_extJson) {
      if (!(this_present_extJson && that_present_extJson))
        return false;
      if (!this.extJson.equals(that.extJson))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_busiId = true;
    list.add(present_busiId);
    if (present_busiId)
      list.add(busiId);

    boolean present_actName = true && (isSetActName());
    list.add(present_actName);
    if (present_actName)
      list.add(actName);

    boolean present_status = true;
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_actBgUrl = true && (isSetActBgUrl());
    list.add(present_actBgUrl);
    if (present_actBgUrl)
      list.add(actBgUrl);

    boolean present_detailUrl = true && (isSetDetailUrl());
    list.add(present_detailUrl);
    if (present_detailUrl)
      list.add(detailUrl);

    boolean present_beginTime = true;
    list.add(present_beginTime);
    if (present_beginTime)
      list.add(beginTime);

    boolean present_endTime = true;
    list.add(present_endTime);
    if (present_endTime)
      list.add(endTime);

    boolean present_actType = true;
    list.add(present_actType);
    if (present_actType)
      list.add(actType);

    boolean present_rankEndTime = true;
    list.add(present_rankEndTime);
    if (present_rankEndTime)
      list.add(rankEndTime);

    boolean present_beginTimeShow = true;
    list.add(present_beginTimeShow);
    if (present_beginTimeShow)
      list.add(beginTimeShow);

    boolean present_endTimeShow = true;
    list.add(present_endTimeShow);
    if (present_endTimeShow)
      list.add(endTimeShow);

    boolean present_extJson = true && (isSetExtJson());
    list.add(present_extJson);
    if (present_extJson)
      list.add(extJson);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(ActivityInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusiId()).compareTo(other.isSetBusiId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusiId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.busiId, other.busiId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActName()).compareTo(other.isSetActName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actName, other.actName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActBgUrl()).compareTo(other.isSetActBgUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActBgUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actBgUrl, other.actBgUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDetailUrl()).compareTo(other.isSetDetailUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDetailUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.detailUrl, other.detailUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBeginTime()).compareTo(other.isSetBeginTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBeginTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.beginTime, other.beginTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetEndTime()).compareTo(other.isSetEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActType()).compareTo(other.isSetActType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actType, other.actType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankEndTime()).compareTo(other.isSetRankEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankEndTime, other.rankEndTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBeginTimeShow()).compareTo(other.isSetBeginTimeShow());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBeginTimeShow()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.beginTimeShow, other.beginTimeShow);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetEndTimeShow()).compareTo(other.isSetEndTimeShow());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndTimeShow()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTimeShow, other.endTimeShow);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtJson()).compareTo(other.isSetExtJson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtJson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extJson, other.extJson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ActivityInfo(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("busiId:");
    sb.append(this.busiId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actName:");
    if (this.actName == null) {
      sb.append("null");
    } else {
      sb.append(this.actName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("status:");
    sb.append(this.status);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actBgUrl:");
    if (this.actBgUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.actBgUrl);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("detailUrl:");
    if (this.detailUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.detailUrl);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("beginTime:");
    sb.append(this.beginTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("endTime:");
    sb.append(this.endTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actType:");
    sb.append(this.actType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankEndTime:");
    sb.append(this.rankEndTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("beginTimeShow:");
    sb.append(this.beginTimeShow);
    first = false;
    if (!first) sb.append(", ");
    sb.append("endTimeShow:");
    sb.append(this.endTimeShow);
    first = false;
    if (isSetExtJson()) {
      if (!first) sb.append(", ");
      sb.append("extJson:");
      if (this.extJson == null) {
        sb.append("null");
      } else {
        sb.append(this.extJson);
      }
      first = false;
    }
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ActivityInfoStandardSchemeFactory implements SchemeFactory {
    public ActivityInfoStandardScheme getScheme() {
      return new ActivityInfoStandardScheme();
    }
  }

  private static class ActivityInfoStandardScheme extends StandardScheme<ActivityInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ActivityInfo struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BUSI_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.busiId = iprot.readI64();
              struct.setBusiIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ACT_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.actName = iprot.readString();
              struct.setActNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.status = iprot.readI64();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ACT_BG_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.actBgUrl = iprot.readString();
              struct.setActBgUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // DETAIL_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.detailUrl = iprot.readString();
              struct.setDetailUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // BEGIN_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.beginTime = iprot.readI64();
              struct.setBeginTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.endTime = iprot.readI64();
              struct.setEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // ACT_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actType = iprot.readI64();
              struct.setActTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // RANK_END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankEndTime = iprot.readI64();
              struct.setRankEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // BEGIN_TIME_SHOW
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.beginTimeShow = iprot.readI64();
              struct.setBeginTimeShowIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // END_TIME_SHOW
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.endTimeShow = iprot.readI64();
              struct.setEndTimeShowIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // EXT_JSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extJson = iprot.readString();
              struct.setExtJsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map292 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map292.size);
                String _key293;
                String _val294;
                for (int _i295 = 0; _i295 < _map292.size; ++_i295)
                {
                  _key293 = iprot.readString();
                  _val294 = iprot.readString();
                  struct.extData.put(_key293, _val294);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ActivityInfo struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(BUSI_ID_FIELD_DESC);
      oprot.writeI64(struct.busiId);
      oprot.writeFieldEnd();
      if (struct.actName != null) {
        oprot.writeFieldBegin(ACT_NAME_FIELD_DESC);
        oprot.writeString(struct.actName);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(STATUS_FIELD_DESC);
      oprot.writeI64(struct.status);
      oprot.writeFieldEnd();
      if (struct.actBgUrl != null) {
        oprot.writeFieldBegin(ACT_BG_URL_FIELD_DESC);
        oprot.writeString(struct.actBgUrl);
        oprot.writeFieldEnd();
      }
      if (struct.detailUrl != null) {
        oprot.writeFieldBegin(DETAIL_URL_FIELD_DESC);
        oprot.writeString(struct.detailUrl);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(BEGIN_TIME_FIELD_DESC);
      oprot.writeI64(struct.beginTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(END_TIME_FIELD_DESC);
      oprot.writeI64(struct.endTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ACT_TYPE_FIELD_DESC);
      oprot.writeI64(struct.actType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANK_END_TIME_FIELD_DESC);
      oprot.writeI64(struct.rankEndTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(BEGIN_TIME_SHOW_FIELD_DESC);
      oprot.writeI64(struct.beginTimeShow);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(END_TIME_SHOW_FIELD_DESC);
      oprot.writeI64(struct.endTimeShow);
      oprot.writeFieldEnd();
      if (struct.extJson != null) {
        if (struct.isSetExtJson()) {
          oprot.writeFieldBegin(EXT_JSON_FIELD_DESC);
          oprot.writeString(struct.extJson);
          oprot.writeFieldEnd();
        }
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter296 : struct.extData.entrySet())
          {
            oprot.writeString(_iter296.getKey());
            oprot.writeString(_iter296.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ActivityInfoTupleSchemeFactory implements SchemeFactory {
    public ActivityInfoTupleScheme getScheme() {
      return new ActivityInfoTupleScheme();
    }
  }

  private static class ActivityInfoTupleScheme extends TupleScheme<ActivityInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ActivityInfo struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetBusiId()) {
        optionals.set(1);
      }
      if (struct.isSetActName()) {
        optionals.set(2);
      }
      if (struct.isSetStatus()) {
        optionals.set(3);
      }
      if (struct.isSetActBgUrl()) {
        optionals.set(4);
      }
      if (struct.isSetDetailUrl()) {
        optionals.set(5);
      }
      if (struct.isSetBeginTime()) {
        optionals.set(6);
      }
      if (struct.isSetEndTime()) {
        optionals.set(7);
      }
      if (struct.isSetActType()) {
        optionals.set(8);
      }
      if (struct.isSetRankEndTime()) {
        optionals.set(9);
      }
      if (struct.isSetBeginTimeShow()) {
        optionals.set(10);
      }
      if (struct.isSetEndTimeShow()) {
        optionals.set(11);
      }
      if (struct.isSetExtJson()) {
        optionals.set(12);
      }
      if (struct.isSetExtData()) {
        optionals.set(13);
      }
      oprot.writeBitSet(optionals, 14);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetBusiId()) {
        oprot.writeI64(struct.busiId);
      }
      if (struct.isSetActName()) {
        oprot.writeString(struct.actName);
      }
      if (struct.isSetStatus()) {
        oprot.writeI64(struct.status);
      }
      if (struct.isSetActBgUrl()) {
        oprot.writeString(struct.actBgUrl);
      }
      if (struct.isSetDetailUrl()) {
        oprot.writeString(struct.detailUrl);
      }
      if (struct.isSetBeginTime()) {
        oprot.writeI64(struct.beginTime);
      }
      if (struct.isSetEndTime()) {
        oprot.writeI64(struct.endTime);
      }
      if (struct.isSetActType()) {
        oprot.writeI64(struct.actType);
      }
      if (struct.isSetRankEndTime()) {
        oprot.writeI64(struct.rankEndTime);
      }
      if (struct.isSetBeginTimeShow()) {
        oprot.writeI64(struct.beginTimeShow);
      }
      if (struct.isSetEndTimeShow()) {
        oprot.writeI64(struct.endTimeShow);
      }
      if (struct.isSetExtJson()) {
        oprot.writeString(struct.extJson);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter297 : struct.extData.entrySet())
          {
            oprot.writeString(_iter297.getKey());
            oprot.writeString(_iter297.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ActivityInfo struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(14);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.busiId = iprot.readI64();
        struct.setBusiIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.actName = iprot.readString();
        struct.setActNameIsSet(true);
      }
      if (incoming.get(3)) {
        struct.status = iprot.readI64();
        struct.setStatusIsSet(true);
      }
      if (incoming.get(4)) {
        struct.actBgUrl = iprot.readString();
        struct.setActBgUrlIsSet(true);
      }
      if (incoming.get(5)) {
        struct.detailUrl = iprot.readString();
        struct.setDetailUrlIsSet(true);
      }
      if (incoming.get(6)) {
        struct.beginTime = iprot.readI64();
        struct.setBeginTimeIsSet(true);
      }
      if (incoming.get(7)) {
        struct.endTime = iprot.readI64();
        struct.setEndTimeIsSet(true);
      }
      if (incoming.get(8)) {
        struct.actType = iprot.readI64();
        struct.setActTypeIsSet(true);
      }
      if (incoming.get(9)) {
        struct.rankEndTime = iprot.readI64();
        struct.setRankEndTimeIsSet(true);
      }
      if (incoming.get(10)) {
        struct.beginTimeShow = iprot.readI64();
        struct.setBeginTimeShowIsSet(true);
      }
      if (incoming.get(11)) {
        struct.endTimeShow = iprot.readI64();
        struct.setEndTimeShowIsSet(true);
      }
      if (incoming.get(12)) {
        struct.extJson = iprot.readString();
        struct.setExtJsonIsSet(true);
      }
      if (incoming.get(13)) {
        {
          org.apache.thrift.protocol.TMap _map298 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map298.size);
          String _key299;
          String _val300;
          for (int _i301 = 0; _i301 < _map298.size; ++_i301)
          {
            _key299 = iprot.readString();
            _val300 = iprot.readString();
            struct.extData.put(_key299, _val300);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

