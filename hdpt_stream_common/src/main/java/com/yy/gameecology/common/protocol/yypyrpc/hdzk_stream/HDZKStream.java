// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: hdzk_stream.proto

package com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream;

public final class HDZKStream {
  private HDZKStream() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface PingOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Ping)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Ping}
   */
  public  static final class Ping extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Ping)
      PingOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Ping.newBuilder() to construct.
    private Ping(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Ping() {
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Ping(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping other = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping) obj;

      boolean result = true;
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Ping}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Ping)
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.PingOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping build() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping result = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Ping)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Ping)
    private static final com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Ping>
        PARSER = new com.google.protobuf.AbstractParser<Ping>() {
      @java.lang.Override
      public Ping parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Ping(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Ping> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Ping> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PongOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Pong)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 result = 1;</code>
     */
    int getResult();
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Pong}
   */
  public  static final class Pong extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Pong)
      PongOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Pong.newBuilder() to construct.
    private Pong(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Pong() {
      result_ = 0;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Pong(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readUInt32();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong.Builder.class);
    }

    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <code>uint32 result = 1;</code>
     */
    public int getResult() {
      return result_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeUInt32(1, result_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, result_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong other = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong) obj;

      boolean result = true;
      result = result && (getResult()
          == other.getResult());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Pong}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Pong)
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.PongOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong build() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong result = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong(this);
        result.result_ = result_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int result_ ;
      /**
       * <code>uint32 result = 1;</code>
       */
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 1;</code>
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Pong)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Pong)
    private static final com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Pong>
        PARSER = new com.google.protobuf.AbstractParser<Pong>() {
      @java.lang.Override
      public Pong parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Pong(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Pong> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Pong> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChannelOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 顶级频道
     * </pre>
     *
     * <code>int64 sid = 1;</code>
     */
    long getSid();

    /**
     * <pre>
     * 顶级频道下的子频道
     * </pre>
     *
     * <code>repeated int64 ssids = 2;</code>
     */
    java.util.List<java.lang.Long> getSsidsList();
    /**
     * <pre>
     * 顶级频道下的子频道
     * </pre>
     *
     * <code>repeated int64 ssids = 2;</code>
     */
    int getSsidsCount();
    /**
     * <pre>
     * 顶级频道下的子频道
     * </pre>
     *
     * <code>repeated int64 ssids = 2;</code>
     */
    long getSsids(int index);
  }
  /**
   * <pre>
   * 频道信息 
   * </pre>
   *
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel}
   */
  public  static final class Channel extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel)
      ChannelOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Channel.newBuilder() to construct.
    private Channel(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Channel() {
      sid_ = 0L;
      ssids_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Channel(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              sid_ = input.readInt64();
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                ssids_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              ssids_.add(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
                ssids_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                ssids_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          ssids_ = java.util.Collections.unmodifiableList(ssids_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder.class);
    }

    private int bitField0_;
    public static final int SID_FIELD_NUMBER = 1;
    private long sid_;
    /**
     * <pre>
     * 顶级频道
     * </pre>
     *
     * <code>int64 sid = 1;</code>
     */
    public long getSid() {
      return sid_;
    }

    public static final int SSIDS_FIELD_NUMBER = 2;
    private java.util.List<java.lang.Long> ssids_;
    /**
     * <pre>
     * 顶级频道下的子频道
     * </pre>
     *
     * <code>repeated int64 ssids = 2;</code>
     */
    public java.util.List<java.lang.Long>
        getSsidsList() {
      return ssids_;
    }
    /**
     * <pre>
     * 顶级频道下的子频道
     * </pre>
     *
     * <code>repeated int64 ssids = 2;</code>
     */
    public int getSsidsCount() {
      return ssids_.size();
    }
    /**
     * <pre>
     * 顶级频道下的子频道
     * </pre>
     *
     * <code>repeated int64 ssids = 2;</code>
     */
    public long getSsids(int index) {
      return ssids_.get(index);
    }
    private int ssidsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (sid_ != 0L) {
        output.writeInt64(1, sid_);
      }
      if (getSsidsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(ssidsMemoizedSerializedSize);
      }
      for (int i = 0; i < ssids_.size(); i++) {
        output.writeInt64NoTag(ssids_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (sid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, sid_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < ssids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(ssids_.get(i));
        }
        size += dataSize;
        if (!getSsidsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        ssidsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel other = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel) obj;

      boolean result = true;
      result = result && (getSid()
          == other.getSid());
      result = result && getSsidsList()
          .equals(other.getSsidsList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSid());
      if (getSsidsCount() > 0) {
        hash = (37 * hash) + SSIDS_FIELD_NUMBER;
        hash = (53 * hash) + getSsidsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 频道信息 
     * </pre>
     *
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel)
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        sid_ = 0L;

        ssids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel build() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel result = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.sid_ = sid_;
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          ssids_ = java.util.Collections.unmodifiableList(ssids_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.ssids_ = ssids_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.getDefaultInstance()) return this;
        if (other.getSid() != 0L) {
          setSid(other.getSid());
        }
        if (!other.ssids_.isEmpty()) {
          if (ssids_.isEmpty()) {
            ssids_ = other.ssids_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureSsidsIsMutable();
            ssids_.addAll(other.ssids_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long sid_ ;
      /**
       * <pre>
       * 顶级频道
       * </pre>
       *
       * <code>int64 sid = 1;</code>
       */
      public long getSid() {
        return sid_;
      }
      /**
       * <pre>
       * 顶级频道
       * </pre>
       *
       * <code>int64 sid = 1;</code>
       */
      public Builder setSid(long value) {
        
        sid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 顶级频道
       * </pre>
       *
       * <code>int64 sid = 1;</code>
       */
      public Builder clearSid() {
        
        sid_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Long> ssids_ = java.util.Collections.emptyList();
      private void ensureSsidsIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          ssids_ = new java.util.ArrayList<java.lang.Long>(ssids_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <pre>
       * 顶级频道下的子频道
       * </pre>
       *
       * <code>repeated int64 ssids = 2;</code>
       */
      public java.util.List<java.lang.Long>
          getSsidsList() {
        return java.util.Collections.unmodifiableList(ssids_);
      }
      /**
       * <pre>
       * 顶级频道下的子频道
       * </pre>
       *
       * <code>repeated int64 ssids = 2;</code>
       */
      public int getSsidsCount() {
        return ssids_.size();
      }
      /**
       * <pre>
       * 顶级频道下的子频道
       * </pre>
       *
       * <code>repeated int64 ssids = 2;</code>
       */
      public long getSsids(int index) {
        return ssids_.get(index);
      }
      /**
       * <pre>
       * 顶级频道下的子频道
       * </pre>
       *
       * <code>repeated int64 ssids = 2;</code>
       */
      public Builder setSsids(
          int index, long value) {
        ensureSsidsIsMutable();
        ssids_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 顶级频道下的子频道
       * </pre>
       *
       * <code>repeated int64 ssids = 2;</code>
       */
      public Builder addSsids(long value) {
        ensureSsidsIsMutable();
        ssids_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 顶级频道下的子频道
       * </pre>
       *
       * <code>repeated int64 ssids = 2;</code>
       */
      public Builder addAllSsids(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureSsidsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ssids_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 顶级频道下的子频道
       * </pre>
       *
       * <code>repeated int64 ssids = 2;</code>
       */
      public Builder clearSsids() {
        ssids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel)
    private static final com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Channel>
        PARSER = new com.google.protobuf.AbstractParser<Channel>() {
      @java.lang.Override
      public Channel parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Channel(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Channel> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Channel> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MemberFilterOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    java.util.List<com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel> 
        getChannelsList();
    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel getChannels(int index);
    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    int getChannelsCount();
    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    java.util.List<? extends com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder> 
        getChannelsOrBuilderList();
    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder getChannelsOrBuilder(
        int index);

    /**
     * <pre>
     * 指定的送礼人UID集合
     * </pre>
     *
     * <code>repeated int64 sendUids = 3;</code>
     */
    java.util.List<java.lang.Long> getSendUidsList();
    /**
     * <pre>
     * 指定的送礼人UID集合
     * </pre>
     *
     * <code>repeated int64 sendUids = 3;</code>
     */
    int getSendUidsCount();
    /**
     * <pre>
     * 指定的送礼人UID集合
     * </pre>
     *
     * <code>repeated int64 sendUids = 3;</code>
     */
    long getSendUids(int index);

    /**
     * <pre>
     * 指定的收礼人UID集合
     * </pre>
     *
     * <code>repeated int64 recvUids = 4;</code>
     */
    java.util.List<java.lang.Long> getRecvUidsList();
    /**
     * <pre>
     * 指定的收礼人UID集合
     * </pre>
     *
     * <code>repeated int64 recvUids = 4;</code>
     */
    int getRecvUidsCount();
    /**
     * <pre>
     * 指定的收礼人UID集合
     * </pre>
     *
     * <code>repeated int64 recvUids = 4;</code>
     */
    long getRecvUids(int index);

    /**
     * <pre>
     * 指定的礼物ID集合
     * </pre>
     *
     * <code>repeated string giftIds = 5;</code>
     */
    java.util.List<java.lang.String>
        getGiftIdsList();
    /**
     * <pre>
     * 指定的礼物ID集合
     * </pre>
     *
     * <code>repeated string giftIds = 5;</code>
     */
    int getGiftIdsCount();
    /**
     * <pre>
     * 指定的礼物ID集合
     * </pre>
     *
     * <code>repeated string giftIds = 5;</code>
     */
    java.lang.String getGiftIds(int index);
    /**
     * <pre>
     * 指定的礼物ID集合
     * </pre>
     *
     * <code>repeated string giftIds = 5;</code>
     */
    com.google.protobuf.ByteString
        getGiftIdsBytes(int index);

    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */
    int getExtMapCount();
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */
    boolean containsExtMap(
        java.lang.String key);
    /**
     * Use {@link #getExtMapMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getExtMap();
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getExtMapMap();
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    java.lang.String getExtMapOrDefault(
        java.lang.String key,
        java.lang.String defaultValue);
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    java.lang.String getExtMapOrThrow(
        java.lang.String key);
  }
  /**
   * <pre>
   *成员过滤器，用来生成具体的过滤实例 和 过滤模板
   *1）过滤实例：笛卡尔积：${recvUid}:${sendUid}:${giftId}:${sid}:${ssid}:结构的过滤模板，对应元素不存在时，用 _ 占位，会覆盖式存储到redis中
   *2）过滤模板：当有送礼流水时，用该模板生产一个流水的过滤实例，然后去之前存储的实例中查找，若找到则命中，使用关联的加成配置
   *3) 送礼流水匹配时，越具体越优先
   * </pre>
   *
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter}
   */
  public  static final class MemberFilter extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter)
      MemberFilterOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MemberFilter.newBuilder() to construct.
    private MemberFilter(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MemberFilter() {
      channels_ = java.util.Collections.emptyList();
      sendUids_ = java.util.Collections.emptyList();
      recvUids_ = java.util.Collections.emptyList();
      giftIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MemberFilter(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                channels_ = new java.util.ArrayList<com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel>();
                mutable_bitField0_ |= 0x00000001;
              }
              channels_.add(
                  input.readMessage(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.parser(), extensionRegistry));
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                sendUids_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              sendUids_.add(input.readInt64());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
                sendUids_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                sendUids_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 32: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                recvUids_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000004;
              }
              recvUids_.add(input.readInt64());
              break;
            }
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004) && input.getBytesUntilLimit() > 0) {
                recvUids_ = new java.util.ArrayList<java.lang.Long>();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                recvUids_.add(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                giftIds_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000008;
              }
              giftIds_.add(s);
              break;
            }
            case 794: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                extMap_ = com.google.protobuf.MapField.newMapField(
                    ExtMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000010;
              }
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              extMap__ = input.readMessage(
                  ExtMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extMap_.getMutableMap().put(
                  extMap__.getKey(), extMap__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          channels_ = java.util.Collections.unmodifiableList(channels_);
        }
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          sendUids_ = java.util.Collections.unmodifiableList(sendUids_);
        }
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          recvUids_ = java.util.Collections.unmodifiableList(recvUids_);
        }
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          giftIds_ = giftIds_.getUnmodifiableView();
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 99:
          return internalGetExtMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.Builder.class);
    }

    public static final int CHANNELS_FIELD_NUMBER = 1;
    private java.util.List<com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel> channels_;
    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    public java.util.List<com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel> getChannelsList() {
      return channels_;
    }
    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    public java.util.List<? extends com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder> 
        getChannelsOrBuilderList() {
      return channels_;
    }
    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    public int getChannelsCount() {
      return channels_.size();
    }
    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel getChannels(int index) {
      return channels_.get(index);
    }
    /**
     * <pre>
     * 指定的频道集合
     * </pre>
     *
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder getChannelsOrBuilder(
        int index) {
      return channels_.get(index);
    }

    public static final int SENDUIDS_FIELD_NUMBER = 3;
    private java.util.List<java.lang.Long> sendUids_;
    /**
     * <pre>
     * 指定的送礼人UID集合
     * </pre>
     *
     * <code>repeated int64 sendUids = 3;</code>
     */
    public java.util.List<java.lang.Long>
        getSendUidsList() {
      return sendUids_;
    }
    /**
     * <pre>
     * 指定的送礼人UID集合
     * </pre>
     *
     * <code>repeated int64 sendUids = 3;</code>
     */
    public int getSendUidsCount() {
      return sendUids_.size();
    }
    /**
     * <pre>
     * 指定的送礼人UID集合
     * </pre>
     *
     * <code>repeated int64 sendUids = 3;</code>
     */
    public long getSendUids(int index) {
      return sendUids_.get(index);
    }
    private int sendUidsMemoizedSerializedSize = -1;

    public static final int RECVUIDS_FIELD_NUMBER = 4;
    private java.util.List<java.lang.Long> recvUids_;
    /**
     * <pre>
     * 指定的收礼人UID集合
     * </pre>
     *
     * <code>repeated int64 recvUids = 4;</code>
     */
    public java.util.List<java.lang.Long>
        getRecvUidsList() {
      return recvUids_;
    }
    /**
     * <pre>
     * 指定的收礼人UID集合
     * </pre>
     *
     * <code>repeated int64 recvUids = 4;</code>
     */
    public int getRecvUidsCount() {
      return recvUids_.size();
    }
    /**
     * <pre>
     * 指定的收礼人UID集合
     * </pre>
     *
     * <code>repeated int64 recvUids = 4;</code>
     */
    public long getRecvUids(int index) {
      return recvUids_.get(index);
    }
    private int recvUidsMemoizedSerializedSize = -1;

    public static final int GIFTIDS_FIELD_NUMBER = 5;
    private com.google.protobuf.LazyStringList giftIds_;
    /**
     * <pre>
     * 指定的礼物ID集合
     * </pre>
     *
     * <code>repeated string giftIds = 5;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getGiftIdsList() {
      return giftIds_;
    }
    /**
     * <pre>
     * 指定的礼物ID集合
     * </pre>
     *
     * <code>repeated string giftIds = 5;</code>
     */
    public int getGiftIdsCount() {
      return giftIds_.size();
    }
    /**
     * <pre>
     * 指定的礼物ID集合
     * </pre>
     *
     * <code>repeated string giftIds = 5;</code>
     */
    public java.lang.String getGiftIds(int index) {
      return giftIds_.get(index);
    }
    /**
     * <pre>
     * 指定的礼物ID集合
     * </pre>
     *
     * <code>repeated string giftIds = 5;</code>
     */
    public com.google.protobuf.ByteString
        getGiftIdsBytes(int index) {
      return giftIds_.getByteString(index);
    }

    public static final int EXTMAP_FIELD_NUMBER = 99;
    private static final class ExtMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_ExtMapEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> extMap_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetExtMap() {
      if (extMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtMapDefaultEntryHolder.defaultEntry);
      }
      return extMap_;
    }

    public int getExtMapCount() {
      return internalGetExtMap().getMap().size();
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public boolean containsExtMap(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      return internalGetExtMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtMapMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
      return getExtMapMap();
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public java.util.Map<java.lang.String, java.lang.String> getExtMapMap() {
      return internalGetExtMap().getMap();
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public java.lang.String getExtMapOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public java.lang.String getExtMapOrThrow(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtMap().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < channels_.size(); i++) {
        output.writeMessage(1, channels_.get(i));
      }
      if (getSendUidsList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(sendUidsMemoizedSerializedSize);
      }
      for (int i = 0; i < sendUids_.size(); i++) {
        output.writeInt64NoTag(sendUids_.get(i));
      }
      if (getRecvUidsList().size() > 0) {
        output.writeUInt32NoTag(34);
        output.writeUInt32NoTag(recvUidsMemoizedSerializedSize);
      }
      for (int i = 0; i < recvUids_.size(); i++) {
        output.writeInt64NoTag(recvUids_.get(i));
      }
      for (int i = 0; i < giftIds_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, giftIds_.getRaw(i));
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtMap(),
          ExtMapDefaultEntryHolder.defaultEntry,
          99);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < channels_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, channels_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < sendUids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(sendUids_.get(i));
        }
        size += dataSize;
        if (!getSendUidsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        sendUidsMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < recvUids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(recvUids_.get(i));
        }
        size += dataSize;
        if (!getRecvUidsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        recvUidsMemoizedSerializedSize = dataSize;
      }
      {
        int dataSize = 0;
        for (int i = 0; i < giftIds_.size(); i++) {
          dataSize += computeStringSizeNoTag(giftIds_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getGiftIdsList().size();
      }
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetExtMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        extMap__ = ExtMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(99, extMap__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter other = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter) obj;

      boolean result = true;
      result = result && getChannelsList()
          .equals(other.getChannelsList());
      result = result && getSendUidsList()
          .equals(other.getSendUidsList());
      result = result && getRecvUidsList()
          .equals(other.getRecvUidsList());
      result = result && getGiftIdsList()
          .equals(other.getGiftIdsList());
      result = result && internalGetExtMap().equals(
          other.internalGetExtMap());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChannelsCount() > 0) {
        hash = (37 * hash) + CHANNELS_FIELD_NUMBER;
        hash = (53 * hash) + getChannelsList().hashCode();
      }
      if (getSendUidsCount() > 0) {
        hash = (37 * hash) + SENDUIDS_FIELD_NUMBER;
        hash = (53 * hash) + getSendUidsList().hashCode();
      }
      if (getRecvUidsCount() > 0) {
        hash = (37 * hash) + RECVUIDS_FIELD_NUMBER;
        hash = (53 * hash) + getRecvUidsList().hashCode();
      }
      if (getGiftIdsCount() > 0) {
        hash = (37 * hash) + GIFTIDS_FIELD_NUMBER;
        hash = (53 * hash) + getGiftIdsList().hashCode();
      }
      if (!internalGetExtMap().getMap().isEmpty()) {
        hash = (37 * hash) + EXTMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtMap().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *成员过滤器，用来生成具体的过滤实例 和 过滤模板
     *1）过滤实例：笛卡尔积：${recvUid}:${sendUid}:${giftId}:${sid}:${ssid}:结构的过滤模板，对应元素不存在时，用 _ 占位，会覆盖式存储到redis中
     *2）过滤模板：当有送礼流水时，用该模板生产一个流水的过滤实例，然后去之前存储的实例中查找，若找到则命中，使用关联的加成配置
     *3) 送礼流水匹配时，越具体越优先
     * </pre>
     *
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter)
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilterOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 99:
            return internalGetExtMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 99:
            return internalGetMutableExtMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChannelsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (channelsBuilder_ == null) {
          channels_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          channelsBuilder_.clear();
        }
        sendUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        recvUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        giftIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        internalGetMutableExtMap().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter build() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter result = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter(this);
        int from_bitField0_ = bitField0_;
        if (channelsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            channels_ = java.util.Collections.unmodifiableList(channels_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.channels_ = channels_;
        } else {
          result.channels_ = channelsBuilder_.build();
        }
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          sendUids_ = java.util.Collections.unmodifiableList(sendUids_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.sendUids_ = sendUids_;
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          recvUids_ = java.util.Collections.unmodifiableList(recvUids_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.recvUids_ = recvUids_;
        if (((bitField0_ & 0x00000008) == 0x00000008)) {
          giftIds_ = giftIds_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.giftIds_ = giftIds_;
        result.extMap_ = internalGetExtMap();
        result.extMap_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.getDefaultInstance()) return this;
        if (channelsBuilder_ == null) {
          if (!other.channels_.isEmpty()) {
            if (channels_.isEmpty()) {
              channels_ = other.channels_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureChannelsIsMutable();
              channels_.addAll(other.channels_);
            }
            onChanged();
          }
        } else {
          if (!other.channels_.isEmpty()) {
            if (channelsBuilder_.isEmpty()) {
              channelsBuilder_.dispose();
              channelsBuilder_ = null;
              channels_ = other.channels_;
              bitField0_ = (bitField0_ & ~0x00000001);
              channelsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChannelsFieldBuilder() : null;
            } else {
              channelsBuilder_.addAllMessages(other.channels_);
            }
          }
        }
        if (!other.sendUids_.isEmpty()) {
          if (sendUids_.isEmpty()) {
            sendUids_ = other.sendUids_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureSendUidsIsMutable();
            sendUids_.addAll(other.sendUids_);
          }
          onChanged();
        }
        if (!other.recvUids_.isEmpty()) {
          if (recvUids_.isEmpty()) {
            recvUids_ = other.recvUids_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureRecvUidsIsMutable();
            recvUids_.addAll(other.recvUids_);
          }
          onChanged();
        }
        if (!other.giftIds_.isEmpty()) {
          if (giftIds_.isEmpty()) {
            giftIds_ = other.giftIds_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensureGiftIdsIsMutable();
            giftIds_.addAll(other.giftIds_);
          }
          onChanged();
        }
        internalGetMutableExtMap().mergeFrom(
            other.internalGetExtMap());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel> channels_ =
        java.util.Collections.emptyList();
      private void ensureChannelsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          channels_ = new java.util.ArrayList<com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel>(channels_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder> channelsBuilder_;

      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public java.util.List<com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel> getChannelsList() {
        if (channelsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(channels_);
        } else {
          return channelsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public int getChannelsCount() {
        if (channelsBuilder_ == null) {
          return channels_.size();
        } else {
          return channelsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel getChannels(int index) {
        if (channelsBuilder_ == null) {
          return channels_.get(index);
        } else {
          return channelsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public Builder setChannels(
          int index, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel value) {
        if (channelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChannelsIsMutable();
          channels_.set(index, value);
          onChanged();
        } else {
          channelsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public Builder setChannels(
          int index, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder builderForValue) {
        if (channelsBuilder_ == null) {
          ensureChannelsIsMutable();
          channels_.set(index, builderForValue.build());
          onChanged();
        } else {
          channelsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public Builder addChannels(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel value) {
        if (channelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChannelsIsMutable();
          channels_.add(value);
          onChanged();
        } else {
          channelsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public Builder addChannels(
          int index, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel value) {
        if (channelsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChannelsIsMutable();
          channels_.add(index, value);
          onChanged();
        } else {
          channelsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public Builder addChannels(
          com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder builderForValue) {
        if (channelsBuilder_ == null) {
          ensureChannelsIsMutable();
          channels_.add(builderForValue.build());
          onChanged();
        } else {
          channelsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public Builder addChannels(
          int index, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder builderForValue) {
        if (channelsBuilder_ == null) {
          ensureChannelsIsMutable();
          channels_.add(index, builderForValue.build());
          onChanged();
        } else {
          channelsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public Builder addAllChannels(
          java.lang.Iterable<? extends com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel> values) {
        if (channelsBuilder_ == null) {
          ensureChannelsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, channels_);
          onChanged();
        } else {
          channelsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public Builder clearChannels() {
        if (channelsBuilder_ == null) {
          channels_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          channelsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public Builder removeChannels(int index) {
        if (channelsBuilder_ == null) {
          ensureChannelsIsMutable();
          channels_.remove(index);
          onChanged();
        } else {
          channelsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder getChannelsBuilder(
          int index) {
        return getChannelsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder getChannelsOrBuilder(
          int index) {
        if (channelsBuilder_ == null) {
          return channels_.get(index);  } else {
          return channelsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public java.util.List<? extends com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder> 
           getChannelsOrBuilderList() {
        if (channelsBuilder_ != null) {
          return channelsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(channels_);
        }
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder addChannelsBuilder() {
        return getChannelsFieldBuilder().addBuilder(
            com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.getDefaultInstance());
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder addChannelsBuilder(
          int index) {
        return getChannelsFieldBuilder().addBuilder(
            index, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.getDefaultInstance());
      }
      /**
       * <pre>
       * 指定的频道集合
       * </pre>
       *
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.Channel channels = 1;</code>
       */
      public java.util.List<com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder> 
           getChannelsBuilderList() {
        return getChannelsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder> 
          getChannelsFieldBuilder() {
        if (channelsBuilder_ == null) {
          channelsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Channel.Builder, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.ChannelOrBuilder>(
                  channels_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          channels_ = null;
        }
        return channelsBuilder_;
      }

      private java.util.List<java.lang.Long> sendUids_ = java.util.Collections.emptyList();
      private void ensureSendUidsIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          sendUids_ = new java.util.ArrayList<java.lang.Long>(sendUids_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <pre>
       * 指定的送礼人UID集合
       * </pre>
       *
       * <code>repeated int64 sendUids = 3;</code>
       */
      public java.util.List<java.lang.Long>
          getSendUidsList() {
        return java.util.Collections.unmodifiableList(sendUids_);
      }
      /**
       * <pre>
       * 指定的送礼人UID集合
       * </pre>
       *
       * <code>repeated int64 sendUids = 3;</code>
       */
      public int getSendUidsCount() {
        return sendUids_.size();
      }
      /**
       * <pre>
       * 指定的送礼人UID集合
       * </pre>
       *
       * <code>repeated int64 sendUids = 3;</code>
       */
      public long getSendUids(int index) {
        return sendUids_.get(index);
      }
      /**
       * <pre>
       * 指定的送礼人UID集合
       * </pre>
       *
       * <code>repeated int64 sendUids = 3;</code>
       */
      public Builder setSendUids(
          int index, long value) {
        ensureSendUidsIsMutable();
        sendUids_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的送礼人UID集合
       * </pre>
       *
       * <code>repeated int64 sendUids = 3;</code>
       */
      public Builder addSendUids(long value) {
        ensureSendUidsIsMutable();
        sendUids_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的送礼人UID集合
       * </pre>
       *
       * <code>repeated int64 sendUids = 3;</code>
       */
      public Builder addAllSendUids(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureSendUidsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, sendUids_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的送礼人UID集合
       * </pre>
       *
       * <code>repeated int64 sendUids = 3;</code>
       */
      public Builder clearSendUids() {
        sendUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Long> recvUids_ = java.util.Collections.emptyList();
      private void ensureRecvUidsIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          recvUids_ = new java.util.ArrayList<java.lang.Long>(recvUids_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <pre>
       * 指定的收礼人UID集合
       * </pre>
       *
       * <code>repeated int64 recvUids = 4;</code>
       */
      public java.util.List<java.lang.Long>
          getRecvUidsList() {
        return java.util.Collections.unmodifiableList(recvUids_);
      }
      /**
       * <pre>
       * 指定的收礼人UID集合
       * </pre>
       *
       * <code>repeated int64 recvUids = 4;</code>
       */
      public int getRecvUidsCount() {
        return recvUids_.size();
      }
      /**
       * <pre>
       * 指定的收礼人UID集合
       * </pre>
       *
       * <code>repeated int64 recvUids = 4;</code>
       */
      public long getRecvUids(int index) {
        return recvUids_.get(index);
      }
      /**
       * <pre>
       * 指定的收礼人UID集合
       * </pre>
       *
       * <code>repeated int64 recvUids = 4;</code>
       */
      public Builder setRecvUids(
          int index, long value) {
        ensureRecvUidsIsMutable();
        recvUids_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的收礼人UID集合
       * </pre>
       *
       * <code>repeated int64 recvUids = 4;</code>
       */
      public Builder addRecvUids(long value) {
        ensureRecvUidsIsMutable();
        recvUids_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的收礼人UID集合
       * </pre>
       *
       * <code>repeated int64 recvUids = 4;</code>
       */
      public Builder addAllRecvUids(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRecvUidsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, recvUids_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的收礼人UID集合
       * </pre>
       *
       * <code>repeated int64 recvUids = 4;</code>
       */
      public Builder clearRecvUids() {
        recvUids_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList giftIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureGiftIdsIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          giftIds_ = new com.google.protobuf.LazyStringArrayList(giftIds_);
          bitField0_ |= 0x00000008;
         }
      }
      /**
       * <pre>
       * 指定的礼物ID集合
       * </pre>
       *
       * <code>repeated string giftIds = 5;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getGiftIdsList() {
        return giftIds_.getUnmodifiableView();
      }
      /**
       * <pre>
       * 指定的礼物ID集合
       * </pre>
       *
       * <code>repeated string giftIds = 5;</code>
       */
      public int getGiftIdsCount() {
        return giftIds_.size();
      }
      /**
       * <pre>
       * 指定的礼物ID集合
       * </pre>
       *
       * <code>repeated string giftIds = 5;</code>
       */
      public java.lang.String getGiftIds(int index) {
        return giftIds_.get(index);
      }
      /**
       * <pre>
       * 指定的礼物ID集合
       * </pre>
       *
       * <code>repeated string giftIds = 5;</code>
       */
      public com.google.protobuf.ByteString
          getGiftIdsBytes(int index) {
        return giftIds_.getByteString(index);
      }
      /**
       * <pre>
       * 指定的礼物ID集合
       * </pre>
       *
       * <code>repeated string giftIds = 5;</code>
       */
      public Builder setGiftIds(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureGiftIdsIsMutable();
        giftIds_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的礼物ID集合
       * </pre>
       *
       * <code>repeated string giftIds = 5;</code>
       */
      public Builder addGiftIds(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureGiftIdsIsMutable();
        giftIds_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的礼物ID集合
       * </pre>
       *
       * <code>repeated string giftIds = 5;</code>
       */
      public Builder addAllGiftIds(
          java.lang.Iterable<java.lang.String> values) {
        ensureGiftIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, giftIds_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的礼物ID集合
       * </pre>
       *
       * <code>repeated string giftIds = 5;</code>
       */
      public Builder clearGiftIds() {
        giftIds_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 指定的礼物ID集合
       * </pre>
       *
       * <code>repeated string giftIds = 5;</code>
       */
      public Builder addGiftIdsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureGiftIdsIsMutable();
        giftIds_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> extMap_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetExtMap() {
        if (extMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtMapDefaultEntryHolder.defaultEntry);
        }
        return extMap_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableExtMap() {
        onChanged();;
        if (extMap_ == null) {
          extMap_ = com.google.protobuf.MapField.newMapField(
              ExtMapDefaultEntryHolder.defaultEntry);
        }
        if (!extMap_.isMutable()) {
          extMap_ = extMap_.copy();
        }
        return extMap_;
      }

      public int getExtMapCount() {
        return internalGetExtMap().getMap().size();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public boolean containsExtMap(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        return internalGetExtMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtMapMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
        return getExtMapMap();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public java.util.Map<java.lang.String, java.lang.String> getExtMapMap() {
        return internalGetExtMap().getMap();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public java.lang.String getExtMapOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public java.lang.String getExtMapOrThrow(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtMap().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtMap() {
        internalGetMutableExtMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public Builder removeExtMap(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableExtMap() {
        return internalGetMutableExtMap().getMutableMap();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */
      public Builder putExtMap(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public Builder putAllExtMap(
          java.util.Map<java.lang.String, java.lang.String> values) {
        internalGetMutableExtMap().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter)
    private static final com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MemberFilter>
        PARSER = new com.google.protobuf.AbstractParser<MemberFilter>() {
      @java.lang.Override
      public MemberFilter parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MemberFilter(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MemberFilter> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MemberFilter> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetBufferRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 活动id
     * </pre>
     *
     * <code>int64 actId = 1;</code>
     */
    long getActId();

    /**
     * <pre>
     * 请求seq（服务端24小时内去重），在actId下唯一即可
     * </pre>
     *
     * <code>string seq = 2;</code>
     */
    java.lang.String getSeq();
    /**
     * <pre>
     * 请求seq（服务端24小时内去重），在actId下唯一即可
     * </pre>
     *
     * <code>string seq = 2;</code>
     */
    com.google.protobuf.ByteString
        getSeqBytes();

    /**
     * <pre>
     * 加成设置
     * </pre>
     *
     * <code>int32 ratio = 10;</code>
     */
    int getRatio();

    /**
     * <pre>
     * 取整指示， true：向下，false：向上
     * </pre>
     *
     * <code>bool roundDown = 11;</code>
     */
    boolean getRoundDown();

    /**
     * <pre>
     * 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string beginTime = 12;</code>
     */
    java.lang.String getBeginTime();
    /**
     * <pre>
     * 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string beginTime = 12;</code>
     */
    com.google.protobuf.ByteString
        getBeginTimeBytes();

    /**
     * <pre>
     * 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string endTime = 13;</code>
     */
    java.lang.String getEndTime();
    /**
     * <pre>
     * 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string endTime = 13;</code>
     */
    com.google.protobuf.ByteString
        getEndTimeBytes();

    /**
     * <pre>
     * 当相同成员过滤key存在时的处理策略， 0：覆盖（默认值），1：不覆盖，其它值待后续实现。。。
     * </pre>
     *
     * <code>int32 policy = 15;</code>
     */
    int getPolicy();

    /**
     * <pre>
     * 加成范围
     * </pre>
     *
     * <code>bool global = 21;</code>
     */
    boolean getGlobal();

    /**
     * <pre>
     * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
     * </pre>
     *
     * <code>repeated string roles = 22;</code>
     */
    java.util.List<java.lang.String>
        getRolesList();
    /**
     * <pre>
     * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
     * </pre>
     *
     * <code>repeated string roles = 22;</code>
     */
    int getRolesCount();
    /**
     * <pre>
     * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
     * </pre>
     *
     * <code>repeated string roles = 22;</code>
     */
    java.lang.String getRoles(int index);
    /**
     * <pre>
     * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
     * </pre>
     *
     * <code>repeated string roles = 22;</code>
     */
    com.google.protobuf.ByteString
        getRolesBytes(int index);

    /**
     * <pre>
     * 为指定榜单做的加成，优先级高于 roles 中的指定
     * </pre>
     *
     * <code>repeated int32 rankings = 23;</code>
     */
    java.util.List<java.lang.Integer> getRankingsList();
    /**
     * <pre>
     * 为指定榜单做的加成，优先级高于 roles 中的指定
     * </pre>
     *
     * <code>repeated int32 rankings = 23;</code>
     */
    int getRankingsCount();
    /**
     * <pre>
     * 为指定榜单做的加成，优先级高于 roles 中的指定
     * </pre>
     *
     * <code>repeated int32 rankings = 23;</code>
     */
    int getRankings(int index);

    /**
     * <pre>
     * 加成过滤条件
     * </pre>
     *
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
     */
    boolean hasMemberFilter();
    /**
     * <pre>
     * 加成过滤条件
     * </pre>
     *
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter getMemberFilter();
    /**
     * <pre>
     * 加成过滤条件
     * </pre>
     *
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilterOrBuilder getMemberFilterOrBuilder();

    /**
     * <pre>
     * 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string timestamp = 32;</code>
     */
    java.lang.String getTimestamp();
    /**
     * <pre>
     * 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string timestamp = 32;</code>
     */
    com.google.protobuf.ByteString
        getTimestampBytes();

    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */
    int getExtMapCount();
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */
    boolean containsExtMap(
        java.lang.String key);
    /**
     * Use {@link #getExtMapMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getExtMap();
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getExtMapMap();
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    java.lang.String getExtMapOrDefault(
        java.lang.String key,
        java.lang.String defaultValue);
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    java.lang.String getExtMapOrThrow(
        java.lang.String key);
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferRequest}
   */
  public  static final class SetBufferRequest extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferRequest)
      SetBufferRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetBufferRequest.newBuilder() to construct.
    private SetBufferRequest(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetBufferRequest() {
      actId_ = 0L;
      seq_ = "";
      ratio_ = 0;
      roundDown_ = false;
      beginTime_ = "";
      endTime_ = "";
      policy_ = 0;
      global_ = false;
      roles_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      rankings_ = java.util.Collections.emptyList();
      timestamp_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SetBufferRequest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              actId_ = input.readInt64();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              seq_ = s;
              break;
            }
            case 80: {

              ratio_ = input.readInt32();
              break;
            }
            case 88: {

              roundDown_ = input.readBool();
              break;
            }
            case 98: {
              java.lang.String s = input.readStringRequireUtf8();

              beginTime_ = s;
              break;
            }
            case 106: {
              java.lang.String s = input.readStringRequireUtf8();

              endTime_ = s;
              break;
            }
            case 120: {

              policy_ = input.readInt32();
              break;
            }
            case 168: {

              global_ = input.readBool();
              break;
            }
            case 178: {
              java.lang.String s = input.readStringRequireUtf8();
              if (!((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
                roles_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000100;
              }
              roles_.add(s);
              break;
            }
            case 184: {
              if (!((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
                rankings_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000200;
              }
              rankings_.add(input.readInt32());
              break;
            }
            case 186: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000200) == 0x00000200) && input.getBytesUntilLimit() > 0) {
                rankings_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000200;
              }
              while (input.getBytesUntilLimit() > 0) {
                rankings_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 250: {
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.Builder subBuilder = null;
              if (memberFilter_ != null) {
                subBuilder = memberFilter_.toBuilder();
              }
              memberFilter_ = input.readMessage(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(memberFilter_);
                memberFilter_ = subBuilder.buildPartial();
              }

              break;
            }
            case 258: {
              java.lang.String s = input.readStringRequireUtf8();

              timestamp_ = s;
              break;
            }
            case 794: {
              if (!((mutable_bitField0_ & 0x00001000) == 0x00001000)) {
                extMap_ = com.google.protobuf.MapField.newMapField(
                    ExtMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00001000;
              }
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              extMap__ = input.readMessage(
                  ExtMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extMap_.getMutableMap().put(
                  extMap__.getKey(), extMap__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000100) == 0x00000100)) {
          roles_ = roles_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000200) == 0x00000200)) {
          rankings_ = java.util.Collections.unmodifiableList(rankings_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 99:
          return internalGetExtMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest.Builder.class);
    }

    private int bitField0_;
    public static final int ACTID_FIELD_NUMBER = 1;
    private long actId_;
    /**
     * <pre>
     * 活动id
     * </pre>
     *
     * <code>int64 actId = 1;</code>
     */
    public long getActId() {
      return actId_;
    }

    public static final int SEQ_FIELD_NUMBER = 2;
    private volatile java.lang.Object seq_;
    /**
     * <pre>
     * 请求seq（服务端24小时内去重），在actId下唯一即可
     * </pre>
     *
     * <code>string seq = 2;</code>
     */
    public java.lang.String getSeq() {
      java.lang.Object ref = seq_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        seq_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 请求seq（服务端24小时内去重），在actId下唯一即可
     * </pre>
     *
     * <code>string seq = 2;</code>
     */
    public com.google.protobuf.ByteString
        getSeqBytes() {
      java.lang.Object ref = seq_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        seq_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int RATIO_FIELD_NUMBER = 10;
    private int ratio_;
    /**
     * <pre>
     * 加成设置
     * </pre>
     *
     * <code>int32 ratio = 10;</code>
     */
    public int getRatio() {
      return ratio_;
    }

    public static final int ROUNDDOWN_FIELD_NUMBER = 11;
    private boolean roundDown_;
    /**
     * <pre>
     * 取整指示， true：向下，false：向上
     * </pre>
     *
     * <code>bool roundDown = 11;</code>
     */
    public boolean getRoundDown() {
      return roundDown_;
    }

    public static final int BEGINTIME_FIELD_NUMBER = 12;
    private volatile java.lang.Object beginTime_;
    /**
     * <pre>
     * 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string beginTime = 12;</code>
     */
    public java.lang.String getBeginTime() {
      java.lang.Object ref = beginTime_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        beginTime_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string beginTime = 12;</code>
     */
    public com.google.protobuf.ByteString
        getBeginTimeBytes() {
      java.lang.Object ref = beginTime_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        beginTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ENDTIME_FIELD_NUMBER = 13;
    private volatile java.lang.Object endTime_;
    /**
     * <pre>
     * 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string endTime = 13;</code>
     */
    public java.lang.String getEndTime() {
      java.lang.Object ref = endTime_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        endTime_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string endTime = 13;</code>
     */
    public com.google.protobuf.ByteString
        getEndTimeBytes() {
      java.lang.Object ref = endTime_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        endTime_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int POLICY_FIELD_NUMBER = 15;
    private int policy_;
    /**
     * <pre>
     * 当相同成员过滤key存在时的处理策略， 0：覆盖（默认值），1：不覆盖，其它值待后续实现。。。
     * </pre>
     *
     * <code>int32 policy = 15;</code>
     */
    public int getPolicy() {
      return policy_;
    }

    public static final int GLOBAL_FIELD_NUMBER = 21;
    private boolean global_;
    /**
     * <pre>
     * 加成范围
     * </pre>
     *
     * <code>bool global = 21;</code>
     */
    public boolean getGlobal() {
      return global_;
    }

    public static final int ROLES_FIELD_NUMBER = 22;
    private com.google.protobuf.LazyStringList roles_;
    /**
     * <pre>
     * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
     * </pre>
     *
     * <code>repeated string roles = 22;</code>
     */
    public com.google.protobuf.ProtocolStringList
        getRolesList() {
      return roles_;
    }
    /**
     * <pre>
     * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
     * </pre>
     *
     * <code>repeated string roles = 22;</code>
     */
    public int getRolesCount() {
      return roles_.size();
    }
    /**
     * <pre>
     * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
     * </pre>
     *
     * <code>repeated string roles = 22;</code>
     */
    public java.lang.String getRoles(int index) {
      return roles_.get(index);
    }
    /**
     * <pre>
     * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
     * </pre>
     *
     * <code>repeated string roles = 22;</code>
     */
    public com.google.protobuf.ByteString
        getRolesBytes(int index) {
      return roles_.getByteString(index);
    }

    public static final int RANKINGS_FIELD_NUMBER = 23;
    private java.util.List<java.lang.Integer> rankings_;
    /**
     * <pre>
     * 为指定榜单做的加成，优先级高于 roles 中的指定
     * </pre>
     *
     * <code>repeated int32 rankings = 23;</code>
     */
    public java.util.List<java.lang.Integer>
        getRankingsList() {
      return rankings_;
    }
    /**
     * <pre>
     * 为指定榜单做的加成，优先级高于 roles 中的指定
     * </pre>
     *
     * <code>repeated int32 rankings = 23;</code>
     */
    public int getRankingsCount() {
      return rankings_.size();
    }
    /**
     * <pre>
     * 为指定榜单做的加成，优先级高于 roles 中的指定
     * </pre>
     *
     * <code>repeated int32 rankings = 23;</code>
     */
    public int getRankings(int index) {
      return rankings_.get(index);
    }
    private int rankingsMemoizedSerializedSize = -1;

    public static final int MEMBERFILTER_FIELD_NUMBER = 31;
    private com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter memberFilter_;
    /**
     * <pre>
     * 加成过滤条件
     * </pre>
     *
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
     */
    public boolean hasMemberFilter() {
      return memberFilter_ != null;
    }
    /**
     * <pre>
     * 加成过滤条件
     * </pre>
     *
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter getMemberFilter() {
      return memberFilter_ == null ? com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.getDefaultInstance() : memberFilter_;
    }
    /**
     * <pre>
     * 加成过滤条件
     * </pre>
     *
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilterOrBuilder getMemberFilterOrBuilder() {
      return getMemberFilter();
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 32;
    private volatile java.lang.Object timestamp_;
    /**
     * <pre>
     * 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string timestamp = 32;</code>
     */
    public java.lang.String getTimestamp() {
      java.lang.Object ref = timestamp_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        timestamp_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
     * </pre>
     *
     * <code>string timestamp = 32;</code>
     */
    public com.google.protobuf.ByteString
        getTimestampBytes() {
      java.lang.Object ref = timestamp_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        timestamp_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EXTMAP_FIELD_NUMBER = 99;
    private static final class ExtMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_ExtMapEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> extMap_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetExtMap() {
      if (extMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtMapDefaultEntryHolder.defaultEntry);
      }
      return extMap_;
    }

    public int getExtMapCount() {
      return internalGetExtMap().getMap().size();
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public boolean containsExtMap(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      return internalGetExtMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtMapMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
      return getExtMapMap();
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public java.util.Map<java.lang.String, java.lang.String> getExtMapMap() {
      return internalGetExtMap().getMap();
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public java.lang.String getExtMapOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public java.lang.String getExtMapOrThrow(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtMap().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (actId_ != 0L) {
        output.writeInt64(1, actId_);
      }
      if (!getSeqBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, seq_);
      }
      if (ratio_ != 0) {
        output.writeInt32(10, ratio_);
      }
      if (roundDown_ != false) {
        output.writeBool(11, roundDown_);
      }
      if (!getBeginTimeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, beginTime_);
      }
      if (!getEndTimeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, endTime_);
      }
      if (policy_ != 0) {
        output.writeInt32(15, policy_);
      }
      if (global_ != false) {
        output.writeBool(21, global_);
      }
      for (int i = 0; i < roles_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 22, roles_.getRaw(i));
      }
      if (getRankingsList().size() > 0) {
        output.writeUInt32NoTag(186);
        output.writeUInt32NoTag(rankingsMemoizedSerializedSize);
      }
      for (int i = 0; i < rankings_.size(); i++) {
        output.writeInt32NoTag(rankings_.get(i));
      }
      if (memberFilter_ != null) {
        output.writeMessage(31, getMemberFilter());
      }
      if (!getTimestampBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 32, timestamp_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtMap(),
          ExtMapDefaultEntryHolder.defaultEntry,
          99);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (actId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, actId_);
      }
      if (!getSeqBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, seq_);
      }
      if (ratio_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, ratio_);
      }
      if (roundDown_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(11, roundDown_);
      }
      if (!getBeginTimeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, beginTime_);
      }
      if (!getEndTimeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, endTime_);
      }
      if (policy_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(15, policy_);
      }
      if (global_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(21, global_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < roles_.size(); i++) {
          dataSize += computeStringSizeNoTag(roles_.getRaw(i));
        }
        size += dataSize;
        size += 2 * getRolesList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rankings_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(rankings_.get(i));
        }
        size += dataSize;
        if (!getRankingsList().isEmpty()) {
          size += 2;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rankingsMemoizedSerializedSize = dataSize;
      }
      if (memberFilter_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(31, getMemberFilter());
      }
      if (!getTimestampBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(32, timestamp_);
      }
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetExtMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        extMap__ = ExtMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(99, extMap__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest other = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest) obj;

      boolean result = true;
      result = result && (getActId()
          == other.getActId());
      result = result && getSeq()
          .equals(other.getSeq());
      result = result && (getRatio()
          == other.getRatio());
      result = result && (getRoundDown()
          == other.getRoundDown());
      result = result && getBeginTime()
          .equals(other.getBeginTime());
      result = result && getEndTime()
          .equals(other.getEndTime());
      result = result && (getPolicy()
          == other.getPolicy());
      result = result && (getGlobal()
          == other.getGlobal());
      result = result && getRolesList()
          .equals(other.getRolesList());
      result = result && getRankingsList()
          .equals(other.getRankingsList());
      result = result && (hasMemberFilter() == other.hasMemberFilter());
      if (hasMemberFilter()) {
        result = result && getMemberFilter()
            .equals(other.getMemberFilter());
      }
      result = result && getTimestamp()
          .equals(other.getTimestamp());
      result = result && internalGetExtMap().equals(
          other.internalGetExtMap());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ACTID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getActId());
      hash = (37 * hash) + SEQ_FIELD_NUMBER;
      hash = (53 * hash) + getSeq().hashCode();
      hash = (37 * hash) + RATIO_FIELD_NUMBER;
      hash = (53 * hash) + getRatio();
      hash = (37 * hash) + ROUNDDOWN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getRoundDown());
      hash = (37 * hash) + BEGINTIME_FIELD_NUMBER;
      hash = (53 * hash) + getBeginTime().hashCode();
      hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + getEndTime().hashCode();
      hash = (37 * hash) + POLICY_FIELD_NUMBER;
      hash = (53 * hash) + getPolicy();
      hash = (37 * hash) + GLOBAL_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getGlobal());
      if (getRolesCount() > 0) {
        hash = (37 * hash) + ROLES_FIELD_NUMBER;
        hash = (53 * hash) + getRolesList().hashCode();
      }
      if (getRankingsCount() > 0) {
        hash = (37 * hash) + RANKINGS_FIELD_NUMBER;
        hash = (53 * hash) + getRankingsList().hashCode();
      }
      if (hasMemberFilter()) {
        hash = (37 * hash) + MEMBERFILTER_FIELD_NUMBER;
        hash = (53 * hash) + getMemberFilter().hashCode();
      }
      hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + getTimestamp().hashCode();
      if (!internalGetExtMap().getMap().isEmpty()) {
        hash = (37 * hash) + EXTMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtMap().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferRequest)
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 99:
            return internalGetExtMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 99:
            return internalGetMutableExtMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        actId_ = 0L;

        seq_ = "";

        ratio_ = 0;

        roundDown_ = false;

        beginTime_ = "";

        endTime_ = "";

        policy_ = 0;

        global_ = false;

        roles_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        rankings_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);
        if (memberFilterBuilder_ == null) {
          memberFilter_ = null;
        } else {
          memberFilter_ = null;
          memberFilterBuilder_ = null;
        }
        timestamp_ = "";

        internalGetMutableExtMap().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest build() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest result = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.actId_ = actId_;
        result.seq_ = seq_;
        result.ratio_ = ratio_;
        result.roundDown_ = roundDown_;
        result.beginTime_ = beginTime_;
        result.endTime_ = endTime_;
        result.policy_ = policy_;
        result.global_ = global_;
        if (((bitField0_ & 0x00000100) == 0x00000100)) {
          roles_ = roles_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000100);
        }
        result.roles_ = roles_;
        if (((bitField0_ & 0x00000200) == 0x00000200)) {
          rankings_ = java.util.Collections.unmodifiableList(rankings_);
          bitField0_ = (bitField0_ & ~0x00000200);
        }
        result.rankings_ = rankings_;
        if (memberFilterBuilder_ == null) {
          result.memberFilter_ = memberFilter_;
        } else {
          result.memberFilter_ = memberFilterBuilder_.build();
        }
        result.timestamp_ = timestamp_;
        result.extMap_ = internalGetExtMap();
        result.extMap_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest.getDefaultInstance()) return this;
        if (other.getActId() != 0L) {
          setActId(other.getActId());
        }
        if (!other.getSeq().isEmpty()) {
          seq_ = other.seq_;
          onChanged();
        }
        if (other.getRatio() != 0) {
          setRatio(other.getRatio());
        }
        if (other.getRoundDown() != false) {
          setRoundDown(other.getRoundDown());
        }
        if (!other.getBeginTime().isEmpty()) {
          beginTime_ = other.beginTime_;
          onChanged();
        }
        if (!other.getEndTime().isEmpty()) {
          endTime_ = other.endTime_;
          onChanged();
        }
        if (other.getPolicy() != 0) {
          setPolicy(other.getPolicy());
        }
        if (other.getGlobal() != false) {
          setGlobal(other.getGlobal());
        }
        if (!other.roles_.isEmpty()) {
          if (roles_.isEmpty()) {
            roles_ = other.roles_;
            bitField0_ = (bitField0_ & ~0x00000100);
          } else {
            ensureRolesIsMutable();
            roles_.addAll(other.roles_);
          }
          onChanged();
        }
        if (!other.rankings_.isEmpty()) {
          if (rankings_.isEmpty()) {
            rankings_ = other.rankings_;
            bitField0_ = (bitField0_ & ~0x00000200);
          } else {
            ensureRankingsIsMutable();
            rankings_.addAll(other.rankings_);
          }
          onChanged();
        }
        if (other.hasMemberFilter()) {
          mergeMemberFilter(other.getMemberFilter());
        }
        if (!other.getTimestamp().isEmpty()) {
          timestamp_ = other.timestamp_;
          onChanged();
        }
        internalGetMutableExtMap().mergeFrom(
            other.internalGetExtMap());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long actId_ ;
      /**
       * <pre>
       * 活动id
       * </pre>
       *
       * <code>int64 actId = 1;</code>
       */
      public long getActId() {
        return actId_;
      }
      /**
       * <pre>
       * 活动id
       * </pre>
       *
       * <code>int64 actId = 1;</code>
       */
      public Builder setActId(long value) {
        
        actId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活动id
       * </pre>
       *
       * <code>int64 actId = 1;</code>
       */
      public Builder clearActId() {
        
        actId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object seq_ = "";
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一即可
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public java.lang.String getSeq() {
        java.lang.Object ref = seq_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          seq_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一即可
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public com.google.protobuf.ByteString
          getSeqBytes() {
        java.lang.Object ref = seq_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          seq_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一即可
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public Builder setSeq(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        seq_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一即可
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public Builder clearSeq() {
        
        seq_ = getDefaultInstance().getSeq();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一即可
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public Builder setSeqBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        seq_ = value;
        onChanged();
        return this;
      }

      private int ratio_ ;
      /**
       * <pre>
       * 加成设置
       * </pre>
       *
       * <code>int32 ratio = 10;</code>
       */
      public int getRatio() {
        return ratio_;
      }
      /**
       * <pre>
       * 加成设置
       * </pre>
       *
       * <code>int32 ratio = 10;</code>
       */
      public Builder setRatio(int value) {
        
        ratio_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加成设置
       * </pre>
       *
       * <code>int32 ratio = 10;</code>
       */
      public Builder clearRatio() {
        
        ratio_ = 0;
        onChanged();
        return this;
      }

      private boolean roundDown_ ;
      /**
       * <pre>
       * 取整指示， true：向下，false：向上
       * </pre>
       *
       * <code>bool roundDown = 11;</code>
       */
      public boolean getRoundDown() {
        return roundDown_;
      }
      /**
       * <pre>
       * 取整指示， true：向下，false：向上
       * </pre>
       *
       * <code>bool roundDown = 11;</code>
       */
      public Builder setRoundDown(boolean value) {
        
        roundDown_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 取整指示， true：向下，false：向上
       * </pre>
       *
       * <code>bool roundDown = 11;</code>
       */
      public Builder clearRoundDown() {
        
        roundDown_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object beginTime_ = "";
      /**
       * <pre>
       * 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string beginTime = 12;</code>
       */
      public java.lang.String getBeginTime() {
        java.lang.Object ref = beginTime_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          beginTime_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string beginTime = 12;</code>
       */
      public com.google.protobuf.ByteString
          getBeginTimeBytes() {
        java.lang.Object ref = beginTime_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          beginTime_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string beginTime = 12;</code>
       */
      public Builder setBeginTime(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        beginTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string beginTime = 12;</code>
       */
      public Builder clearBeginTime() {
        
        beginTime_ = getDefaultInstance().getBeginTime();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string beginTime = 12;</code>
       */
      public Builder setBeginTimeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        beginTime_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object endTime_ = "";
      /**
       * <pre>
       * 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string endTime = 13;</code>
       */
      public java.lang.String getEndTime() {
        java.lang.Object ref = endTime_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          endTime_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string endTime = 13;</code>
       */
      public com.google.protobuf.ByteString
          getEndTimeBytes() {
        java.lang.Object ref = endTime_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          endTime_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string endTime = 13;</code>
       */
      public Builder setEndTime(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        endTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string endTime = 13;</code>
       */
      public Builder clearEndTime() {
        
        endTime_ = getDefaultInstance().getEndTime();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string endTime = 13;</code>
       */
      public Builder setEndTimeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        endTime_ = value;
        onChanged();
        return this;
      }

      private int policy_ ;
      /**
       * <pre>
       * 当相同成员过滤key存在时的处理策略， 0：覆盖（默认值），1：不覆盖，其它值待后续实现。。。
       * </pre>
       *
       * <code>int32 policy = 15;</code>
       */
      public int getPolicy() {
        return policy_;
      }
      /**
       * <pre>
       * 当相同成员过滤key存在时的处理策略， 0：覆盖（默认值），1：不覆盖，其它值待后续实现。。。
       * </pre>
       *
       * <code>int32 policy = 15;</code>
       */
      public Builder setPolicy(int value) {
        
        policy_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当相同成员过滤key存在时的处理策略， 0：覆盖（默认值），1：不覆盖，其它值待后续实现。。。
       * </pre>
       *
       * <code>int32 policy = 15;</code>
       */
      public Builder clearPolicy() {
        
        policy_ = 0;
        onChanged();
        return this;
      }

      private boolean global_ ;
      /**
       * <pre>
       * 加成范围
       * </pre>
       *
       * <code>bool global = 21;</code>
       */
      public boolean getGlobal() {
        return global_;
      }
      /**
       * <pre>
       * 加成范围
       * </pre>
       *
       * <code>bool global = 21;</code>
       */
      public Builder setGlobal(boolean value) {
        
        global_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 加成范围
       * </pre>
       *
       * <code>bool global = 21;</code>
       */
      public Builder clearGlobal() {
        
        global_ = false;
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList roles_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureRolesIsMutable() {
        if (!((bitField0_ & 0x00000100) == 0x00000100)) {
          roles_ = new com.google.protobuf.LazyStringArrayList(roles_);
          bitField0_ |= 0x00000100;
         }
      }
      /**
       * <pre>
       * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
       * </pre>
       *
       * <code>repeated string roles = 22;</code>
       */
      public com.google.protobuf.ProtocolStringList
          getRolesList() {
        return roles_.getUnmodifiableView();
      }
      /**
       * <pre>
       * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
       * </pre>
       *
       * <code>repeated string roles = 22;</code>
       */
      public int getRolesCount() {
        return roles_.size();
      }
      /**
       * <pre>
       * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
       * </pre>
       *
       * <code>repeated string roles = 22;</code>
       */
      public java.lang.String getRoles(int index) {
        return roles_.get(index);
      }
      /**
       * <pre>
       * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
       * </pre>
       *
       * <code>repeated string roles = 22;</code>
       */
      public com.google.protobuf.ByteString
          getRolesBytes(int index) {
        return roles_.getByteString(index);
      }
      /**
       * <pre>
       * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
       * </pre>
       *
       * <code>repeated string roles = 22;</code>
       */
      public Builder setRoles(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureRolesIsMutable();
        roles_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
       * </pre>
       *
       * <code>repeated string roles = 22;</code>
       */
      public Builder addRoles(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureRolesIsMutable();
        roles_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
       * </pre>
       *
       * <code>repeated string roles = 22;</code>
       */
      public Builder addAllRoles(
          java.lang.Iterable<java.lang.String> values) {
        ensureRolesIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, roles_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
       * </pre>
       *
       * <code>repeated string roles = 22;</code>
       */
      public Builder clearRoles() {
        roles_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000100);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
       * </pre>
       *
       * <code>repeated string roles = 22;</code>
       */
      public Builder addRolesBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        ensureRolesIsMutable();
        roles_.add(value);
        onChanged();
        return this;
      }

      private java.util.List<java.lang.Integer> rankings_ = java.util.Collections.emptyList();
      private void ensureRankingsIsMutable() {
        if (!((bitField0_ & 0x00000200) == 0x00000200)) {
          rankings_ = new java.util.ArrayList<java.lang.Integer>(rankings_);
          bitField0_ |= 0x00000200;
         }
      }
      /**
       * <pre>
       * 为指定榜单做的加成，优先级高于 roles 中的指定
       * </pre>
       *
       * <code>repeated int32 rankings = 23;</code>
       */
      public java.util.List<java.lang.Integer>
          getRankingsList() {
        return java.util.Collections.unmodifiableList(rankings_);
      }
      /**
       * <pre>
       * 为指定榜单做的加成，优先级高于 roles 中的指定
       * </pre>
       *
       * <code>repeated int32 rankings = 23;</code>
       */
      public int getRankingsCount() {
        return rankings_.size();
      }
      /**
       * <pre>
       * 为指定榜单做的加成，优先级高于 roles 中的指定
       * </pre>
       *
       * <code>repeated int32 rankings = 23;</code>
       */
      public int getRankings(int index) {
        return rankings_.get(index);
      }
      /**
       * <pre>
       * 为指定榜单做的加成，优先级高于 roles 中的指定
       * </pre>
       *
       * <code>repeated int32 rankings = 23;</code>
       */
      public Builder setRankings(
          int index, int value) {
        ensureRankingsIsMutable();
        rankings_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 为指定榜单做的加成，优先级高于 roles 中的指定
       * </pre>
       *
       * <code>repeated int32 rankings = 23;</code>
       */
      public Builder addRankings(int value) {
        ensureRankingsIsMutable();
        rankings_.add(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 为指定榜单做的加成，优先级高于 roles 中的指定
       * </pre>
       *
       * <code>repeated int32 rankings = 23;</code>
       */
      public Builder addAllRankings(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureRankingsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rankings_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 为指定榜单做的加成，优先级高于 roles 中的指定
       * </pre>
       *
       * <code>repeated int32 rankings = 23;</code>
       */
      public Builder clearRankings() {
        rankings_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }

      private com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter memberFilter_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.Builder, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilterOrBuilder> memberFilterBuilder_;
      /**
       * <pre>
       * 加成过滤条件
       * </pre>
       *
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
       */
      public boolean hasMemberFilter() {
        return memberFilterBuilder_ != null || memberFilter_ != null;
      }
      /**
       * <pre>
       * 加成过滤条件
       * </pre>
       *
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter getMemberFilter() {
        if (memberFilterBuilder_ == null) {
          return memberFilter_ == null ? com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.getDefaultInstance() : memberFilter_;
        } else {
          return memberFilterBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 加成过滤条件
       * </pre>
       *
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
       */
      public Builder setMemberFilter(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter value) {
        if (memberFilterBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          memberFilter_ = value;
          onChanged();
        } else {
          memberFilterBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       * 加成过滤条件
       * </pre>
       *
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
       */
      public Builder setMemberFilter(
          com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.Builder builderForValue) {
        if (memberFilterBuilder_ == null) {
          memberFilter_ = builderForValue.build();
          onChanged();
        } else {
          memberFilterBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       * 加成过滤条件
       * </pre>
       *
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
       */
      public Builder mergeMemberFilter(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter value) {
        if (memberFilterBuilder_ == null) {
          if (memberFilter_ != null) {
            memberFilter_ =
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.newBuilder(memberFilter_).mergeFrom(value).buildPartial();
          } else {
            memberFilter_ = value;
          }
          onChanged();
        } else {
          memberFilterBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       * 加成过滤条件
       * </pre>
       *
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
       */
      public Builder clearMemberFilter() {
        if (memberFilterBuilder_ == null) {
          memberFilter_ = null;
          onChanged();
        } else {
          memberFilter_ = null;
          memberFilterBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       * 加成过滤条件
       * </pre>
       *
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.Builder getMemberFilterBuilder() {
        
        onChanged();
        return getMemberFilterFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 加成过滤条件
       * </pre>
       *
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilterOrBuilder getMemberFilterOrBuilder() {
        if (memberFilterBuilder_ != null) {
          return memberFilterBuilder_.getMessageOrBuilder();
        } else {
          return memberFilter_ == null ?
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.getDefaultInstance() : memberFilter_;
        }
      }
      /**
       * <pre>
       * 加成过滤条件
       * </pre>
       *
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.MemberFilter memberFilter = 31;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.Builder, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilterOrBuilder> 
          getMemberFilterFieldBuilder() {
        if (memberFilterBuilder_ == null) {
          memberFilterBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilter.Builder, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.MemberFilterOrBuilder>(
                  getMemberFilter(),
                  getParentForChildren(),
                  isClean());
          memberFilter_ = null;
        }
        return memberFilterBuilder_;
      }

      private java.lang.Object timestamp_ = "";
      /**
       * <pre>
       * 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string timestamp = 32;</code>
       */
      public java.lang.String getTimestamp() {
        java.lang.Object ref = timestamp_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          timestamp_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string timestamp = 32;</code>
       */
      public com.google.protobuf.ByteString
          getTimestampBytes() {
        java.lang.Object ref = timestamp_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          timestamp_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string timestamp = 32;</code>
       */
      public Builder setTimestamp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        timestamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string timestamp = 32;</code>
       */
      public Builder clearTimestamp() {
        
        timestamp_ = getDefaultInstance().getTimestamp();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
       * </pre>
       *
       * <code>string timestamp = 32;</code>
       */
      public Builder setTimestampBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        timestamp_ = value;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> extMap_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetExtMap() {
        if (extMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtMapDefaultEntryHolder.defaultEntry);
        }
        return extMap_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableExtMap() {
        onChanged();;
        if (extMap_ == null) {
          extMap_ = com.google.protobuf.MapField.newMapField(
              ExtMapDefaultEntryHolder.defaultEntry);
        }
        if (!extMap_.isMutable()) {
          extMap_ = extMap_.copy();
        }
        return extMap_;
      }

      public int getExtMapCount() {
        return internalGetExtMap().getMap().size();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public boolean containsExtMap(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        return internalGetExtMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtMapMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
        return getExtMapMap();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public java.util.Map<java.lang.String, java.lang.String> getExtMapMap() {
        return internalGetExtMap().getMap();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public java.lang.String getExtMapOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public java.lang.String getExtMapOrThrow(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtMap().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtMap() {
        internalGetMutableExtMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public Builder removeExtMap(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableExtMap() {
        return internalGetMutableExtMap().getMutableMap();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */
      public Builder putExtMap(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public Builder putAllExtMap(
          java.util.Map<java.lang.String, java.lang.String> values) {
        internalGetMutableExtMap().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferRequest)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferRequest)
    private static final com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SetBufferRequest>
        PARSER = new com.google.protobuf.AbstractParser<SetBufferRequest>() {
      @java.lang.Override
      public SetBufferRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SetBufferRequest(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SetBufferRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetBufferRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SetBufferResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 活动id
     * </pre>
     *
     * <code>int64 actId = 1;</code>
     */
    long getActId();

    /**
     * <pre>
     * 请求seq（服务端24小时内去重），在actId下唯一
     * </pre>
     *
     * <code>string seq = 2;</code>
     */
    java.lang.String getSeq();
    /**
     * <pre>
     * 请求seq（服务端24小时内去重），在actId下唯一
     * </pre>
     *
     * <code>string seq = 2;</code>
     */
    com.google.protobuf.ByteString
        getSeqBytes();

    /**
     * <pre>
     * 结果码，0-成功，非零-失败或其他含义
     * </pre>
     *
     * <code>int32 code = 3;</code>
     */
    int getCode();

    /**
     * <pre>
     * 成功或失败的提示信息
     * </pre>
     *
     * <code>string reason = 4;</code>
     */
    java.lang.String getReason();
    /**
     * <pre>
     * 成功或失败的提示信息
     * </pre>
     *
     * <code>string reason = 4;</code>
     */
    com.google.protobuf.ByteString
        getReasonBytes();

    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */
    int getExtMapCount();
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */
    boolean containsExtMap(
        java.lang.String key);
    /**
     * Use {@link #getExtMapMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.String, java.lang.String>
    getExtMap();
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */
    java.util.Map<java.lang.String, java.lang.String>
    getExtMapMap();
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    java.lang.String getExtMapOrDefault(
        java.lang.String key,
        java.lang.String defaultValue);
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    java.lang.String getExtMapOrThrow(
        java.lang.String key);
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferResponse}
   */
  public  static final class SetBufferResponse extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferResponse)
      SetBufferResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SetBufferResponse.newBuilder() to construct.
    private SetBufferResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SetBufferResponse() {
      actId_ = 0L;
      seq_ = "";
      code_ = 0;
      reason_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SetBufferResponse(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              actId_ = input.readInt64();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              seq_ = s;
              break;
            }
            case 24: {

              code_ = input.readInt32();
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              reason_ = s;
              break;
            }
            case 794: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                extMap_ = com.google.protobuf.MapField.newMapField(
                    ExtMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000010;
              }
              com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
              extMap__ = input.readMessage(
                  ExtMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              extMap_.getMutableMap().put(
                  extMap__.getKey(), extMap__.getValue());
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 99:
          return internalGetExtMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse.Builder.class);
    }

    private int bitField0_;
    public static final int ACTID_FIELD_NUMBER = 1;
    private long actId_;
    /**
     * <pre>
     * 活动id
     * </pre>
     *
     * <code>int64 actId = 1;</code>
     */
    public long getActId() {
      return actId_;
    }

    public static final int SEQ_FIELD_NUMBER = 2;
    private volatile java.lang.Object seq_;
    /**
     * <pre>
     * 请求seq（服务端24小时内去重），在actId下唯一
     * </pre>
     *
     * <code>string seq = 2;</code>
     */
    public java.lang.String getSeq() {
      java.lang.Object ref = seq_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        seq_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 请求seq（服务端24小时内去重），在actId下唯一
     * </pre>
     *
     * <code>string seq = 2;</code>
     */
    public com.google.protobuf.ByteString
        getSeqBytes() {
      java.lang.Object ref = seq_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        seq_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CODE_FIELD_NUMBER = 3;
    private int code_;
    /**
     * <pre>
     * 结果码，0-成功，非零-失败或其他含义
     * </pre>
     *
     * <code>int32 code = 3;</code>
     */
    public int getCode() {
      return code_;
    }

    public static final int REASON_FIELD_NUMBER = 4;
    private volatile java.lang.Object reason_;
    /**
     * <pre>
     * 成功或失败的提示信息
     * </pre>
     *
     * <code>string reason = 4;</code>
     */
    public java.lang.String getReason() {
      java.lang.Object ref = reason_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        reason_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 成功或失败的提示信息
     * </pre>
     *
     * <code>string reason = 4;</code>
     */
    public com.google.protobuf.ByteString
        getReasonBytes() {
      java.lang.Object ref = reason_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        reason_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EXTMAP_FIELD_NUMBER = 99;
    private static final class ExtMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.String, java.lang.String> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.String, java.lang.String>newDefaultInstance(
                  com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_ExtMapEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "",
                  com.google.protobuf.WireFormat.FieldType.STRING,
                  "");
    }
    private com.google.protobuf.MapField<
        java.lang.String, java.lang.String> extMap_;
    private com.google.protobuf.MapField<java.lang.String, java.lang.String>
    internalGetExtMap() {
      if (extMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ExtMapDefaultEntryHolder.defaultEntry);
      }
      return extMap_;
    }

    public int getExtMapCount() {
      return internalGetExtMap().getMap().size();
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public boolean containsExtMap(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      return internalGetExtMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getExtMapMap()} instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
      return getExtMapMap();
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public java.util.Map<java.lang.String, java.lang.String> getExtMapMap() {
      return internalGetExtMap().getMap();
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public java.lang.String getExtMapOrDefault(
        java.lang.String key,
        java.lang.String defaultValue) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 扩展数据，双方约定使用
     * </pre>
     *
     * <code>map&lt;string, string&gt; extMap = 99;</code>
     */

    public java.lang.String getExtMapOrThrow(
        java.lang.String key) {
      if (key == null) { throw new java.lang.NullPointerException(); }
      java.util.Map<java.lang.String, java.lang.String> map =
          internalGetExtMap().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (actId_ != 0L) {
        output.writeInt64(1, actId_);
      }
      if (!getSeqBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, seq_);
      }
      if (code_ != 0) {
        output.writeInt32(3, code_);
      }
      if (!getReasonBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, reason_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeStringMapTo(
          output,
          internalGetExtMap(),
          ExtMapDefaultEntryHolder.defaultEntry,
          99);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (actId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, actId_);
      }
      if (!getSeqBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, seq_);
      }
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, code_);
      }
      if (!getReasonBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, reason_);
      }
      for (java.util.Map.Entry<java.lang.String, java.lang.String> entry
           : internalGetExtMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.String, java.lang.String>
        extMap__ = ExtMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(99, extMap__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse other = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse) obj;

      boolean result = true;
      result = result && (getActId()
          == other.getActId());
      result = result && getSeq()
          .equals(other.getSeq());
      result = result && (getCode()
          == other.getCode());
      result = result && getReason()
          .equals(other.getReason());
      result = result && internalGetExtMap().equals(
          other.internalGetExtMap());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ACTID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getActId());
      hash = (37 * hash) + SEQ_FIELD_NUMBER;
      hash = (53 * hash) + getSeq().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + REASON_FIELD_NUMBER;
      hash = (53 * hash) + getReason().hashCode();
      if (!internalGetExtMap().getMap().isEmpty()) {
        hash = (37 * hash) + EXTMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetExtMap().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferResponse)
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 99:
            return internalGetExtMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 99:
            return internalGetMutableExtMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse.class, com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        actId_ = 0L;

        seq_ = "";

        code_ = 0;

        reason_ = "";

        internalGetMutableExtMap().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse build() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse result = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.actId_ = actId_;
        result.seq_ = seq_;
        result.code_ = code_;
        result.reason_ = reason_;
        result.extMap_ = internalGetExtMap();
        result.extMap_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse.getDefaultInstance()) return this;
        if (other.getActId() != 0L) {
          setActId(other.getActId());
        }
        if (!other.getSeq().isEmpty()) {
          seq_ = other.seq_;
          onChanged();
        }
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (!other.getReason().isEmpty()) {
          reason_ = other.reason_;
          onChanged();
        }
        internalGetMutableExtMap().mergeFrom(
            other.internalGetExtMap());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long actId_ ;
      /**
       * <pre>
       * 活动id
       * </pre>
       *
       * <code>int64 actId = 1;</code>
       */
      public long getActId() {
        return actId_;
      }
      /**
       * <pre>
       * 活动id
       * </pre>
       *
       * <code>int64 actId = 1;</code>
       */
      public Builder setActId(long value) {
        
        actId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活动id
       * </pre>
       *
       * <code>int64 actId = 1;</code>
       */
      public Builder clearActId() {
        
        actId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object seq_ = "";
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public java.lang.String getSeq() {
        java.lang.Object ref = seq_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          seq_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public com.google.protobuf.ByteString
          getSeqBytes() {
        java.lang.Object ref = seq_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          seq_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public Builder setSeq(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        seq_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public Builder clearSeq() {
        
        seq_ = getDefaultInstance().getSeq();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 请求seq（服务端24小时内去重），在actId下唯一
       * </pre>
       *
       * <code>string seq = 2;</code>
       */
      public Builder setSeqBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        seq_ = value;
        onChanged();
        return this;
      }

      private int code_ ;
      /**
       * <pre>
       * 结果码，0-成功，非零-失败或其他含义
       * </pre>
       *
       * <code>int32 code = 3;</code>
       */
      public int getCode() {
        return code_;
      }
      /**
       * <pre>
       * 结果码，0-成功，非零-失败或其他含义
       * </pre>
       *
       * <code>int32 code = 3;</code>
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 结果码，0-成功，非零-失败或其他含义
       * </pre>
       *
       * <code>int32 code = 3;</code>
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object reason_ = "";
      /**
       * <pre>
       * 成功或失败的提示信息
       * </pre>
       *
       * <code>string reason = 4;</code>
       */
      public java.lang.String getReason() {
        java.lang.Object ref = reason_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          reason_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 成功或失败的提示信息
       * </pre>
       *
       * <code>string reason = 4;</code>
       */
      public com.google.protobuf.ByteString
          getReasonBytes() {
        java.lang.Object ref = reason_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          reason_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 成功或失败的提示信息
       * </pre>
       *
       * <code>string reason = 4;</code>
       */
      public Builder setReason(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        reason_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成功或失败的提示信息
       * </pre>
       *
       * <code>string reason = 4;</code>
       */
      public Builder clearReason() {
        
        reason_ = getDefaultInstance().getReason();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成功或失败的提示信息
       * </pre>
       *
       * <code>string reason = 4;</code>
       */
      public Builder setReasonBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        reason_ = value;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.String, java.lang.String> extMap_;
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetExtMap() {
        if (extMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ExtMapDefaultEntryHolder.defaultEntry);
        }
        return extMap_;
      }
      private com.google.protobuf.MapField<java.lang.String, java.lang.String>
      internalGetMutableExtMap() {
        onChanged();;
        if (extMap_ == null) {
          extMap_ = com.google.protobuf.MapField.newMapField(
              ExtMapDefaultEntryHolder.defaultEntry);
        }
        if (!extMap_.isMutable()) {
          extMap_ = extMap_.copy();
        }
        return extMap_;
      }

      public int getExtMapCount() {
        return internalGetExtMap().getMap().size();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public boolean containsExtMap(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        return internalGetExtMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getExtMapMap()} instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String> getExtMap() {
        return getExtMapMap();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public java.util.Map<java.lang.String, java.lang.String> getExtMapMap() {
        return internalGetExtMap().getMap();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public java.lang.String getExtMapOrDefault(
          java.lang.String key,
          java.lang.String defaultValue) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public java.lang.String getExtMapOrThrow(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        java.util.Map<java.lang.String, java.lang.String> map =
            internalGetExtMap().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearExtMap() {
        internalGetMutableExtMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public Builder removeExtMap(
          java.lang.String key) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.String, java.lang.String>
      getMutableExtMap() {
        return internalGetMutableExtMap().getMutableMap();
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */
      public Builder putExtMap(
          java.lang.String key,
          java.lang.String value) {
        if (key == null) { throw new java.lang.NullPointerException(); }
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableExtMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 扩展数据，双方约定使用
       * </pre>
       *
       * <code>map&lt;string, string&gt; extMap = 99;</code>
       */

      public Builder putAllExtMap(
          java.util.Map<java.lang.String, java.lang.String> values) {
        internalGetMutableExtMap().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferResponse)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.SetBufferResponse)
    private static final com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SetBufferResponse>
        PARSER = new com.google.protobuf.AbstractParser<SetBufferResponse>() {
      @java.lang.Override
      public SetBufferResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SetBufferResponse(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SetBufferResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SetBufferResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_ExtMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_ExtMapEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_ExtMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_ExtMapEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_ExtMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_ExtMapEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021hdzk_stream.proto\0226com.yy.gameecology." +
      "common.protocol.yypyrpc.hdzk_stream\032\turi" +
      ".proto\"\r\n\004Ping:\005\310>\202\323\017\"\035\n\004Pong\022\016\n\006result\030" +
      "\001 \001(\r:\005\310>\202\325\017\"%\n\007Channel\022\013\n\003sid\030\001 \001(\003\022\r\n\005" +
      "ssids\030\002 \003(\003\"\247\002\n\014MemberFilter\022Q\n\010channels" +
      "\030\001 \003(\0132?.com.yy.gameecology.common.proto" +
      "col.yypyrpc.hdzk_stream.Channel\022\020\n\010sendU" +
      "ids\030\003 \003(\003\022\020\n\010recvUids\030\004 \003(\003\022\017\n\007giftIds\030\005" +
      " \003(\t\022`\n\006extMap\030c \003(\0132P.com.yy.gameecolog" +
      "y.common.protocol.yypyrpc.hdzk_stream.Me" +
      "mberFilter.ExtMapEntry\032-\n\013ExtMapEntry\022\013\n" +
      "\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\300\003\n\020SetBuf" +
      "ferRequest\022\r\n\005actId\030\001 \001(\003\022\013\n\003seq\030\002 \001(\t\022\r" +
      "\n\005ratio\030\n \001(\005\022\021\n\troundDown\030\013 \001(\010\022\021\n\tbegi" +
      "nTime\030\014 \001(\t\022\017\n\007endTime\030\r \001(\t\022\016\n\006policy\030\017" +
      " \001(\005\022\016\n\006global\030\025 \001(\010\022\r\n\005roles\030\026 \003(\t\022\020\n\010r" +
      "ankings\030\027 \003(\005\022Z\n\014memberFilter\030\037 \001(\0132D.co" +
      "m.yy.gameecology.common.protocol.yypyrpc" +
      ".hdzk_stream.MemberFilter\022\021\n\ttimestamp\030 " +
      " \001(\t\022d\n\006extMap\030c \003(\0132T.com.yy.gameecolog" +
      "y.common.protocol.yypyrpc.hdzk_stream.Se" +
      "tBufferRequest.ExtMapEntry\032-\n\013ExtMapEntr" +
      "y\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001:\005\310>\202\327\017" +
      "\"\352\001\n\021SetBufferResponse\022\r\n\005actId\030\001 \001(\003\022\013\n" +
      "\003seq\030\002 \001(\t\022\014\n\004code\030\003 \001(\005\022\016\n\006reason\030\004 \001(\t" +
      "\022e\n\006extMap\030c \003(\0132U.com.yy.gameecology.co" +
      "mmon.protocol.yypyrpc.hdzk_stream.SetBuf" +
      "ferResponse.ExtMapEntry\032-\n\013ExtMapEntry\022\013" +
      "\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001:\005\310>\202\331\0172\273\002" +
      "\n\021HDZKStreamService\022\202\001\n\004ping\022<.com.yy.ga" +
      "meecology.common.protocol.yypyrpc.hdzk_s" +
      "tream.Ping\032<.com.yy.gameecology.common.p" +
      "rotocol.yypyrpc.hdzk_stream.Pong\022\240\001\n\tset" +
      "Buffer\022H.com.yy.gameecology.common.proto" +
      "col.yypyrpc.hdzk_stream.SetBufferRequest" +
      "\032I.com.yy.gameecology.common.protocol.yy" +
      "pyrpc.hdzk_stream.SetBufferResponseB\016B\nH" +
      "DZKStreamP\000b\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          yyp.Uri.getDescriptor(),
        }, assigner);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Ping_descriptor,
        new java.lang.String[] { });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Pong_descriptor,
        new java.lang.String[] { "Result", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_Channel_descriptor,
        new java.lang.String[] { "Sid", "Ssids", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_descriptor,
        new java.lang.String[] { "Channels", "SendUids", "RecvUids", "GiftIds", "ExtMap", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_ExtMapEntry_descriptor =
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_ExtMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_MemberFilter_ExtMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_descriptor,
        new java.lang.String[] { "ActId", "Seq", "Ratio", "RoundDown", "BeginTime", "EndTime", "Policy", "Global", "Roles", "Rankings", "MemberFilter", "Timestamp", "ExtMap", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_ExtMapEntry_descriptor =
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_ExtMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferRequest_ExtMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_descriptor,
        new java.lang.String[] { "ActId", "Seq", "Code", "Reason", "ExtMap", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_ExtMapEntry_descriptor =
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_descriptor.getNestedTypes().get(0);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_ExtMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_hdzk_stream_SetBufferResponse_ExtMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(yyp.Uri.uri);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    yyp.Uri.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
