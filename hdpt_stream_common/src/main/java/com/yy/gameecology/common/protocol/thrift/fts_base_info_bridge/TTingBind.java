/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class TTingBind implements org.apache.thrift.TBase<TTingBind, TTingBind._Fields>, java.io.Serializable, Cloneable, Comparable<TTingBind> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TTingBind");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField APPID_FIELD_DESC = new org.apache.thrift.protocol.TField("appid", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField TING_MGR_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("tingMgrUid", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField TID_FIELD_DESC = new org.apache.thrift.protocol.TField("tid", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField ANCHOR_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("anchorUid", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField CREATE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("createTime", org.apache.thrift.protocol.TType.I64, (short)8);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TTingBindStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TTingBindTupleSchemeFactory());
  }

  public long id; // required
  public int appid; // required
  public long sid; // required
  public long tingMgrUid; // required
  public long tid; // required
  public long anchorUid; // required
  public String expand; // required
  public long createTime; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ID((short)1, "id"),
    APPID((short)2, "appid"),
    SID((short)3, "sid"),
    TING_MGR_UID((short)4, "tingMgrUid"),
    TID((short)5, "tid"),
    ANCHOR_UID((short)6, "anchorUid"),
    EXPAND((short)7, "expand"),
    CREATE_TIME((short)8, "createTime");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // APPID
          return APPID;
        case 3: // SID
          return SID;
        case 4: // TING_MGR_UID
          return TING_MGR_UID;
        case 5: // TID
          return TID;
        case 6: // ANCHOR_UID
          return ANCHOR_UID;
        case 7: // EXPAND
          return EXPAND;
        case 8: // CREATE_TIME
          return CREATE_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ID_ISSET_ID = 0;
  private static final int __APPID_ISSET_ID = 1;
  private static final int __SID_ISSET_ID = 2;
  private static final int __TINGMGRUID_ISSET_ID = 3;
  private static final int __TID_ISSET_ID = 4;
  private static final int __ANCHORUID_ISSET_ID = 5;
  private static final int __CREATETIME_ISSET_ID = 6;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.APPID, new org.apache.thrift.meta_data.FieldMetaData("appid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TING_MGR_UID, new org.apache.thrift.meta_data.FieldMetaData("tingMgrUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TID, new org.apache.thrift.meta_data.FieldMetaData("tid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ANCHOR_UID, new org.apache.thrift.meta_data.FieldMetaData("anchorUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CREATE_TIME, new org.apache.thrift.meta_data.FieldMetaData("createTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TTingBind.class, metaDataMap);
  }

  public TTingBind() {
  }

  public TTingBind(
    long id,
    int appid,
    long sid,
    long tingMgrUid,
    long tid,
    long anchorUid,
    String expand,
    long createTime)
  {
    this();
    this.id = id;
    setIdIsSet(true);
    this.appid = appid;
    setAppidIsSet(true);
    this.sid = sid;
    setSidIsSet(true);
    this.tingMgrUid = tingMgrUid;
    setTingMgrUidIsSet(true);
    this.tid = tid;
    setTidIsSet(true);
    this.anchorUid = anchorUid;
    setAnchorUidIsSet(true);
    this.expand = expand;
    this.createTime = createTime;
    setCreateTimeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TTingBind(TTingBind other) {
    __isset_bitfield = other.__isset_bitfield;
    this.id = other.id;
    this.appid = other.appid;
    this.sid = other.sid;
    this.tingMgrUid = other.tingMgrUid;
    this.tid = other.tid;
    this.anchorUid = other.anchorUid;
    if (other.isSetExpand()) {
      this.expand = other.expand;
    }
    this.createTime = other.createTime;
  }

  public TTingBind deepCopy() {
    return new TTingBind(this);
  }

  @Override
  public void clear() {
    setIdIsSet(false);
    this.id = 0;
    setAppidIsSet(false);
    this.appid = 0;
    setSidIsSet(false);
    this.sid = 0;
    setTingMgrUidIsSet(false);
    this.tingMgrUid = 0;
    setTidIsSet(false);
    this.tid = 0;
    setAnchorUidIsSet(false);
    this.anchorUid = 0;
    this.expand = null;
    setCreateTimeIsSet(false);
    this.createTime = 0;
  }

  public long getId() {
    return this.id;
  }

  public TTingBind setId(long id) {
    this.id = id;
    setIdIsSet(true);
    return this;
  }

  public void unsetId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
  }

  public void setIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
  }

  public int getAppid() {
    return this.appid;
  }

  public TTingBind setAppid(int appid) {
    this.appid = appid;
    setAppidIsSet(true);
    return this;
  }

  public void unsetAppid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  /** Returns true if field appid is set (has been assigned a value) and false otherwise */
  public boolean isSetAppid() {
    return EncodingUtils.testBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  public void setAppidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __APPID_ISSET_ID, value);
  }

  public long getSid() {
    return this.sid;
  }

  public TTingBind setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getTingMgrUid() {
    return this.tingMgrUid;
  }

  public TTingBind setTingMgrUid(long tingMgrUid) {
    this.tingMgrUid = tingMgrUid;
    setTingMgrUidIsSet(true);
    return this;
  }

  public void unsetTingMgrUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TINGMGRUID_ISSET_ID);
  }

  /** Returns true if field tingMgrUid is set (has been assigned a value) and false otherwise */
  public boolean isSetTingMgrUid() {
    return EncodingUtils.testBit(__isset_bitfield, __TINGMGRUID_ISSET_ID);
  }

  public void setTingMgrUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TINGMGRUID_ISSET_ID, value);
  }

  public long getTid() {
    return this.tid;
  }

  public TTingBind setTid(long tid) {
    this.tid = tid;
    setTidIsSet(true);
    return this;
  }

  public void unsetTid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TID_ISSET_ID);
  }

  /** Returns true if field tid is set (has been assigned a value) and false otherwise */
  public boolean isSetTid() {
    return EncodingUtils.testBit(__isset_bitfield, __TID_ISSET_ID);
  }

  public void setTidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TID_ISSET_ID, value);
  }

  public long getAnchorUid() {
    return this.anchorUid;
  }

  public TTingBind setAnchorUid(long anchorUid) {
    this.anchorUid = anchorUid;
    setAnchorUidIsSet(true);
    return this;
  }

  public void unsetAnchorUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ANCHORUID_ISSET_ID);
  }

  /** Returns true if field anchorUid is set (has been assigned a value) and false otherwise */
  public boolean isSetAnchorUid() {
    return EncodingUtils.testBit(__isset_bitfield, __ANCHORUID_ISSET_ID);
  }

  public void setAnchorUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ANCHORUID_ISSET_ID, value);
  }

  public String getExpand() {
    return this.expand;
  }

  public TTingBind setExpand(String expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  public long getCreateTime() {
    return this.createTime;
  }

  public TTingBind setCreateTime(long createTime) {
    this.createTime = createTime;
    setCreateTimeIsSet(true);
    return this;
  }

  public void unsetCreateTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CREATETIME_ISSET_ID);
  }

  /** Returns true if field createTime is set (has been assigned a value) and false otherwise */
  public boolean isSetCreateTime() {
    return EncodingUtils.testBit(__isset_bitfield, __CREATETIME_ISSET_ID);
  }

  public void setCreateTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CREATETIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((Long)value);
      }
      break;

    case APPID:
      if (value == null) {
        unsetAppid();
      } else {
        setAppid((Integer)value);
      }
      break;

    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case TING_MGR_UID:
      if (value == null) {
        unsetTingMgrUid();
      } else {
        setTingMgrUid((Long)value);
      }
      break;

    case TID:
      if (value == null) {
        unsetTid();
      } else {
        setTid((Long)value);
      }
      break;

    case ANCHOR_UID:
      if (value == null) {
        unsetAnchorUid();
      } else {
        setAnchorUid((Long)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((String)value);
      }
      break;

    case CREATE_TIME:
      if (value == null) {
        unsetCreateTime();
      } else {
        setCreateTime((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case APPID:
      return getAppid();

    case SID:
      return getSid();

    case TING_MGR_UID:
      return getTingMgrUid();

    case TID:
      return getTid();

    case ANCHOR_UID:
      return getAnchorUid();

    case EXPAND:
      return getExpand();

    case CREATE_TIME:
      return getCreateTime();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case APPID:
      return isSetAppid();
    case SID:
      return isSetSid();
    case TING_MGR_UID:
      return isSetTingMgrUid();
    case TID:
      return isSetTid();
    case ANCHOR_UID:
      return isSetAnchorUid();
    case EXPAND:
      return isSetExpand();
    case CREATE_TIME:
      return isSetCreateTime();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TTingBind)
      return this.equals((TTingBind)that);
    return false;
  }

  public boolean equals(TTingBind that) {
    if (that == null)
      return false;

    boolean this_present_id = true;
    boolean that_present_id = true;
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (this.id != that.id)
        return false;
    }

    boolean this_present_appid = true;
    boolean that_present_appid = true;
    if (this_present_appid || that_present_appid) {
      if (!(this_present_appid && that_present_appid))
        return false;
      if (this.appid != that.appid)
        return false;
    }

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_tingMgrUid = true;
    boolean that_present_tingMgrUid = true;
    if (this_present_tingMgrUid || that_present_tingMgrUid) {
      if (!(this_present_tingMgrUid && that_present_tingMgrUid))
        return false;
      if (this.tingMgrUid != that.tingMgrUid)
        return false;
    }

    boolean this_present_tid = true;
    boolean that_present_tid = true;
    if (this_present_tid || that_present_tid) {
      if (!(this_present_tid && that_present_tid))
        return false;
      if (this.tid != that.tid)
        return false;
    }

    boolean this_present_anchorUid = true;
    boolean that_present_anchorUid = true;
    if (this_present_anchorUid || that_present_anchorUid) {
      if (!(this_present_anchorUid && that_present_anchorUid))
        return false;
      if (this.anchorUid != that.anchorUid)
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    boolean this_present_createTime = true;
    boolean that_present_createTime = true;
    if (this_present_createTime || that_present_createTime) {
      if (!(this_present_createTime && that_present_createTime))
        return false;
      if (this.createTime != that.createTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_id = true;
    list.add(present_id);
    if (present_id)
      list.add(id);

    boolean present_appid = true;
    list.add(present_appid);
    if (present_appid)
      list.add(appid);

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_tingMgrUid = true;
    list.add(present_tingMgrUid);
    if (present_tingMgrUid)
      list.add(tingMgrUid);

    boolean present_tid = true;
    list.add(present_tid);
    if (present_tid)
      list.add(tid);

    boolean present_anchorUid = true;
    list.add(present_anchorUid);
    if (present_anchorUid)
      list.add(anchorUid);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    boolean present_createTime = true;
    list.add(present_createTime);
    if (present_createTime)
      list.add(createTime);

    return list.hashCode();
  }

  @Override
  public int compareTo(TTingBind other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetId()).compareTo(other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppid()).compareTo(other.isSetAppid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appid, other.appid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTingMgrUid()).compareTo(other.isSetTingMgrUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTingMgrUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tingMgrUid, other.tingMgrUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTid()).compareTo(other.isSetTid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tid, other.tid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAnchorUid()).compareTo(other.isSetAnchorUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAnchorUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.anchorUid, other.anchorUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCreateTime()).compareTo(other.isSetCreateTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCreateTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.createTime, other.createTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TTingBind(");
    boolean first = true;

    sb.append("id:");
    sb.append(this.id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("appid:");
    sb.append(this.appid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("tingMgrUid:");
    sb.append(this.tingMgrUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("tid:");
    sb.append(this.tid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("anchorUid:");
    sb.append(this.anchorUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("createTime:");
    sb.append(this.createTime);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TTingBindStandardSchemeFactory implements SchemeFactory {
    public TTingBindStandardScheme getScheme() {
      return new TTingBindStandardScheme();
    }
  }

  private static class TTingBindStandardScheme extends StandardScheme<TTingBind> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TTingBind struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.id = iprot.readI64();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.appid = iprot.readI32();
              struct.setAppidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TING_MGR_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.tingMgrUid = iprot.readI64();
              struct.setTingMgrUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // TID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.tid = iprot.readI64();
              struct.setTidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // ANCHOR_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.anchorUid = iprot.readI64();
              struct.setAnchorUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.expand = iprot.readString();
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // CREATE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.createTime = iprot.readI64();
              struct.setCreateTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TTingBind struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ID_FIELD_DESC);
      oprot.writeI64(struct.id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(APPID_FIELD_DESC);
      oprot.writeI32(struct.appid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TING_MGR_UID_FIELD_DESC);
      oprot.writeI64(struct.tingMgrUid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TID_FIELD_DESC);
      oprot.writeI64(struct.tid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ANCHOR_UID_FIELD_DESC);
      oprot.writeI64(struct.anchorUid);
      oprot.writeFieldEnd();
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        oprot.writeString(struct.expand);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CREATE_TIME_FIELD_DESC);
      oprot.writeI64(struct.createTime);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TTingBindTupleSchemeFactory implements SchemeFactory {
    public TTingBindTupleScheme getScheme() {
      return new TTingBindTupleScheme();
    }
  }

  private static class TTingBindTupleScheme extends TupleScheme<TTingBind> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TTingBind struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetId()) {
        optionals.set(0);
      }
      if (struct.isSetAppid()) {
        optionals.set(1);
      }
      if (struct.isSetSid()) {
        optionals.set(2);
      }
      if (struct.isSetTingMgrUid()) {
        optionals.set(3);
      }
      if (struct.isSetTid()) {
        optionals.set(4);
      }
      if (struct.isSetAnchorUid()) {
        optionals.set(5);
      }
      if (struct.isSetExpand()) {
        optionals.set(6);
      }
      if (struct.isSetCreateTime()) {
        optionals.set(7);
      }
      oprot.writeBitSet(optionals, 8);
      if (struct.isSetId()) {
        oprot.writeI64(struct.id);
      }
      if (struct.isSetAppid()) {
        oprot.writeI32(struct.appid);
      }
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetTingMgrUid()) {
        oprot.writeI64(struct.tingMgrUid);
      }
      if (struct.isSetTid()) {
        oprot.writeI64(struct.tid);
      }
      if (struct.isSetAnchorUid()) {
        oprot.writeI64(struct.anchorUid);
      }
      if (struct.isSetExpand()) {
        oprot.writeString(struct.expand);
      }
      if (struct.isSetCreateTime()) {
        oprot.writeI64(struct.createTime);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TTingBind struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(8);
      if (incoming.get(0)) {
        struct.id = iprot.readI64();
        struct.setIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appid = iprot.readI32();
        struct.setAppidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.tingMgrUid = iprot.readI64();
        struct.setTingMgrUidIsSet(true);
      }
      if (incoming.get(4)) {
        struct.tid = iprot.readI64();
        struct.setTidIsSet(true);
      }
      if (incoming.get(5)) {
        struct.anchorUid = iprot.readI64();
        struct.setAnchorUidIsSet(true);
      }
      if (incoming.get(6)) {
        struct.expand = iprot.readString();
        struct.setExpandIsSet(true);
      }
      if (incoming.get(7)) {
        struct.createTime = iprot.readI64();
        struct.setCreateTimeIsSet(true);
      }
    }
  }

}

