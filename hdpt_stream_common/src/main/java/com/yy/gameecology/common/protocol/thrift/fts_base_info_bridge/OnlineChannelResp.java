/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class OnlineChannelResp implements org.apache.thrift.TBase<OnlineChannelResp, OnlineChannelResp._Fields>, java.io.Serializable, Cloneable, Comparable<OnlineChannelResp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("OnlineChannelResp");

  private static final org.apache.thrift.protocol.TField RET_FIELD_DESC = new org.apache.thrift.protocol.TField("ret", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField RET_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("ret_list", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new OnlineChannelRespStandardSchemeFactory());
    schemes.put(TupleScheme.class, new OnlineChannelRespTupleSchemeFactory());
  }

  public CommonRet ret; // required
  public List<OnlineChannel> ret_list; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RET((short)1, "ret"),
    RET_LIST((short)2, "ret_list");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RET
          return RET;
        case 2: // RET_LIST
          return RET_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RET, new org.apache.thrift.meta_data.FieldMetaData("ret", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CommonRet.class)));
    tmpMap.put(_Fields.RET_LIST, new org.apache.thrift.meta_data.FieldMetaData("ret_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, OnlineChannel.class))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(OnlineChannelResp.class, metaDataMap);
  }

  public OnlineChannelResp() {
  }

  public OnlineChannelResp(
    CommonRet ret,
    List<OnlineChannel> ret_list)
  {
    this();
    this.ret = ret;
    this.ret_list = ret_list;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public OnlineChannelResp(OnlineChannelResp other) {
    if (other.isSetRet()) {
      this.ret = new CommonRet(other.ret);
    }
    if (other.isSetRet_list()) {
      List<OnlineChannel> __this__ret_list = new ArrayList<OnlineChannel>(other.ret_list.size());
      for (OnlineChannel other_element : other.ret_list) {
        __this__ret_list.add(new OnlineChannel(other_element));
      }
      this.ret_list = __this__ret_list;
    }
  }

  public OnlineChannelResp deepCopy() {
    return new OnlineChannelResp(this);
  }

  @Override
  public void clear() {
    this.ret = null;
    this.ret_list = null;
  }

  public CommonRet getRet() {
    return this.ret;
  }

  public OnlineChannelResp setRet(CommonRet ret) {
    this.ret = ret;
    return this;
  }

  public void unsetRet() {
    this.ret = null;
  }

  /** Returns true if field ret is set (has been assigned a value) and false otherwise */
  public boolean isSetRet() {
    return this.ret != null;
  }

  public void setRetIsSet(boolean value) {
    if (!value) {
      this.ret = null;
    }
  }

  public int getRet_listSize() {
    return (this.ret_list == null) ? 0 : this.ret_list.size();
  }

  public java.util.Iterator<OnlineChannel> getRet_listIterator() {
    return (this.ret_list == null) ? null : this.ret_list.iterator();
  }

  public void addToRet_list(OnlineChannel elem) {
    if (this.ret_list == null) {
      this.ret_list = new ArrayList<OnlineChannel>();
    }
    this.ret_list.add(elem);
  }

  public List<OnlineChannel> getRet_list() {
    return this.ret_list;
  }

  public OnlineChannelResp setRet_list(List<OnlineChannel> ret_list) {
    this.ret_list = ret_list;
    return this;
  }

  public void unsetRet_list() {
    this.ret_list = null;
  }

  /** Returns true if field ret_list is set (has been assigned a value) and false otherwise */
  public boolean isSetRet_list() {
    return this.ret_list != null;
  }

  public void setRet_listIsSet(boolean value) {
    if (!value) {
      this.ret_list = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RET:
      if (value == null) {
        unsetRet();
      } else {
        setRet((CommonRet)value);
      }
      break;

    case RET_LIST:
      if (value == null) {
        unsetRet_list();
      } else {
        setRet_list((List<OnlineChannel>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RET:
      return getRet();

    case RET_LIST:
      return getRet_list();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RET:
      return isSetRet();
    case RET_LIST:
      return isSetRet_list();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof OnlineChannelResp)
      return this.equals((OnlineChannelResp)that);
    return false;
  }

  public boolean equals(OnlineChannelResp that) {
    if (that == null)
      return false;

    boolean this_present_ret = true && this.isSetRet();
    boolean that_present_ret = true && that.isSetRet();
    if (this_present_ret || that_present_ret) {
      if (!(this_present_ret && that_present_ret))
        return false;
      if (!this.ret.equals(that.ret))
        return false;
    }

    boolean this_present_ret_list = true && this.isSetRet_list();
    boolean that_present_ret_list = true && that.isSetRet_list();
    if (this_present_ret_list || that_present_ret_list) {
      if (!(this_present_ret_list && that_present_ret_list))
        return false;
      if (!this.ret_list.equals(that.ret_list))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_ret = true && (isSetRet());
    list.add(present_ret);
    if (present_ret)
      list.add(ret);

    boolean present_ret_list = true && (isSetRet_list());
    list.add(present_ret_list);
    if (present_ret_list)
      list.add(ret_list);

    return list.hashCode();
  }

  @Override
  public int compareTo(OnlineChannelResp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRet()).compareTo(other.isSetRet());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRet()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ret, other.ret);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRet_list()).compareTo(other.isSetRet_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRet_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ret_list, other.ret_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("OnlineChannelResp(");
    boolean first = true;

    sb.append("ret:");
    if (this.ret == null) {
      sb.append("null");
    } else {
      sb.append(this.ret);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ret_list:");
    if (this.ret_list == null) {
      sb.append("null");
    } else {
      sb.append(this.ret_list);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (ret != null) {
      ret.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class OnlineChannelRespStandardSchemeFactory implements SchemeFactory {
    public OnlineChannelRespStandardScheme getScheme() {
      return new OnlineChannelRespStandardScheme();
    }
  }

  private static class OnlineChannelRespStandardScheme extends StandardScheme<OnlineChannelResp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, OnlineChannelResp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RET
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.ret = new CommonRet();
              struct.ret.read(iprot);
              struct.setRetIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RET_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list88 = iprot.readListBegin();
                struct.ret_list = new ArrayList<OnlineChannel>(_list88.size);
                OnlineChannel _elem89;
                for (int _i90 = 0; _i90 < _list88.size; ++_i90)
                {
                  _elem89 = new OnlineChannel();
                  _elem89.read(iprot);
                  struct.ret_list.add(_elem89);
                }
                iprot.readListEnd();
              }
              struct.setRet_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, OnlineChannelResp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.ret != null) {
        oprot.writeFieldBegin(RET_FIELD_DESC);
        struct.ret.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.ret_list != null) {
        oprot.writeFieldBegin(RET_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.ret_list.size()));
          for (OnlineChannel _iter91 : struct.ret_list)
          {
            _iter91.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class OnlineChannelRespTupleSchemeFactory implements SchemeFactory {
    public OnlineChannelRespTupleScheme getScheme() {
      return new OnlineChannelRespTupleScheme();
    }
  }

  private static class OnlineChannelRespTupleScheme extends TupleScheme<OnlineChannelResp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, OnlineChannelResp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRet()) {
        optionals.set(0);
      }
      if (struct.isSetRet_list()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRet()) {
        struct.ret.write(oprot);
      }
      if (struct.isSetRet_list()) {
        {
          oprot.writeI32(struct.ret_list.size());
          for (OnlineChannel _iter92 : struct.ret_list)
          {
            _iter92.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, OnlineChannelResp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.ret = new CommonRet();
        struct.ret.read(iprot);
        struct.setRetIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list93 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.ret_list = new ArrayList<OnlineChannel>(_list93.size);
          OnlineChannel _elem94;
          for (int _i95 = 0; _i95 < _list93.size; ++_i95)
          {
            _elem94 = new OnlineChannel();
            _elem94.read(iprot);
            struct.ret_list.add(_elem94);
          }
        }
        struct.setRet_listIsSet(true);
      }
    }
  }

}

