/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class Rank implements org.apache.thrift.TBase<Rank, Rank._Fields>, java.io.Serializable, Cloneable, Comparable<Rank> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("Rank");

  private static final org.apache.thrift.protocol.TField MEMBER_FIELD_DESC = new org.apache.thrift.protocol.TField("member", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField RANK_FIELD_DESC = new org.apache.thrift.protocol.TField("rank", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("score", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField ITEM_DESC_FIELD_DESC = new org.apache.thrift.protocol.TField("itemDesc", org.apache.thrift.protocol.TType.STRING, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RankStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RankTupleSchemeFactory());
  }

  public String member; // required
  public int rank; // required
  public long score; // required
  public String itemDesc; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    MEMBER((short)1, "member"),
    RANK((short)2, "rank"),
    SCORE((short)3, "score"),
    ITEM_DESC((short)4, "itemDesc");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // MEMBER
          return MEMBER;
        case 2: // RANK
          return RANK;
        case 3: // SCORE
          return SCORE;
        case 4: // ITEM_DESC
          return ITEM_DESC;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RANK_ISSET_ID = 0;
  private static final int __SCORE_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.MEMBER, new org.apache.thrift.meta_data.FieldMetaData("member", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RANK, new org.apache.thrift.meta_data.FieldMetaData("rank", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SCORE, new org.apache.thrift.meta_data.FieldMetaData("score", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ITEM_DESC, new org.apache.thrift.meta_data.FieldMetaData("itemDesc", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(Rank.class, metaDataMap);
  }

  public Rank() {
  }

  public Rank(
    String member,
    int rank,
    long score,
    String itemDesc)
  {
    this();
    this.member = member;
    this.rank = rank;
    setRankIsSet(true);
    this.score = score;
    setScoreIsSet(true);
    this.itemDesc = itemDesc;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public Rank(Rank other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetMember()) {
      this.member = other.member;
    }
    this.rank = other.rank;
    this.score = other.score;
    if (other.isSetItemDesc()) {
      this.itemDesc = other.itemDesc;
    }
  }

  public Rank deepCopy() {
    return new Rank(this);
  }

  @Override
  public void clear() {
    this.member = null;
    setRankIsSet(false);
    this.rank = 0;
    setScoreIsSet(false);
    this.score = 0;
    this.itemDesc = null;
  }

  public String getMember() {
    return this.member;
  }

  public Rank setMember(String member) {
    this.member = member;
    return this;
  }

  public void unsetMember() {
    this.member = null;
  }

  /** Returns true if field member is set (has been assigned a value) and false otherwise */
  public boolean isSetMember() {
    return this.member != null;
  }

  public void setMemberIsSet(boolean value) {
    if (!value) {
      this.member = null;
    }
  }

  public int getRank() {
    return this.rank;
  }

  public Rank setRank(int rank) {
    this.rank = rank;
    setRankIsSet(true);
    return this;
  }

  public void unsetRank() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANK_ISSET_ID);
  }

  /** Returns true if field rank is set (has been assigned a value) and false otherwise */
  public boolean isSetRank() {
    return EncodingUtils.testBit(__isset_bitfield, __RANK_ISSET_ID);
  }

  public void setRankIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANK_ISSET_ID, value);
  }

  public long getScore() {
    return this.score;
  }

  public Rank setScore(long score) {
    this.score = score;
    setScoreIsSet(true);
    return this;
  }

  public void unsetScore() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  /** Returns true if field score is set (has been assigned a value) and false otherwise */
  public boolean isSetScore() {
    return EncodingUtils.testBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  public void setScoreIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SCORE_ISSET_ID, value);
  }

  public String getItemDesc() {
    return this.itemDesc;
  }

  public Rank setItemDesc(String itemDesc) {
    this.itemDesc = itemDesc;
    return this;
  }

  public void unsetItemDesc() {
    this.itemDesc = null;
  }

  /** Returns true if field itemDesc is set (has been assigned a value) and false otherwise */
  public boolean isSetItemDesc() {
    return this.itemDesc != null;
  }

  public void setItemDescIsSet(boolean value) {
    if (!value) {
      this.itemDesc = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case MEMBER:
      if (value == null) {
        unsetMember();
      } else {
        setMember((String)value);
      }
      break;

    case RANK:
      if (value == null) {
        unsetRank();
      } else {
        setRank((Integer)value);
      }
      break;

    case SCORE:
      if (value == null) {
        unsetScore();
      } else {
        setScore((Long)value);
      }
      break;

    case ITEM_DESC:
      if (value == null) {
        unsetItemDesc();
      } else {
        setItemDesc((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case MEMBER:
      return getMember();

    case RANK:
      return getRank();

    case SCORE:
      return getScore();

    case ITEM_DESC:
      return getItemDesc();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case MEMBER:
      return isSetMember();
    case RANK:
      return isSetRank();
    case SCORE:
      return isSetScore();
    case ITEM_DESC:
      return isSetItemDesc();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof Rank)
      return this.equals((Rank)that);
    return false;
  }

  public boolean equals(Rank that) {
    if (that == null)
      return false;

    boolean this_present_member = true && this.isSetMember();
    boolean that_present_member = true && that.isSetMember();
    if (this_present_member || that_present_member) {
      if (!(this_present_member && that_present_member))
        return false;
      if (!this.member.equals(that.member))
        return false;
    }

    boolean this_present_rank = true;
    boolean that_present_rank = true;
    if (this_present_rank || that_present_rank) {
      if (!(this_present_rank && that_present_rank))
        return false;
      if (this.rank != that.rank)
        return false;
    }

    boolean this_present_score = true;
    boolean that_present_score = true;
    if (this_present_score || that_present_score) {
      if (!(this_present_score && that_present_score))
        return false;
      if (this.score != that.score)
        return false;
    }

    boolean this_present_itemDesc = true && this.isSetItemDesc();
    boolean that_present_itemDesc = true && that.isSetItemDesc();
    if (this_present_itemDesc || that_present_itemDesc) {
      if (!(this_present_itemDesc && that_present_itemDesc))
        return false;
      if (!this.itemDesc.equals(that.itemDesc))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_member = true && (isSetMember());
    list.add(present_member);
    if (present_member)
      list.add(member);

    boolean present_rank = true;
    list.add(present_rank);
    if (present_rank)
      list.add(rank);

    boolean present_score = true;
    list.add(present_score);
    if (present_score)
      list.add(score);

    boolean present_itemDesc = true && (isSetItemDesc());
    list.add(present_itemDesc);
    if (present_itemDesc)
      list.add(itemDesc);

    return list.hashCode();
  }

  @Override
  public int compareTo(Rank other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetMember()).compareTo(other.isSetMember());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMember()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.member, other.member);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRank()).compareTo(other.isSetRank());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRank()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rank, other.rank);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetScore()).compareTo(other.isSetScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.score, other.score);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemDesc()).compareTo(other.isSetItemDesc());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemDesc()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemDesc, other.itemDesc);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("Rank(");
    boolean first = true;

    sb.append("member:");
    if (this.member == null) {
      sb.append("null");
    } else {
      sb.append(this.member);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rank:");
    sb.append(this.rank);
    first = false;
    if (!first) sb.append(", ");
    sb.append("score:");
    sb.append(this.score);
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemDesc:");
    if (this.itemDesc == null) {
      sb.append("null");
    } else {
      sb.append(this.itemDesc);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RankStandardSchemeFactory implements SchemeFactory {
    public RankStandardScheme getScheme() {
      return new RankStandardScheme();
    }
  }

  private static class RankStandardScheme extends StandardScheme<Rank> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, Rank struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // MEMBER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.member = iprot.readString();
              struct.setMemberIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RANK
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rank = iprot.readI32();
              struct.setRankIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.score = iprot.readI64();
              struct.setScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ITEM_DESC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.itemDesc = iprot.readString();
              struct.setItemDescIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, Rank struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.member != null) {
        oprot.writeFieldBegin(MEMBER_FIELD_DESC);
        oprot.writeString(struct.member);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(RANK_FIELD_DESC);
      oprot.writeI32(struct.rank);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SCORE_FIELD_DESC);
      oprot.writeI64(struct.score);
      oprot.writeFieldEnd();
      if (struct.itemDesc != null) {
        oprot.writeFieldBegin(ITEM_DESC_FIELD_DESC);
        oprot.writeString(struct.itemDesc);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RankTupleSchemeFactory implements SchemeFactory {
    public RankTupleScheme getScheme() {
      return new RankTupleScheme();
    }
  }

  private static class RankTupleScheme extends TupleScheme<Rank> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, Rank struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetMember()) {
        optionals.set(0);
      }
      if (struct.isSetRank()) {
        optionals.set(1);
      }
      if (struct.isSetScore()) {
        optionals.set(2);
      }
      if (struct.isSetItemDesc()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetMember()) {
        oprot.writeString(struct.member);
      }
      if (struct.isSetRank()) {
        oprot.writeI32(struct.rank);
      }
      if (struct.isSetScore()) {
        oprot.writeI64(struct.score);
      }
      if (struct.isSetItemDesc()) {
        oprot.writeString(struct.itemDesc);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, Rank struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.member = iprot.readString();
        struct.setMemberIsSet(true);
      }
      if (incoming.get(1)) {
        struct.rank = iprot.readI32();
        struct.setRankIsSet(true);
      }
      if (incoming.get(2)) {
        struct.score = iprot.readI64();
        struct.setScoreIsSet(true);
      }
      if (incoming.get(3)) {
        struct.itemDesc = iprot.readString();
        struct.setItemDescIsSet(true);
      }
    }
  }

}

