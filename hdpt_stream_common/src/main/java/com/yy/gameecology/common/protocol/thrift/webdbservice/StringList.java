/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class StringList implements org.apache.thrift.TBase<StringList, StringList._Fields>, java.io.Serializable, Cloneable, Comparable<StringList> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("StringList");

  private static final org.apache.thrift.protocol.TField STR_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("strList", org.apache.thrift.protocol.TType.LIST, (short)1);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new StringListStandardSchemeFactory());
    schemes.put(TupleScheme.class, new StringListTupleSchemeFactory());
  }

  public List<String> strList; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    STR_LIST((short)1, "strList");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // STR_LIST
          return STR_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.STR_LIST, new org.apache.thrift.meta_data.FieldMetaData("strList", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(StringList.class, metaDataMap);
  }

  public StringList() {
  }

  public StringList(
    List<String> strList)
  {
    this();
    this.strList = strList;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public StringList(StringList other) {
    if (other.isSetStrList()) {
      List<String> __this__strList = new ArrayList<String>(other.strList);
      this.strList = __this__strList;
    }
  }

  public StringList deepCopy() {
    return new StringList(this);
  }

  @Override
  public void clear() {
    this.strList = null;
  }

  public int getStrListSize() {
    return (this.strList == null) ? 0 : this.strList.size();
  }

  public java.util.Iterator<String> getStrListIterator() {
    return (this.strList == null) ? null : this.strList.iterator();
  }

  public void addToStrList(String elem) {
    if (this.strList == null) {
      this.strList = new ArrayList<String>();
    }
    this.strList.add(elem);
  }

  public List<String> getStrList() {
    return this.strList;
  }

  public StringList setStrList(List<String> strList) {
    this.strList = strList;
    return this;
  }

  public void unsetStrList() {
    this.strList = null;
  }

  /** Returns true if field strList is set (has been assigned a value) and false otherwise */
  public boolean isSetStrList() {
    return this.strList != null;
  }

  public void setStrListIsSet(boolean value) {
    if (!value) {
      this.strList = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case STR_LIST:
      if (value == null) {
        unsetStrList();
      } else {
        setStrList((List<String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case STR_LIST:
      return getStrList();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case STR_LIST:
      return isSetStrList();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof StringList)
      return this.equals((StringList)that);
    return false;
  }

  public boolean equals(StringList that) {
    if (that == null)
      return false;

    boolean this_present_strList = true && this.isSetStrList();
    boolean that_present_strList = true && that.isSetStrList();
    if (this_present_strList || that_present_strList) {
      if (!(this_present_strList && that_present_strList))
        return false;
      if (!this.strList.equals(that.strList))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_strList = true && (isSetStrList());
    list.add(present_strList);
    if (present_strList)
      list.add(strList);

    return list.hashCode();
  }

  @Override
  public int compareTo(StringList other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetStrList()).compareTo(other.isSetStrList());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStrList()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.strList, other.strList);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("StringList(");
    boolean first = true;

    sb.append("strList:");
    if (this.strList == null) {
      sb.append("null");
    } else {
      sb.append(this.strList);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class StringListStandardSchemeFactory implements SchemeFactory {
    public StringListStandardScheme getScheme() {
      return new StringListStandardScheme();
    }
  }

  private static class StringListStandardScheme extends StandardScheme<StringList> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, StringList struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // STR_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list10 = iprot.readListBegin();
                struct.strList = new ArrayList<String>(_list10.size);
                String _elem11;
                for (int _i12 = 0; _i12 < _list10.size; ++_i12)
                {
                  _elem11 = iprot.readString();
                  struct.strList.add(_elem11);
                }
                iprot.readListEnd();
              }
              struct.setStrListIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, StringList struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.strList != null) {
        oprot.writeFieldBegin(STR_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.strList.size()));
          for (String _iter13 : struct.strList)
          {
            oprot.writeString(_iter13);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class StringListTupleSchemeFactory implements SchemeFactory {
    public StringListTupleScheme getScheme() {
      return new StringListTupleScheme();
    }
  }

  private static class StringListTupleScheme extends TupleScheme<StringList> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, StringList struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetStrList()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetStrList()) {
        {
          oprot.writeI32(struct.strList.size());
          for (String _iter14 : struct.strList)
          {
            oprot.writeString(_iter14);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, StringList struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list15 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.strList = new ArrayList<String>(_list15.size);
          String _elem16;
          for (int _i17 = 0; _i17 < _list15.size; ++_i17)
          {
            _elem16 = iprot.readString();
            struct.strList.add(_elem16);
          }
        }
        struct.setStrListIsSet(true);
      }
    }
  }

}

