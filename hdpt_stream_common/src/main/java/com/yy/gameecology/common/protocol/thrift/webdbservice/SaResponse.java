/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * @param     rescode            返回码
 * @param     keyValue           保留字段
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class SaResponse implements org.apache.thrift.TBase<SaResponse, SaResponse._Fields>, java.io.Serializable, Cloneable, Comparable<SaResponse> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaResponse");

  private static final org.apache.thrift.protocol.TField RESCODE_FIELD_DESC = new org.apache.thrift.protocol.TField("rescode", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField KEY_VALUE_FIELD_DESC = new org.apache.thrift.protocol.TField("keyValue", org.apache.thrift.protocol.TType.MAP, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaResponseStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaResponseTupleSchemeFactory());
  }

  public int rescode; // required
  public Map<String,String> keyValue; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RESCODE((short)1, "rescode"),
    KEY_VALUE((short)2, "keyValue");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RESCODE
          return RESCODE;
        case 2: // KEY_VALUE
          return KEY_VALUE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RESCODE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RESCODE, new org.apache.thrift.meta_data.FieldMetaData("rescode", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.KEY_VALUE, new org.apache.thrift.meta_data.FieldMetaData("keyValue", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaResponse.class, metaDataMap);
  }

  public SaResponse() {
  }

  public SaResponse(
    int rescode,
    Map<String,String> keyValue)
  {
    this();
    this.rescode = rescode;
    setRescodeIsSet(true);
    this.keyValue = keyValue;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaResponse(SaResponse other) {
    __isset_bitfield = other.__isset_bitfield;
    this.rescode = other.rescode;
    if (other.isSetKeyValue()) {
      Map<String,String> __this__keyValue = new HashMap<String,String>(other.keyValue);
      this.keyValue = __this__keyValue;
    }
  }

  public SaResponse deepCopy() {
    return new SaResponse(this);
  }

  @Override
  public void clear() {
    setRescodeIsSet(false);
    this.rescode = 0;
    this.keyValue = null;
  }

  public int getRescode() {
    return this.rescode;
  }

  public SaResponse setRescode(int rescode) {
    this.rescode = rescode;
    setRescodeIsSet(true);
    return this;
  }

  public void unsetRescode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RESCODE_ISSET_ID);
  }

  /** Returns true if field rescode is set (has been assigned a value) and false otherwise */
  public boolean isSetRescode() {
    return EncodingUtils.testBit(__isset_bitfield, __RESCODE_ISSET_ID);
  }

  public void setRescodeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RESCODE_ISSET_ID, value);
  }

  public int getKeyValueSize() {
    return (this.keyValue == null) ? 0 : this.keyValue.size();
  }

  public void putToKeyValue(String key, String val) {
    if (this.keyValue == null) {
      this.keyValue = new HashMap<String,String>();
    }
    this.keyValue.put(key, val);
  }

  public Map<String,String> getKeyValue() {
    return this.keyValue;
  }

  public SaResponse setKeyValue(Map<String,String> keyValue) {
    this.keyValue = keyValue;
    return this;
  }

  public void unsetKeyValue() {
    this.keyValue = null;
  }

  /** Returns true if field keyValue is set (has been assigned a value) and false otherwise */
  public boolean isSetKeyValue() {
    return this.keyValue != null;
  }

  public void setKeyValueIsSet(boolean value) {
    if (!value) {
      this.keyValue = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RESCODE:
      if (value == null) {
        unsetRescode();
      } else {
        setRescode((Integer)value);
      }
      break;

    case KEY_VALUE:
      if (value == null) {
        unsetKeyValue();
      } else {
        setKeyValue((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RESCODE:
      return getRescode();

    case KEY_VALUE:
      return getKeyValue();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RESCODE:
      return isSetRescode();
    case KEY_VALUE:
      return isSetKeyValue();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaResponse)
      return this.equals((SaResponse)that);
    return false;
  }

  public boolean equals(SaResponse that) {
    if (that == null)
      return false;

    boolean this_present_rescode = true;
    boolean that_present_rescode = true;
    if (this_present_rescode || that_present_rescode) {
      if (!(this_present_rescode && that_present_rescode))
        return false;
      if (this.rescode != that.rescode)
        return false;
    }

    boolean this_present_keyValue = true && this.isSetKeyValue();
    boolean that_present_keyValue = true && that.isSetKeyValue();
    if (this_present_keyValue || that_present_keyValue) {
      if (!(this_present_keyValue && that_present_keyValue))
        return false;
      if (!this.keyValue.equals(that.keyValue))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_rescode = true;
    list.add(present_rescode);
    if (present_rescode)
      list.add(rescode);

    boolean present_keyValue = true && (isSetKeyValue());
    list.add(present_keyValue);
    if (present_keyValue)
      list.add(keyValue);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaResponse other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRescode()).compareTo(other.isSetRescode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRescode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rescode, other.rescode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKeyValue()).compareTo(other.isSetKeyValue());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKeyValue()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.keyValue, other.keyValue);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaResponse(");
    boolean first = true;

    sb.append("rescode:");
    sb.append(this.rescode);
    first = false;
    if (!first) sb.append(", ");
    sb.append("keyValue:");
    if (this.keyValue == null) {
      sb.append("null");
    } else {
      sb.append(this.keyValue);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaResponseStandardSchemeFactory implements SchemeFactory {
    public SaResponseStandardScheme getScheme() {
      return new SaResponseStandardScheme();
    }
  }

  private static class SaResponseStandardScheme extends StandardScheme<SaResponse> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaResponse struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RESCODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rescode = iprot.readI32();
              struct.setRescodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // KEY_VALUE
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map18 = iprot.readMapBegin();
                struct.keyValue = new HashMap<String,String>(2*_map18.size);
                String _key19;
                String _val20;
                for (int _i21 = 0; _i21 < _map18.size; ++_i21)
                {
                  _key19 = iprot.readString();
                  _val20 = iprot.readString();
                  struct.keyValue.put(_key19, _val20);
                }
                iprot.readMapEnd();
              }
              struct.setKeyValueIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaResponse struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RESCODE_FIELD_DESC);
      oprot.writeI32(struct.rescode);
      oprot.writeFieldEnd();
      if (struct.keyValue != null) {
        oprot.writeFieldBegin(KEY_VALUE_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.keyValue.size()));
          for (Map.Entry<String, String> _iter22 : struct.keyValue.entrySet())
          {
            oprot.writeString(_iter22.getKey());
            oprot.writeString(_iter22.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaResponseTupleSchemeFactory implements SchemeFactory {
    public SaResponseTupleScheme getScheme() {
      return new SaResponseTupleScheme();
    }
  }

  private static class SaResponseTupleScheme extends TupleScheme<SaResponse> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRescode()) {
        optionals.set(0);
      }
      if (struct.isSetKeyValue()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRescode()) {
        oprot.writeI32(struct.rescode);
      }
      if (struct.isSetKeyValue()) {
        {
          oprot.writeI32(struct.keyValue.size());
          for (Map.Entry<String, String> _iter23 : struct.keyValue.entrySet())
          {
            oprot.writeString(_iter23.getKey());
            oprot.writeString(_iter23.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.rescode = iprot.readI32();
        struct.setRescodeIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map24 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.keyValue = new HashMap<String,String>(2*_map24.size);
          String _key25;
          String _val26;
          for (int _i27 = 0; _i27 < _map24.size; ++_i27)
          {
            _key25 = iprot.readString();
            _val26 = iprot.readString();
            struct.keyValue.put(_key25, _val26);
          }
        }
        struct.setKeyValueIsSet(true);
      }
    }
  }

}

