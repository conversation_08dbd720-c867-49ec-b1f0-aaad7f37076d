/**
 * Autogenerated by <PERSON><PERSON><PERSON>mpiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.turnover_family;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum TAppId implements org.apache.thrift.TEnum {
  Finance(1),
  <PERSON>ting(2),
  Hundred(3),
  FreeShow(4),
  GameGuild(5),
  Ktv(6),
  Blackjack(7),
  Spy(8),
  SlaveSales(9),
  ScratchOff(10),
  <PERSON><PERSON><PERSON>(11),
  MedicalTreatment(12),
  Sport(13),
  VipPk(14),
  HelloApp(15),
  FinanceForceRelieveContract(16),
  GameSpot(17),
  <PERSON><PERSON><PERSON>(18),
  <PERSON><PERSON><PERSON><PERSON>(19),
  <PERSON><PERSON><PERSON>(20),
  TinyTime(21),
  YoMall(22),
  GameTemplate(23),
  MEPlus(24),
  Werewolf<PERSON>ill(25),
  <PERSON><PERSON><PERSON><PERSON>(26),
  <PERSON><PERSON><PERSON><PERSON><PERSON>(27),
  <PERSON><PERSON><PERSON><PERSON>(28),
  <PERSON><PERSON><PERSON><PERSON>(29),
  <PERSON><PERSON><PERSON><PERSON>(30),
  <PERSON><PERSON><PERSON>(31),
  <PERSON>(33),
  <PERSON><PERSON><PERSON>(34),
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(35),
  <PERSON>(36),
  <PERSON><PERSON>wan<PERSON><PERSON><PERSON>(37),
  <PERSON><PERSON>(38),
  <PERSON>Live(39),
  <PERSON><PERSON>(40),
  <PERSON>du<PERSON>ieba(41),
  <PERSON>okan<PERSON>hipin(42),
  <PERSON>uan<PERSON><PERSON>ia<PERSON><PERSON>(43),
  <PERSON><PERSON><PERSON>ive(44),
  <PERSON>jiah<PERSON>(45),
  <PERSON><PERSON><PERSON>harge<PERSON><PERSON>(46),
  <PERSON>d<PERSON>sist(47),
  <PERSON>(48),
  <PERSON><PERSON><PERSON><PERSON><PERSON>(49),
  <PERSON>du<PERSON>ite(50),
  BaiduHealth(51),
  ZfbApplets(52),
  BaiduXR(53),
  BaiduBZ(55),
  DatingYYLiveWithdraw(20001),
  DatingYYLiveWithdrawV3(20002),
  ShengDongWithdraw(190000),
  ShengLangWithdraw(190001),
  ZhuiWanGiftbagWithdraw(300001),
  ZhuiWanActWithdraw(300002),
  ZhuiWanGunKingWithdraw(300003),
  ZhuiWanGunKingV2Withdraw(300004),
  ZhuiWanYYFWithdraw(3401),
  SDKCourseWithdraw(3901);

  private final int value;

  private TAppId(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static TAppId findByValue(int value) { 
    switch (value) {
      case 1:
        return Finance;
      case 2:
        return Dating;
      case 3:
        return Hundred;
      case 4:
        return FreeShow;
      case 5:
        return GameGuild;
      case 6:
        return Ktv;
      case 7:
        return Blackjack;
      case 8:
        return Spy;
      case 9:
        return SlaveSales;
      case 10:
        return ScratchOff;
      case 11:
        return Niuniu;
      case 12:
        return MedicalTreatment;
      case 13:
        return Sport;
      case 14:
        return VipPk;
      case 15:
        return HelloApp;
      case 16:
        return FinanceForceRelieveContract;
      case 17:
        return GameSpot;
      case 18:
        return Bilin;
      case 19:
        return XunHuan;
      case 20:
        return WeiFang;
      case 21:
        return TinyTime;
      case 22:
        return YoMall;
      case 23:
        return GameTemplate;
      case 24:
        return MEPlus;
      case 25:
        return WerewolfKill;
      case 26:
        return TinyVideo;
      case 27:
        return MGameVoice;
      case 28:
        return DianHu;
      case 29:
        return ZhuiDu;
      case 30:
        return ZhuiYa;
      case 31:
        return Findyou;
      case 33:
        return Nearby;
      case 34:
        return PeopleGame;
      case 35:
        return DatingHuabaRepay;
      case 36:
        return Baby;
      case 37:
        return PeiwanPaidan;
      case 38:
        return Baidu;
      case 39:
        return GameLive;
      case 40:
        return Demo;
      case 41:
        return BaiduTieba;
      case 42:
        return HaokanShipin;
      case 43:
        return QuanminXiaoshipin;
      case 44:
        return YYLive;
      case 45:
        return Baijiahao;
      case 46:
        return YYChargeCenter;
      case 47:
        return Bdgassist;
      case 48:
        return PC;
      case 49:
        return FanZhiShi;
      case 50:
        return BaiduLite;
      case 51:
        return BaiduHealth;
      case 52:
        return ZfbApplets;
      case 53:
        return BaiduXR;
      case 55:
        return BaiduBZ;
      case 20001:
        return DatingYYLiveWithdraw;
      case 20002:
        return DatingYYLiveWithdrawV3;
      case 190000:
        return ShengDongWithdraw;
      case 190001:
        return ShengLangWithdraw;
      case 300001:
        return ZhuiWanGiftbagWithdraw;
      case 300002:
        return ZhuiWanActWithdraw;
      case 300003:
        return ZhuiWanGunKingWithdraw;
      case 300004:
        return ZhuiWanGunKingV2Withdraw;
      case 3401:
        return ZhuiWanYYFWithdraw;
      case 3901:
        return SDKCourseWithdraw;
      default:
        return null;
    }
  }
}
