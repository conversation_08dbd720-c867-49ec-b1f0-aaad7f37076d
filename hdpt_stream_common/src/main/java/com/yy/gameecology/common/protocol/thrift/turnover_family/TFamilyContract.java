/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.turnover_family;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-09-09")
public class TFamilyContract implements org.apache.thrift.TBase<TFamilyContract, TFamilyContract._Fields>, java.io.Serializable, Cloneable, Comparable<TFamilyContract> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TFamilyContract");

  private static final org.apache.thrift.protocol.TField APPID_FIELD_DESC = new org.apache.thrift.protocol.TField("appid", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField FAMILY_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("familyId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField LIVE_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("liveUid", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField WEIGHT_FIELD_DESC = new org.apache.thrift.protocol.TField("weight", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField SIGN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("signTime", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField FINISH_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("finishTime", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField AGENT_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("agentUid", org.apache.thrift.protocol.TType.I64, (short)8);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TFamilyContractStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TFamilyContractTupleSchemeFactory());
  }

  /**
   * 业务id
   */
  public int appid; // required
  /**
   * 家族id
   */
  public long familyId; // required
  /**
   * 主持uid
   */
  public long liveUid; // required
  /**
   * 分成比例
   */
  public int weight; // required
  /**
   * 生效日期
   */
  public long signTime; // required
  /**
   * 失效日期
   */
  public long finishTime; // required
  /**
   * 扩展json字段 {"delete":1} 表示解约中
   */
  public String expand; // required
  /**
   * 经纪人uid
   */
  public long agentUid; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 业务id
     */
    APPID((short)1, "appid"),
    /**
     * 家族id
     */
    FAMILY_ID((short)2, "familyId"),
    /**
     * 主持uid
     */
    LIVE_UID((short)3, "liveUid"),
    /**
     * 分成比例
     */
    WEIGHT((short)4, "weight"),
    /**
     * 生效日期
     */
    SIGN_TIME((short)5, "signTime"),
    /**
     * 失效日期
     */
    FINISH_TIME((short)6, "finishTime"),
    /**
     * 扩展json字段 {"delete":1} 表示解约中
     */
    EXPAND((short)7, "expand"),
    /**
     * 经纪人uid
     */
    AGENT_UID((short)8, "agentUid");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // APPID
          return APPID;
        case 2: // FAMILY_ID
          return FAMILY_ID;
        case 3: // LIVE_UID
          return LIVE_UID;
        case 4: // WEIGHT
          return WEIGHT;
        case 5: // SIGN_TIME
          return SIGN_TIME;
        case 6: // FINISH_TIME
          return FINISH_TIME;
        case 7: // EXPAND
          return EXPAND;
        case 8: // AGENT_UID
          return AGENT_UID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __APPID_ISSET_ID = 0;
  private static final int __FAMILYID_ISSET_ID = 1;
  private static final int __LIVEUID_ISSET_ID = 2;
  private static final int __WEIGHT_ISSET_ID = 3;
  private static final int __SIGNTIME_ISSET_ID = 4;
  private static final int __FINISHTIME_ISSET_ID = 5;
  private static final int __AGENTUID_ISSET_ID = 6;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.APPID, new org.apache.thrift.meta_data.FieldMetaData("appid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.FAMILY_ID, new org.apache.thrift.meta_data.FieldMetaData("familyId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.LIVE_UID, new org.apache.thrift.meta_data.FieldMetaData("liveUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.WEIGHT, new org.apache.thrift.meta_data.FieldMetaData("weight", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SIGN_TIME, new org.apache.thrift.meta_data.FieldMetaData("signTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.FINISH_TIME, new org.apache.thrift.meta_data.FieldMetaData("finishTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.AGENT_UID, new org.apache.thrift.meta_data.FieldMetaData("agentUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TFamilyContract.class, metaDataMap);
  }

  public TFamilyContract() {
  }

  public TFamilyContract(
    int appid,
    long familyId,
    long liveUid,
    int weight,
    long signTime,
    long finishTime,
    String expand,
    long agentUid)
  {
    this();
    this.appid = appid;
    setAppidIsSet(true);
    this.familyId = familyId;
    setFamilyIdIsSet(true);
    this.liveUid = liveUid;
    setLiveUidIsSet(true);
    this.weight = weight;
    setWeightIsSet(true);
    this.signTime = signTime;
    setSignTimeIsSet(true);
    this.finishTime = finishTime;
    setFinishTimeIsSet(true);
    this.expand = expand;
    this.agentUid = agentUid;
    setAgentUidIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TFamilyContract(TFamilyContract other) {
    __isset_bitfield = other.__isset_bitfield;
    this.appid = other.appid;
    this.familyId = other.familyId;
    this.liveUid = other.liveUid;
    this.weight = other.weight;
    this.signTime = other.signTime;
    this.finishTime = other.finishTime;
    if (other.isSetExpand()) {
      this.expand = other.expand;
    }
    this.agentUid = other.agentUid;
  }

  public TFamilyContract deepCopy() {
    return new TFamilyContract(this);
  }

  @Override
  public void clear() {
    setAppidIsSet(false);
    this.appid = 0;
    setFamilyIdIsSet(false);
    this.familyId = 0;
    setLiveUidIsSet(false);
    this.liveUid = 0;
    setWeightIsSet(false);
    this.weight = 0;
    setSignTimeIsSet(false);
    this.signTime = 0;
    setFinishTimeIsSet(false);
    this.finishTime = 0;
    this.expand = null;
    setAgentUidIsSet(false);
    this.agentUid = 0;
  }

  /**
   * 业务id
   */
  public int getAppid() {
    return this.appid;
  }

  /**
   * 业务id
   */
  public TFamilyContract setAppid(int appid) {
    this.appid = appid;
    setAppidIsSet(true);
    return this;
  }

  public void unsetAppid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  /** Returns true if field appid is set (has been assigned a value) and false otherwise */
  public boolean isSetAppid() {
    return EncodingUtils.testBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  public void setAppidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __APPID_ISSET_ID, value);
  }

  /**
   * 家族id
   */
  public long getFamilyId() {
    return this.familyId;
  }

  /**
   * 家族id
   */
  public TFamilyContract setFamilyId(long familyId) {
    this.familyId = familyId;
    setFamilyIdIsSet(true);
    return this;
  }

  public void unsetFamilyId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FAMILYID_ISSET_ID);
  }

  /** Returns true if field familyId is set (has been assigned a value) and false otherwise */
  public boolean isSetFamilyId() {
    return EncodingUtils.testBit(__isset_bitfield, __FAMILYID_ISSET_ID);
  }

  public void setFamilyIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FAMILYID_ISSET_ID, value);
  }

  /**
   * 主持uid
   */
  public long getLiveUid() {
    return this.liveUid;
  }

  /**
   * 主持uid
   */
  public TFamilyContract setLiveUid(long liveUid) {
    this.liveUid = liveUid;
    setLiveUidIsSet(true);
    return this;
  }

  public void unsetLiveUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LIVEUID_ISSET_ID);
  }

  /** Returns true if field liveUid is set (has been assigned a value) and false otherwise */
  public boolean isSetLiveUid() {
    return EncodingUtils.testBit(__isset_bitfield, __LIVEUID_ISSET_ID);
  }

  public void setLiveUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LIVEUID_ISSET_ID, value);
  }

  /**
   * 分成比例
   */
  public int getWeight() {
    return this.weight;
  }

  /**
   * 分成比例
   */
  public TFamilyContract setWeight(int weight) {
    this.weight = weight;
    setWeightIsSet(true);
    return this;
  }

  public void unsetWeight() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __WEIGHT_ISSET_ID);
  }

  /** Returns true if field weight is set (has been assigned a value) and false otherwise */
  public boolean isSetWeight() {
    return EncodingUtils.testBit(__isset_bitfield, __WEIGHT_ISSET_ID);
  }

  public void setWeightIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __WEIGHT_ISSET_ID, value);
  }

  /**
   * 生效日期
   */
  public long getSignTime() {
    return this.signTime;
  }

  /**
   * 生效日期
   */
  public TFamilyContract setSignTime(long signTime) {
    this.signTime = signTime;
    setSignTimeIsSet(true);
    return this;
  }

  public void unsetSignTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SIGNTIME_ISSET_ID);
  }

  /** Returns true if field signTime is set (has been assigned a value) and false otherwise */
  public boolean isSetSignTime() {
    return EncodingUtils.testBit(__isset_bitfield, __SIGNTIME_ISSET_ID);
  }

  public void setSignTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SIGNTIME_ISSET_ID, value);
  }

  /**
   * 失效日期
   */
  public long getFinishTime() {
    return this.finishTime;
  }

  /**
   * 失效日期
   */
  public TFamilyContract setFinishTime(long finishTime) {
    this.finishTime = finishTime;
    setFinishTimeIsSet(true);
    return this;
  }

  public void unsetFinishTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FINISHTIME_ISSET_ID);
  }

  /** Returns true if field finishTime is set (has been assigned a value) and false otherwise */
  public boolean isSetFinishTime() {
    return EncodingUtils.testBit(__isset_bitfield, __FINISHTIME_ISSET_ID);
  }

  public void setFinishTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FINISHTIME_ISSET_ID, value);
  }

  /**
   * 扩展json字段 {"delete":1} 表示解约中
   */
  public String getExpand() {
    return this.expand;
  }

  /**
   * 扩展json字段 {"delete":1} 表示解约中
   */
  public TFamilyContract setExpand(String expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  /**
   * 经纪人uid
   */
  public long getAgentUid() {
    return this.agentUid;
  }

  /**
   * 经纪人uid
   */
  public TFamilyContract setAgentUid(long agentUid) {
    this.agentUid = agentUid;
    setAgentUidIsSet(true);
    return this;
  }

  public void unsetAgentUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __AGENTUID_ISSET_ID);
  }

  /** Returns true if field agentUid is set (has been assigned a value) and false otherwise */
  public boolean isSetAgentUid() {
    return EncodingUtils.testBit(__isset_bitfield, __AGENTUID_ISSET_ID);
  }

  public void setAgentUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __AGENTUID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case APPID:
      if (value == null) {
        unsetAppid();
      } else {
        setAppid((Integer)value);
      }
      break;

    case FAMILY_ID:
      if (value == null) {
        unsetFamilyId();
      } else {
        setFamilyId((Long)value);
      }
      break;

    case LIVE_UID:
      if (value == null) {
        unsetLiveUid();
      } else {
        setLiveUid((Long)value);
      }
      break;

    case WEIGHT:
      if (value == null) {
        unsetWeight();
      } else {
        setWeight((Integer)value);
      }
      break;

    case SIGN_TIME:
      if (value == null) {
        unsetSignTime();
      } else {
        setSignTime((Long)value);
      }
      break;

    case FINISH_TIME:
      if (value == null) {
        unsetFinishTime();
      } else {
        setFinishTime((Long)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((String)value);
      }
      break;

    case AGENT_UID:
      if (value == null) {
        unsetAgentUid();
      } else {
        setAgentUid((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case APPID:
      return getAppid();

    case FAMILY_ID:
      return getFamilyId();

    case LIVE_UID:
      return getLiveUid();

    case WEIGHT:
      return getWeight();

    case SIGN_TIME:
      return getSignTime();

    case FINISH_TIME:
      return getFinishTime();

    case EXPAND:
      return getExpand();

    case AGENT_UID:
      return getAgentUid();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case APPID:
      return isSetAppid();
    case FAMILY_ID:
      return isSetFamilyId();
    case LIVE_UID:
      return isSetLiveUid();
    case WEIGHT:
      return isSetWeight();
    case SIGN_TIME:
      return isSetSignTime();
    case FINISH_TIME:
      return isSetFinishTime();
    case EXPAND:
      return isSetExpand();
    case AGENT_UID:
      return isSetAgentUid();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TFamilyContract)
      return this.equals((TFamilyContract)that);
    return false;
  }

  public boolean equals(TFamilyContract that) {
    if (that == null)
      return false;

    boolean this_present_appid = true;
    boolean that_present_appid = true;
    if (this_present_appid || that_present_appid) {
      if (!(this_present_appid && that_present_appid))
        return false;
      if (this.appid != that.appid)
        return false;
    }

    boolean this_present_familyId = true;
    boolean that_present_familyId = true;
    if (this_present_familyId || that_present_familyId) {
      if (!(this_present_familyId && that_present_familyId))
        return false;
      if (this.familyId != that.familyId)
        return false;
    }

    boolean this_present_liveUid = true;
    boolean that_present_liveUid = true;
    if (this_present_liveUid || that_present_liveUid) {
      if (!(this_present_liveUid && that_present_liveUid))
        return false;
      if (this.liveUid != that.liveUid)
        return false;
    }

    boolean this_present_weight = true;
    boolean that_present_weight = true;
    if (this_present_weight || that_present_weight) {
      if (!(this_present_weight && that_present_weight))
        return false;
      if (this.weight != that.weight)
        return false;
    }

    boolean this_present_signTime = true;
    boolean that_present_signTime = true;
    if (this_present_signTime || that_present_signTime) {
      if (!(this_present_signTime && that_present_signTime))
        return false;
      if (this.signTime != that.signTime)
        return false;
    }

    boolean this_present_finishTime = true;
    boolean that_present_finishTime = true;
    if (this_present_finishTime || that_present_finishTime) {
      if (!(this_present_finishTime && that_present_finishTime))
        return false;
      if (this.finishTime != that.finishTime)
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    boolean this_present_agentUid = true;
    boolean that_present_agentUid = true;
    if (this_present_agentUid || that_present_agentUid) {
      if (!(this_present_agentUid && that_present_agentUid))
        return false;
      if (this.agentUid != that.agentUid)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_appid = true;
    list.add(present_appid);
    if (present_appid)
      list.add(appid);

    boolean present_familyId = true;
    list.add(present_familyId);
    if (present_familyId)
      list.add(familyId);

    boolean present_liveUid = true;
    list.add(present_liveUid);
    if (present_liveUid)
      list.add(liveUid);

    boolean present_weight = true;
    list.add(present_weight);
    if (present_weight)
      list.add(weight);

    boolean present_signTime = true;
    list.add(present_signTime);
    if (present_signTime)
      list.add(signTime);

    boolean present_finishTime = true;
    list.add(present_finishTime);
    if (present_finishTime)
      list.add(finishTime);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    boolean present_agentUid = true;
    list.add(present_agentUid);
    if (present_agentUid)
      list.add(agentUid);

    return list.hashCode();
  }

  @Override
  public int compareTo(TFamilyContract other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAppid()).compareTo(other.isSetAppid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appid, other.appid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFamilyId()).compareTo(other.isSetFamilyId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFamilyId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.familyId, other.familyId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLiveUid()).compareTo(other.isSetLiveUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLiveUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.liveUid, other.liveUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetWeight()).compareTo(other.isSetWeight());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetWeight()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.weight, other.weight);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSignTime()).compareTo(other.isSetSignTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSignTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.signTime, other.signTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFinishTime()).compareTo(other.isSetFinishTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFinishTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.finishTime, other.finishTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAgentUid()).compareTo(other.isSetAgentUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAgentUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.agentUid, other.agentUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TFamilyContract(");
    boolean first = true;

    sb.append("appid:");
    sb.append(this.appid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("familyId:");
    sb.append(this.familyId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("liveUid:");
    sb.append(this.liveUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("weight:");
    sb.append(this.weight);
    first = false;
    if (!first) sb.append(", ");
    sb.append("signTime:");
    sb.append(this.signTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("finishTime:");
    sb.append(this.finishTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("agentUid:");
    sb.append(this.agentUid);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TFamilyContractStandardSchemeFactory implements SchemeFactory {
    public TFamilyContractStandardScheme getScheme() {
      return new TFamilyContractStandardScheme();
    }
  }

  private static class TFamilyContractStandardScheme extends StandardScheme<TFamilyContract> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TFamilyContract struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // APPID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.appid = iprot.readI32();
              struct.setAppidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // FAMILY_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.familyId = iprot.readI64();
              struct.setFamilyIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // LIVE_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.liveUid = iprot.readI64();
              struct.setLiveUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // WEIGHT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.weight = iprot.readI32();
              struct.setWeightIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SIGN_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.signTime = iprot.readI64();
              struct.setSignTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // FINISH_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.finishTime = iprot.readI64();
              struct.setFinishTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.expand = iprot.readString();
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // AGENT_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.agentUid = iprot.readI64();
              struct.setAgentUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TFamilyContract struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(APPID_FIELD_DESC);
      oprot.writeI32(struct.appid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FAMILY_ID_FIELD_DESC);
      oprot.writeI64(struct.familyId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(LIVE_UID_FIELD_DESC);
      oprot.writeI64(struct.liveUid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(WEIGHT_FIELD_DESC);
      oprot.writeI32(struct.weight);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SIGN_TIME_FIELD_DESC);
      oprot.writeI64(struct.signTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FINISH_TIME_FIELD_DESC);
      oprot.writeI64(struct.finishTime);
      oprot.writeFieldEnd();
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        oprot.writeString(struct.expand);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(AGENT_UID_FIELD_DESC);
      oprot.writeI64(struct.agentUid);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TFamilyContractTupleSchemeFactory implements SchemeFactory {
    public TFamilyContractTupleScheme getScheme() {
      return new TFamilyContractTupleScheme();
    }
  }

  private static class TFamilyContractTupleScheme extends TupleScheme<TFamilyContract> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TFamilyContract struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAppid()) {
        optionals.set(0);
      }
      if (struct.isSetFamilyId()) {
        optionals.set(1);
      }
      if (struct.isSetLiveUid()) {
        optionals.set(2);
      }
      if (struct.isSetWeight()) {
        optionals.set(3);
      }
      if (struct.isSetSignTime()) {
        optionals.set(4);
      }
      if (struct.isSetFinishTime()) {
        optionals.set(5);
      }
      if (struct.isSetExpand()) {
        optionals.set(6);
      }
      if (struct.isSetAgentUid()) {
        optionals.set(7);
      }
      oprot.writeBitSet(optionals, 8);
      if (struct.isSetAppid()) {
        oprot.writeI32(struct.appid);
      }
      if (struct.isSetFamilyId()) {
        oprot.writeI64(struct.familyId);
      }
      if (struct.isSetLiveUid()) {
        oprot.writeI64(struct.liveUid);
      }
      if (struct.isSetWeight()) {
        oprot.writeI32(struct.weight);
      }
      if (struct.isSetSignTime()) {
        oprot.writeI64(struct.signTime);
      }
      if (struct.isSetFinishTime()) {
        oprot.writeI64(struct.finishTime);
      }
      if (struct.isSetExpand()) {
        oprot.writeString(struct.expand);
      }
      if (struct.isSetAgentUid()) {
        oprot.writeI64(struct.agentUid);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TFamilyContract struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(8);
      if (incoming.get(0)) {
        struct.appid = iprot.readI32();
        struct.setAppidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.familyId = iprot.readI64();
        struct.setFamilyIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.liveUid = iprot.readI64();
        struct.setLiveUidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.weight = iprot.readI32();
        struct.setWeightIsSet(true);
      }
      if (incoming.get(4)) {
        struct.signTime = iprot.readI64();
        struct.setSignTimeIsSet(true);
      }
      if (incoming.get(5)) {
        struct.finishTime = iprot.readI64();
        struct.setFinishTimeIsSet(true);
      }
      if (incoming.get(6)) {
        struct.expand = iprot.readString();
        struct.setExpandIsSet(true);
      }
      if (incoming.get(7)) {
        struct.agentUid = iprot.readI64();
        struct.setAgentUidIsSet(true);
      }
    }
  }

}

