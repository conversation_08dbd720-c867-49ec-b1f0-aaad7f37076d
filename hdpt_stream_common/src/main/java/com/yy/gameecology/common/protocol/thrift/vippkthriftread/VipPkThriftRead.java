/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.vippkthriftread;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-07")
public class VipPkThriftRead {

  public interface Iface extends ReadBaseService.Iface {

    public long pkIdBySid(long sid, long ssid, long sendTime) throws org.apache.thrift.TException;

  }

  public interface AsyncIface extends ReadBaseService .AsyncIface {

    public void pkIdBySid(long sid, long ssid, long sendTime, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException;

  }

  public static class Client extends ReadBaseService.Client implements Iface {
    public static class Factory implements org.apache.thrift.TServiceClientFactory<Client> {
      public Factory() {}
      public Client getClient(org.apache.thrift.protocol.TProtocol prot) {
        return new Client(prot);
      }
      public Client getClient(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
        return new Client(iprot, oprot);
      }
    }

    public Client(org.apache.thrift.protocol.TProtocol prot)
    {
      super(prot, prot);
    }

    public Client(org.apache.thrift.protocol.TProtocol iprot, org.apache.thrift.protocol.TProtocol oprot) {
      super(iprot, oprot);
    }

    public long pkIdBySid(long sid, long ssid, long sendTime) throws org.apache.thrift.TException
    {
      send_pkIdBySid(sid, ssid, sendTime);
      return recv_pkIdBySid();
    }

    public void send_pkIdBySid(long sid, long ssid, long sendTime) throws org.apache.thrift.TException
    {
      pkIdBySid_args args = new pkIdBySid_args();
      args.setSid(sid);
      args.setSsid(ssid);
      args.setSendTime(sendTime);
      sendBase("pkIdBySid", args);
    }

    public long recv_pkIdBySid() throws org.apache.thrift.TException
    {
      pkIdBySid_result result = new pkIdBySid_result();
      receiveBase(result, "pkIdBySid");
      if (result.isSetSuccess()) {
        return result.success;
      }
      throw new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.MISSING_RESULT, "pkIdBySid failed: unknown result");
    }

  }
  public static class AsyncClient extends ReadBaseService.AsyncClient implements AsyncIface {
    public static class Factory implements org.apache.thrift.async.TAsyncClientFactory<AsyncClient> {
      private org.apache.thrift.async.TAsyncClientManager clientManager;
      private org.apache.thrift.protocol.TProtocolFactory protocolFactory;
      public Factory(org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.protocol.TProtocolFactory protocolFactory) {
        this.clientManager = clientManager;
        this.protocolFactory = protocolFactory;
      }
      public AsyncClient getAsyncClient(org.apache.thrift.transport.TNonblockingTransport transport) {
        return new AsyncClient(protocolFactory, clientManager, transport);
      }
    }

    public AsyncClient(org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.async.TAsyncClientManager clientManager, org.apache.thrift.transport.TNonblockingTransport transport) {
      super(protocolFactory, clientManager, transport);
    }

    public void pkIdBySid(long sid, long ssid, long sendTime, org.apache.thrift.async.AsyncMethodCallback resultHandler) throws org.apache.thrift.TException {
      checkReady();
      pkIdBySid_call method_call = new pkIdBySid_call(sid, ssid, sendTime, resultHandler, this, ___protocolFactory, ___transport);
      this.___currentMethod = method_call;
      ___manager.call(method_call);
    }

    public static class pkIdBySid_call extends org.apache.thrift.async.TAsyncMethodCall {
      private long sid;
      private long ssid;
      private long sendTime;
      public pkIdBySid_call(long sid, long ssid, long sendTime, org.apache.thrift.async.AsyncMethodCallback resultHandler, org.apache.thrift.async.TAsyncClient client, org.apache.thrift.protocol.TProtocolFactory protocolFactory, org.apache.thrift.transport.TNonblockingTransport transport) throws org.apache.thrift.TException {
        super(client, protocolFactory, transport, resultHandler, false);
        this.sid = sid;
        this.ssid = ssid;
        this.sendTime = sendTime;
      }

      public void write_args(org.apache.thrift.protocol.TProtocol prot) throws org.apache.thrift.TException {
        prot.writeMessageBegin(new org.apache.thrift.protocol.TMessage("pkIdBySid", org.apache.thrift.protocol.TMessageType.CALL, 0));
        pkIdBySid_args args = new pkIdBySid_args();
        args.setSid(sid);
        args.setSsid(ssid);
        args.setSendTime(sendTime);
        args.write(prot);
        prot.writeMessageEnd();
      }

      public long getResult() throws org.apache.thrift.TException {
        if (getState() != org.apache.thrift.async.TAsyncMethodCall.State.RESPONSE_READ) {
          throw new IllegalStateException("Method call not finished!");
        }
        org.apache.thrift.transport.TMemoryInputTransport memoryTransport = new org.apache.thrift.transport.TMemoryInputTransport(getFrameBuffer().array());
        org.apache.thrift.protocol.TProtocol prot = client.getProtocolFactory().getProtocol(memoryTransport);
        return (new Client(prot)).recv_pkIdBySid();
      }
    }

  }

  public static class Processor<I extends Iface> extends ReadBaseService.Processor<I> implements org.apache.thrift.TProcessor {
    private static final Logger LOGGER = LoggerFactory.getLogger(Processor.class.getName());
    public Processor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.ProcessFunction<I, ? extends org.apache.thrift.TBase>>()));
    }

    protected Processor(I iface, Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends Iface> Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> getProcessMap(Map<String,  org.apache.thrift.ProcessFunction<I, ? extends  org.apache.thrift.TBase>> processMap) {
      processMap.put("pkIdBySid", new pkIdBySid());
      return processMap;
    }

    public static class pkIdBySid<I extends Iface> extends org.apache.thrift.ProcessFunction<I, pkIdBySid_args> {
      public pkIdBySid() {
        super("pkIdBySid");
      }

      public pkIdBySid_args getEmptyArgsInstance() {
        return new pkIdBySid_args();
      }

      protected boolean isOneway() {
        return false;
      }

      public pkIdBySid_result getResult(I iface, pkIdBySid_args args) throws org.apache.thrift.TException {
        pkIdBySid_result result = new pkIdBySid_result();
        result.success = iface.pkIdBySid(args.sid, args.ssid, args.sendTime);
        result.setSuccessIsSet(true);
        return result;
      }
    }

  }

  public static class AsyncProcessor<I extends AsyncIface> extends ReadBaseService.AsyncProcessor<I> {
    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncProcessor.class.getName());
    public AsyncProcessor(I iface) {
      super(iface, getProcessMap(new HashMap<String, org.apache.thrift.AsyncProcessFunction<I, ? extends org.apache.thrift.TBase, ?>>()));
    }

    protected AsyncProcessor(I iface, Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      super(iface, getProcessMap(processMap));
    }

    private static <I extends AsyncIface> Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase,?>> getProcessMap(Map<String,  org.apache.thrift.AsyncProcessFunction<I, ? extends  org.apache.thrift.TBase, ?>> processMap) {
      processMap.put("pkIdBySid", new pkIdBySid());
      return processMap;
    }

    public static class pkIdBySid<I extends AsyncIface> extends org.apache.thrift.AsyncProcessFunction<I, pkIdBySid_args, Long> {
      public pkIdBySid() {
        super("pkIdBySid");
      }

      public pkIdBySid_args getEmptyArgsInstance() {
        return new pkIdBySid_args();
      }

      public AsyncMethodCallback<Long> getResultHandler(final AsyncFrameBuffer fb, final int seqid) {
        final org.apache.thrift.AsyncProcessFunction fcall = this;
        return new AsyncMethodCallback<Long>() { 
          public void onComplete(Long o) {
            pkIdBySid_result result = new pkIdBySid_result();
            result.success = o;
            result.setSuccessIsSet(true);
            try {
              fcall.sendResponse(fb,result, org.apache.thrift.protocol.TMessageType.REPLY,seqid);
              return;
            } catch (Exception e) {
              LOGGER.error("Exception writing to internal frame buffer", e);
            }
            fb.close();
          }
          public void onError(Exception e) {
            byte msgType = org.apache.thrift.protocol.TMessageType.REPLY;
            org.apache.thrift.TBase msg;
            pkIdBySid_result result = new pkIdBySid_result();
            {
              msgType = org.apache.thrift.protocol.TMessageType.EXCEPTION;
              msg = (org.apache.thrift.TBase)new org.apache.thrift.TApplicationException(org.apache.thrift.TApplicationException.INTERNAL_ERROR, e.getMessage());
            }
            try {
              fcall.sendResponse(fb,msg,msgType,seqid);
              return;
            } catch (Exception ex) {
              LOGGER.error("Exception writing to internal frame buffer", ex);
            }
            fb.close();
          }
        };
      }

      protected boolean isOneway() {
        return false;
      }

      public void start(I iface, pkIdBySid_args args, org.apache.thrift.async.AsyncMethodCallback<Long> resultHandler) throws TException {
        iface.pkIdBySid(args.sid, args.ssid, args.sendTime,resultHandler);
      }
    }

  }

  public static class pkIdBySid_args implements org.apache.thrift.TBase<pkIdBySid_args, pkIdBySid_args._Fields>, java.io.Serializable, Cloneable, Comparable<pkIdBySid_args>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("pkIdBySid_args");

    private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)1);
    private static final org.apache.thrift.protocol.TField SSID_FIELD_DESC = new org.apache.thrift.protocol.TField("ssid", org.apache.thrift.protocol.TType.I64, (short)2);
    private static final org.apache.thrift.protocol.TField SEND_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("sendTime", org.apache.thrift.protocol.TType.I64, (short)3);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new pkIdBySid_argsStandardSchemeFactory());
      schemes.put(TupleScheme.class, new pkIdBySid_argsTupleSchemeFactory());
    }

    public long sid; // required
    public long ssid; // required
    public long sendTime; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SID((short)1, "sid"),
      SSID((short)2, "ssid"),
      SEND_TIME((short)3, "sendTime");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 1: // SID
            return SID;
          case 2: // SSID
            return SSID;
          case 3: // SEND_TIME
            return SEND_TIME;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __SID_ISSET_ID = 0;
    private static final int __SSID_ISSET_ID = 1;
    private static final int __SENDTIME_ISSET_ID = 2;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.SSID, new org.apache.thrift.meta_data.FieldMetaData("ssid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      tmpMap.put(_Fields.SEND_TIME, new org.apache.thrift.meta_data.FieldMetaData("sendTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(pkIdBySid_args.class, metaDataMap);
    }

    public pkIdBySid_args() {
    }

    public pkIdBySid_args(
      long sid,
      long ssid,
      long sendTime)
    {
      this();
      this.sid = sid;
      setSidIsSet(true);
      this.ssid = ssid;
      setSsidIsSet(true);
      this.sendTime = sendTime;
      setSendTimeIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public pkIdBySid_args(pkIdBySid_args other) {
      __isset_bitfield = other.__isset_bitfield;
      this.sid = other.sid;
      this.ssid = other.ssid;
      this.sendTime = other.sendTime;
    }

    public pkIdBySid_args deepCopy() {
      return new pkIdBySid_args(this);
    }

    @Override
    public void clear() {
      setSidIsSet(false);
      this.sid = 0;
      setSsidIsSet(false);
      this.ssid = 0;
      setSendTimeIsSet(false);
      this.sendTime = 0;
    }

    public long getSid() {
      return this.sid;
    }

    public pkIdBySid_args setSid(long sid) {
      this.sid = sid;
      setSidIsSet(true);
      return this;
    }

    public void unsetSid() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
    }

    /** Returns true if field sid is set (has been assigned a value) and false otherwise */
    public boolean isSetSid() {
      return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
    }

    public void setSidIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
    }

    public long getSsid() {
      return this.ssid;
    }

    public pkIdBySid_args setSsid(long ssid) {
      this.ssid = ssid;
      setSsidIsSet(true);
      return this;
    }

    public void unsetSsid() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SSID_ISSET_ID);
    }

    /** Returns true if field ssid is set (has been assigned a value) and false otherwise */
    public boolean isSetSsid() {
      return EncodingUtils.testBit(__isset_bitfield, __SSID_ISSET_ID);
    }

    public void setSsidIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SSID_ISSET_ID, value);
    }

    public long getSendTime() {
      return this.sendTime;
    }

    public pkIdBySid_args setSendTime(long sendTime) {
      this.sendTime = sendTime;
      setSendTimeIsSet(true);
      return this;
    }

    public void unsetSendTime() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SENDTIME_ISSET_ID);
    }

    /** Returns true if field sendTime is set (has been assigned a value) and false otherwise */
    public boolean isSetSendTime() {
      return EncodingUtils.testBit(__isset_bitfield, __SENDTIME_ISSET_ID);
    }

    public void setSendTimeIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SENDTIME_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SID:
        if (value == null) {
          unsetSid();
        } else {
          setSid((Long)value);
        }
        break;

      case SSID:
        if (value == null) {
          unsetSsid();
        } else {
          setSsid((Long)value);
        }
        break;

      case SEND_TIME:
        if (value == null) {
          unsetSendTime();
        } else {
          setSendTime((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SID:
        return getSid();

      case SSID:
        return getSsid();

      case SEND_TIME:
        return getSendTime();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SID:
        return isSetSid();
      case SSID:
        return isSetSsid();
      case SEND_TIME:
        return isSetSendTime();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof pkIdBySid_args)
        return this.equals((pkIdBySid_args)that);
      return false;
    }

    public boolean equals(pkIdBySid_args that) {
      if (that == null)
        return false;

      boolean this_present_sid = true;
      boolean that_present_sid = true;
      if (this_present_sid || that_present_sid) {
        if (!(this_present_sid && that_present_sid))
          return false;
        if (this.sid != that.sid)
          return false;
      }

      boolean this_present_ssid = true;
      boolean that_present_ssid = true;
      if (this_present_ssid || that_present_ssid) {
        if (!(this_present_ssid && that_present_ssid))
          return false;
        if (this.ssid != that.ssid)
          return false;
      }

      boolean this_present_sendTime = true;
      boolean that_present_sendTime = true;
      if (this_present_sendTime || that_present_sendTime) {
        if (!(this_present_sendTime && that_present_sendTime))
          return false;
        if (this.sendTime != that.sendTime)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_sid = true;
      list.add(present_sid);
      if (present_sid)
        list.add(sid);

      boolean present_ssid = true;
      list.add(present_ssid);
      if (present_ssid)
        list.add(ssid);

      boolean present_sendTime = true;
      list.add(present_sendTime);
      if (present_sendTime)
        list.add(sendTime);

      return list.hashCode();
    }

    @Override
    public int compareTo(pkIdBySid_args other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSid()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetSsid()).compareTo(other.isSetSsid());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSsid()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssid, other.ssid);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      lastComparison = Boolean.valueOf(isSetSendTime()).compareTo(other.isSetSendTime());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSendTime()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sendTime, other.sendTime);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
    }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("pkIdBySid_args(");
      boolean first = true;

      sb.append("sid:");
      sb.append(this.sid);
      first = false;
      if (!first) sb.append(", ");
      sb.append("ssid:");
      sb.append(this.ssid);
      first = false;
      if (!first) sb.append(", ");
      sb.append("sendTime:");
      sb.append(this.sendTime);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class pkIdBySid_argsStandardSchemeFactory implements SchemeFactory {
      public pkIdBySid_argsStandardScheme getScheme() {
        return new pkIdBySid_argsStandardScheme();
      }
    }

    private static class pkIdBySid_argsStandardScheme extends StandardScheme<pkIdBySid_args> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, pkIdBySid_args struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 1: // SID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.sid = iprot.readI64();
                struct.setSidIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 2: // SSID
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.ssid = iprot.readI64();
                struct.setSsidIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            case 3: // SEND_TIME
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.sendTime = iprot.readI64();
                struct.setSendTimeIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, pkIdBySid_args struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        oprot.writeFieldBegin(SID_FIELD_DESC);
        oprot.writeI64(struct.sid);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(SSID_FIELD_DESC);
        oprot.writeI64(struct.ssid);
        oprot.writeFieldEnd();
        oprot.writeFieldBegin(SEND_TIME_FIELD_DESC);
        oprot.writeI64(struct.sendTime);
        oprot.writeFieldEnd();
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class pkIdBySid_argsTupleSchemeFactory implements SchemeFactory {
      public pkIdBySid_argsTupleScheme getScheme() {
        return new pkIdBySid_argsTupleScheme();
      }
    }

    private static class pkIdBySid_argsTupleScheme extends TupleScheme<pkIdBySid_args> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, pkIdBySid_args struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSid()) {
          optionals.set(0);
        }
        if (struct.isSetSsid()) {
          optionals.set(1);
        }
        if (struct.isSetSendTime()) {
          optionals.set(2);
        }
        oprot.writeBitSet(optionals, 3);
        if (struct.isSetSid()) {
          oprot.writeI64(struct.sid);
        }
        if (struct.isSetSsid()) {
          oprot.writeI64(struct.ssid);
        }
        if (struct.isSetSendTime()) {
          oprot.writeI64(struct.sendTime);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, pkIdBySid_args struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(3);
        if (incoming.get(0)) {
          struct.sid = iprot.readI64();
          struct.setSidIsSet(true);
        }
        if (incoming.get(1)) {
          struct.ssid = iprot.readI64();
          struct.setSsidIsSet(true);
        }
        if (incoming.get(2)) {
          struct.sendTime = iprot.readI64();
          struct.setSendTimeIsSet(true);
        }
      }
    }

  }

  public static class pkIdBySid_result implements org.apache.thrift.TBase<pkIdBySid_result, pkIdBySid_result._Fields>, java.io.Serializable, Cloneable, Comparable<pkIdBySid_result>   {
    private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("pkIdBySid_result");

    private static final org.apache.thrift.protocol.TField SUCCESS_FIELD_DESC = new org.apache.thrift.protocol.TField("success", org.apache.thrift.protocol.TType.I64, (short)0);

    private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
    static {
      schemes.put(StandardScheme.class, new pkIdBySid_resultStandardSchemeFactory());
      schemes.put(TupleScheme.class, new pkIdBySid_resultTupleSchemeFactory());
    }

    public long success; // required

    /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
    public enum _Fields implements org.apache.thrift.TFieldIdEnum {
      SUCCESS((short)0, "success");

      private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

      static {
        for (_Fields field : EnumSet.allOf(_Fields.class)) {
          byName.put(field.getFieldName(), field);
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, or null if its not found.
       */
      public static _Fields findByThriftId(int fieldId) {
        switch(fieldId) {
          case 0: // SUCCESS
            return SUCCESS;
          default:
            return null;
        }
      }

      /**
       * Find the _Fields constant that matches fieldId, throwing an exception
       * if it is not found.
       */
      public static _Fields findByThriftIdOrThrow(int fieldId) {
        _Fields fields = findByThriftId(fieldId);
        if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
        return fields;
      }

      /**
       * Find the _Fields constant that matches name, or null if its not found.
       */
      public static _Fields findByName(String name) {
        return byName.get(name);
      }

      private final short _thriftId;
      private final String _fieldName;

      _Fields(short thriftId, String fieldName) {
        _thriftId = thriftId;
        _fieldName = fieldName;
      }

      public short getThriftFieldId() {
        return _thriftId;
      }

      public String getFieldName() {
        return _fieldName;
      }
    }

    // isset id assignments
    private static final int __SUCCESS_ISSET_ID = 0;
    private byte __isset_bitfield = 0;
    public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
    static {
      Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
      tmpMap.put(_Fields.SUCCESS, new org.apache.thrift.meta_data.FieldMetaData("success", org.apache.thrift.TFieldRequirementType.DEFAULT, 
          new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
      metaDataMap = Collections.unmodifiableMap(tmpMap);
      org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(pkIdBySid_result.class, metaDataMap);
    }

    public pkIdBySid_result() {
    }

    public pkIdBySid_result(
      long success)
    {
      this();
      this.success = success;
      setSuccessIsSet(true);
    }

    /**
     * Performs a deep copy on <i>other</i>.
     */
    public pkIdBySid_result(pkIdBySid_result other) {
      __isset_bitfield = other.__isset_bitfield;
      this.success = other.success;
    }

    public pkIdBySid_result deepCopy() {
      return new pkIdBySid_result(this);
    }

    @Override
    public void clear() {
      setSuccessIsSet(false);
      this.success = 0;
    }

    public long getSuccess() {
      return this.success;
    }

    public pkIdBySid_result setSuccess(long success) {
      this.success = success;
      setSuccessIsSet(true);
      return this;
    }

    public void unsetSuccess() {
      __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SUCCESS_ISSET_ID);
    }

    /** Returns true if field success is set (has been assigned a value) and false otherwise */
    public boolean isSetSuccess() {
      return EncodingUtils.testBit(__isset_bitfield, __SUCCESS_ISSET_ID);
    }

    public void setSuccessIsSet(boolean value) {
      __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SUCCESS_ISSET_ID, value);
    }

    public void setFieldValue(_Fields field, Object value) {
      switch (field) {
      case SUCCESS:
        if (value == null) {
          unsetSuccess();
        } else {
          setSuccess((Long)value);
        }
        break;

      }
    }

    public Object getFieldValue(_Fields field) {
      switch (field) {
      case SUCCESS:
        return getSuccess();

      }
      throw new IllegalStateException();
    }

    /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
    public boolean isSet(_Fields field) {
      if (field == null) {
        throw new IllegalArgumentException();
      }

      switch (field) {
      case SUCCESS:
        return isSetSuccess();
      }
      throw new IllegalStateException();
    }

    @Override
    public boolean equals(Object that) {
      if (that == null)
        return false;
      if (that instanceof pkIdBySid_result)
        return this.equals((pkIdBySid_result)that);
      return false;
    }

    public boolean equals(pkIdBySid_result that) {
      if (that == null)
        return false;

      boolean this_present_success = true;
      boolean that_present_success = true;
      if (this_present_success || that_present_success) {
        if (!(this_present_success && that_present_success))
          return false;
        if (this.success != that.success)
          return false;
      }

      return true;
    }

    @Override
    public int hashCode() {
      List<Object> list = new ArrayList<Object>();

      boolean present_success = true;
      list.add(present_success);
      if (present_success)
        list.add(success);

      return list.hashCode();
    }

    @Override
    public int compareTo(pkIdBySid_result other) {
      if (!getClass().equals(other.getClass())) {
        return getClass().getName().compareTo(other.getClass().getName());
      }

      int lastComparison = 0;

      lastComparison = Boolean.valueOf(isSetSuccess()).compareTo(other.isSetSuccess());
      if (lastComparison != 0) {
        return lastComparison;
      }
      if (isSetSuccess()) {
        lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.success, other.success);
        if (lastComparison != 0) {
          return lastComparison;
        }
      }
      return 0;
    }

    public _Fields fieldForId(int fieldId) {
      return _Fields.findByThriftId(fieldId);
    }

    public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
      schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
      schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
      }

    @Override
    public String toString() {
      StringBuilder sb = new StringBuilder("pkIdBySid_result(");
      boolean first = true;

      sb.append("success:");
      sb.append(this.success);
      first = false;
      sb.append(")");
      return sb.toString();
    }

    public void validate() throws org.apache.thrift.TException {
      // check for required fields
      // check for sub-struct validity
    }

    private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
      try {
        write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
      try {
        // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
        __isset_bitfield = 0;
        read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
      } catch (org.apache.thrift.TException te) {
        throw new java.io.IOException(te);
      }
    }

    private static class pkIdBySid_resultStandardSchemeFactory implements SchemeFactory {
      public pkIdBySid_resultStandardScheme getScheme() {
        return new pkIdBySid_resultStandardScheme();
      }
    }

    private static class pkIdBySid_resultStandardScheme extends StandardScheme<pkIdBySid_result> {

      public void read(org.apache.thrift.protocol.TProtocol iprot, pkIdBySid_result struct) throws org.apache.thrift.TException {
        org.apache.thrift.protocol.TField schemeField;
        iprot.readStructBegin();
        while (true)
        {
          schemeField = iprot.readFieldBegin();
          if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
            break;
          }
          switch (schemeField.id) {
            case 0: // SUCCESS
              if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
                struct.success = iprot.readI64();
                struct.setSuccessIsSet(true);
              } else { 
                org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
              }
              break;
            default:
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
          }
          iprot.readFieldEnd();
        }
        iprot.readStructEnd();

        // check for required fields of primitive type, which can't be checked in the validate method
        struct.validate();
      }

      public void write(org.apache.thrift.protocol.TProtocol oprot, pkIdBySid_result struct) throws org.apache.thrift.TException {
        struct.validate();

        oprot.writeStructBegin(STRUCT_DESC);
        if (struct.isSetSuccess()) {
          oprot.writeFieldBegin(SUCCESS_FIELD_DESC);
          oprot.writeI64(struct.success);
          oprot.writeFieldEnd();
        }
        oprot.writeFieldStop();
        oprot.writeStructEnd();
      }

    }

    private static class pkIdBySid_resultTupleSchemeFactory implements SchemeFactory {
      public pkIdBySid_resultTupleScheme getScheme() {
        return new pkIdBySid_resultTupleScheme();
      }
    }

    private static class pkIdBySid_resultTupleScheme extends TupleScheme<pkIdBySid_result> {

      @Override
      public void write(org.apache.thrift.protocol.TProtocol prot, pkIdBySid_result struct) throws org.apache.thrift.TException {
        TTupleProtocol oprot = (TTupleProtocol) prot;
        BitSet optionals = new BitSet();
        if (struct.isSetSuccess()) {
          optionals.set(0);
        }
        oprot.writeBitSet(optionals, 1);
        if (struct.isSetSuccess()) {
          oprot.writeI64(struct.success);
        }
      }

      @Override
      public void read(org.apache.thrift.protocol.TProtocol prot, pkIdBySid_result struct) throws org.apache.thrift.TException {
        TTupleProtocol iprot = (TTupleProtocol) prot;
        BitSet incoming = iprot.readBitSet(1);
        if (incoming.get(0)) {
          struct.success = iprot.readI64();
          struct.setSuccessIsSet(true);
        }
      }
    }

  }

}
