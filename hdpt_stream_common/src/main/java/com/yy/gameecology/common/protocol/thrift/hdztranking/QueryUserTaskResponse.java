/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class QueryUserTaskResponse implements org.apache.thrift.TBase<QueryUserTaskResponse, QueryUserTaskResponse._Fields>, java.io.Serializable, Cloneable, Comparable<QueryUserTaskResponse> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryUserTaskResponse");

  private static final org.apache.thrift.protocol.TField CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("code", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField REASON_FIELD_DESC = new org.apache.thrift.protocol.TField("reason", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField ITEMS_FIELD_DESC = new org.apache.thrift.protocol.TField("items", org.apache.thrift.protocol.TType.MAP, (short)3);
  private static final org.apache.thrift.protocol.TField CUR_ROUND_FIELD_DESC = new org.apache.thrift.protocol.TField("curRound", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField CUR_TASK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("curTaskId", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryUserTaskResponseStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryUserTaskResponseTupleSchemeFactory());
  }

  public int code; // required
  public String reason; // required
  public Map<String,UserTaskItem> items; // required
  public long curRound; // required
  public long curTaskId; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    CODE((short)1, "code"),
    REASON((short)2, "reason"),
    ITEMS((short)3, "items"),
    CUR_ROUND((short)4, "curRound"),
    CUR_TASK_ID((short)5, "curTaskId"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CODE
          return CODE;
        case 2: // REASON
          return REASON;
        case 3: // ITEMS
          return ITEMS;
        case 4: // CUR_ROUND
          return CUR_ROUND;
        case 5: // CUR_TASK_ID
          return CUR_TASK_ID;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __CODE_ISSET_ID = 0;
  private static final int __CURROUND_ISSET_ID = 1;
  private static final int __CURTASKID_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CODE, new org.apache.thrift.meta_data.FieldMetaData("code", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.REASON, new org.apache.thrift.meta_data.FieldMetaData("reason", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ITEMS, new org.apache.thrift.meta_data.FieldMetaData("items", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, UserTaskItem.class))));
    tmpMap.put(_Fields.CUR_ROUND, new org.apache.thrift.meta_data.FieldMetaData("curRound", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CUR_TASK_ID, new org.apache.thrift.meta_data.FieldMetaData("curTaskId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryUserTaskResponse.class, metaDataMap);
  }

  public QueryUserTaskResponse() {
  }

  public QueryUserTaskResponse(
    int code,
    String reason,
    Map<String,UserTaskItem> items,
    long curRound,
    long curTaskId,
    Map<String,String> extData)
  {
    this();
    this.code = code;
    setCodeIsSet(true);
    this.reason = reason;
    this.items = items;
    this.curRound = curRound;
    setCurRoundIsSet(true);
    this.curTaskId = curTaskId;
    setCurTaskIdIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryUserTaskResponse(QueryUserTaskResponse other) {
    __isset_bitfield = other.__isset_bitfield;
    this.code = other.code;
    if (other.isSetReason()) {
      this.reason = other.reason;
    }
    if (other.isSetItems()) {
      Map<String,UserTaskItem> __this__items = new HashMap<String,UserTaskItem>(other.items.size());
      for (Map.Entry<String, UserTaskItem> other_element : other.items.entrySet()) {

        String other_element_key = other_element.getKey();
        UserTaskItem other_element_value = other_element.getValue();

        String __this__items_copy_key = other_element_key;

        UserTaskItem __this__items_copy_value = new UserTaskItem(other_element_value);

        __this__items.put(__this__items_copy_key, __this__items_copy_value);
      }
      this.items = __this__items;
    }
    this.curRound = other.curRound;
    this.curTaskId = other.curTaskId;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public QueryUserTaskResponse deepCopy() {
    return new QueryUserTaskResponse(this);
  }

  @Override
  public void clear() {
    setCodeIsSet(false);
    this.code = 0;
    this.reason = null;
    this.items = null;
    setCurRoundIsSet(false);
    this.curRound = 0;
    setCurTaskIdIsSet(false);
    this.curTaskId = 0;
    this.extData = null;
  }

  public int getCode() {
    return this.code;
  }

  public QueryUserTaskResponse setCode(int code) {
    this.code = code;
    setCodeIsSet(true);
    return this;
  }

  public void unsetCode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CODE_ISSET_ID);
  }

  /** Returns true if field code is set (has been assigned a value) and false otherwise */
  public boolean isSetCode() {
    return EncodingUtils.testBit(__isset_bitfield, __CODE_ISSET_ID);
  }

  public void setCodeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CODE_ISSET_ID, value);
  }

  public String getReason() {
    return this.reason;
  }

  public QueryUserTaskResponse setReason(String reason) {
    this.reason = reason;
    return this;
  }

  public void unsetReason() {
    this.reason = null;
  }

  /** Returns true if field reason is set (has been assigned a value) and false otherwise */
  public boolean isSetReason() {
    return this.reason != null;
  }

  public void setReasonIsSet(boolean value) {
    if (!value) {
      this.reason = null;
    }
  }

  public int getItemsSize() {
    return (this.items == null) ? 0 : this.items.size();
  }

  public void putToItems(String key, UserTaskItem val) {
    if (this.items == null) {
      this.items = new HashMap<String,UserTaskItem>();
    }
    this.items.put(key, val);
  }

  public Map<String,UserTaskItem> getItems() {
    return this.items;
  }

  public QueryUserTaskResponse setItems(Map<String,UserTaskItem> items) {
    this.items = items;
    return this;
  }

  public void unsetItems() {
    this.items = null;
  }

  /** Returns true if field items is set (has been assigned a value) and false otherwise */
  public boolean isSetItems() {
    return this.items != null;
  }

  public void setItemsIsSet(boolean value) {
    if (!value) {
      this.items = null;
    }
  }

  public long getCurRound() {
    return this.curRound;
  }

  public QueryUserTaskResponse setCurRound(long curRound) {
    this.curRound = curRound;
    setCurRoundIsSet(true);
    return this;
  }

  public void unsetCurRound() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CURROUND_ISSET_ID);
  }

  /** Returns true if field curRound is set (has been assigned a value) and false otherwise */
  public boolean isSetCurRound() {
    return EncodingUtils.testBit(__isset_bitfield, __CURROUND_ISSET_ID);
  }

  public void setCurRoundIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CURROUND_ISSET_ID, value);
  }

  public long getCurTaskId() {
    return this.curTaskId;
  }

  public QueryUserTaskResponse setCurTaskId(long curTaskId) {
    this.curTaskId = curTaskId;
    setCurTaskIdIsSet(true);
    return this;
  }

  public void unsetCurTaskId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CURTASKID_ISSET_ID);
  }

  /** Returns true if field curTaskId is set (has been assigned a value) and false otherwise */
  public boolean isSetCurTaskId() {
    return EncodingUtils.testBit(__isset_bitfield, __CURTASKID_ISSET_ID);
  }

  public void setCurTaskIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CURTASKID_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public QueryUserTaskResponse setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case CODE:
      if (value == null) {
        unsetCode();
      } else {
        setCode((Integer)value);
      }
      break;

    case REASON:
      if (value == null) {
        unsetReason();
      } else {
        setReason((String)value);
      }
      break;

    case ITEMS:
      if (value == null) {
        unsetItems();
      } else {
        setItems((Map<String,UserTaskItem>)value);
      }
      break;

    case CUR_ROUND:
      if (value == null) {
        unsetCurRound();
      } else {
        setCurRound((Long)value);
      }
      break;

    case CUR_TASK_ID:
      if (value == null) {
        unsetCurTaskId();
      } else {
        setCurTaskId((Long)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case CODE:
      return getCode();

    case REASON:
      return getReason();

    case ITEMS:
      return getItems();

    case CUR_ROUND:
      return getCurRound();

    case CUR_TASK_ID:
      return getCurTaskId();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case CODE:
      return isSetCode();
    case REASON:
      return isSetReason();
    case ITEMS:
      return isSetItems();
    case CUR_ROUND:
      return isSetCurRound();
    case CUR_TASK_ID:
      return isSetCurTaskId();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryUserTaskResponse)
      return this.equals((QueryUserTaskResponse)that);
    return false;
  }

  public boolean equals(QueryUserTaskResponse that) {
    if (that == null)
      return false;

    boolean this_present_code = true;
    boolean that_present_code = true;
    if (this_present_code || that_present_code) {
      if (!(this_present_code && that_present_code))
        return false;
      if (this.code != that.code)
        return false;
    }

    boolean this_present_reason = true && this.isSetReason();
    boolean that_present_reason = true && that.isSetReason();
    if (this_present_reason || that_present_reason) {
      if (!(this_present_reason && that_present_reason))
        return false;
      if (!this.reason.equals(that.reason))
        return false;
    }

    boolean this_present_items = true && this.isSetItems();
    boolean that_present_items = true && that.isSetItems();
    if (this_present_items || that_present_items) {
      if (!(this_present_items && that_present_items))
        return false;
      if (!this.items.equals(that.items))
        return false;
    }

    boolean this_present_curRound = true;
    boolean that_present_curRound = true;
    if (this_present_curRound || that_present_curRound) {
      if (!(this_present_curRound && that_present_curRound))
        return false;
      if (this.curRound != that.curRound)
        return false;
    }

    boolean this_present_curTaskId = true;
    boolean that_present_curTaskId = true;
    if (this_present_curTaskId || that_present_curTaskId) {
      if (!(this_present_curTaskId && that_present_curTaskId))
        return false;
      if (this.curTaskId != that.curTaskId)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_code = true;
    list.add(present_code);
    if (present_code)
      list.add(code);

    boolean present_reason = true && (isSetReason());
    list.add(present_reason);
    if (present_reason)
      list.add(reason);

    boolean present_items = true && (isSetItems());
    list.add(present_items);
    if (present_items)
      list.add(items);

    boolean present_curRound = true;
    list.add(present_curRound);
    if (present_curRound)
      list.add(curRound);

    boolean present_curTaskId = true;
    list.add(present_curTaskId);
    if (present_curTaskId)
      list.add(curTaskId);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryUserTaskResponse other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetCode()).compareTo(other.isSetCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.code, other.code);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReason()).compareTo(other.isSetReason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reason, other.reason);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItems()).compareTo(other.isSetItems());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItems()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.items, other.items);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurRound()).compareTo(other.isSetCurRound());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurRound()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.curRound, other.curRound);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurTaskId()).compareTo(other.isSetCurTaskId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurTaskId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.curTaskId, other.curTaskId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryUserTaskResponse(");
    boolean first = true;

    sb.append("code:");
    sb.append(this.code);
    first = false;
    if (!first) sb.append(", ");
    sb.append("reason:");
    if (this.reason == null) {
      sb.append("null");
    } else {
      sb.append(this.reason);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("items:");
    if (this.items == null) {
      sb.append("null");
    } else {
      sb.append(this.items);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("curRound:");
    sb.append(this.curRound);
    first = false;
    if (!first) sb.append(", ");
    sb.append("curTaskId:");
    sb.append(this.curTaskId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryUserTaskResponseStandardSchemeFactory implements SchemeFactory {
    public QueryUserTaskResponseStandardScheme getScheme() {
      return new QueryUserTaskResponseStandardScheme();
    }
  }

  private static class QueryUserTaskResponseStandardScheme extends StandardScheme<QueryUserTaskResponse> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryUserTaskResponse struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.code = iprot.readI32();
              struct.setCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // REASON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.reason = iprot.readString();
              struct.setReasonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ITEMS
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map482 = iprot.readMapBegin();
                struct.items = new HashMap<String,UserTaskItem>(2*_map482.size);
                String _key483;
                UserTaskItem _val484;
                for (int _i485 = 0; _i485 < _map482.size; ++_i485)
                {
                  _key483 = iprot.readString();
                  _val484 = new UserTaskItem();
                  _val484.read(iprot);
                  struct.items.put(_key483, _val484);
                }
                iprot.readMapEnd();
              }
              struct.setItemsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CUR_ROUND
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.curRound = iprot.readI64();
              struct.setCurRoundIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // CUR_TASK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.curTaskId = iprot.readI64();
              struct.setCurTaskIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map486 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map486.size);
                String _key487;
                String _val488;
                for (int _i489 = 0; _i489 < _map486.size; ++_i489)
                {
                  _key487 = iprot.readString();
                  _val488 = iprot.readString();
                  struct.extData.put(_key487, _val488);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryUserTaskResponse struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(CODE_FIELD_DESC);
      oprot.writeI32(struct.code);
      oprot.writeFieldEnd();
      if (struct.reason != null) {
        oprot.writeFieldBegin(REASON_FIELD_DESC);
        oprot.writeString(struct.reason);
        oprot.writeFieldEnd();
      }
      if (struct.items != null) {
        oprot.writeFieldBegin(ITEMS_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRUCT, struct.items.size()));
          for (Map.Entry<String, UserTaskItem> _iter490 : struct.items.entrySet())
          {
            oprot.writeString(_iter490.getKey());
            _iter490.getValue().write(oprot);
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CUR_ROUND_FIELD_DESC);
      oprot.writeI64(struct.curRound);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CUR_TASK_ID_FIELD_DESC);
      oprot.writeI64(struct.curTaskId);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter491 : struct.extData.entrySet())
          {
            oprot.writeString(_iter491.getKey());
            oprot.writeString(_iter491.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryUserTaskResponseTupleSchemeFactory implements SchemeFactory {
    public QueryUserTaskResponseTupleScheme getScheme() {
      return new QueryUserTaskResponseTupleScheme();
    }
  }

  private static class QueryUserTaskResponseTupleScheme extends TupleScheme<QueryUserTaskResponse> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryUserTaskResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetCode()) {
        optionals.set(0);
      }
      if (struct.isSetReason()) {
        optionals.set(1);
      }
      if (struct.isSetItems()) {
        optionals.set(2);
      }
      if (struct.isSetCurRound()) {
        optionals.set(3);
      }
      if (struct.isSetCurTaskId()) {
        optionals.set(4);
      }
      if (struct.isSetExtData()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetCode()) {
        oprot.writeI32(struct.code);
      }
      if (struct.isSetReason()) {
        oprot.writeString(struct.reason);
      }
      if (struct.isSetItems()) {
        {
          oprot.writeI32(struct.items.size());
          for (Map.Entry<String, UserTaskItem> _iter492 : struct.items.entrySet())
          {
            oprot.writeString(_iter492.getKey());
            _iter492.getValue().write(oprot);
          }
        }
      }
      if (struct.isSetCurRound()) {
        oprot.writeI64(struct.curRound);
      }
      if (struct.isSetCurTaskId()) {
        oprot.writeI64(struct.curTaskId);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter493 : struct.extData.entrySet())
          {
            oprot.writeString(_iter493.getKey());
            oprot.writeString(_iter493.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryUserTaskResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.code = iprot.readI32();
        struct.setCodeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.reason = iprot.readString();
        struct.setReasonIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map494 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.items = new HashMap<String,UserTaskItem>(2*_map494.size);
          String _key495;
          UserTaskItem _val496;
          for (int _i497 = 0; _i497 < _map494.size; ++_i497)
          {
            _key495 = iprot.readString();
            _val496 = new UserTaskItem();
            _val496.read(iprot);
            struct.items.put(_key495, _val496);
          }
        }
        struct.setItemsIsSet(true);
      }
      if (incoming.get(3)) {
        struct.curRound = iprot.readI64();
        struct.setCurRoundIsSet(true);
      }
      if (incoming.get(4)) {
        struct.curTaskId = iprot.readI64();
        struct.setCurTaskIdIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TMap _map498 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map498.size);
          String _key499;
          String _val500;
          for (int _i501 = 0; _i501 < _map498.size; ++_i501)
          {
            _key499 = iprot.readString();
            _val500 = iprot.readString();
            struct.extData.put(_key499, _val500);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

