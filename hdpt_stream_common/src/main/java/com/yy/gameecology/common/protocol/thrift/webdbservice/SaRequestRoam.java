/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 查询漫游信息(最近访问频道)
 * @param     appkey           客户端的标识
 * @param     uids             用户id列表(列表最大长度为100)
 * @param     columns          需要查询的字段集合，可以查询如下字段
 *                               - "uid",
 *                               - "version",
 *                               - "value",
 * @SaResponseSet              漫游信息结果集，如果没有查到结果则dataSet为空
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class SaRequestRoam implements org.apache.thrift.TBase<SaRequestRoam, SaRequestRoam._Fields>, java.io.Serializable, Cloneable, Comparable<SaRequestRoam> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaRequestRoam");

  private static final org.apache.thrift.protocol.TField AUTH_MSG_FIELD_DESC = new org.apache.thrift.protocol.TField("authMsg", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField APPKEY_FIELD_DESC = new org.apache.thrift.protocol.TField("appkey", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField UIDS_FIELD_DESC = new org.apache.thrift.protocol.TField("uids", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField COLUMNS_FIELD_DESC = new org.apache.thrift.protocol.TField("columns", org.apache.thrift.protocol.TType.LIST, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaRequestRoamStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaRequestRoamTupleSchemeFactory());
  }

  public AuthorizeMsg authMsg; // required
  public String appkey; // required
  public List<String> uids; // required
  public List<String> columns; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    AUTH_MSG((short)1, "authMsg"),
    APPKEY((short)2, "appkey"),
    UIDS((short)3, "uids"),
    COLUMNS((short)4, "columns");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // AUTH_MSG
          return AUTH_MSG;
        case 2: // APPKEY
          return APPKEY;
        case 3: // UIDS
          return UIDS;
        case 4: // COLUMNS
          return COLUMNS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.AUTH_MSG, new org.apache.thrift.meta_data.FieldMetaData("authMsg", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AuthorizeMsg.class)));
    tmpMap.put(_Fields.APPKEY, new org.apache.thrift.meta_data.FieldMetaData("appkey", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.UIDS, new org.apache.thrift.meta_data.FieldMetaData("uids", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.COLUMNS, new org.apache.thrift.meta_data.FieldMetaData("columns", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaRequestRoam.class, metaDataMap);
  }

  public SaRequestRoam() {
  }

  public SaRequestRoam(
    AuthorizeMsg authMsg,
    String appkey,
    List<String> uids,
    List<String> columns)
  {
    this();
    this.authMsg = authMsg;
    this.appkey = appkey;
    this.uids = uids;
    this.columns = columns;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaRequestRoam(SaRequestRoam other) {
    if (other.isSetAuthMsg()) {
      this.authMsg = new AuthorizeMsg(other.authMsg);
    }
    if (other.isSetAppkey()) {
      this.appkey = other.appkey;
    }
    if (other.isSetUids()) {
      List<String> __this__uids = new ArrayList<String>(other.uids);
      this.uids = __this__uids;
    }
    if (other.isSetColumns()) {
      List<String> __this__columns = new ArrayList<String>(other.columns);
      this.columns = __this__columns;
    }
  }

  public SaRequestRoam deepCopy() {
    return new SaRequestRoam(this);
  }

  @Override
  public void clear() {
    this.authMsg = null;
    this.appkey = null;
    this.uids = null;
    this.columns = null;
  }

  public AuthorizeMsg getAuthMsg() {
    return this.authMsg;
  }

  public SaRequestRoam setAuthMsg(AuthorizeMsg authMsg) {
    this.authMsg = authMsg;
    return this;
  }

  public void unsetAuthMsg() {
    this.authMsg = null;
  }

  /** Returns true if field authMsg is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthMsg() {
    return this.authMsg != null;
  }

  public void setAuthMsgIsSet(boolean value) {
    if (!value) {
      this.authMsg = null;
    }
  }

  public String getAppkey() {
    return this.appkey;
  }

  public SaRequestRoam setAppkey(String appkey) {
    this.appkey = appkey;
    return this;
  }

  public void unsetAppkey() {
    this.appkey = null;
  }

  /** Returns true if field appkey is set (has been assigned a value) and false otherwise */
  public boolean isSetAppkey() {
    return this.appkey != null;
  }

  public void setAppkeyIsSet(boolean value) {
    if (!value) {
      this.appkey = null;
    }
  }

  public int getUidsSize() {
    return (this.uids == null) ? 0 : this.uids.size();
  }

  public java.util.Iterator<String> getUidsIterator() {
    return (this.uids == null) ? null : this.uids.iterator();
  }

  public void addToUids(String elem) {
    if (this.uids == null) {
      this.uids = new ArrayList<String>();
    }
    this.uids.add(elem);
  }

  public List<String> getUids() {
    return this.uids;
  }

  public SaRequestRoam setUids(List<String> uids) {
    this.uids = uids;
    return this;
  }

  public void unsetUids() {
    this.uids = null;
  }

  /** Returns true if field uids is set (has been assigned a value) and false otherwise */
  public boolean isSetUids() {
    return this.uids != null;
  }

  public void setUidsIsSet(boolean value) {
    if (!value) {
      this.uids = null;
    }
  }

  public int getColumnsSize() {
    return (this.columns == null) ? 0 : this.columns.size();
  }

  public java.util.Iterator<String> getColumnsIterator() {
    return (this.columns == null) ? null : this.columns.iterator();
  }

  public void addToColumns(String elem) {
    if (this.columns == null) {
      this.columns = new ArrayList<String>();
    }
    this.columns.add(elem);
  }

  public List<String> getColumns() {
    return this.columns;
  }

  public SaRequestRoam setColumns(List<String> columns) {
    this.columns = columns;
    return this;
  }

  public void unsetColumns() {
    this.columns = null;
  }

  /** Returns true if field columns is set (has been assigned a value) and false otherwise */
  public boolean isSetColumns() {
    return this.columns != null;
  }

  public void setColumnsIsSet(boolean value) {
    if (!value) {
      this.columns = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case AUTH_MSG:
      if (value == null) {
        unsetAuthMsg();
      } else {
        setAuthMsg((AuthorizeMsg)value);
      }
      break;

    case APPKEY:
      if (value == null) {
        unsetAppkey();
      } else {
        setAppkey((String)value);
      }
      break;

    case UIDS:
      if (value == null) {
        unsetUids();
      } else {
        setUids((List<String>)value);
      }
      break;

    case COLUMNS:
      if (value == null) {
        unsetColumns();
      } else {
        setColumns((List<String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case AUTH_MSG:
      return getAuthMsg();

    case APPKEY:
      return getAppkey();

    case UIDS:
      return getUids();

    case COLUMNS:
      return getColumns();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case AUTH_MSG:
      return isSetAuthMsg();
    case APPKEY:
      return isSetAppkey();
    case UIDS:
      return isSetUids();
    case COLUMNS:
      return isSetColumns();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaRequestRoam)
      return this.equals((SaRequestRoam)that);
    return false;
  }

  public boolean equals(SaRequestRoam that) {
    if (that == null)
      return false;

    boolean this_present_authMsg = true && this.isSetAuthMsg();
    boolean that_present_authMsg = true && that.isSetAuthMsg();
    if (this_present_authMsg || that_present_authMsg) {
      if (!(this_present_authMsg && that_present_authMsg))
        return false;
      if (!this.authMsg.equals(that.authMsg))
        return false;
    }

    boolean this_present_appkey = true && this.isSetAppkey();
    boolean that_present_appkey = true && that.isSetAppkey();
    if (this_present_appkey || that_present_appkey) {
      if (!(this_present_appkey && that_present_appkey))
        return false;
      if (!this.appkey.equals(that.appkey))
        return false;
    }

    boolean this_present_uids = true && this.isSetUids();
    boolean that_present_uids = true && that.isSetUids();
    if (this_present_uids || that_present_uids) {
      if (!(this_present_uids && that_present_uids))
        return false;
      if (!this.uids.equals(that.uids))
        return false;
    }

    boolean this_present_columns = true && this.isSetColumns();
    boolean that_present_columns = true && that.isSetColumns();
    if (this_present_columns || that_present_columns) {
      if (!(this_present_columns && that_present_columns))
        return false;
      if (!this.columns.equals(that.columns))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_authMsg = true && (isSetAuthMsg());
    list.add(present_authMsg);
    if (present_authMsg)
      list.add(authMsg);

    boolean present_appkey = true && (isSetAppkey());
    list.add(present_appkey);
    if (present_appkey)
      list.add(appkey);

    boolean present_uids = true && (isSetUids());
    list.add(present_uids);
    if (present_uids)
      list.add(uids);

    boolean present_columns = true && (isSetColumns());
    list.add(present_columns);
    if (present_columns)
      list.add(columns);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaRequestRoam other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAuthMsg()).compareTo(other.isSetAuthMsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthMsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.authMsg, other.authMsg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppkey()).compareTo(other.isSetAppkey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppkey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appkey, other.appkey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUids()).compareTo(other.isSetUids());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUids()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uids, other.uids);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetColumns()).compareTo(other.isSetColumns());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetColumns()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.columns, other.columns);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaRequestRoam(");
    boolean first = true;

    sb.append("authMsg:");
    if (this.authMsg == null) {
      sb.append("null");
    } else {
      sb.append(this.authMsg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appkey:");
    if (this.appkey == null) {
      sb.append("null");
    } else {
      sb.append(this.appkey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("uids:");
    if (this.uids == null) {
      sb.append("null");
    } else {
      sb.append(this.uids);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("columns:");
    if (this.columns == null) {
      sb.append("null");
    } else {
      sb.append(this.columns);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (authMsg != null) {
      authMsg.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaRequestRoamStandardSchemeFactory implements SchemeFactory {
    public SaRequestRoamStandardScheme getScheme() {
      return new SaRequestRoamStandardScheme();
    }
  }

  private static class SaRequestRoamStandardScheme extends StandardScheme<SaRequestRoam> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaRequestRoam struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // AUTH_MSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.authMsg = new AuthorizeMsg();
              struct.authMsg.read(iprot);
              struct.setAuthMsgIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPKEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appkey = iprot.readString();
              struct.setAppkeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // UIDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list236 = iprot.readListBegin();
                struct.uids = new ArrayList<String>(_list236.size);
                String _elem237;
                for (int _i238 = 0; _i238 < _list236.size; ++_i238)
                {
                  _elem237 = iprot.readString();
                  struct.uids.add(_elem237);
                }
                iprot.readListEnd();
              }
              struct.setUidsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // COLUMNS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list239 = iprot.readListBegin();
                struct.columns = new ArrayList<String>(_list239.size);
                String _elem240;
                for (int _i241 = 0; _i241 < _list239.size; ++_i241)
                {
                  _elem240 = iprot.readString();
                  struct.columns.add(_elem240);
                }
                iprot.readListEnd();
              }
              struct.setColumnsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaRequestRoam struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.authMsg != null) {
        oprot.writeFieldBegin(AUTH_MSG_FIELD_DESC);
        struct.authMsg.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.appkey != null) {
        oprot.writeFieldBegin(APPKEY_FIELD_DESC);
        oprot.writeString(struct.appkey);
        oprot.writeFieldEnd();
      }
      if (struct.uids != null) {
        oprot.writeFieldBegin(UIDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.uids.size()));
          for (String _iter242 : struct.uids)
          {
            oprot.writeString(_iter242);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.columns != null) {
        oprot.writeFieldBegin(COLUMNS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.columns.size()));
          for (String _iter243 : struct.columns)
          {
            oprot.writeString(_iter243);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaRequestRoamTupleSchemeFactory implements SchemeFactory {
    public SaRequestRoamTupleScheme getScheme() {
      return new SaRequestRoamTupleScheme();
    }
  }

  private static class SaRequestRoamTupleScheme extends TupleScheme<SaRequestRoam> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaRequestRoam struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAuthMsg()) {
        optionals.set(0);
      }
      if (struct.isSetAppkey()) {
        optionals.set(1);
      }
      if (struct.isSetUids()) {
        optionals.set(2);
      }
      if (struct.isSetColumns()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetAuthMsg()) {
        struct.authMsg.write(oprot);
      }
      if (struct.isSetAppkey()) {
        oprot.writeString(struct.appkey);
      }
      if (struct.isSetUids()) {
        {
          oprot.writeI32(struct.uids.size());
          for (String _iter244 : struct.uids)
          {
            oprot.writeString(_iter244);
          }
        }
      }
      if (struct.isSetColumns()) {
        {
          oprot.writeI32(struct.columns.size());
          for (String _iter245 : struct.columns)
          {
            oprot.writeString(_iter245);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaRequestRoam struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.authMsg = new AuthorizeMsg();
        struct.authMsg.read(iprot);
        struct.setAuthMsgIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appkey = iprot.readString();
        struct.setAppkeyIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TList _list246 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.uids = new ArrayList<String>(_list246.size);
          String _elem247;
          for (int _i248 = 0; _i248 < _list246.size; ++_i248)
          {
            _elem247 = iprot.readString();
            struct.uids.add(_elem247);
          }
        }
        struct.setUidsIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TList _list249 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.columns = new ArrayList<String>(_list249.size);
          String _elem250;
          for (int _i251 = 0; _i251 < _list249.size; ++_i251)
          {
            _elem250 = iprot.readString();
            struct.columns.add(_elem250);
          }
        }
        struct.setColumnsIsSet(true);
      }
    }
  }

}

