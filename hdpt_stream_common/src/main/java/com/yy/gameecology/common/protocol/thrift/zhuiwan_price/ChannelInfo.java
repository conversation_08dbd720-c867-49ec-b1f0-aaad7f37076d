/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_price;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-02-22")
public class ChannelInfo implements org.apache.thrift.TBase<ChannelInfo, ChannelInfo._Fields>, java.io.Serializable, Cloneable, Comparable<ChannelInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ChannelInfo");

  private static final org.apache.thrift.protocol.TField TOP_SID_FIELD_DESC = new org.apache.thrift.protocol.TField("topSid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField SUB_SID_FIELD_DESC = new org.apache.thrift.protocol.TField("subSid", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField CHANNEL_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("channelType", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField PLUGIN_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("pluginType", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField TEMPLATE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("templateId", org.apache.thrift.protocol.TType.I32, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ChannelInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ChannelInfoTupleSchemeFactory());
  }

  public long topSid; // required
  public long subSid; // required
  public int channelType; // required
  public int pluginType; // required
  public int templateId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    TOP_SID((short)1, "topSid"),
    SUB_SID((short)2, "subSid"),
    CHANNEL_TYPE((short)3, "channelType"),
    PLUGIN_TYPE((short)4, "pluginType"),
    TEMPLATE_ID((short)5, "templateId");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TOP_SID
          return TOP_SID;
        case 2: // SUB_SID
          return SUB_SID;
        case 3: // CHANNEL_TYPE
          return CHANNEL_TYPE;
        case 4: // PLUGIN_TYPE
          return PLUGIN_TYPE;
        case 5: // TEMPLATE_ID
          return TEMPLATE_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __TOPSID_ISSET_ID = 0;
  private static final int __SUBSID_ISSET_ID = 1;
  private static final int __CHANNELTYPE_ISSET_ID = 2;
  private static final int __PLUGINTYPE_ISSET_ID = 3;
  private static final int __TEMPLATEID_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TOP_SID, new org.apache.thrift.meta_data.FieldMetaData("topSid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SUB_SID, new org.apache.thrift.meta_data.FieldMetaData("subSid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CHANNEL_TYPE, new org.apache.thrift.meta_data.FieldMetaData("channelType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PLUGIN_TYPE, new org.apache.thrift.meta_data.FieldMetaData("pluginType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TEMPLATE_ID, new org.apache.thrift.meta_data.FieldMetaData("templateId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ChannelInfo.class, metaDataMap);
  }

  public ChannelInfo() {
  }

  public ChannelInfo(
    long topSid,
    long subSid,
    int channelType,
    int pluginType,
    int templateId)
  {
    this();
    this.topSid = topSid;
    setTopSidIsSet(true);
    this.subSid = subSid;
    setSubSidIsSet(true);
    this.channelType = channelType;
    setChannelTypeIsSet(true);
    this.pluginType = pluginType;
    setPluginTypeIsSet(true);
    this.templateId = templateId;
    setTemplateIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ChannelInfo(ChannelInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.topSid = other.topSid;
    this.subSid = other.subSid;
    this.channelType = other.channelType;
    this.pluginType = other.pluginType;
    this.templateId = other.templateId;
  }

  public ChannelInfo deepCopy() {
    return new ChannelInfo(this);
  }

  @Override
  public void clear() {
    setTopSidIsSet(false);
    this.topSid = 0;
    setSubSidIsSet(false);
    this.subSid = 0;
    setChannelTypeIsSet(false);
    this.channelType = 0;
    setPluginTypeIsSet(false);
    this.pluginType = 0;
    setTemplateIdIsSet(false);
    this.templateId = 0;
  }

  public long getTopSid() {
    return this.topSid;
  }

  public ChannelInfo setTopSid(long topSid) {
    this.topSid = topSid;
    setTopSidIsSet(true);
    return this;
  }

  public void unsetTopSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOPSID_ISSET_ID);
  }

  /** Returns true if field topSid is set (has been assigned a value) and false otherwise */
  public boolean isSetTopSid() {
    return EncodingUtils.testBit(__isset_bitfield, __TOPSID_ISSET_ID);
  }

  public void setTopSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOPSID_ISSET_ID, value);
  }

  public long getSubSid() {
    return this.subSid;
  }

  public ChannelInfo setSubSid(long subSid) {
    this.subSid = subSid;
    setSubSidIsSet(true);
    return this;
  }

  public void unsetSubSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SUBSID_ISSET_ID);
  }

  /** Returns true if field subSid is set (has been assigned a value) and false otherwise */
  public boolean isSetSubSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SUBSID_ISSET_ID);
  }

  public void setSubSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SUBSID_ISSET_ID, value);
  }

  public int getChannelType() {
    return this.channelType;
  }

  public ChannelInfo setChannelType(int channelType) {
    this.channelType = channelType;
    setChannelTypeIsSet(true);
    return this;
  }

  public void unsetChannelType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CHANNELTYPE_ISSET_ID);
  }

  /** Returns true if field channelType is set (has been assigned a value) and false otherwise */
  public boolean isSetChannelType() {
    return EncodingUtils.testBit(__isset_bitfield, __CHANNELTYPE_ISSET_ID);
  }

  public void setChannelTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CHANNELTYPE_ISSET_ID, value);
  }

  public int getPluginType() {
    return this.pluginType;
  }

  public ChannelInfo setPluginType(int pluginType) {
    this.pluginType = pluginType;
    setPluginTypeIsSet(true);
    return this;
  }

  public void unsetPluginType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PLUGINTYPE_ISSET_ID);
  }

  /** Returns true if field pluginType is set (has been assigned a value) and false otherwise */
  public boolean isSetPluginType() {
    return EncodingUtils.testBit(__isset_bitfield, __PLUGINTYPE_ISSET_ID);
  }

  public void setPluginTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PLUGINTYPE_ISSET_ID, value);
  }

  public int getTemplateId() {
    return this.templateId;
  }

  public ChannelInfo setTemplateId(int templateId) {
    this.templateId = templateId;
    setTemplateIdIsSet(true);
    return this;
  }

  public void unsetTemplateId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TEMPLATEID_ISSET_ID);
  }

  /** Returns true if field templateId is set (has been assigned a value) and false otherwise */
  public boolean isSetTemplateId() {
    return EncodingUtils.testBit(__isset_bitfield, __TEMPLATEID_ISSET_ID);
  }

  public void setTemplateIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TEMPLATEID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case TOP_SID:
      if (value == null) {
        unsetTopSid();
      } else {
        setTopSid((Long)value);
      }
      break;

    case SUB_SID:
      if (value == null) {
        unsetSubSid();
      } else {
        setSubSid((Long)value);
      }
      break;

    case CHANNEL_TYPE:
      if (value == null) {
        unsetChannelType();
      } else {
        setChannelType((Integer)value);
      }
      break;

    case PLUGIN_TYPE:
      if (value == null) {
        unsetPluginType();
      } else {
        setPluginType((Integer)value);
      }
      break;

    case TEMPLATE_ID:
      if (value == null) {
        unsetTemplateId();
      } else {
        setTemplateId((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case TOP_SID:
      return getTopSid();

    case SUB_SID:
      return getSubSid();

    case CHANNEL_TYPE:
      return getChannelType();

    case PLUGIN_TYPE:
      return getPluginType();

    case TEMPLATE_ID:
      return getTemplateId();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case TOP_SID:
      return isSetTopSid();
    case SUB_SID:
      return isSetSubSid();
    case CHANNEL_TYPE:
      return isSetChannelType();
    case PLUGIN_TYPE:
      return isSetPluginType();
    case TEMPLATE_ID:
      return isSetTemplateId();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ChannelInfo)
      return this.equals((ChannelInfo)that);
    return false;
  }

  public boolean equals(ChannelInfo that) {
    if (that == null)
      return false;

    boolean this_present_topSid = true;
    boolean that_present_topSid = true;
    if (this_present_topSid || that_present_topSid) {
      if (!(this_present_topSid && that_present_topSid))
        return false;
      if (this.topSid != that.topSid)
        return false;
    }

    boolean this_present_subSid = true;
    boolean that_present_subSid = true;
    if (this_present_subSid || that_present_subSid) {
      if (!(this_present_subSid && that_present_subSid))
        return false;
      if (this.subSid != that.subSid)
        return false;
    }

    boolean this_present_channelType = true;
    boolean that_present_channelType = true;
    if (this_present_channelType || that_present_channelType) {
      if (!(this_present_channelType && that_present_channelType))
        return false;
      if (this.channelType != that.channelType)
        return false;
    }

    boolean this_present_pluginType = true;
    boolean that_present_pluginType = true;
    if (this_present_pluginType || that_present_pluginType) {
      if (!(this_present_pluginType && that_present_pluginType))
        return false;
      if (this.pluginType != that.pluginType)
        return false;
    }

    boolean this_present_templateId = true;
    boolean that_present_templateId = true;
    if (this_present_templateId || that_present_templateId) {
      if (!(this_present_templateId && that_present_templateId))
        return false;
      if (this.templateId != that.templateId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_topSid = true;
    list.add(present_topSid);
    if (present_topSid)
      list.add(topSid);

    boolean present_subSid = true;
    list.add(present_subSid);
    if (present_subSid)
      list.add(subSid);

    boolean present_channelType = true;
    list.add(present_channelType);
    if (present_channelType)
      list.add(channelType);

    boolean present_pluginType = true;
    list.add(present_pluginType);
    if (present_pluginType)
      list.add(pluginType);

    boolean present_templateId = true;
    list.add(present_templateId);
    if (present_templateId)
      list.add(templateId);

    return list.hashCode();
  }

  @Override
  public int compareTo(ChannelInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetTopSid()).compareTo(other.isSetTopSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTopSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.topSid, other.topSid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSubSid()).compareTo(other.isSetSubSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSubSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.subSid, other.subSid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetChannelType()).compareTo(other.isSetChannelType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetChannelType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.channelType, other.channelType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPluginType()).compareTo(other.isSetPluginType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPluginType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pluginType, other.pluginType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTemplateId()).compareTo(other.isSetTemplateId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTemplateId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.templateId, other.templateId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ChannelInfo(");
    boolean first = true;

    sb.append("topSid:");
    sb.append(this.topSid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("subSid:");
    sb.append(this.subSid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("channelType:");
    sb.append(this.channelType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("pluginType:");
    sb.append(this.pluginType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("templateId:");
    sb.append(this.templateId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ChannelInfoStandardSchemeFactory implements SchemeFactory {
    public ChannelInfoStandardScheme getScheme() {
      return new ChannelInfoStandardScheme();
    }
  }

  private static class ChannelInfoStandardScheme extends StandardScheme<ChannelInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ChannelInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TOP_SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.topSid = iprot.readI64();
              struct.setTopSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SUB_SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.subSid = iprot.readI64();
              struct.setSubSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // CHANNEL_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.channelType = iprot.readI32();
              struct.setChannelTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PLUGIN_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pluginType = iprot.readI32();
              struct.setPluginTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // TEMPLATE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.templateId = iprot.readI32();
              struct.setTemplateIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ChannelInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(TOP_SID_FIELD_DESC);
      oprot.writeI64(struct.topSid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SUB_SID_FIELD_DESC);
      oprot.writeI64(struct.subSid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CHANNEL_TYPE_FIELD_DESC);
      oprot.writeI32(struct.channelType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PLUGIN_TYPE_FIELD_DESC);
      oprot.writeI32(struct.pluginType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TEMPLATE_ID_FIELD_DESC);
      oprot.writeI32(struct.templateId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ChannelInfoTupleSchemeFactory implements SchemeFactory {
    public ChannelInfoTupleScheme getScheme() {
      return new ChannelInfoTupleScheme();
    }
  }

  private static class ChannelInfoTupleScheme extends TupleScheme<ChannelInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ChannelInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetTopSid()) {
        optionals.set(0);
      }
      if (struct.isSetSubSid()) {
        optionals.set(1);
      }
      if (struct.isSetChannelType()) {
        optionals.set(2);
      }
      if (struct.isSetPluginType()) {
        optionals.set(3);
      }
      if (struct.isSetTemplateId()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetTopSid()) {
        oprot.writeI64(struct.topSid);
      }
      if (struct.isSetSubSid()) {
        oprot.writeI64(struct.subSid);
      }
      if (struct.isSetChannelType()) {
        oprot.writeI32(struct.channelType);
      }
      if (struct.isSetPluginType()) {
        oprot.writeI32(struct.pluginType);
      }
      if (struct.isSetTemplateId()) {
        oprot.writeI32(struct.templateId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ChannelInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.topSid = iprot.readI64();
        struct.setTopSidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.subSid = iprot.readI64();
        struct.setSubSidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.channelType = iprot.readI32();
        struct.setChannelTypeIsSet(true);
      }
      if (incoming.get(3)) {
        struct.pluginType = iprot.readI32();
        struct.setPluginTypeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.templateId = iprot.readI32();
        struct.setTemplateIdIsSet(true);
      }
    }
  }

}

