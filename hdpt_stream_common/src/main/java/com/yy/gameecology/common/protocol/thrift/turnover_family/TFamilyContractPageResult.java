/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.turnover_family;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-09-09")
public class TFamilyContractPageResult implements org.apache.thrift.TBase<TFamilyContractPageResult, TFamilyContractPageResult._Fields>, java.io.Serializable, Cloneable, Comparable<TFamilyContractPageResult> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TFamilyContractPageResult");

  private static final org.apache.thrift.protocol.TField PAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("page", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField PAGESIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pagesize", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField TOTAL_FIELD_DESC = new org.apache.thrift.protocol.TField("total", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField TOTAL_PAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("totalPage", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField CONTENTS_FIELD_DESC = new org.apache.thrift.protocol.TField("contents", org.apache.thrift.protocol.TType.LIST, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TFamilyContractPageResultStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TFamilyContractPageResultTupleSchemeFactory());
  }

  public int page; // required
  public int pagesize; // required
  public int total; // required
  public int totalPage; // required
  public List<TFamilyContract> contents; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    PAGE((short)1, "page"),
    PAGESIZE((short)2, "pagesize"),
    TOTAL((short)3, "total"),
    TOTAL_PAGE((short)4, "totalPage"),
    CONTENTS((short)5, "contents");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // PAGE
          return PAGE;
        case 2: // PAGESIZE
          return PAGESIZE;
        case 3: // TOTAL
          return TOTAL;
        case 4: // TOTAL_PAGE
          return TOTAL_PAGE;
        case 5: // CONTENTS
          return CONTENTS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __PAGE_ISSET_ID = 0;
  private static final int __PAGESIZE_ISSET_ID = 1;
  private static final int __TOTAL_ISSET_ID = 2;
  private static final int __TOTALPAGE_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.PAGE, new org.apache.thrift.meta_data.FieldMetaData("page", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PAGESIZE, new org.apache.thrift.meta_data.FieldMetaData("pagesize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TOTAL, new org.apache.thrift.meta_data.FieldMetaData("total", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TOTAL_PAGE, new org.apache.thrift.meta_data.FieldMetaData("totalPage", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.CONTENTS, new org.apache.thrift.meta_data.FieldMetaData("contents", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, TFamilyContract.class))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TFamilyContractPageResult.class, metaDataMap);
  }

  public TFamilyContractPageResult() {
  }

  public TFamilyContractPageResult(
    int page,
    int pagesize,
    int total,
    int totalPage,
    List<TFamilyContract> contents)
  {
    this();
    this.page = page;
    setPageIsSet(true);
    this.pagesize = pagesize;
    setPagesizeIsSet(true);
    this.total = total;
    setTotalIsSet(true);
    this.totalPage = totalPage;
    setTotalPageIsSet(true);
    this.contents = contents;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TFamilyContractPageResult(TFamilyContractPageResult other) {
    __isset_bitfield = other.__isset_bitfield;
    this.page = other.page;
    this.pagesize = other.pagesize;
    this.total = other.total;
    this.totalPage = other.totalPage;
    if (other.isSetContents()) {
      List<TFamilyContract> __this__contents = new ArrayList<TFamilyContract>(other.contents.size());
      for (TFamilyContract other_element : other.contents) {
        __this__contents.add(new TFamilyContract(other_element));
      }
      this.contents = __this__contents;
    }
  }

  public TFamilyContractPageResult deepCopy() {
    return new TFamilyContractPageResult(this);
  }

  @Override
  public void clear() {
    setPageIsSet(false);
    this.page = 0;
    setPagesizeIsSet(false);
    this.pagesize = 0;
    setTotalIsSet(false);
    this.total = 0;
    setTotalPageIsSet(false);
    this.totalPage = 0;
    this.contents = null;
  }

  public int getPage() {
    return this.page;
  }

  public TFamilyContractPageResult setPage(int page) {
    this.page = page;
    setPageIsSet(true);
    return this;
  }

  public void unsetPage() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGE_ISSET_ID);
  }

  /** Returns true if field page is set (has been assigned a value) and false otherwise */
  public boolean isSetPage() {
    return EncodingUtils.testBit(__isset_bitfield, __PAGE_ISSET_ID);
  }

  public void setPageIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGE_ISSET_ID, value);
  }

  public int getPagesize() {
    return this.pagesize;
  }

  public TFamilyContractPageResult setPagesize(int pagesize) {
    this.pagesize = pagesize;
    setPagesizeIsSet(true);
    return this;
  }

  public void unsetPagesize() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
  }

  /** Returns true if field pagesize is set (has been assigned a value) and false otherwise */
  public boolean isSetPagesize() {
    return EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
  }

  public void setPagesizeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
  }

  public int getTotal() {
    return this.total;
  }

  public TFamilyContractPageResult setTotal(int total) {
    this.total = total;
    setTotalIsSet(true);
    return this;
  }

  public void unsetTotal() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOTAL_ISSET_ID);
  }

  /** Returns true if field total is set (has been assigned a value) and false otherwise */
  public boolean isSetTotal() {
    return EncodingUtils.testBit(__isset_bitfield, __TOTAL_ISSET_ID);
  }

  public void setTotalIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOTAL_ISSET_ID, value);
  }

  public int getTotalPage() {
    return this.totalPage;
  }

  public TFamilyContractPageResult setTotalPage(int totalPage) {
    this.totalPage = totalPage;
    setTotalPageIsSet(true);
    return this;
  }

  public void unsetTotalPage() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOTALPAGE_ISSET_ID);
  }

  /** Returns true if field totalPage is set (has been assigned a value) and false otherwise */
  public boolean isSetTotalPage() {
    return EncodingUtils.testBit(__isset_bitfield, __TOTALPAGE_ISSET_ID);
  }

  public void setTotalPageIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOTALPAGE_ISSET_ID, value);
  }

  public int getContentsSize() {
    return (this.contents == null) ? 0 : this.contents.size();
  }

  public java.util.Iterator<TFamilyContract> getContentsIterator() {
    return (this.contents == null) ? null : this.contents.iterator();
  }

  public void addToContents(TFamilyContract elem) {
    if (this.contents == null) {
      this.contents = new ArrayList<TFamilyContract>();
    }
    this.contents.add(elem);
  }

  public List<TFamilyContract> getContents() {
    return this.contents;
  }

  public TFamilyContractPageResult setContents(List<TFamilyContract> contents) {
    this.contents = contents;
    return this;
  }

  public void unsetContents() {
    this.contents = null;
  }

  /** Returns true if field contents is set (has been assigned a value) and false otherwise */
  public boolean isSetContents() {
    return this.contents != null;
  }

  public void setContentsIsSet(boolean value) {
    if (!value) {
      this.contents = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case PAGE:
      if (value == null) {
        unsetPage();
      } else {
        setPage((Integer)value);
      }
      break;

    case PAGESIZE:
      if (value == null) {
        unsetPagesize();
      } else {
        setPagesize((Integer)value);
      }
      break;

    case TOTAL:
      if (value == null) {
        unsetTotal();
      } else {
        setTotal((Integer)value);
      }
      break;

    case TOTAL_PAGE:
      if (value == null) {
        unsetTotalPage();
      } else {
        setTotalPage((Integer)value);
      }
      break;

    case CONTENTS:
      if (value == null) {
        unsetContents();
      } else {
        setContents((List<TFamilyContract>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case PAGE:
      return getPage();

    case PAGESIZE:
      return getPagesize();

    case TOTAL:
      return getTotal();

    case TOTAL_PAGE:
      return getTotalPage();

    case CONTENTS:
      return getContents();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case PAGE:
      return isSetPage();
    case PAGESIZE:
      return isSetPagesize();
    case TOTAL:
      return isSetTotal();
    case TOTAL_PAGE:
      return isSetTotalPage();
    case CONTENTS:
      return isSetContents();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TFamilyContractPageResult)
      return this.equals((TFamilyContractPageResult)that);
    return false;
  }

  public boolean equals(TFamilyContractPageResult that) {
    if (that == null)
      return false;

    boolean this_present_page = true;
    boolean that_present_page = true;
    if (this_present_page || that_present_page) {
      if (!(this_present_page && that_present_page))
        return false;
      if (this.page != that.page)
        return false;
    }

    boolean this_present_pagesize = true;
    boolean that_present_pagesize = true;
    if (this_present_pagesize || that_present_pagesize) {
      if (!(this_present_pagesize && that_present_pagesize))
        return false;
      if (this.pagesize != that.pagesize)
        return false;
    }

    boolean this_present_total = true;
    boolean that_present_total = true;
    if (this_present_total || that_present_total) {
      if (!(this_present_total && that_present_total))
        return false;
      if (this.total != that.total)
        return false;
    }

    boolean this_present_totalPage = true;
    boolean that_present_totalPage = true;
    if (this_present_totalPage || that_present_totalPage) {
      if (!(this_present_totalPage && that_present_totalPage))
        return false;
      if (this.totalPage != that.totalPage)
        return false;
    }

    boolean this_present_contents = true && this.isSetContents();
    boolean that_present_contents = true && that.isSetContents();
    if (this_present_contents || that_present_contents) {
      if (!(this_present_contents && that_present_contents))
        return false;
      if (!this.contents.equals(that.contents))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_page = true;
    list.add(present_page);
    if (present_page)
      list.add(page);

    boolean present_pagesize = true;
    list.add(present_pagesize);
    if (present_pagesize)
      list.add(pagesize);

    boolean present_total = true;
    list.add(present_total);
    if (present_total)
      list.add(total);

    boolean present_totalPage = true;
    list.add(present_totalPage);
    if (present_totalPage)
      list.add(totalPage);

    boolean present_contents = true && (isSetContents());
    list.add(present_contents);
    if (present_contents)
      list.add(contents);

    return list.hashCode();
  }

  @Override
  public int compareTo(TFamilyContractPageResult other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetPage()).compareTo(other.isSetPage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.page, other.page);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPagesize()).compareTo(other.isSetPagesize());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPagesize()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pagesize, other.pagesize);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTotal()).compareTo(other.isSetTotal());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTotal()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.total, other.total);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTotalPage()).compareTo(other.isSetTotalPage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTotalPage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.totalPage, other.totalPage);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetContents()).compareTo(other.isSetContents());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetContents()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.contents, other.contents);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TFamilyContractPageResult(");
    boolean first = true;

    sb.append("page:");
    sb.append(this.page);
    first = false;
    if (!first) sb.append(", ");
    sb.append("pagesize:");
    sb.append(this.pagesize);
    first = false;
    if (!first) sb.append(", ");
    sb.append("total:");
    sb.append(this.total);
    first = false;
    if (!first) sb.append(", ");
    sb.append("totalPage:");
    sb.append(this.totalPage);
    first = false;
    if (!first) sb.append(", ");
    sb.append("contents:");
    if (this.contents == null) {
      sb.append("null");
    } else {
      sb.append(this.contents);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TFamilyContractPageResultStandardSchemeFactory implements SchemeFactory {
    public TFamilyContractPageResultStandardScheme getScheme() {
      return new TFamilyContractPageResultStandardScheme();
    }
  }

  private static class TFamilyContractPageResultStandardScheme extends StandardScheme<TFamilyContractPageResult> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TFamilyContractPageResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // PAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.page = iprot.readI32();
              struct.setPageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // PAGESIZE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pagesize = iprot.readI32();
              struct.setPagesizeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // TOTAL
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.total = iprot.readI32();
              struct.setTotalIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TOTAL_PAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.totalPage = iprot.readI32();
              struct.setTotalPageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // CONTENTS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list8 = iprot.readListBegin();
                struct.contents = new ArrayList<TFamilyContract>(_list8.size);
                TFamilyContract _elem9;
                for (int _i10 = 0; _i10 < _list8.size; ++_i10)
                {
                  _elem9 = new TFamilyContract();
                  _elem9.read(iprot);
                  struct.contents.add(_elem9);
                }
                iprot.readListEnd();
              }
              struct.setContentsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TFamilyContractPageResult struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(PAGE_FIELD_DESC);
      oprot.writeI32(struct.page);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PAGESIZE_FIELD_DESC);
      oprot.writeI32(struct.pagesize);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TOTAL_FIELD_DESC);
      oprot.writeI32(struct.total);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TOTAL_PAGE_FIELD_DESC);
      oprot.writeI32(struct.totalPage);
      oprot.writeFieldEnd();
      if (struct.contents != null) {
        oprot.writeFieldBegin(CONTENTS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.contents.size()));
          for (TFamilyContract _iter11 : struct.contents)
          {
            _iter11.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TFamilyContractPageResultTupleSchemeFactory implements SchemeFactory {
    public TFamilyContractPageResultTupleScheme getScheme() {
      return new TFamilyContractPageResultTupleScheme();
    }
  }

  private static class TFamilyContractPageResultTupleScheme extends TupleScheme<TFamilyContractPageResult> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TFamilyContractPageResult struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetPage()) {
        optionals.set(0);
      }
      if (struct.isSetPagesize()) {
        optionals.set(1);
      }
      if (struct.isSetTotal()) {
        optionals.set(2);
      }
      if (struct.isSetTotalPage()) {
        optionals.set(3);
      }
      if (struct.isSetContents()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetPage()) {
        oprot.writeI32(struct.page);
      }
      if (struct.isSetPagesize()) {
        oprot.writeI32(struct.pagesize);
      }
      if (struct.isSetTotal()) {
        oprot.writeI32(struct.total);
      }
      if (struct.isSetTotalPage()) {
        oprot.writeI32(struct.totalPage);
      }
      if (struct.isSetContents()) {
        {
          oprot.writeI32(struct.contents.size());
          for (TFamilyContract _iter12 : struct.contents)
          {
            _iter12.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TFamilyContractPageResult struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.page = iprot.readI32();
        struct.setPageIsSet(true);
      }
      if (incoming.get(1)) {
        struct.pagesize = iprot.readI32();
        struct.setPagesizeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.total = iprot.readI32();
        struct.setTotalIsSet(true);
      }
      if (incoming.get(3)) {
        struct.totalPage = iprot.readI32();
        struct.setTotalPageIsSet(true);
      }
      if (incoming.get(4)) {
        {
          org.apache.thrift.protocol.TList _list13 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.contents = new ArrayList<TFamilyContract>(_list13.size);
          TFamilyContract _elem14;
          for (int _i15 = 0; _i15 < _list13.size; ++_i15)
          {
            _elem14 = new TFamilyContract();
            _elem14.read(iprot);
            struct.contents.add(_elem14);
          }
        }
        struct.setContentsIsSet(true);
      }
    }
  }

}

