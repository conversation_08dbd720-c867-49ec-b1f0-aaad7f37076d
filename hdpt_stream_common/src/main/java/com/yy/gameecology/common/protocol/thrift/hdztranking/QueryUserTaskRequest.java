/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class QueryUserTaskRequest implements org.apache.thrift.TBase<QueryUserTaskRequest, QueryUserTaskRequest._Fields>, java.io.Serializable, Cloneable, Comparable<QueryUserTaskRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryUserTaskRequest");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RANK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rankId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField MEMBER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("memberId", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField DATE_STR_FIELD_DESC = new org.apache.thrift.protocol.TField("dateStr", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryUserTaskRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryUserTaskRequestTupleSchemeFactory());
  }

  public long actId; // required
  public long rankId; // required
  public String memberId; // required
  public long phaseId; // required
  public String dateStr; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    RANK_ID((short)2, "rankId"),
    MEMBER_ID((short)3, "memberId"),
    PHASE_ID((short)4, "phaseId"),
    DATE_STR((short)5, "dateStr"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // RANK_ID
          return RANK_ID;
        case 3: // MEMBER_ID
          return MEMBER_ID;
        case 4: // PHASE_ID
          return PHASE_ID;
        case 5: // DATE_STR
          return DATE_STR;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __RANKID_ISSET_ID = 1;
  private static final int __PHASEID_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANK_ID, new org.apache.thrift.meta_data.FieldMetaData("rankId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MEMBER_ID, new org.apache.thrift.meta_data.FieldMetaData("memberId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DATE_STR, new org.apache.thrift.meta_data.FieldMetaData("dateStr", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryUserTaskRequest.class, metaDataMap);
  }

  public QueryUserTaskRequest() {
  }

  public QueryUserTaskRequest(
    long actId,
    long rankId,
    String memberId,
    long phaseId,
    String dateStr,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.rankId = rankId;
    setRankIdIsSet(true);
    this.memberId = memberId;
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    this.dateStr = dateStr;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryUserTaskRequest(QueryUserTaskRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    this.rankId = other.rankId;
    if (other.isSetMemberId()) {
      this.memberId = other.memberId;
    }
    this.phaseId = other.phaseId;
    if (other.isSetDateStr()) {
      this.dateStr = other.dateStr;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public QueryUserTaskRequest deepCopy() {
    return new QueryUserTaskRequest(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    setRankIdIsSet(false);
    this.rankId = 0;
    this.memberId = null;
    setPhaseIdIsSet(false);
    this.phaseId = 0;
    this.dateStr = null;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public QueryUserTaskRequest setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getRankId() {
    return this.rankId;
  }

  public QueryUserTaskRequest setRankId(long rankId) {
    this.rankId = rankId;
    setRankIdIsSet(true);
    return this;
  }

  public void unsetRankId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  /** Returns true if field rankId is set (has been assigned a value) and false otherwise */
  public boolean isSetRankId() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  public void setRankIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKID_ISSET_ID, value);
  }

  public String getMemberId() {
    return this.memberId;
  }

  public QueryUserTaskRequest setMemberId(String memberId) {
    this.memberId = memberId;
    return this;
  }

  public void unsetMemberId() {
    this.memberId = null;
  }

  /** Returns true if field memberId is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberId() {
    return this.memberId != null;
  }

  public void setMemberIdIsSet(boolean value) {
    if (!value) {
      this.memberId = null;
    }
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public QueryUserTaskRequest setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public String getDateStr() {
    return this.dateStr;
  }

  public QueryUserTaskRequest setDateStr(String dateStr) {
    this.dateStr = dateStr;
    return this;
  }

  public void unsetDateStr() {
    this.dateStr = null;
  }

  /** Returns true if field dateStr is set (has been assigned a value) and false otherwise */
  public boolean isSetDateStr() {
    return this.dateStr != null;
  }

  public void setDateStrIsSet(boolean value) {
    if (!value) {
      this.dateStr = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public QueryUserTaskRequest setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case RANK_ID:
      if (value == null) {
        unsetRankId();
      } else {
        setRankId((Long)value);
      }
      break;

    case MEMBER_ID:
      if (value == null) {
        unsetMemberId();
      } else {
        setMemberId((String)value);
      }
      break;

    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    case DATE_STR:
      if (value == null) {
        unsetDateStr();
      } else {
        setDateStr((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case RANK_ID:
      return getRankId();

    case MEMBER_ID:
      return getMemberId();

    case PHASE_ID:
      return getPhaseId();

    case DATE_STR:
      return getDateStr();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case RANK_ID:
      return isSetRankId();
    case MEMBER_ID:
      return isSetMemberId();
    case PHASE_ID:
      return isSetPhaseId();
    case DATE_STR:
      return isSetDateStr();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryUserTaskRequest)
      return this.equals((QueryUserTaskRequest)that);
    return false;
  }

  public boolean equals(QueryUserTaskRequest that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_rankId = true;
    boolean that_present_rankId = true;
    if (this_present_rankId || that_present_rankId) {
      if (!(this_present_rankId && that_present_rankId))
        return false;
      if (this.rankId != that.rankId)
        return false;
    }

    boolean this_present_memberId = true && this.isSetMemberId();
    boolean that_present_memberId = true && that.isSetMemberId();
    if (this_present_memberId || that_present_memberId) {
      if (!(this_present_memberId && that_present_memberId))
        return false;
      if (!this.memberId.equals(that.memberId))
        return false;
    }

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    boolean this_present_dateStr = true && this.isSetDateStr();
    boolean that_present_dateStr = true && that.isSetDateStr();
    if (this_present_dateStr || that_present_dateStr) {
      if (!(this_present_dateStr && that_present_dateStr))
        return false;
      if (!this.dateStr.equals(that.dateStr))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_rankId = true;
    list.add(present_rankId);
    if (present_rankId)
      list.add(rankId);

    boolean present_memberId = true && (isSetMemberId());
    list.add(present_memberId);
    if (present_memberId)
      list.add(memberId);

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    boolean present_dateStr = true && (isSetDateStr());
    list.add(present_dateStr);
    if (present_dateStr)
      list.add(dateStr);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryUserTaskRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankId()).compareTo(other.isSetRankId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankId, other.rankId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMemberId()).compareTo(other.isSetMemberId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberId, other.memberId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDateStr()).compareTo(other.isSetDateStr());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDateStr()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dateStr, other.dateStr);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryUserTaskRequest(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankId:");
    sb.append(this.rankId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("memberId:");
    if (this.memberId == null) {
      sb.append("null");
    } else {
      sb.append(this.memberId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("dateStr:");
    if (this.dateStr == null) {
      sb.append("null");
    } else {
      sb.append(this.dateStr);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryUserTaskRequestStandardSchemeFactory implements SchemeFactory {
    public QueryUserTaskRequestStandardScheme getScheme() {
      return new QueryUserTaskRequestStandardScheme();
    }
  }

  private static class QueryUserTaskRequestStandardScheme extends StandardScheme<QueryUserTaskRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryUserTaskRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RANK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankId = iprot.readI64();
              struct.setRankIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // MEMBER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.memberId = iprot.readString();
              struct.setMemberIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // DATE_STR
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.dateStr = iprot.readString();
              struct.setDateStrIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map462 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map462.size);
                String _key463;
                String _val464;
                for (int _i465 = 0; _i465 < _map462.size; ++_i465)
                {
                  _key463 = iprot.readString();
                  _val464 = iprot.readString();
                  struct.extData.put(_key463, _val464);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryUserTaskRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANK_ID_FIELD_DESC);
      oprot.writeI64(struct.rankId);
      oprot.writeFieldEnd();
      if (struct.memberId != null) {
        oprot.writeFieldBegin(MEMBER_ID_FIELD_DESC);
        oprot.writeString(struct.memberId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      if (struct.dateStr != null) {
        oprot.writeFieldBegin(DATE_STR_FIELD_DESC);
        oprot.writeString(struct.dateStr);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter466 : struct.extData.entrySet())
          {
            oprot.writeString(_iter466.getKey());
            oprot.writeString(_iter466.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryUserTaskRequestTupleSchemeFactory implements SchemeFactory {
    public QueryUserTaskRequestTupleScheme getScheme() {
      return new QueryUserTaskRequestTupleScheme();
    }
  }

  private static class QueryUserTaskRequestTupleScheme extends TupleScheme<QueryUserTaskRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryUserTaskRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetRankId()) {
        optionals.set(1);
      }
      if (struct.isSetMemberId()) {
        optionals.set(2);
      }
      if (struct.isSetPhaseId()) {
        optionals.set(3);
      }
      if (struct.isSetDateStr()) {
        optionals.set(4);
      }
      if (struct.isSetExtData()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetRankId()) {
        oprot.writeI64(struct.rankId);
      }
      if (struct.isSetMemberId()) {
        oprot.writeString(struct.memberId);
      }
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
      if (struct.isSetDateStr()) {
        oprot.writeString(struct.dateStr);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter467 : struct.extData.entrySet())
          {
            oprot.writeString(_iter467.getKey());
            oprot.writeString(_iter467.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryUserTaskRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.rankId = iprot.readI64();
        struct.setRankIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.memberId = iprot.readString();
        struct.setMemberIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
      if (incoming.get(4)) {
        struct.dateStr = iprot.readString();
        struct.setDateStrIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TMap _map468 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map468.size);
          String _key469;
          String _val470;
          for (int _i471 = 0; _i471 < _map468.size; ++_i471)
          {
            _key469 = iprot.readString();
            _val470 = iprot.readString();
            struct.extData.put(_key469, _val470);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

