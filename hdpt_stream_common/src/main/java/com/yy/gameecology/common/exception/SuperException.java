
package com.yy.gameecology.common.exception;

/**
 * Title:
 * Description:
 * Create Time: 2014-02-25 上午10:35
 * author: wangyan
 * version: 1.0
 */
@SuppressWarnings("serial")
public class SuperException extends RuntimeException {
    public static final int E_UNKNOWN = 99999; // 未知错误（因为YY框架不支持负数编码，只能如此）

    public static final int E_FAIL_GEN_ID = 99998; // 标识生成失败

    public static final int E_WRONG_MAC = 99997; // 签名不正确

    public static final int E_WRONG_TIMESTAMP = 99996; // 无效时间戳

    public static final int E_WRONG_PARAM = 99994; // 参数不正确

    public static final int E_DB_OPER = -2; // 数据库操作失败

    public static final int E_PARAM_NOTNULL = -3; // 参数不能为空

    public static final int E_DATA_ERROR = -4; // 数据错误

    public static final int E_IP_ALLOW = 99995; // 不在IP白名单中

    public static final int E_PARAM_ILLEGAL = 99994; // 参数非法

    public static final int E_CONF_ILLEGAL = 99993; // 系统配置出错

    public static final int E_FAIL = 8888; // 请求失败，用来提示交易错误信息

    protected int code = -1;

    public SuperException(String message, int code) {
        super(message);
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public static int getErrCode(Throwable e) {
        return (e instanceof SuperException) ? ((SuperException) e).getCode() : E_UNKNOWN;
    }

    public static String getErrMessage(Throwable e) {
        return (e instanceof SuperException) ? e.getMessage() : "E9999#网络错误,稍后重试";
    }
}
