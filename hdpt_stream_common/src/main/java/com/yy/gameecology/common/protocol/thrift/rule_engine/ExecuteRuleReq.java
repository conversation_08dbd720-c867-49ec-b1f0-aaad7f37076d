/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.rule_engine;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class ExecuteRuleReq implements org.apache.thrift.TBase<ExecuteRuleReq, ExecuteRuleReq._Fields>, java.io.Serializable, Cloneable, Comparable<ExecuteRuleReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ExecuteRuleReq");

  private static final org.apache.thrift.protocol.TField APP_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("appId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RULE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("ruleId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField PARAM_JSON_FIELD_DESC = new org.apache.thrift.protocol.TField("paramJSON", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.STRING, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ExecuteRuleReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ExecuteRuleReqTupleSchemeFactory());
  }

  public long appId; // required
  public long ruleId; // required
  public String paramJSON; // required
  public String expand; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    APP_ID((short)1, "appId"),
    RULE_ID((short)2, "ruleId"),
    PARAM_JSON((short)3, "paramJSON"),
    EXPAND((short)4, "expand");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // APP_ID
          return APP_ID;
        case 2: // RULE_ID
          return RULE_ID;
        case 3: // PARAM_JSON
          return PARAM_JSON;
        case 4: // EXPAND
          return EXPAND;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __APPID_ISSET_ID = 0;
  private static final int __RULEID_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.APP_ID, new org.apache.thrift.meta_data.FieldMetaData("appId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RULE_ID, new org.apache.thrift.meta_data.FieldMetaData("ruleId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PARAM_JSON, new org.apache.thrift.meta_data.FieldMetaData("paramJSON", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ExecuteRuleReq.class, metaDataMap);
  }

  public ExecuteRuleReq() {
  }

  public ExecuteRuleReq(
    long appId,
    long ruleId,
    String paramJSON,
    String expand)
  {
    this();
    this.appId = appId;
    setAppIdIsSet(true);
    this.ruleId = ruleId;
    setRuleIdIsSet(true);
    this.paramJSON = paramJSON;
    this.expand = expand;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ExecuteRuleReq(ExecuteRuleReq other) {
    __isset_bitfield = other.__isset_bitfield;
    this.appId = other.appId;
    this.ruleId = other.ruleId;
    if (other.isSetParamJSON()) {
      this.paramJSON = other.paramJSON;
    }
    if (other.isSetExpand()) {
      this.expand = other.expand;
    }
  }

  public ExecuteRuleReq deepCopy() {
    return new ExecuteRuleReq(this);
  }

  @Override
  public void clear() {
    setAppIdIsSet(false);
    this.appId = 0;
    setRuleIdIsSet(false);
    this.ruleId = 0;
    this.paramJSON = null;
    this.expand = null;
  }

  public long getAppId() {
    return this.appId;
  }

  public ExecuteRuleReq setAppId(long appId) {
    this.appId = appId;
    setAppIdIsSet(true);
    return this;
  }

  public void unsetAppId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  /** Returns true if field appId is set (has been assigned a value) and false otherwise */
  public boolean isSetAppId() {
    return EncodingUtils.testBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  public void setAppIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __APPID_ISSET_ID, value);
  }

  public long getRuleId() {
    return this.ruleId;
  }

  public ExecuteRuleReq setRuleId(long ruleId) {
    this.ruleId = ruleId;
    setRuleIdIsSet(true);
    return this;
  }

  public void unsetRuleId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RULEID_ISSET_ID);
  }

  /** Returns true if field ruleId is set (has been assigned a value) and false otherwise */
  public boolean isSetRuleId() {
    return EncodingUtils.testBit(__isset_bitfield, __RULEID_ISSET_ID);
  }

  public void setRuleIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RULEID_ISSET_ID, value);
  }

  public String getParamJSON() {
    return this.paramJSON;
  }

  public ExecuteRuleReq setParamJSON(String paramJSON) {
    this.paramJSON = paramJSON;
    return this;
  }

  public void unsetParamJSON() {
    this.paramJSON = null;
  }

  /** Returns true if field paramJSON is set (has been assigned a value) and false otherwise */
  public boolean isSetParamJSON() {
    return this.paramJSON != null;
  }

  public void setParamJSONIsSet(boolean value) {
    if (!value) {
      this.paramJSON = null;
    }
  }

  public String getExpand() {
    return this.expand;
  }

  public ExecuteRuleReq setExpand(String expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case APP_ID:
      if (value == null) {
        unsetAppId();
      } else {
        setAppId((Long)value);
      }
      break;

    case RULE_ID:
      if (value == null) {
        unsetRuleId();
      } else {
        setRuleId((Long)value);
      }
      break;

    case PARAM_JSON:
      if (value == null) {
        unsetParamJSON();
      } else {
        setParamJSON((String)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case APP_ID:
      return getAppId();

    case RULE_ID:
      return getRuleId();

    case PARAM_JSON:
      return getParamJSON();

    case EXPAND:
      return getExpand();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case APP_ID:
      return isSetAppId();
    case RULE_ID:
      return isSetRuleId();
    case PARAM_JSON:
      return isSetParamJSON();
    case EXPAND:
      return isSetExpand();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ExecuteRuleReq)
      return this.equals((ExecuteRuleReq)that);
    return false;
  }

  public boolean equals(ExecuteRuleReq that) {
    if (that == null)
      return false;

    boolean this_present_appId = true;
    boolean that_present_appId = true;
    if (this_present_appId || that_present_appId) {
      if (!(this_present_appId && that_present_appId))
        return false;
      if (this.appId != that.appId)
        return false;
    }

    boolean this_present_ruleId = true;
    boolean that_present_ruleId = true;
    if (this_present_ruleId || that_present_ruleId) {
      if (!(this_present_ruleId && that_present_ruleId))
        return false;
      if (this.ruleId != that.ruleId)
        return false;
    }

    boolean this_present_paramJSON = true && this.isSetParamJSON();
    boolean that_present_paramJSON = true && that.isSetParamJSON();
    if (this_present_paramJSON || that_present_paramJSON) {
      if (!(this_present_paramJSON && that_present_paramJSON))
        return false;
      if (!this.paramJSON.equals(that.paramJSON))
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_appId = true;
    list.add(present_appId);
    if (present_appId)
      list.add(appId);

    boolean present_ruleId = true;
    list.add(present_ruleId);
    if (present_ruleId)
      list.add(ruleId);

    boolean present_paramJSON = true && (isSetParamJSON());
    list.add(present_paramJSON);
    if (present_paramJSON)
      list.add(paramJSON);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    return list.hashCode();
  }

  @Override
  public int compareTo(ExecuteRuleReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAppId()).compareTo(other.isSetAppId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appId, other.appId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRuleId()).compareTo(other.isSetRuleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRuleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ruleId, other.ruleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetParamJSON()).compareTo(other.isSetParamJSON());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetParamJSON()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.paramJSON, other.paramJSON);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ExecuteRuleReq(");
    boolean first = true;

    sb.append("appId:");
    sb.append(this.appId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ruleId:");
    sb.append(this.ruleId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("paramJSON:");
    if (this.paramJSON == null) {
      sb.append("null");
    } else {
      sb.append(this.paramJSON);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ExecuteRuleReqStandardSchemeFactory implements SchemeFactory {
    public ExecuteRuleReqStandardScheme getScheme() {
      return new ExecuteRuleReqStandardScheme();
    }
  }

  private static class ExecuteRuleReqStandardScheme extends StandardScheme<ExecuteRuleReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ExecuteRuleReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // APP_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.appId = iprot.readI64();
              struct.setAppIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RULE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ruleId = iprot.readI64();
              struct.setRuleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PARAM_JSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.paramJSON = iprot.readString();
              struct.setParamJSONIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.expand = iprot.readString();
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ExecuteRuleReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(APP_ID_FIELD_DESC);
      oprot.writeI64(struct.appId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RULE_ID_FIELD_DESC);
      oprot.writeI64(struct.ruleId);
      oprot.writeFieldEnd();
      if (struct.paramJSON != null) {
        oprot.writeFieldBegin(PARAM_JSON_FIELD_DESC);
        oprot.writeString(struct.paramJSON);
        oprot.writeFieldEnd();
      }
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        oprot.writeString(struct.expand);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ExecuteRuleReqTupleSchemeFactory implements SchemeFactory {
    public ExecuteRuleReqTupleScheme getScheme() {
      return new ExecuteRuleReqTupleScheme();
    }
  }

  private static class ExecuteRuleReqTupleScheme extends TupleScheme<ExecuteRuleReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ExecuteRuleReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAppId()) {
        optionals.set(0);
      }
      if (struct.isSetRuleId()) {
        optionals.set(1);
      }
      if (struct.isSetParamJSON()) {
        optionals.set(2);
      }
      if (struct.isSetExpand()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetAppId()) {
        oprot.writeI64(struct.appId);
      }
      if (struct.isSetRuleId()) {
        oprot.writeI64(struct.ruleId);
      }
      if (struct.isSetParamJSON()) {
        oprot.writeString(struct.paramJSON);
      }
      if (struct.isSetExpand()) {
        oprot.writeString(struct.expand);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ExecuteRuleReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.appId = iprot.readI64();
        struct.setAppIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.ruleId = iprot.readI64();
        struct.setRuleIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.paramJSON = iprot.readString();
        struct.setParamJSONIsSet(true);
      }
      if (incoming.get(3)) {
        struct.expand = iprot.readString();
        struct.setExpandIsSet(true);
      }
    }
  }

}

