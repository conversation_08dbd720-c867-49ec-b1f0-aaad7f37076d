/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_price;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-02-22")
public class ZhuiWanComponentCommonReq implements org.apache.thrift.TBase<ZhuiWanComponentCommonReq, ZhuiWanComponentCommonReq._Fields>, java.io.Serializable, Cloneable, Comparable<ZhuiWanComponentCommonReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ZhuiWanComponentCommonReq");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField LOGIN_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("loginUid", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField CLIENT_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("clientType", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField HDID_FIELD_DESC = new org.apache.thrift.protocol.TField("hdid", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)6);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ZhuiWanComponentCommonReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ZhuiWanComponentCommonReqTupleSchemeFactory());
  }

  public long actId; // required
  public long loginUid; // required
  public long sid; // required
  public int clientType; // required
  public String hdid; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    LOGIN_UID((short)2, "loginUid"),
    SID((short)3, "sid"),
    CLIENT_TYPE((short)4, "clientType"),
    HDID((short)5, "hdid"),
    EXT_DATA((short)6, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // LOGIN_UID
          return LOGIN_UID;
        case 3: // SID
          return SID;
        case 4: // CLIENT_TYPE
          return CLIENT_TYPE;
        case 5: // HDID
          return HDID;
        case 6: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __LOGINUID_ISSET_ID = 1;
  private static final int __SID_ISSET_ID = 2;
  private static final int __CLIENTTYPE_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.LOGIN_UID, new org.apache.thrift.meta_data.FieldMetaData("loginUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CLIENT_TYPE, new org.apache.thrift.meta_data.FieldMetaData("clientType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.HDID, new org.apache.thrift.meta_data.FieldMetaData("hdid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ZhuiWanComponentCommonReq.class, metaDataMap);
  }

  public ZhuiWanComponentCommonReq() {
  }

  public ZhuiWanComponentCommonReq(
    long actId,
    long loginUid,
    long sid,
    int clientType,
    String hdid,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.loginUid = loginUid;
    setLoginUidIsSet(true);
    this.sid = sid;
    setSidIsSet(true);
    this.clientType = clientType;
    setClientTypeIsSet(true);
    this.hdid = hdid;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ZhuiWanComponentCommonReq(ZhuiWanComponentCommonReq other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    this.loginUid = other.loginUid;
    this.sid = other.sid;
    this.clientType = other.clientType;
    if (other.isSetHdid()) {
      this.hdid = other.hdid;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public ZhuiWanComponentCommonReq deepCopy() {
    return new ZhuiWanComponentCommonReq(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    setLoginUidIsSet(false);
    this.loginUid = 0;
    setSidIsSet(false);
    this.sid = 0;
    setClientTypeIsSet(false);
    this.clientType = 0;
    this.hdid = null;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public ZhuiWanComponentCommonReq setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getLoginUid() {
    return this.loginUid;
  }

  public ZhuiWanComponentCommonReq setLoginUid(long loginUid) {
    this.loginUid = loginUid;
    setLoginUidIsSet(true);
    return this;
  }

  public void unsetLoginUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LOGINUID_ISSET_ID);
  }

  /** Returns true if field loginUid is set (has been assigned a value) and false otherwise */
  public boolean isSetLoginUid() {
    return EncodingUtils.testBit(__isset_bitfield, __LOGINUID_ISSET_ID);
  }

  public void setLoginUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LOGINUID_ISSET_ID, value);
  }

  public long getSid() {
    return this.sid;
  }

  public ZhuiWanComponentCommonReq setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public int getClientType() {
    return this.clientType;
  }

  public ZhuiWanComponentCommonReq setClientType(int clientType) {
    this.clientType = clientType;
    setClientTypeIsSet(true);
    return this;
  }

  public void unsetClientType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CLIENTTYPE_ISSET_ID);
  }

  /** Returns true if field clientType is set (has been assigned a value) and false otherwise */
  public boolean isSetClientType() {
    return EncodingUtils.testBit(__isset_bitfield, __CLIENTTYPE_ISSET_ID);
  }

  public void setClientTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CLIENTTYPE_ISSET_ID, value);
  }

  public String getHdid() {
    return this.hdid;
  }

  public ZhuiWanComponentCommonReq setHdid(String hdid) {
    this.hdid = hdid;
    return this;
  }

  public void unsetHdid() {
    this.hdid = null;
  }

  /** Returns true if field hdid is set (has been assigned a value) and false otherwise */
  public boolean isSetHdid() {
    return this.hdid != null;
  }

  public void setHdidIsSet(boolean value) {
    if (!value) {
      this.hdid = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public ZhuiWanComponentCommonReq setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case LOGIN_UID:
      if (value == null) {
        unsetLoginUid();
      } else {
        setLoginUid((Long)value);
      }
      break;

    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case CLIENT_TYPE:
      if (value == null) {
        unsetClientType();
      } else {
        setClientType((Integer)value);
      }
      break;

    case HDID:
      if (value == null) {
        unsetHdid();
      } else {
        setHdid((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case LOGIN_UID:
      return getLoginUid();

    case SID:
      return getSid();

    case CLIENT_TYPE:
      return getClientType();

    case HDID:
      return getHdid();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case LOGIN_UID:
      return isSetLoginUid();
    case SID:
      return isSetSid();
    case CLIENT_TYPE:
      return isSetClientType();
    case HDID:
      return isSetHdid();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ZhuiWanComponentCommonReq)
      return this.equals((ZhuiWanComponentCommonReq)that);
    return false;
  }

  public boolean equals(ZhuiWanComponentCommonReq that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_loginUid = true;
    boolean that_present_loginUid = true;
    if (this_present_loginUid || that_present_loginUid) {
      if (!(this_present_loginUid && that_present_loginUid))
        return false;
      if (this.loginUid != that.loginUid)
        return false;
    }

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_clientType = true;
    boolean that_present_clientType = true;
    if (this_present_clientType || that_present_clientType) {
      if (!(this_present_clientType && that_present_clientType))
        return false;
      if (this.clientType != that.clientType)
        return false;
    }

    boolean this_present_hdid = true && this.isSetHdid();
    boolean that_present_hdid = true && that.isSetHdid();
    if (this_present_hdid || that_present_hdid) {
      if (!(this_present_hdid && that_present_hdid))
        return false;
      if (!this.hdid.equals(that.hdid))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_loginUid = true;
    list.add(present_loginUid);
    if (present_loginUid)
      list.add(loginUid);

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_clientType = true;
    list.add(present_clientType);
    if (present_clientType)
      list.add(clientType);

    boolean present_hdid = true && (isSetHdid());
    list.add(present_hdid);
    if (present_hdid)
      list.add(hdid);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(ZhuiWanComponentCommonReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLoginUid()).compareTo(other.isSetLoginUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLoginUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.loginUid, other.loginUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetClientType()).compareTo(other.isSetClientType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetClientType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.clientType, other.clientType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetHdid()).compareTo(other.isSetHdid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHdid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.hdid, other.hdid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ZhuiWanComponentCommonReq(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("loginUid:");
    sb.append(this.loginUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("clientType:");
    sb.append(this.clientType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("hdid:");
    if (this.hdid == null) {
      sb.append("null");
    } else {
      sb.append(this.hdid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ZhuiWanComponentCommonReqStandardSchemeFactory implements SchemeFactory {
    public ZhuiWanComponentCommonReqStandardScheme getScheme() {
      return new ZhuiWanComponentCommonReqStandardScheme();
    }
  }

  private static class ZhuiWanComponentCommonReqStandardScheme extends StandardScheme<ZhuiWanComponentCommonReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ZhuiWanComponentCommonReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // LOGIN_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.loginUid = iprot.readI64();
              struct.setLoginUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CLIENT_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.clientType = iprot.readI32();
              struct.setClientTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // HDID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.hdid = iprot.readString();
              struct.setHdidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map40 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map40.size);
                String _key41;
                String _val42;
                for (int _i43 = 0; _i43 < _map40.size; ++_i43)
                {
                  _key41 = iprot.readString();
                  _val42 = iprot.readString();
                  struct.extData.put(_key41, _val42);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ZhuiWanComponentCommonReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(LOGIN_UID_FIELD_DESC);
      oprot.writeI64(struct.loginUid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CLIENT_TYPE_FIELD_DESC);
      oprot.writeI32(struct.clientType);
      oprot.writeFieldEnd();
      if (struct.hdid != null) {
        oprot.writeFieldBegin(HDID_FIELD_DESC);
        oprot.writeString(struct.hdid);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter44 : struct.extData.entrySet())
          {
            oprot.writeString(_iter44.getKey());
            oprot.writeString(_iter44.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ZhuiWanComponentCommonReqTupleSchemeFactory implements SchemeFactory {
    public ZhuiWanComponentCommonReqTupleScheme getScheme() {
      return new ZhuiWanComponentCommonReqTupleScheme();
    }
  }

  private static class ZhuiWanComponentCommonReqTupleScheme extends TupleScheme<ZhuiWanComponentCommonReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ZhuiWanComponentCommonReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetLoginUid()) {
        optionals.set(1);
      }
      if (struct.isSetSid()) {
        optionals.set(2);
      }
      if (struct.isSetClientType()) {
        optionals.set(3);
      }
      if (struct.isSetHdid()) {
        optionals.set(4);
      }
      if (struct.isSetExtData()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetLoginUid()) {
        oprot.writeI64(struct.loginUid);
      }
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetClientType()) {
        oprot.writeI32(struct.clientType);
      }
      if (struct.isSetHdid()) {
        oprot.writeString(struct.hdid);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter45 : struct.extData.entrySet())
          {
            oprot.writeString(_iter45.getKey());
            oprot.writeString(_iter45.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ZhuiWanComponentCommonReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.loginUid = iprot.readI64();
        struct.setLoginUidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.clientType = iprot.readI32();
        struct.setClientTypeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.hdid = iprot.readString();
        struct.setHdidIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TMap _map46 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map46.size);
          String _key47;
          String _val48;
          for (int _i49 = 0; _i49 < _map46.size; ++_i49)
          {
            _key47 = iprot.readString();
            _val48 = iprot.readString();
            struct.extData.put(_key47, _val48);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

