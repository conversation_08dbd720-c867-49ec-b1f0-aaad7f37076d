/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class RankingPhaseInfo implements org.apache.thrift.TBase<RankingPhaseInfo, RankingPhaseInfo._Fields>, java.io.Serializable, Cloneable, Comparable<RankingPhaseInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RankingPhaseInfo");

  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField PHASE_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseName", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField BEGIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("beginTime", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("endTime", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField PASS_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("passCount", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField TOTAL_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("totalCount", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField PHASE_GROUP_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseGroupCode", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField PHASE_NAME_SHOW_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseNameShow", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField SHOW_BEGIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("showBeginTime", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField SHOW_END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("showEndTime", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField EXT_JSON_FIELD_DESC = new org.apache.thrift.protocol.TField("extJson", org.apache.thrift.protocol.TType.STRING, (short)11);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RankingPhaseInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RankingPhaseInfoTupleSchemeFactory());
  }

  public long phaseId; // required
  public String phaseName; // required
  public long beginTime; // required
  public long endTime; // required
  public long passCount; // required
  public long totalCount; // required
  public String phaseGroupCode; // required
  public String phaseNameShow; // required
  public long showBeginTime; // required
  public long showEndTime; // required
  public String extJson; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    PHASE_ID((short)1, "phaseId"),
    PHASE_NAME((short)2, "phaseName"),
    BEGIN_TIME((short)3, "beginTime"),
    END_TIME((short)4, "endTime"),
    PASS_COUNT((short)5, "passCount"),
    TOTAL_COUNT((short)6, "totalCount"),
    PHASE_GROUP_CODE((short)7, "phaseGroupCode"),
    PHASE_NAME_SHOW((short)8, "phaseNameShow"),
    SHOW_BEGIN_TIME((short)9, "showBeginTime"),
    SHOW_END_TIME((short)10, "showEndTime"),
    EXT_JSON((short)11, "extJson"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // PHASE_ID
          return PHASE_ID;
        case 2: // PHASE_NAME
          return PHASE_NAME;
        case 3: // BEGIN_TIME
          return BEGIN_TIME;
        case 4: // END_TIME
          return END_TIME;
        case 5: // PASS_COUNT
          return PASS_COUNT;
        case 6: // TOTAL_COUNT
          return TOTAL_COUNT;
        case 7: // PHASE_GROUP_CODE
          return PHASE_GROUP_CODE;
        case 8: // PHASE_NAME_SHOW
          return PHASE_NAME_SHOW;
        case 9: // SHOW_BEGIN_TIME
          return SHOW_BEGIN_TIME;
        case 10: // SHOW_END_TIME
          return SHOW_END_TIME;
        case 11: // EXT_JSON
          return EXT_JSON;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __PHASEID_ISSET_ID = 0;
  private static final int __BEGINTIME_ISSET_ID = 1;
  private static final int __ENDTIME_ISSET_ID = 2;
  private static final int __PASSCOUNT_ISSET_ID = 3;
  private static final int __TOTALCOUNT_ISSET_ID = 4;
  private static final int __SHOWBEGINTIME_ISSET_ID = 5;
  private static final int __SHOWENDTIME_ISSET_ID = 6;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PHASE_NAME, new org.apache.thrift.meta_data.FieldMetaData("phaseName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BEGIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("beginTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.END_TIME, new org.apache.thrift.meta_data.FieldMetaData("endTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PASS_COUNT, new org.apache.thrift.meta_data.FieldMetaData("passCount", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TOTAL_COUNT, new org.apache.thrift.meta_data.FieldMetaData("totalCount", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PHASE_GROUP_CODE, new org.apache.thrift.meta_data.FieldMetaData("phaseGroupCode", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PHASE_NAME_SHOW, new org.apache.thrift.meta_data.FieldMetaData("phaseNameShow", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SHOW_BEGIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("showBeginTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SHOW_END_TIME, new org.apache.thrift.meta_data.FieldMetaData("showEndTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_JSON, new org.apache.thrift.meta_data.FieldMetaData("extJson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RankingPhaseInfo.class, metaDataMap);
  }

  public RankingPhaseInfo() {
  }

  public RankingPhaseInfo(
    long phaseId,
    String phaseName,
    long beginTime,
    long endTime,
    long passCount,
    long totalCount,
    String phaseGroupCode,
    String phaseNameShow,
    long showBeginTime,
    long showEndTime,
    String extJson,
    Map<String,String> extData)
  {
    this();
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    this.phaseName = phaseName;
    this.beginTime = beginTime;
    setBeginTimeIsSet(true);
    this.endTime = endTime;
    setEndTimeIsSet(true);
    this.passCount = passCount;
    setPassCountIsSet(true);
    this.totalCount = totalCount;
    setTotalCountIsSet(true);
    this.phaseGroupCode = phaseGroupCode;
    this.phaseNameShow = phaseNameShow;
    this.showBeginTime = showBeginTime;
    setShowBeginTimeIsSet(true);
    this.showEndTime = showEndTime;
    setShowEndTimeIsSet(true);
    this.extJson = extJson;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RankingPhaseInfo(RankingPhaseInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.phaseId = other.phaseId;
    if (other.isSetPhaseName()) {
      this.phaseName = other.phaseName;
    }
    this.beginTime = other.beginTime;
    this.endTime = other.endTime;
    this.passCount = other.passCount;
    this.totalCount = other.totalCount;
    if (other.isSetPhaseGroupCode()) {
      this.phaseGroupCode = other.phaseGroupCode;
    }
    if (other.isSetPhaseNameShow()) {
      this.phaseNameShow = other.phaseNameShow;
    }
    this.showBeginTime = other.showBeginTime;
    this.showEndTime = other.showEndTime;
    if (other.isSetExtJson()) {
      this.extJson = other.extJson;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public RankingPhaseInfo deepCopy() {
    return new RankingPhaseInfo(this);
  }

  @Override
  public void clear() {
    setPhaseIdIsSet(false);
    this.phaseId = 0;
    this.phaseName = null;
    setBeginTimeIsSet(false);
    this.beginTime = 0;
    setEndTimeIsSet(false);
    this.endTime = 0;
    setPassCountIsSet(false);
    this.passCount = 0;
    setTotalCountIsSet(false);
    this.totalCount = 0;
    this.phaseGroupCode = null;
    this.phaseNameShow = null;
    setShowBeginTimeIsSet(false);
    this.showBeginTime = 0;
    setShowEndTimeIsSet(false);
    this.showEndTime = 0;
    this.extJson = null;
    this.extData = null;
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public RankingPhaseInfo setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public String getPhaseName() {
    return this.phaseName;
  }

  public RankingPhaseInfo setPhaseName(String phaseName) {
    this.phaseName = phaseName;
    return this;
  }

  public void unsetPhaseName() {
    this.phaseName = null;
  }

  /** Returns true if field phaseName is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseName() {
    return this.phaseName != null;
  }

  public void setPhaseNameIsSet(boolean value) {
    if (!value) {
      this.phaseName = null;
    }
  }

  public long getBeginTime() {
    return this.beginTime;
  }

  public RankingPhaseInfo setBeginTime(long beginTime) {
    this.beginTime = beginTime;
    setBeginTimeIsSet(true);
    return this;
  }

  public void unsetBeginTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
  }

  /** Returns true if field beginTime is set (has been assigned a value) and false otherwise */
  public boolean isSetBeginTime() {
    return EncodingUtils.testBit(__isset_bitfield, __BEGINTIME_ISSET_ID);
  }

  public void setBeginTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BEGINTIME_ISSET_ID, value);
  }

  public long getEndTime() {
    return this.endTime;
  }

  public RankingPhaseInfo setEndTime(long endTime) {
    this.endTime = endTime;
    setEndTimeIsSet(true);
    return this;
  }

  public void unsetEndTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  /** Returns true if field endTime is set (has been assigned a value) and false otherwise */
  public boolean isSetEndTime() {
    return EncodingUtils.testBit(__isset_bitfield, __ENDTIME_ISSET_ID);
  }

  public void setEndTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ENDTIME_ISSET_ID, value);
  }

  public long getPassCount() {
    return this.passCount;
  }

  public RankingPhaseInfo setPassCount(long passCount) {
    this.passCount = passCount;
    setPassCountIsSet(true);
    return this;
  }

  public void unsetPassCount() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PASSCOUNT_ISSET_ID);
  }

  /** Returns true if field passCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPassCount() {
    return EncodingUtils.testBit(__isset_bitfield, __PASSCOUNT_ISSET_ID);
  }

  public void setPassCountIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PASSCOUNT_ISSET_ID, value);
  }

  public long getTotalCount() {
    return this.totalCount;
  }

  public RankingPhaseInfo setTotalCount(long totalCount) {
    this.totalCount = totalCount;
    setTotalCountIsSet(true);
    return this;
  }

  public void unsetTotalCount() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOTALCOUNT_ISSET_ID);
  }

  /** Returns true if field totalCount is set (has been assigned a value) and false otherwise */
  public boolean isSetTotalCount() {
    return EncodingUtils.testBit(__isset_bitfield, __TOTALCOUNT_ISSET_ID);
  }

  public void setTotalCountIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOTALCOUNT_ISSET_ID, value);
  }

  public String getPhaseGroupCode() {
    return this.phaseGroupCode;
  }

  public RankingPhaseInfo setPhaseGroupCode(String phaseGroupCode) {
    this.phaseGroupCode = phaseGroupCode;
    return this;
  }

  public void unsetPhaseGroupCode() {
    this.phaseGroupCode = null;
  }

  /** Returns true if field phaseGroupCode is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseGroupCode() {
    return this.phaseGroupCode != null;
  }

  public void setPhaseGroupCodeIsSet(boolean value) {
    if (!value) {
      this.phaseGroupCode = null;
    }
  }

  public String getPhaseNameShow() {
    return this.phaseNameShow;
  }

  public RankingPhaseInfo setPhaseNameShow(String phaseNameShow) {
    this.phaseNameShow = phaseNameShow;
    return this;
  }

  public void unsetPhaseNameShow() {
    this.phaseNameShow = null;
  }

  /** Returns true if field phaseNameShow is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseNameShow() {
    return this.phaseNameShow != null;
  }

  public void setPhaseNameShowIsSet(boolean value) {
    if (!value) {
      this.phaseNameShow = null;
    }
  }

  public long getShowBeginTime() {
    return this.showBeginTime;
  }

  public RankingPhaseInfo setShowBeginTime(long showBeginTime) {
    this.showBeginTime = showBeginTime;
    setShowBeginTimeIsSet(true);
    return this;
  }

  public void unsetShowBeginTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SHOWBEGINTIME_ISSET_ID);
  }

  /** Returns true if field showBeginTime is set (has been assigned a value) and false otherwise */
  public boolean isSetShowBeginTime() {
    return EncodingUtils.testBit(__isset_bitfield, __SHOWBEGINTIME_ISSET_ID);
  }

  public void setShowBeginTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SHOWBEGINTIME_ISSET_ID, value);
  }

  public long getShowEndTime() {
    return this.showEndTime;
  }

  public RankingPhaseInfo setShowEndTime(long showEndTime) {
    this.showEndTime = showEndTime;
    setShowEndTimeIsSet(true);
    return this;
  }

  public void unsetShowEndTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SHOWENDTIME_ISSET_ID);
  }

  /** Returns true if field showEndTime is set (has been assigned a value) and false otherwise */
  public boolean isSetShowEndTime() {
    return EncodingUtils.testBit(__isset_bitfield, __SHOWENDTIME_ISSET_ID);
  }

  public void setShowEndTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SHOWENDTIME_ISSET_ID, value);
  }

  public String getExtJson() {
    return this.extJson;
  }

  public RankingPhaseInfo setExtJson(String extJson) {
    this.extJson = extJson;
    return this;
  }

  public void unsetExtJson() {
    this.extJson = null;
  }

  /** Returns true if field extJson is set (has been assigned a value) and false otherwise */
  public boolean isSetExtJson() {
    return this.extJson != null;
  }

  public void setExtJsonIsSet(boolean value) {
    if (!value) {
      this.extJson = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public RankingPhaseInfo setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    case PHASE_NAME:
      if (value == null) {
        unsetPhaseName();
      } else {
        setPhaseName((String)value);
      }
      break;

    case BEGIN_TIME:
      if (value == null) {
        unsetBeginTime();
      } else {
        setBeginTime((Long)value);
      }
      break;

    case END_TIME:
      if (value == null) {
        unsetEndTime();
      } else {
        setEndTime((Long)value);
      }
      break;

    case PASS_COUNT:
      if (value == null) {
        unsetPassCount();
      } else {
        setPassCount((Long)value);
      }
      break;

    case TOTAL_COUNT:
      if (value == null) {
        unsetTotalCount();
      } else {
        setTotalCount((Long)value);
      }
      break;

    case PHASE_GROUP_CODE:
      if (value == null) {
        unsetPhaseGroupCode();
      } else {
        setPhaseGroupCode((String)value);
      }
      break;

    case PHASE_NAME_SHOW:
      if (value == null) {
        unsetPhaseNameShow();
      } else {
        setPhaseNameShow((String)value);
      }
      break;

    case SHOW_BEGIN_TIME:
      if (value == null) {
        unsetShowBeginTime();
      } else {
        setShowBeginTime((Long)value);
      }
      break;

    case SHOW_END_TIME:
      if (value == null) {
        unsetShowEndTime();
      } else {
        setShowEndTime((Long)value);
      }
      break;

    case EXT_JSON:
      if (value == null) {
        unsetExtJson();
      } else {
        setExtJson((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case PHASE_ID:
      return getPhaseId();

    case PHASE_NAME:
      return getPhaseName();

    case BEGIN_TIME:
      return getBeginTime();

    case END_TIME:
      return getEndTime();

    case PASS_COUNT:
      return getPassCount();

    case TOTAL_COUNT:
      return getTotalCount();

    case PHASE_GROUP_CODE:
      return getPhaseGroupCode();

    case PHASE_NAME_SHOW:
      return getPhaseNameShow();

    case SHOW_BEGIN_TIME:
      return getShowBeginTime();

    case SHOW_END_TIME:
      return getShowEndTime();

    case EXT_JSON:
      return getExtJson();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case PHASE_ID:
      return isSetPhaseId();
    case PHASE_NAME:
      return isSetPhaseName();
    case BEGIN_TIME:
      return isSetBeginTime();
    case END_TIME:
      return isSetEndTime();
    case PASS_COUNT:
      return isSetPassCount();
    case TOTAL_COUNT:
      return isSetTotalCount();
    case PHASE_GROUP_CODE:
      return isSetPhaseGroupCode();
    case PHASE_NAME_SHOW:
      return isSetPhaseNameShow();
    case SHOW_BEGIN_TIME:
      return isSetShowBeginTime();
    case SHOW_END_TIME:
      return isSetShowEndTime();
    case EXT_JSON:
      return isSetExtJson();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RankingPhaseInfo)
      return this.equals((RankingPhaseInfo)that);
    return false;
  }

  public boolean equals(RankingPhaseInfo that) {
    if (that == null)
      return false;

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    boolean this_present_phaseName = true && this.isSetPhaseName();
    boolean that_present_phaseName = true && that.isSetPhaseName();
    if (this_present_phaseName || that_present_phaseName) {
      if (!(this_present_phaseName && that_present_phaseName))
        return false;
      if (!this.phaseName.equals(that.phaseName))
        return false;
    }

    boolean this_present_beginTime = true;
    boolean that_present_beginTime = true;
    if (this_present_beginTime || that_present_beginTime) {
      if (!(this_present_beginTime && that_present_beginTime))
        return false;
      if (this.beginTime != that.beginTime)
        return false;
    }

    boolean this_present_endTime = true;
    boolean that_present_endTime = true;
    if (this_present_endTime || that_present_endTime) {
      if (!(this_present_endTime && that_present_endTime))
        return false;
      if (this.endTime != that.endTime)
        return false;
    }

    boolean this_present_passCount = true;
    boolean that_present_passCount = true;
    if (this_present_passCount || that_present_passCount) {
      if (!(this_present_passCount && that_present_passCount))
        return false;
      if (this.passCount != that.passCount)
        return false;
    }

    boolean this_present_totalCount = true;
    boolean that_present_totalCount = true;
    if (this_present_totalCount || that_present_totalCount) {
      if (!(this_present_totalCount && that_present_totalCount))
        return false;
      if (this.totalCount != that.totalCount)
        return false;
    }

    boolean this_present_phaseGroupCode = true && this.isSetPhaseGroupCode();
    boolean that_present_phaseGroupCode = true && that.isSetPhaseGroupCode();
    if (this_present_phaseGroupCode || that_present_phaseGroupCode) {
      if (!(this_present_phaseGroupCode && that_present_phaseGroupCode))
        return false;
      if (!this.phaseGroupCode.equals(that.phaseGroupCode))
        return false;
    }

    boolean this_present_phaseNameShow = true && this.isSetPhaseNameShow();
    boolean that_present_phaseNameShow = true && that.isSetPhaseNameShow();
    if (this_present_phaseNameShow || that_present_phaseNameShow) {
      if (!(this_present_phaseNameShow && that_present_phaseNameShow))
        return false;
      if (!this.phaseNameShow.equals(that.phaseNameShow))
        return false;
    }

    boolean this_present_showBeginTime = true;
    boolean that_present_showBeginTime = true;
    if (this_present_showBeginTime || that_present_showBeginTime) {
      if (!(this_present_showBeginTime && that_present_showBeginTime))
        return false;
      if (this.showBeginTime != that.showBeginTime)
        return false;
    }

    boolean this_present_showEndTime = true;
    boolean that_present_showEndTime = true;
    if (this_present_showEndTime || that_present_showEndTime) {
      if (!(this_present_showEndTime && that_present_showEndTime))
        return false;
      if (this.showEndTime != that.showEndTime)
        return false;
    }

    boolean this_present_extJson = true && this.isSetExtJson();
    boolean that_present_extJson = true && that.isSetExtJson();
    if (this_present_extJson || that_present_extJson) {
      if (!(this_present_extJson && that_present_extJson))
        return false;
      if (!this.extJson.equals(that.extJson))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    boolean present_phaseName = true && (isSetPhaseName());
    list.add(present_phaseName);
    if (present_phaseName)
      list.add(phaseName);

    boolean present_beginTime = true;
    list.add(present_beginTime);
    if (present_beginTime)
      list.add(beginTime);

    boolean present_endTime = true;
    list.add(present_endTime);
    if (present_endTime)
      list.add(endTime);

    boolean present_passCount = true;
    list.add(present_passCount);
    if (present_passCount)
      list.add(passCount);

    boolean present_totalCount = true;
    list.add(present_totalCount);
    if (present_totalCount)
      list.add(totalCount);

    boolean present_phaseGroupCode = true && (isSetPhaseGroupCode());
    list.add(present_phaseGroupCode);
    if (present_phaseGroupCode)
      list.add(phaseGroupCode);

    boolean present_phaseNameShow = true && (isSetPhaseNameShow());
    list.add(present_phaseNameShow);
    if (present_phaseNameShow)
      list.add(phaseNameShow);

    boolean present_showBeginTime = true;
    list.add(present_showBeginTime);
    if (present_showBeginTime)
      list.add(showBeginTime);

    boolean present_showEndTime = true;
    list.add(present_showEndTime);
    if (present_showEndTime)
      list.add(showEndTime);

    boolean present_extJson = true && (isSetExtJson());
    list.add(present_extJson);
    if (present_extJson)
      list.add(extJson);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(RankingPhaseInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseName()).compareTo(other.isSetPhaseName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseName, other.phaseName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBeginTime()).compareTo(other.isSetBeginTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBeginTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.beginTime, other.beginTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetEndTime()).compareTo(other.isSetEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.endTime, other.endTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPassCount()).compareTo(other.isSetPassCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPassCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.passCount, other.passCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTotalCount()).compareTo(other.isSetTotalCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTotalCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.totalCount, other.totalCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseGroupCode()).compareTo(other.isSetPhaseGroupCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseGroupCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseGroupCode, other.phaseGroupCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseNameShow()).compareTo(other.isSetPhaseNameShow());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseNameShow()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseNameShow, other.phaseNameShow);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetShowBeginTime()).compareTo(other.isSetShowBeginTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetShowBeginTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.showBeginTime, other.showBeginTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetShowEndTime()).compareTo(other.isSetShowEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetShowEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.showEndTime, other.showEndTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtJson()).compareTo(other.isSetExtJson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtJson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extJson, other.extJson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RankingPhaseInfo(");
    boolean first = true;

    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseName:");
    if (this.phaseName == null) {
      sb.append("null");
    } else {
      sb.append(this.phaseName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("beginTime:");
    sb.append(this.beginTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("endTime:");
    sb.append(this.endTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("passCount:");
    sb.append(this.passCount);
    first = false;
    if (!first) sb.append(", ");
    sb.append("totalCount:");
    sb.append(this.totalCount);
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseGroupCode:");
    if (this.phaseGroupCode == null) {
      sb.append("null");
    } else {
      sb.append(this.phaseGroupCode);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseNameShow:");
    if (this.phaseNameShow == null) {
      sb.append("null");
    } else {
      sb.append(this.phaseNameShow);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("showBeginTime:");
    sb.append(this.showBeginTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("showEndTime:");
    sb.append(this.showEndTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extJson:");
    if (this.extJson == null) {
      sb.append("null");
    } else {
      sb.append(this.extJson);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RankingPhaseInfoStandardSchemeFactory implements SchemeFactory {
    public RankingPhaseInfoStandardScheme getScheme() {
      return new RankingPhaseInfoStandardScheme();
    }
  }

  private static class RankingPhaseInfoStandardScheme extends StandardScheme<RankingPhaseInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RankingPhaseInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // PHASE_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.phaseName = iprot.readString();
              struct.setPhaseNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // BEGIN_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.beginTime = iprot.readI64();
              struct.setBeginTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.endTime = iprot.readI64();
              struct.setEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // PASS_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.passCount = iprot.readI64();
              struct.setPassCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // TOTAL_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.totalCount = iprot.readI64();
              struct.setTotalCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // PHASE_GROUP_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.phaseGroupCode = iprot.readString();
              struct.setPhaseGroupCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // PHASE_NAME_SHOW
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.phaseNameShow = iprot.readString();
              struct.setPhaseNameShowIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // SHOW_BEGIN_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.showBeginTime = iprot.readI64();
              struct.setShowBeginTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // SHOW_END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.showEndTime = iprot.readI64();
              struct.setShowEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // EXT_JSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extJson = iprot.readString();
              struct.setExtJsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map84 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map84.size);
                String _key85;
                String _val86;
                for (int _i87 = 0; _i87 < _map84.size; ++_i87)
                {
                  _key85 = iprot.readString();
                  _val86 = iprot.readString();
                  struct.extData.put(_key85, _val86);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RankingPhaseInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      if (struct.phaseName != null) {
        oprot.writeFieldBegin(PHASE_NAME_FIELD_DESC);
        oprot.writeString(struct.phaseName);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(BEGIN_TIME_FIELD_DESC);
      oprot.writeI64(struct.beginTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(END_TIME_FIELD_DESC);
      oprot.writeI64(struct.endTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PASS_COUNT_FIELD_DESC);
      oprot.writeI64(struct.passCount);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TOTAL_COUNT_FIELD_DESC);
      oprot.writeI64(struct.totalCount);
      oprot.writeFieldEnd();
      if (struct.phaseGroupCode != null) {
        oprot.writeFieldBegin(PHASE_GROUP_CODE_FIELD_DESC);
        oprot.writeString(struct.phaseGroupCode);
        oprot.writeFieldEnd();
      }
      if (struct.phaseNameShow != null) {
        oprot.writeFieldBegin(PHASE_NAME_SHOW_FIELD_DESC);
        oprot.writeString(struct.phaseNameShow);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SHOW_BEGIN_TIME_FIELD_DESC);
      oprot.writeI64(struct.showBeginTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SHOW_END_TIME_FIELD_DESC);
      oprot.writeI64(struct.showEndTime);
      oprot.writeFieldEnd();
      if (struct.extJson != null) {
        oprot.writeFieldBegin(EXT_JSON_FIELD_DESC);
        oprot.writeString(struct.extJson);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter88 : struct.extData.entrySet())
          {
            oprot.writeString(_iter88.getKey());
            oprot.writeString(_iter88.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RankingPhaseInfoTupleSchemeFactory implements SchemeFactory {
    public RankingPhaseInfoTupleScheme getScheme() {
      return new RankingPhaseInfoTupleScheme();
    }
  }

  private static class RankingPhaseInfoTupleScheme extends TupleScheme<RankingPhaseInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RankingPhaseInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetPhaseId()) {
        optionals.set(0);
      }
      if (struct.isSetPhaseName()) {
        optionals.set(1);
      }
      if (struct.isSetBeginTime()) {
        optionals.set(2);
      }
      if (struct.isSetEndTime()) {
        optionals.set(3);
      }
      if (struct.isSetPassCount()) {
        optionals.set(4);
      }
      if (struct.isSetTotalCount()) {
        optionals.set(5);
      }
      if (struct.isSetPhaseGroupCode()) {
        optionals.set(6);
      }
      if (struct.isSetPhaseNameShow()) {
        optionals.set(7);
      }
      if (struct.isSetShowBeginTime()) {
        optionals.set(8);
      }
      if (struct.isSetShowEndTime()) {
        optionals.set(9);
      }
      if (struct.isSetExtJson()) {
        optionals.set(10);
      }
      if (struct.isSetExtData()) {
        optionals.set(11);
      }
      oprot.writeBitSet(optionals, 12);
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
      if (struct.isSetPhaseName()) {
        oprot.writeString(struct.phaseName);
      }
      if (struct.isSetBeginTime()) {
        oprot.writeI64(struct.beginTime);
      }
      if (struct.isSetEndTime()) {
        oprot.writeI64(struct.endTime);
      }
      if (struct.isSetPassCount()) {
        oprot.writeI64(struct.passCount);
      }
      if (struct.isSetTotalCount()) {
        oprot.writeI64(struct.totalCount);
      }
      if (struct.isSetPhaseGroupCode()) {
        oprot.writeString(struct.phaseGroupCode);
      }
      if (struct.isSetPhaseNameShow()) {
        oprot.writeString(struct.phaseNameShow);
      }
      if (struct.isSetShowBeginTime()) {
        oprot.writeI64(struct.showBeginTime);
      }
      if (struct.isSetShowEndTime()) {
        oprot.writeI64(struct.showEndTime);
      }
      if (struct.isSetExtJson()) {
        oprot.writeString(struct.extJson);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter89 : struct.extData.entrySet())
          {
            oprot.writeString(_iter89.getKey());
            oprot.writeString(_iter89.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RankingPhaseInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(12);
      if (incoming.get(0)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.phaseName = iprot.readString();
        struct.setPhaseNameIsSet(true);
      }
      if (incoming.get(2)) {
        struct.beginTime = iprot.readI64();
        struct.setBeginTimeIsSet(true);
      }
      if (incoming.get(3)) {
        struct.endTime = iprot.readI64();
        struct.setEndTimeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.passCount = iprot.readI64();
        struct.setPassCountIsSet(true);
      }
      if (incoming.get(5)) {
        struct.totalCount = iprot.readI64();
        struct.setTotalCountIsSet(true);
      }
      if (incoming.get(6)) {
        struct.phaseGroupCode = iprot.readString();
        struct.setPhaseGroupCodeIsSet(true);
      }
      if (incoming.get(7)) {
        struct.phaseNameShow = iprot.readString();
        struct.setPhaseNameShowIsSet(true);
      }
      if (incoming.get(8)) {
        struct.showBeginTime = iprot.readI64();
        struct.setShowBeginTimeIsSet(true);
      }
      if (incoming.get(9)) {
        struct.showEndTime = iprot.readI64();
        struct.setShowEndTimeIsSet(true);
      }
      if (incoming.get(10)) {
        struct.extJson = iprot.readString();
        struct.setExtJsonIsSet(true);
      }
      if (incoming.get(11)) {
        {
          org.apache.thrift.protocol.TMap _map90 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map90.size);
          String _key91;
          String _val92;
          for (int _i93 = 0; _i93 < _map90.size; ++_i93)
          {
            _key91 = iprot.readString();
            _val92 = iprot.readString();
            struct.extData.put(_key91, _val92);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

