/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_compere_group;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-08-06")
public class QueryCompereGroupResp implements org.apache.thrift.TBase<QueryCompereGroupResp, QueryCompereGroupResp._Fields>, java.io.Serializable, Cloneable, Comparable<QueryCompereGroupResp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryCompereGroupResp");

  private static final org.apache.thrift.protocol.TField HEADER_FIELD_DESC = new org.apache.thrift.protocol.TField("header", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField GROUP_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("group_map", org.apache.thrift.protocol.TType.MAP, (short)2);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.MAP, (short)15);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryCompereGroupRespStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryCompereGroupRespTupleSchemeFactory());
  }

  public CommonRet header; // required
  public Map<Long,CompereGroup> group_map; // required
  public Map<String,String> expand; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    HEADER((short)1, "header"),
    GROUP_MAP((short)2, "group_map"),
    EXPAND((short)15, "expand");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // HEADER
          return HEADER;
        case 2: // GROUP_MAP
          return GROUP_MAP;
        case 15: // EXPAND
          return EXPAND;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.HEADER, new org.apache.thrift.meta_data.FieldMetaData("header", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CommonRet.class)));
    tmpMap.put(_Fields.GROUP_MAP, new org.apache.thrift.meta_data.FieldMetaData("group_map", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CompereGroup.class))));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryCompereGroupResp.class, metaDataMap);
  }

  public QueryCompereGroupResp() {
  }

  public QueryCompereGroupResp(
    CommonRet header,
    Map<Long,CompereGroup> group_map,
    Map<String,String> expand)
  {
    this();
    this.header = header;
    this.group_map = group_map;
    this.expand = expand;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryCompereGroupResp(QueryCompereGroupResp other) {
    if (other.isSetHeader()) {
      this.header = new CommonRet(other.header);
    }
    if (other.isSetGroup_map()) {
      Map<Long,CompereGroup> __this__group_map = new HashMap<Long,CompereGroup>(other.group_map.size());
      for (Map.Entry<Long, CompereGroup> other_element : other.group_map.entrySet()) {

        Long other_element_key = other_element.getKey();
        CompereGroup other_element_value = other_element.getValue();

        Long __this__group_map_copy_key = other_element_key;

        CompereGroup __this__group_map_copy_value = new CompereGroup(other_element_value);

        __this__group_map.put(__this__group_map_copy_key, __this__group_map_copy_value);
      }
      this.group_map = __this__group_map;
    }
    if (other.isSetExpand()) {
      Map<String,String> __this__expand = new HashMap<String,String>(other.expand);
      this.expand = __this__expand;
    }
  }

  public QueryCompereGroupResp deepCopy() {
    return new QueryCompereGroupResp(this);
  }

  @Override
  public void clear() {
    this.header = null;
    this.group_map = null;
    this.expand = null;
  }

  public CommonRet getHeader() {
    return this.header;
  }

  public QueryCompereGroupResp setHeader(CommonRet header) {
    this.header = header;
    return this;
  }

  public void unsetHeader() {
    this.header = null;
  }

  /** Returns true if field header is set (has been assigned a value) and false otherwise */
  public boolean isSetHeader() {
    return this.header != null;
  }

  public void setHeaderIsSet(boolean value) {
    if (!value) {
      this.header = null;
    }
  }

  public int getGroup_mapSize() {
    return (this.group_map == null) ? 0 : this.group_map.size();
  }

  public void putToGroup_map(long key, CompereGroup val) {
    if (this.group_map == null) {
      this.group_map = new HashMap<Long,CompereGroup>();
    }
    this.group_map.put(key, val);
  }

  public Map<Long,CompereGroup> getGroup_map() {
    return this.group_map;
  }

  public QueryCompereGroupResp setGroup_map(Map<Long,CompereGroup> group_map) {
    this.group_map = group_map;
    return this;
  }

  public void unsetGroup_map() {
    this.group_map = null;
  }

  /** Returns true if field group_map is set (has been assigned a value) and false otherwise */
  public boolean isSetGroup_map() {
    return this.group_map != null;
  }

  public void setGroup_mapIsSet(boolean value) {
    if (!value) {
      this.group_map = null;
    }
  }

  public int getExpandSize() {
    return (this.expand == null) ? 0 : this.expand.size();
  }

  public void putToExpand(String key, String val) {
    if (this.expand == null) {
      this.expand = new HashMap<String,String>();
    }
    this.expand.put(key, val);
  }

  public Map<String,String> getExpand() {
    return this.expand;
  }

  public QueryCompereGroupResp setExpand(Map<String,String> expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case HEADER:
      if (value == null) {
        unsetHeader();
      } else {
        setHeader((CommonRet)value);
      }
      break;

    case GROUP_MAP:
      if (value == null) {
        unsetGroup_map();
      } else {
        setGroup_map((Map<Long,CompereGroup>)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case HEADER:
      return getHeader();

    case GROUP_MAP:
      return getGroup_map();

    case EXPAND:
      return getExpand();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case HEADER:
      return isSetHeader();
    case GROUP_MAP:
      return isSetGroup_map();
    case EXPAND:
      return isSetExpand();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryCompereGroupResp)
      return this.equals((QueryCompereGroupResp)that);
    return false;
  }

  public boolean equals(QueryCompereGroupResp that) {
    if (that == null)
      return false;

    boolean this_present_header = true && this.isSetHeader();
    boolean that_present_header = true && that.isSetHeader();
    if (this_present_header || that_present_header) {
      if (!(this_present_header && that_present_header))
        return false;
      if (!this.header.equals(that.header))
        return false;
    }

    boolean this_present_group_map = true && this.isSetGroup_map();
    boolean that_present_group_map = true && that.isSetGroup_map();
    if (this_present_group_map || that_present_group_map) {
      if (!(this_present_group_map && that_present_group_map))
        return false;
      if (!this.group_map.equals(that.group_map))
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_header = true && (isSetHeader());
    list.add(present_header);
    if (present_header)
      list.add(header);

    boolean present_group_map = true && (isSetGroup_map());
    list.add(present_group_map);
    if (present_group_map)
      list.add(group_map);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryCompereGroupResp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetHeader()).compareTo(other.isSetHeader());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeader()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.header, other.header);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGroup_map()).compareTo(other.isSetGroup_map());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGroup_map()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.group_map, other.group_map);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryCompereGroupResp(");
    boolean first = true;

    sb.append("header:");
    if (this.header == null) {
      sb.append("null");
    } else {
      sb.append(this.header);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("group_map:");
    if (this.group_map == null) {
      sb.append("null");
    } else {
      sb.append(this.group_map);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (header != null) {
      header.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryCompereGroupRespStandardSchemeFactory implements SchemeFactory {
    public QueryCompereGroupRespStandardScheme getScheme() {
      return new QueryCompereGroupRespStandardScheme();
    }
  }

  private static class QueryCompereGroupRespStandardScheme extends StandardScheme<QueryCompereGroupResp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryCompereGroupResp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // HEADER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.header = new CommonRet();
              struct.header.read(iprot);
              struct.setHeaderIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // GROUP_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map18 = iprot.readMapBegin();
                struct.group_map = new HashMap<Long,CompereGroup>(2*_map18.size);
                long _key19;
                CompereGroup _val20;
                for (int _i21 = 0; _i21 < _map18.size; ++_i21)
                {
                  _key19 = iprot.readI64();
                  _val20 = new CompereGroup();
                  _val20.read(iprot);
                  struct.group_map.put(_key19, _val20);
                }
                iprot.readMapEnd();
              }
              struct.setGroup_mapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map22 = iprot.readMapBegin();
                struct.expand = new HashMap<String,String>(2*_map22.size);
                String _key23;
                String _val24;
                for (int _i25 = 0; _i25 < _map22.size; ++_i25)
                {
                  _key23 = iprot.readString();
                  _val24 = iprot.readString();
                  struct.expand.put(_key23, _val24);
                }
                iprot.readMapEnd();
              }
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryCompereGroupResp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.header != null) {
        oprot.writeFieldBegin(HEADER_FIELD_DESC);
        struct.header.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.group_map != null) {
        oprot.writeFieldBegin(GROUP_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRUCT, struct.group_map.size()));
          for (Map.Entry<Long, CompereGroup> _iter26 : struct.group_map.entrySet())
          {
            oprot.writeI64(_iter26.getKey());
            _iter26.getValue().write(oprot);
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.expand.size()));
          for (Map.Entry<String, String> _iter27 : struct.expand.entrySet())
          {
            oprot.writeString(_iter27.getKey());
            oprot.writeString(_iter27.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryCompereGroupRespTupleSchemeFactory implements SchemeFactory {
    public QueryCompereGroupRespTupleScheme getScheme() {
      return new QueryCompereGroupRespTupleScheme();
    }
  }

  private static class QueryCompereGroupRespTupleScheme extends TupleScheme<QueryCompereGroupResp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryCompereGroupResp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetHeader()) {
        optionals.set(0);
      }
      if (struct.isSetGroup_map()) {
        optionals.set(1);
      }
      if (struct.isSetExpand()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetHeader()) {
        struct.header.write(oprot);
      }
      if (struct.isSetGroup_map()) {
        {
          oprot.writeI32(struct.group_map.size());
          for (Map.Entry<Long, CompereGroup> _iter28 : struct.group_map.entrySet())
          {
            oprot.writeI64(_iter28.getKey());
            _iter28.getValue().write(oprot);
          }
        }
      }
      if (struct.isSetExpand()) {
        {
          oprot.writeI32(struct.expand.size());
          for (Map.Entry<String, String> _iter29 : struct.expand.entrySet())
          {
            oprot.writeString(_iter29.getKey());
            oprot.writeString(_iter29.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryCompereGroupResp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.header = new CommonRet();
        struct.header.read(iprot);
        struct.setHeaderIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map30 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.group_map = new HashMap<Long,CompereGroup>(2*_map30.size);
          long _key31;
          CompereGroup _val32;
          for (int _i33 = 0; _i33 < _map30.size; ++_i33)
          {
            _key31 = iprot.readI64();
            _val32 = new CompereGroup();
            _val32.read(iprot);
            struct.group_map.put(_key31, _val32);
          }
        }
        struct.setGroup_mapIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map34 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.expand = new HashMap<String,String>(2*_map34.size);
          String _key35;
          String _val36;
          for (int _i37 = 0; _i37 < _map34.size; ++_i37)
          {
            _key35 = iprot.readString();
            _val36 = iprot.readString();
            struct.expand.put(_key35, _val36);
          }
        }
        struct.setExpandIsSet(true);
      }
    }
  }

}

