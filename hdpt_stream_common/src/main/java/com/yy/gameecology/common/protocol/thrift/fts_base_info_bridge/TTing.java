/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class TTing implements org.apache.thrift.TBase<TTing, TTing._Fields>, java.io.Serializable, Cloneable, Comparable<TTing> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TTing");

  private static final org.apache.thrift.protocol.TField ID_FIELD_DESC = new org.apache.thrift.protocol.TField("id", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField APPID_FIELD_DESC = new org.apache.thrift.protocol.TField("appid", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField OW_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("owUid", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField TING_MGR_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("tingMgrUid", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField WEIGHT_FIELD_DESC = new org.apache.thrift.protocol.TField("weight", org.apache.thrift.protocol.TType.I32, (short)6);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField CREATE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("createTime", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField UPDATE_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("updateTime", org.apache.thrift.protocol.TType.I64, (short)9);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TTingStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TTingTupleSchemeFactory());
  }

  public long id; // required
  public int appid; // required
  public long sid; // required
  public long owUid; // required
  public long tingMgrUid; // required
  public int weight; // required
  public String expand; // required
  public long createTime; // required
  public long updateTime; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ID((short)1, "id"),
    APPID((short)2, "appid"),
    SID((short)3, "sid"),
    OW_UID((short)4, "owUid"),
    TING_MGR_UID((short)5, "tingMgrUid"),
    WEIGHT((short)6, "weight"),
    EXPAND((short)7, "expand"),
    CREATE_TIME((short)8, "createTime"),
    UPDATE_TIME((short)9, "updateTime");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ID
          return ID;
        case 2: // APPID
          return APPID;
        case 3: // SID
          return SID;
        case 4: // OW_UID
          return OW_UID;
        case 5: // TING_MGR_UID
          return TING_MGR_UID;
        case 6: // WEIGHT
          return WEIGHT;
        case 7: // EXPAND
          return EXPAND;
        case 8: // CREATE_TIME
          return CREATE_TIME;
        case 9: // UPDATE_TIME
          return UPDATE_TIME;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ID_ISSET_ID = 0;
  private static final int __APPID_ISSET_ID = 1;
  private static final int __SID_ISSET_ID = 2;
  private static final int __OWUID_ISSET_ID = 3;
  private static final int __TINGMGRUID_ISSET_ID = 4;
  private static final int __WEIGHT_ISSET_ID = 5;
  private static final int __CREATETIME_ISSET_ID = 6;
  private static final int __UPDATETIME_ISSET_ID = 7;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ID, new org.apache.thrift.meta_data.FieldMetaData("id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.APPID, new org.apache.thrift.meta_data.FieldMetaData("appid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.OW_UID, new org.apache.thrift.meta_data.FieldMetaData("owUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TING_MGR_UID, new org.apache.thrift.meta_data.FieldMetaData("tingMgrUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.WEIGHT, new org.apache.thrift.meta_data.FieldMetaData("weight", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CREATE_TIME, new org.apache.thrift.meta_data.FieldMetaData("createTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.UPDATE_TIME, new org.apache.thrift.meta_data.FieldMetaData("updateTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TTing.class, metaDataMap);
  }

  public TTing() {
  }

  public TTing(
    long id,
    int appid,
    long sid,
    long owUid,
    long tingMgrUid,
    int weight,
    String expand,
    long createTime,
    long updateTime)
  {
    this();
    this.id = id;
    setIdIsSet(true);
    this.appid = appid;
    setAppidIsSet(true);
    this.sid = sid;
    setSidIsSet(true);
    this.owUid = owUid;
    setOwUidIsSet(true);
    this.tingMgrUid = tingMgrUid;
    setTingMgrUidIsSet(true);
    this.weight = weight;
    setWeightIsSet(true);
    this.expand = expand;
    this.createTime = createTime;
    setCreateTimeIsSet(true);
    this.updateTime = updateTime;
    setUpdateTimeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TTing(TTing other) {
    __isset_bitfield = other.__isset_bitfield;
    this.id = other.id;
    this.appid = other.appid;
    this.sid = other.sid;
    this.owUid = other.owUid;
    this.tingMgrUid = other.tingMgrUid;
    this.weight = other.weight;
    if (other.isSetExpand()) {
      this.expand = other.expand;
    }
    this.createTime = other.createTime;
    this.updateTime = other.updateTime;
  }

  public TTing deepCopy() {
    return new TTing(this);
  }

  @Override
  public void clear() {
    setIdIsSet(false);
    this.id = 0;
    setAppidIsSet(false);
    this.appid = 0;
    setSidIsSet(false);
    this.sid = 0;
    setOwUidIsSet(false);
    this.owUid = 0;
    setTingMgrUidIsSet(false);
    this.tingMgrUid = 0;
    setWeightIsSet(false);
    this.weight = 0;
    this.expand = null;
    setCreateTimeIsSet(false);
    this.createTime = 0;
    setUpdateTimeIsSet(false);
    this.updateTime = 0;
  }

  public long getId() {
    return this.id;
  }

  public TTing setId(long id) {
    this.id = id;
    setIdIsSet(true);
    return this;
  }

  public void unsetId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ID_ISSET_ID);
  }

  /** Returns true if field id is set (has been assigned a value) and false otherwise */
  public boolean isSetId() {
    return EncodingUtils.testBit(__isset_bitfield, __ID_ISSET_ID);
  }

  public void setIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ID_ISSET_ID, value);
  }

  public int getAppid() {
    return this.appid;
  }

  public TTing setAppid(int appid) {
    this.appid = appid;
    setAppidIsSet(true);
    return this;
  }

  public void unsetAppid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  /** Returns true if field appid is set (has been assigned a value) and false otherwise */
  public boolean isSetAppid() {
    return EncodingUtils.testBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  public void setAppidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __APPID_ISSET_ID, value);
  }

  public long getSid() {
    return this.sid;
  }

  public TTing setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getOwUid() {
    return this.owUid;
  }

  public TTing setOwUid(long owUid) {
    this.owUid = owUid;
    setOwUidIsSet(true);
    return this;
  }

  public void unsetOwUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __OWUID_ISSET_ID);
  }

  /** Returns true if field owUid is set (has been assigned a value) and false otherwise */
  public boolean isSetOwUid() {
    return EncodingUtils.testBit(__isset_bitfield, __OWUID_ISSET_ID);
  }

  public void setOwUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __OWUID_ISSET_ID, value);
  }

  public long getTingMgrUid() {
    return this.tingMgrUid;
  }

  public TTing setTingMgrUid(long tingMgrUid) {
    this.tingMgrUid = tingMgrUid;
    setTingMgrUidIsSet(true);
    return this;
  }

  public void unsetTingMgrUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TINGMGRUID_ISSET_ID);
  }

  /** Returns true if field tingMgrUid is set (has been assigned a value) and false otherwise */
  public boolean isSetTingMgrUid() {
    return EncodingUtils.testBit(__isset_bitfield, __TINGMGRUID_ISSET_ID);
  }

  public void setTingMgrUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TINGMGRUID_ISSET_ID, value);
  }

  public int getWeight() {
    return this.weight;
  }

  public TTing setWeight(int weight) {
    this.weight = weight;
    setWeightIsSet(true);
    return this;
  }

  public void unsetWeight() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __WEIGHT_ISSET_ID);
  }

  /** Returns true if field weight is set (has been assigned a value) and false otherwise */
  public boolean isSetWeight() {
    return EncodingUtils.testBit(__isset_bitfield, __WEIGHT_ISSET_ID);
  }

  public void setWeightIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __WEIGHT_ISSET_ID, value);
  }

  public String getExpand() {
    return this.expand;
  }

  public TTing setExpand(String expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  public long getCreateTime() {
    return this.createTime;
  }

  public TTing setCreateTime(long createTime) {
    this.createTime = createTime;
    setCreateTimeIsSet(true);
    return this;
  }

  public void unsetCreateTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CREATETIME_ISSET_ID);
  }

  /** Returns true if field createTime is set (has been assigned a value) and false otherwise */
  public boolean isSetCreateTime() {
    return EncodingUtils.testBit(__isset_bitfield, __CREATETIME_ISSET_ID);
  }

  public void setCreateTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CREATETIME_ISSET_ID, value);
  }

  public long getUpdateTime() {
    return this.updateTime;
  }

  public TTing setUpdateTime(long updateTime) {
    this.updateTime = updateTime;
    setUpdateTimeIsSet(true);
    return this;
  }

  public void unsetUpdateTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __UPDATETIME_ISSET_ID);
  }

  /** Returns true if field updateTime is set (has been assigned a value) and false otherwise */
  public boolean isSetUpdateTime() {
    return EncodingUtils.testBit(__isset_bitfield, __UPDATETIME_ISSET_ID);
  }

  public void setUpdateTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __UPDATETIME_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ID:
      if (value == null) {
        unsetId();
      } else {
        setId((Long)value);
      }
      break;

    case APPID:
      if (value == null) {
        unsetAppid();
      } else {
        setAppid((Integer)value);
      }
      break;

    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case OW_UID:
      if (value == null) {
        unsetOwUid();
      } else {
        setOwUid((Long)value);
      }
      break;

    case TING_MGR_UID:
      if (value == null) {
        unsetTingMgrUid();
      } else {
        setTingMgrUid((Long)value);
      }
      break;

    case WEIGHT:
      if (value == null) {
        unsetWeight();
      } else {
        setWeight((Integer)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((String)value);
      }
      break;

    case CREATE_TIME:
      if (value == null) {
        unsetCreateTime();
      } else {
        setCreateTime((Long)value);
      }
      break;

    case UPDATE_TIME:
      if (value == null) {
        unsetUpdateTime();
      } else {
        setUpdateTime((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ID:
      return getId();

    case APPID:
      return getAppid();

    case SID:
      return getSid();

    case OW_UID:
      return getOwUid();

    case TING_MGR_UID:
      return getTingMgrUid();

    case WEIGHT:
      return getWeight();

    case EXPAND:
      return getExpand();

    case CREATE_TIME:
      return getCreateTime();

    case UPDATE_TIME:
      return getUpdateTime();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ID:
      return isSetId();
    case APPID:
      return isSetAppid();
    case SID:
      return isSetSid();
    case OW_UID:
      return isSetOwUid();
    case TING_MGR_UID:
      return isSetTingMgrUid();
    case WEIGHT:
      return isSetWeight();
    case EXPAND:
      return isSetExpand();
    case CREATE_TIME:
      return isSetCreateTime();
    case UPDATE_TIME:
      return isSetUpdateTime();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TTing)
      return this.equals((TTing)that);
    return false;
  }

  public boolean equals(TTing that) {
    if (that == null)
      return false;

    boolean this_present_id = true;
    boolean that_present_id = true;
    if (this_present_id || that_present_id) {
      if (!(this_present_id && that_present_id))
        return false;
      if (this.id != that.id)
        return false;
    }

    boolean this_present_appid = true;
    boolean that_present_appid = true;
    if (this_present_appid || that_present_appid) {
      if (!(this_present_appid && that_present_appid))
        return false;
      if (this.appid != that.appid)
        return false;
    }

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_owUid = true;
    boolean that_present_owUid = true;
    if (this_present_owUid || that_present_owUid) {
      if (!(this_present_owUid && that_present_owUid))
        return false;
      if (this.owUid != that.owUid)
        return false;
    }

    boolean this_present_tingMgrUid = true;
    boolean that_present_tingMgrUid = true;
    if (this_present_tingMgrUid || that_present_tingMgrUid) {
      if (!(this_present_tingMgrUid && that_present_tingMgrUid))
        return false;
      if (this.tingMgrUid != that.tingMgrUid)
        return false;
    }

    boolean this_present_weight = true;
    boolean that_present_weight = true;
    if (this_present_weight || that_present_weight) {
      if (!(this_present_weight && that_present_weight))
        return false;
      if (this.weight != that.weight)
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    boolean this_present_createTime = true;
    boolean that_present_createTime = true;
    if (this_present_createTime || that_present_createTime) {
      if (!(this_present_createTime && that_present_createTime))
        return false;
      if (this.createTime != that.createTime)
        return false;
    }

    boolean this_present_updateTime = true;
    boolean that_present_updateTime = true;
    if (this_present_updateTime || that_present_updateTime) {
      if (!(this_present_updateTime && that_present_updateTime))
        return false;
      if (this.updateTime != that.updateTime)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_id = true;
    list.add(present_id);
    if (present_id)
      list.add(id);

    boolean present_appid = true;
    list.add(present_appid);
    if (present_appid)
      list.add(appid);

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_owUid = true;
    list.add(present_owUid);
    if (present_owUid)
      list.add(owUid);

    boolean present_tingMgrUid = true;
    list.add(present_tingMgrUid);
    if (present_tingMgrUid)
      list.add(tingMgrUid);

    boolean present_weight = true;
    list.add(present_weight);
    if (present_weight)
      list.add(weight);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    boolean present_createTime = true;
    list.add(present_createTime);
    if (present_createTime)
      list.add(createTime);

    boolean present_updateTime = true;
    list.add(present_updateTime);
    if (present_updateTime)
      list.add(updateTime);

    return list.hashCode();
  }

  @Override
  public int compareTo(TTing other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetId()).compareTo(other.isSetId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.id, other.id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppid()).compareTo(other.isSetAppid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appid, other.appid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOwUid()).compareTo(other.isSetOwUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOwUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.owUid, other.owUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTingMgrUid()).compareTo(other.isSetTingMgrUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTingMgrUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tingMgrUid, other.tingMgrUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetWeight()).compareTo(other.isSetWeight());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetWeight()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.weight, other.weight);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCreateTime()).compareTo(other.isSetCreateTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCreateTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.createTime, other.createTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUpdateTime()).compareTo(other.isSetUpdateTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUpdateTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.updateTime, other.updateTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TTing(");
    boolean first = true;

    sb.append("id:");
    sb.append(this.id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("appid:");
    sb.append(this.appid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("owUid:");
    sb.append(this.owUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("tingMgrUid:");
    sb.append(this.tingMgrUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("weight:");
    sb.append(this.weight);
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("createTime:");
    sb.append(this.createTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("updateTime:");
    sb.append(this.updateTime);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TTingStandardSchemeFactory implements SchemeFactory {
    public TTingStandardScheme getScheme() {
      return new TTingStandardScheme();
    }
  }

  private static class TTingStandardScheme extends StandardScheme<TTing> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TTing struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.id = iprot.readI64();
              struct.setIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.appid = iprot.readI32();
              struct.setAppidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // OW_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.owUid = iprot.readI64();
              struct.setOwUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // TING_MGR_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.tingMgrUid = iprot.readI64();
              struct.setTingMgrUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // WEIGHT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.weight = iprot.readI32();
              struct.setWeightIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.expand = iprot.readString();
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // CREATE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.createTime = iprot.readI64();
              struct.setCreateTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // UPDATE_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.updateTime = iprot.readI64();
              struct.setUpdateTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TTing struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ID_FIELD_DESC);
      oprot.writeI64(struct.id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(APPID_FIELD_DESC);
      oprot.writeI32(struct.appid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(OW_UID_FIELD_DESC);
      oprot.writeI64(struct.owUid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TING_MGR_UID_FIELD_DESC);
      oprot.writeI64(struct.tingMgrUid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(WEIGHT_FIELD_DESC);
      oprot.writeI32(struct.weight);
      oprot.writeFieldEnd();
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        oprot.writeString(struct.expand);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CREATE_TIME_FIELD_DESC);
      oprot.writeI64(struct.createTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(UPDATE_TIME_FIELD_DESC);
      oprot.writeI64(struct.updateTime);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TTingTupleSchemeFactory implements SchemeFactory {
    public TTingTupleScheme getScheme() {
      return new TTingTupleScheme();
    }
  }

  private static class TTingTupleScheme extends TupleScheme<TTing> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TTing struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetId()) {
        optionals.set(0);
      }
      if (struct.isSetAppid()) {
        optionals.set(1);
      }
      if (struct.isSetSid()) {
        optionals.set(2);
      }
      if (struct.isSetOwUid()) {
        optionals.set(3);
      }
      if (struct.isSetTingMgrUid()) {
        optionals.set(4);
      }
      if (struct.isSetWeight()) {
        optionals.set(5);
      }
      if (struct.isSetExpand()) {
        optionals.set(6);
      }
      if (struct.isSetCreateTime()) {
        optionals.set(7);
      }
      if (struct.isSetUpdateTime()) {
        optionals.set(8);
      }
      oprot.writeBitSet(optionals, 9);
      if (struct.isSetId()) {
        oprot.writeI64(struct.id);
      }
      if (struct.isSetAppid()) {
        oprot.writeI32(struct.appid);
      }
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetOwUid()) {
        oprot.writeI64(struct.owUid);
      }
      if (struct.isSetTingMgrUid()) {
        oprot.writeI64(struct.tingMgrUid);
      }
      if (struct.isSetWeight()) {
        oprot.writeI32(struct.weight);
      }
      if (struct.isSetExpand()) {
        oprot.writeString(struct.expand);
      }
      if (struct.isSetCreateTime()) {
        oprot.writeI64(struct.createTime);
      }
      if (struct.isSetUpdateTime()) {
        oprot.writeI64(struct.updateTime);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TTing struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(9);
      if (incoming.get(0)) {
        struct.id = iprot.readI64();
        struct.setIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appid = iprot.readI32();
        struct.setAppidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.owUid = iprot.readI64();
        struct.setOwUidIsSet(true);
      }
      if (incoming.get(4)) {
        struct.tingMgrUid = iprot.readI64();
        struct.setTingMgrUidIsSet(true);
      }
      if (incoming.get(5)) {
        struct.weight = iprot.readI32();
        struct.setWeightIsSet(true);
      }
      if (incoming.get(6)) {
        struct.expand = iprot.readString();
        struct.setExpandIsSet(true);
      }
      if (incoming.get(7)) {
        struct.createTime = iprot.readI64();
        struct.setCreateTimeIsSet(true);
      }
      if (incoming.get(8)) {
        struct.updateTime = iprot.readI64();
        struct.setUpdateTimeIsSet(true);
      }
    }
  }

}

