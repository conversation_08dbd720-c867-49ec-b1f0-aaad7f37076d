/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class RankingConfigRequest implements org.apache.thrift.TBase<RankingConfigRequest, RankingConfigRequest._Fields>, java.io.Serializable, Cloneable, Comparable<RankingConfigRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RankingConfigRequest");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RANKING_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("rankingIds", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField CURRENT_DATE_FIELD_DESC = new org.apache.thrift.protocol.TField("currentDate", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField CUR_PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("curPhaseId", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RankingConfigRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RankingConfigRequestTupleSchemeFactory());
  }

  public long actId; // required
  public List<Long> rankingIds; // required
  public long currentDate; // required
  public long curPhaseId; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    RANKING_IDS((short)2, "rankingIds"),
    CURRENT_DATE((short)3, "currentDate"),
    CUR_PHASE_ID((short)4, "curPhaseId"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // RANKING_IDS
          return RANKING_IDS;
        case 3: // CURRENT_DATE
          return CURRENT_DATE;
        case 4: // CUR_PHASE_ID
          return CUR_PHASE_ID;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __CURRENTDATE_ISSET_ID = 1;
  private static final int __CURPHASEID_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANKING_IDS, new org.apache.thrift.meta_data.FieldMetaData("rankingIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.CURRENT_DATE, new org.apache.thrift.meta_data.FieldMetaData("currentDate", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CUR_PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("curPhaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RankingConfigRequest.class, metaDataMap);
  }

  public RankingConfigRequest() {
  }

  public RankingConfigRequest(
    long actId,
    List<Long> rankingIds,
    long currentDate,
    long curPhaseId,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.rankingIds = rankingIds;
    this.currentDate = currentDate;
    setCurrentDateIsSet(true);
    this.curPhaseId = curPhaseId;
    setCurPhaseIdIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RankingConfigRequest(RankingConfigRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    if (other.isSetRankingIds()) {
      List<Long> __this__rankingIds = new ArrayList<Long>(other.rankingIds);
      this.rankingIds = __this__rankingIds;
    }
    this.currentDate = other.currentDate;
    this.curPhaseId = other.curPhaseId;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public RankingConfigRequest deepCopy() {
    return new RankingConfigRequest(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    this.rankingIds = null;
    setCurrentDateIsSet(false);
    this.currentDate = 0;
    setCurPhaseIdIsSet(false);
    this.curPhaseId = 0;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public RankingConfigRequest setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public int getRankingIdsSize() {
    return (this.rankingIds == null) ? 0 : this.rankingIds.size();
  }

  public java.util.Iterator<Long> getRankingIdsIterator() {
    return (this.rankingIds == null) ? null : this.rankingIds.iterator();
  }

  public void addToRankingIds(long elem) {
    if (this.rankingIds == null) {
      this.rankingIds = new ArrayList<Long>();
    }
    this.rankingIds.add(elem);
  }

  public List<Long> getRankingIds() {
    return this.rankingIds;
  }

  public RankingConfigRequest setRankingIds(List<Long> rankingIds) {
    this.rankingIds = rankingIds;
    return this;
  }

  public void unsetRankingIds() {
    this.rankingIds = null;
  }

  /** Returns true if field rankingIds is set (has been assigned a value) and false otherwise */
  public boolean isSetRankingIds() {
    return this.rankingIds != null;
  }

  public void setRankingIdsIsSet(boolean value) {
    if (!value) {
      this.rankingIds = null;
    }
  }

  public long getCurrentDate() {
    return this.currentDate;
  }

  public RankingConfigRequest setCurrentDate(long currentDate) {
    this.currentDate = currentDate;
    setCurrentDateIsSet(true);
    return this;
  }

  public void unsetCurrentDate() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CURRENTDATE_ISSET_ID);
  }

  /** Returns true if field currentDate is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrentDate() {
    return EncodingUtils.testBit(__isset_bitfield, __CURRENTDATE_ISSET_ID);
  }

  public void setCurrentDateIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CURRENTDATE_ISSET_ID, value);
  }

  public long getCurPhaseId() {
    return this.curPhaseId;
  }

  public RankingConfigRequest setCurPhaseId(long curPhaseId) {
    this.curPhaseId = curPhaseId;
    setCurPhaseIdIsSet(true);
    return this;
  }

  public void unsetCurPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CURPHASEID_ISSET_ID);
  }

  /** Returns true if field curPhaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetCurPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __CURPHASEID_ISSET_ID);
  }

  public void setCurPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CURPHASEID_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public RankingConfigRequest setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case RANKING_IDS:
      if (value == null) {
        unsetRankingIds();
      } else {
        setRankingIds((List<Long>)value);
      }
      break;

    case CURRENT_DATE:
      if (value == null) {
        unsetCurrentDate();
      } else {
        setCurrentDate((Long)value);
      }
      break;

    case CUR_PHASE_ID:
      if (value == null) {
        unsetCurPhaseId();
      } else {
        setCurPhaseId((Long)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case RANKING_IDS:
      return getRankingIds();

    case CURRENT_DATE:
      return getCurrentDate();

    case CUR_PHASE_ID:
      return getCurPhaseId();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case RANKING_IDS:
      return isSetRankingIds();
    case CURRENT_DATE:
      return isSetCurrentDate();
    case CUR_PHASE_ID:
      return isSetCurPhaseId();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RankingConfigRequest)
      return this.equals((RankingConfigRequest)that);
    return false;
  }

  public boolean equals(RankingConfigRequest that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_rankingIds = true && this.isSetRankingIds();
    boolean that_present_rankingIds = true && that.isSetRankingIds();
    if (this_present_rankingIds || that_present_rankingIds) {
      if (!(this_present_rankingIds && that_present_rankingIds))
        return false;
      if (!this.rankingIds.equals(that.rankingIds))
        return false;
    }

    boolean this_present_currentDate = true;
    boolean that_present_currentDate = true;
    if (this_present_currentDate || that_present_currentDate) {
      if (!(this_present_currentDate && that_present_currentDate))
        return false;
      if (this.currentDate != that.currentDate)
        return false;
    }

    boolean this_present_curPhaseId = true;
    boolean that_present_curPhaseId = true;
    if (this_present_curPhaseId || that_present_curPhaseId) {
      if (!(this_present_curPhaseId && that_present_curPhaseId))
        return false;
      if (this.curPhaseId != that.curPhaseId)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_rankingIds = true && (isSetRankingIds());
    list.add(present_rankingIds);
    if (present_rankingIds)
      list.add(rankingIds);

    boolean present_currentDate = true;
    list.add(present_currentDate);
    if (present_currentDate)
      list.add(currentDate);

    boolean present_curPhaseId = true;
    list.add(present_curPhaseId);
    if (present_curPhaseId)
      list.add(curPhaseId);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(RankingConfigRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankingIds()).compareTo(other.isSetRankingIds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankingIds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankingIds, other.rankingIds);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurrentDate()).compareTo(other.isSetCurrentDate());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrentDate()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.currentDate, other.currentDate);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurPhaseId()).compareTo(other.isSetCurPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.curPhaseId, other.curPhaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RankingConfigRequest(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankingIds:");
    if (this.rankingIds == null) {
      sb.append("null");
    } else {
      sb.append(this.rankingIds);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("currentDate:");
    sb.append(this.currentDate);
    first = false;
    if (!first) sb.append(", ");
    sb.append("curPhaseId:");
    sb.append(this.curPhaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RankingConfigRequestStandardSchemeFactory implements SchemeFactory {
    public RankingConfigRequestStandardScheme getScheme() {
      return new RankingConfigRequestStandardScheme();
    }
  }

  private static class RankingConfigRequestStandardScheme extends StandardScheme<RankingConfigRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RankingConfigRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RANKING_IDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list66 = iprot.readListBegin();
                struct.rankingIds = new ArrayList<Long>(_list66.size);
                long _elem67;
                for (int _i68 = 0; _i68 < _list66.size; ++_i68)
                {
                  _elem67 = iprot.readI64();
                  struct.rankingIds.add(_elem67);
                }
                iprot.readListEnd();
              }
              struct.setRankingIdsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // CURRENT_DATE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.currentDate = iprot.readI64();
              struct.setCurrentDateIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CUR_PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.curPhaseId = iprot.readI64();
              struct.setCurPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map69 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map69.size);
                String _key70;
                String _val71;
                for (int _i72 = 0; _i72 < _map69.size; ++_i72)
                {
                  _key70 = iprot.readString();
                  _val71 = iprot.readString();
                  struct.extData.put(_key70, _val71);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RankingConfigRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      if (struct.rankingIds != null) {
        oprot.writeFieldBegin(RANKING_IDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.rankingIds.size()));
          for (long _iter73 : struct.rankingIds)
          {
            oprot.writeI64(_iter73);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CURRENT_DATE_FIELD_DESC);
      oprot.writeI64(struct.currentDate);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CUR_PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.curPhaseId);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter74 : struct.extData.entrySet())
          {
            oprot.writeString(_iter74.getKey());
            oprot.writeString(_iter74.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RankingConfigRequestTupleSchemeFactory implements SchemeFactory {
    public RankingConfigRequestTupleScheme getScheme() {
      return new RankingConfigRequestTupleScheme();
    }
  }

  private static class RankingConfigRequestTupleScheme extends TupleScheme<RankingConfigRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RankingConfigRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetRankingIds()) {
        optionals.set(1);
      }
      if (struct.isSetCurrentDate()) {
        optionals.set(2);
      }
      if (struct.isSetCurPhaseId()) {
        optionals.set(3);
      }
      if (struct.isSetExtData()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetRankingIds()) {
        {
          oprot.writeI32(struct.rankingIds.size());
          for (long _iter75 : struct.rankingIds)
          {
            oprot.writeI64(_iter75);
          }
        }
      }
      if (struct.isSetCurrentDate()) {
        oprot.writeI64(struct.currentDate);
      }
      if (struct.isSetCurPhaseId()) {
        oprot.writeI64(struct.curPhaseId);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter76 : struct.extData.entrySet())
          {
            oprot.writeString(_iter76.getKey());
            oprot.writeString(_iter76.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RankingConfigRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list77 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.rankingIds = new ArrayList<Long>(_list77.size);
          long _elem78;
          for (int _i79 = 0; _i79 < _list77.size; ++_i79)
          {
            _elem78 = iprot.readI64();
            struct.rankingIds.add(_elem78);
          }
        }
        struct.setRankingIdsIsSet(true);
      }
      if (incoming.get(2)) {
        struct.currentDate = iprot.readI64();
        struct.setCurrentDateIsSet(true);
      }
      if (incoming.get(3)) {
        struct.curPhaseId = iprot.readI64();
        struct.setCurPhaseIdIsSet(true);
      }
      if (incoming.get(4)) {
        {
          org.apache.thrift.protocol.TMap _map80 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map80.size);
          String _key81;
          String _val82;
          for (int _i83 = 0; _i83 < _map80.size; ++_i83)
          {
            _key81 = iprot.readString();
            _val82 = iprot.readString();
            struct.extData.put(_key81, _val82);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

