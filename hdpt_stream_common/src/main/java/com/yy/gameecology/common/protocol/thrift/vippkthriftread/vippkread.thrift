########################################
# 名字空间定义

namespace java com.yy.gameecology.common.protocol.thrift.vippkthriftread


########################################
# 基础service定义

service ReadBaseService
{
  /**
   * 简单的连接测试
   */
  void test_ping(),

  /**
   * 简单的版本查询
   */
  string get_version(),
}

##############################
## service接口

service VipPkThriftRead extends ReadBaseService
{

    i64 pkIdBySid(1:i64 sid, 2:i64 ssid, 3:i64 sendTime);
}
