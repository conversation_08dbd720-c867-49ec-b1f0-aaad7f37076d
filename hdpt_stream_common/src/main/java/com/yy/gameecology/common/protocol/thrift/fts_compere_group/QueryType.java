/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_compere_group;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum QueryType implements org.apache.thrift.TEnum {
  TypeCompere(0),
  TypeGuild(1),
  TypeChannel(2);

  private final int value;

  private QueryType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static QueryType findByValue(int value) { 
    switch (value) {
      case 0:
        return TypeCompere;
      case 1:
        return TypeGuild;
      case 2:
        return TypeChannel;
      default:
        return null;
    }
  }
}
