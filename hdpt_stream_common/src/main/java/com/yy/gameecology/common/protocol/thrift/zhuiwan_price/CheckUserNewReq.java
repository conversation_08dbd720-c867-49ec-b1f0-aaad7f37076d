/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_price;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-02-22")
public class CheckUserNewReq implements org.apache.thrift.TBase<CheckUserNewReq, CheckUserNewReq._Fields>, java.io.Serializable, Cloneable, Comparable<CheckUserNewReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CheckUserNewReq");

  private static final org.apache.thrift.protocol.TField UIDS_FIELD_DESC = new org.apache.thrift.protocol.TField("uids", org.apache.thrift.protocol.TType.LIST, (short)1);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new CheckUserNewReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new CheckUserNewReqTupleSchemeFactory());
  }

  public List<Long> uids; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    UIDS((short)1, "uids");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // UIDS
          return UIDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.UIDS, new org.apache.thrift.meta_data.FieldMetaData("uids", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CheckUserNewReq.class, metaDataMap);
  }

  public CheckUserNewReq() {
  }

  public CheckUserNewReq(
    List<Long> uids)
  {
    this();
    this.uids = uids;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CheckUserNewReq(CheckUserNewReq other) {
    if (other.isSetUids()) {
      List<Long> __this__uids = new ArrayList<Long>(other.uids);
      this.uids = __this__uids;
    }
  }

  public CheckUserNewReq deepCopy() {
    return new CheckUserNewReq(this);
  }

  @Override
  public void clear() {
    this.uids = null;
  }

  public int getUidsSize() {
    return (this.uids == null) ? 0 : this.uids.size();
  }

  public java.util.Iterator<Long> getUidsIterator() {
    return (this.uids == null) ? null : this.uids.iterator();
  }

  public void addToUids(long elem) {
    if (this.uids == null) {
      this.uids = new ArrayList<Long>();
    }
    this.uids.add(elem);
  }

  public List<Long> getUids() {
    return this.uids;
  }

  public CheckUserNewReq setUids(List<Long> uids) {
    this.uids = uids;
    return this;
  }

  public void unsetUids() {
    this.uids = null;
  }

  /** Returns true if field uids is set (has been assigned a value) and false otherwise */
  public boolean isSetUids() {
    return this.uids != null;
  }

  public void setUidsIsSet(boolean value) {
    if (!value) {
      this.uids = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case UIDS:
      if (value == null) {
        unsetUids();
      } else {
        setUids((List<Long>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case UIDS:
      return getUids();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case UIDS:
      return isSetUids();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof CheckUserNewReq)
      return this.equals((CheckUserNewReq)that);
    return false;
  }

  public boolean equals(CheckUserNewReq that) {
    if (that == null)
      return false;

    boolean this_present_uids = true && this.isSetUids();
    boolean that_present_uids = true && that.isSetUids();
    if (this_present_uids || that_present_uids) {
      if (!(this_present_uids && that_present_uids))
        return false;
      if (!this.uids.equals(that.uids))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_uids = true && (isSetUids());
    list.add(present_uids);
    if (present_uids)
      list.add(uids);

    return list.hashCode();
  }

  @Override
  public int compareTo(CheckUserNewReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetUids()).compareTo(other.isSetUids());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUids()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uids, other.uids);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("CheckUserNewReq(");
    boolean first = true;

    sb.append("uids:");
    if (this.uids == null) {
      sb.append("null");
    } else {
      sb.append(this.uids);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CheckUserNewReqStandardSchemeFactory implements SchemeFactory {
    public CheckUserNewReqStandardScheme getScheme() {
      return new CheckUserNewReqStandardScheme();
    }
  }

  private static class CheckUserNewReqStandardScheme extends StandardScheme<CheckUserNewReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CheckUserNewReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // UIDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list76 = iprot.readListBegin();
                struct.uids = new ArrayList<Long>(_list76.size);
                long _elem77;
                for (int _i78 = 0; _i78 < _list76.size; ++_i78)
                {
                  _elem77 = iprot.readI64();
                  struct.uids.add(_elem77);
                }
                iprot.readListEnd();
              }
              struct.setUidsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CheckUserNewReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.uids != null) {
        oprot.writeFieldBegin(UIDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.uids.size()));
          for (long _iter79 : struct.uids)
          {
            oprot.writeI64(_iter79);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CheckUserNewReqTupleSchemeFactory implements SchemeFactory {
    public CheckUserNewReqTupleScheme getScheme() {
      return new CheckUserNewReqTupleScheme();
    }
  }

  private static class CheckUserNewReqTupleScheme extends TupleScheme<CheckUserNewReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CheckUserNewReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetUids()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetUids()) {
        {
          oprot.writeI32(struct.uids.size());
          for (long _iter80 : struct.uids)
          {
            oprot.writeI64(_iter80);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CheckUserNewReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list81 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.uids = new ArrayList<Long>(_list81.size);
          long _elem82;
          for (int _i83 = 0; _i83 < _list81.size; ++_i83)
          {
            _elem82 = iprot.readI64();
            struct.uids.add(_elem82);
          }
        }
        struct.setUidsIsSet(true);
      }
    }
  }

}

