/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_room_manager;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-09-05")
public class AntiPoachingInfo implements org.apache.thrift.TBase<AntiPoachingInfo, AntiPoachingInfo._Fields>, java.io.Serializable, Cloneable, Comparable<AntiPoachingInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("AntiPoachingInfo");

  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField SSID_FIELD_DESC = new org.apache.thrift.protocol.TField("ssid", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField IS_EFFECTS_FIELD_DESC = new org.apache.thrift.protocol.TField("isEffects", org.apache.thrift.protocol.TType.BOOL, (short)3);
  private static final org.apache.thrift.protocol.TField IS_BROADCAST_FIELD_DESC = new org.apache.thrift.protocol.TField("isBroadcast", org.apache.thrift.protocol.TType.BOOL, (short)4);
  private static final org.apache.thrift.protocol.TField IS_BILLBOARD_FIELD_DESC = new org.apache.thrift.protocol.TField("isBillboard", org.apache.thrift.protocol.TType.BOOL, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new AntiPoachingInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new AntiPoachingInfoTupleSchemeFactory());
  }

  public long sid; // required
  public long ssid; // required
  public boolean isEffects; // required
  public boolean isBroadcast; // required
  public boolean isBillboard; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SID((short)1, "sid"),
    SSID((short)2, "ssid"),
    IS_EFFECTS((short)3, "isEffects"),
    IS_BROADCAST((short)4, "isBroadcast"),
    IS_BILLBOARD((short)5, "isBillboard");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SID
          return SID;
        case 2: // SSID
          return SSID;
        case 3: // IS_EFFECTS
          return IS_EFFECTS;
        case 4: // IS_BROADCAST
          return IS_BROADCAST;
        case 5: // IS_BILLBOARD
          return IS_BILLBOARD;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SID_ISSET_ID = 0;
  private static final int __SSID_ISSET_ID = 1;
  private static final int __ISEFFECTS_ISSET_ID = 2;
  private static final int __ISBROADCAST_ISSET_ID = 3;
  private static final int __ISBILLBOARD_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SSID, new org.apache.thrift.meta_data.FieldMetaData("ssid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.IS_EFFECTS, new org.apache.thrift.meta_data.FieldMetaData("isEffects", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.IS_BROADCAST, new org.apache.thrift.meta_data.FieldMetaData("isBroadcast", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.IS_BILLBOARD, new org.apache.thrift.meta_data.FieldMetaData("isBillboard", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(AntiPoachingInfo.class, metaDataMap);
  }

  public AntiPoachingInfo() {
  }

  public AntiPoachingInfo(
    long sid,
    long ssid,
    boolean isEffects,
    boolean isBroadcast,
    boolean isBillboard)
  {
    this();
    this.sid = sid;
    setSidIsSet(true);
    this.ssid = ssid;
    setSsidIsSet(true);
    this.isEffects = isEffects;
    setIsEffectsIsSet(true);
    this.isBroadcast = isBroadcast;
    setIsBroadcastIsSet(true);
    this.isBillboard = isBillboard;
    setIsBillboardIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public AntiPoachingInfo(AntiPoachingInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.sid = other.sid;
    this.ssid = other.ssid;
    this.isEffects = other.isEffects;
    this.isBroadcast = other.isBroadcast;
    this.isBillboard = other.isBillboard;
  }

  public AntiPoachingInfo deepCopy() {
    return new AntiPoachingInfo(this);
  }

  @Override
  public void clear() {
    setSidIsSet(false);
    this.sid = 0;
    setSsidIsSet(false);
    this.ssid = 0;
    setIsEffectsIsSet(false);
    this.isEffects = false;
    setIsBroadcastIsSet(false);
    this.isBroadcast = false;
    setIsBillboardIsSet(false);
    this.isBillboard = false;
  }

  public long getSid() {
    return this.sid;
  }

  public AntiPoachingInfo setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getSsid() {
    return this.ssid;
  }

  public AntiPoachingInfo setSsid(long ssid) {
    this.ssid = ssid;
    setSsidIsSet(true);
    return this;
  }

  public void unsetSsid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  /** Returns true if field ssid is set (has been assigned a value) and false otherwise */
  public boolean isSetSsid() {
    return EncodingUtils.testBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  public void setSsidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SSID_ISSET_ID, value);
  }

  public boolean isIsEffects() {
    return this.isEffects;
  }

  public AntiPoachingInfo setIsEffects(boolean isEffects) {
    this.isEffects = isEffects;
    setIsEffectsIsSet(true);
    return this;
  }

  public void unsetIsEffects() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISEFFECTS_ISSET_ID);
  }

  /** Returns true if field isEffects is set (has been assigned a value) and false otherwise */
  public boolean isSetIsEffects() {
    return EncodingUtils.testBit(__isset_bitfield, __ISEFFECTS_ISSET_ID);
  }

  public void setIsEffectsIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISEFFECTS_ISSET_ID, value);
  }

  public boolean isIsBroadcast() {
    return this.isBroadcast;
  }

  public AntiPoachingInfo setIsBroadcast(boolean isBroadcast) {
    this.isBroadcast = isBroadcast;
    setIsBroadcastIsSet(true);
    return this;
  }

  public void unsetIsBroadcast() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISBROADCAST_ISSET_ID);
  }

  /** Returns true if field isBroadcast is set (has been assigned a value) and false otherwise */
  public boolean isSetIsBroadcast() {
    return EncodingUtils.testBit(__isset_bitfield, __ISBROADCAST_ISSET_ID);
  }

  public void setIsBroadcastIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISBROADCAST_ISSET_ID, value);
  }

  public boolean isIsBillboard() {
    return this.isBillboard;
  }

  public AntiPoachingInfo setIsBillboard(boolean isBillboard) {
    this.isBillboard = isBillboard;
    setIsBillboardIsSet(true);
    return this;
  }

  public void unsetIsBillboard() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ISBILLBOARD_ISSET_ID);
  }

  /** Returns true if field isBillboard is set (has been assigned a value) and false otherwise */
  public boolean isSetIsBillboard() {
    return EncodingUtils.testBit(__isset_bitfield, __ISBILLBOARD_ISSET_ID);
  }

  public void setIsBillboardIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ISBILLBOARD_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case SSID:
      if (value == null) {
        unsetSsid();
      } else {
        setSsid((Long)value);
      }
      break;

    case IS_EFFECTS:
      if (value == null) {
        unsetIsEffects();
      } else {
        setIsEffects((Boolean)value);
      }
      break;

    case IS_BROADCAST:
      if (value == null) {
        unsetIsBroadcast();
      } else {
        setIsBroadcast((Boolean)value);
      }
      break;

    case IS_BILLBOARD:
      if (value == null) {
        unsetIsBillboard();
      } else {
        setIsBillboard((Boolean)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case SID:
      return getSid();

    case SSID:
      return getSsid();

    case IS_EFFECTS:
      return isIsEffects();

    case IS_BROADCAST:
      return isIsBroadcast();

    case IS_BILLBOARD:
      return isIsBillboard();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SID:
      return isSetSid();
    case SSID:
      return isSetSsid();
    case IS_EFFECTS:
      return isSetIsEffects();
    case IS_BROADCAST:
      return isSetIsBroadcast();
    case IS_BILLBOARD:
      return isSetIsBillboard();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof AntiPoachingInfo)
      return this.equals((AntiPoachingInfo)that);
    return false;
  }

  public boolean equals(AntiPoachingInfo that) {
    if (that == null)
      return false;

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_ssid = true;
    boolean that_present_ssid = true;
    if (this_present_ssid || that_present_ssid) {
      if (!(this_present_ssid && that_present_ssid))
        return false;
      if (this.ssid != that.ssid)
        return false;
    }

    boolean this_present_isEffects = true;
    boolean that_present_isEffects = true;
    if (this_present_isEffects || that_present_isEffects) {
      if (!(this_present_isEffects && that_present_isEffects))
        return false;
      if (this.isEffects != that.isEffects)
        return false;
    }

    boolean this_present_isBroadcast = true;
    boolean that_present_isBroadcast = true;
    if (this_present_isBroadcast || that_present_isBroadcast) {
      if (!(this_present_isBroadcast && that_present_isBroadcast))
        return false;
      if (this.isBroadcast != that.isBroadcast)
        return false;
    }

    boolean this_present_isBillboard = true;
    boolean that_present_isBillboard = true;
    if (this_present_isBillboard || that_present_isBillboard) {
      if (!(this_present_isBillboard && that_present_isBillboard))
        return false;
      if (this.isBillboard != that.isBillboard)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_ssid = true;
    list.add(present_ssid);
    if (present_ssid)
      list.add(ssid);

    boolean present_isEffects = true;
    list.add(present_isEffects);
    if (present_isEffects)
      list.add(isEffects);

    boolean present_isBroadcast = true;
    list.add(present_isBroadcast);
    if (present_isBroadcast)
      list.add(isBroadcast);

    boolean present_isBillboard = true;
    list.add(present_isBillboard);
    if (present_isBillboard)
      list.add(isBillboard);

    return list.hashCode();
  }

  @Override
  public int compareTo(AntiPoachingInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSsid()).compareTo(other.isSetSsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssid, other.ssid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsEffects()).compareTo(other.isSetIsEffects());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsEffects()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isEffects, other.isEffects);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsBroadcast()).compareTo(other.isSetIsBroadcast());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsBroadcast()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isBroadcast, other.isBroadcast);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIsBillboard()).compareTo(other.isSetIsBillboard());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIsBillboard()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.isBillboard, other.isBillboard);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("AntiPoachingInfo(");
    boolean first = true;

    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ssid:");
    sb.append(this.ssid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("isEffects:");
    sb.append(this.isEffects);
    first = false;
    if (!first) sb.append(", ");
    sb.append("isBroadcast:");
    sb.append(this.isBroadcast);
    first = false;
    if (!first) sb.append(", ");
    sb.append("isBillboard:");
    sb.append(this.isBillboard);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class AntiPoachingInfoStandardSchemeFactory implements SchemeFactory {
    public AntiPoachingInfoStandardScheme getScheme() {
      return new AntiPoachingInfoStandardScheme();
    }
  }

  private static class AntiPoachingInfoStandardScheme extends StandardScheme<AntiPoachingInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, AntiPoachingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SSID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ssid = iprot.readI64();
              struct.setSsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // IS_EFFECTS
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isEffects = iprot.readBool();
              struct.setIsEffectsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // IS_BROADCAST
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isBroadcast = iprot.readBool();
              struct.setIsBroadcastIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // IS_BILLBOARD
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.isBillboard = iprot.readBool();
              struct.setIsBillboardIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, AntiPoachingInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SSID_FIELD_DESC);
      oprot.writeI64(struct.ssid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(IS_EFFECTS_FIELD_DESC);
      oprot.writeBool(struct.isEffects);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(IS_BROADCAST_FIELD_DESC);
      oprot.writeBool(struct.isBroadcast);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(IS_BILLBOARD_FIELD_DESC);
      oprot.writeBool(struct.isBillboard);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class AntiPoachingInfoTupleSchemeFactory implements SchemeFactory {
    public AntiPoachingInfoTupleScheme getScheme() {
      return new AntiPoachingInfoTupleScheme();
    }
  }

  private static class AntiPoachingInfoTupleScheme extends TupleScheme<AntiPoachingInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, AntiPoachingInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetSid()) {
        optionals.set(0);
      }
      if (struct.isSetSsid()) {
        optionals.set(1);
      }
      if (struct.isSetIsEffects()) {
        optionals.set(2);
      }
      if (struct.isSetIsBroadcast()) {
        optionals.set(3);
      }
      if (struct.isSetIsBillboard()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetSsid()) {
        oprot.writeI64(struct.ssid);
      }
      if (struct.isSetIsEffects()) {
        oprot.writeBool(struct.isEffects);
      }
      if (struct.isSetIsBroadcast()) {
        oprot.writeBool(struct.isBroadcast);
      }
      if (struct.isSetIsBillboard()) {
        oprot.writeBool(struct.isBillboard);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, AntiPoachingInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.ssid = iprot.readI64();
        struct.setSsidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.isEffects = iprot.readBool();
        struct.setIsEffectsIsSet(true);
      }
      if (incoming.get(3)) {
        struct.isBroadcast = iprot.readBool();
        struct.setIsBroadcastIsSet(true);
      }
      if (incoming.get(4)) {
        struct.isBillboard = iprot.readBool();
        struct.setIsBillboardIsSet(true);
      }
    }
  }

}

