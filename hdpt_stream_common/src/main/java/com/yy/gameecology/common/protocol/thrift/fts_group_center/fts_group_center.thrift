 # 交友 刘志斌 提供/2021-10-15

namespace java com.yy.gameecology.common.protocol.thrift.fts_group_center

// s2s name
// 测试：fts_group_center_test
// 生产：fts_group_center

struct CommonRet
{
    1:i64 code; // 0-success
    2:string message;
}

enum Source {
    Unknown = 0;
    SourceJY = 500;
    SourcePK = 600;
    SourceBaby = 400;
    SourceZW  = 800;
    SourcePW  = 900
}

/**
* 这个可以随便加，但是对应到boss后台那里配置角色ID的时候，需要配置对应的数字，
* 比如下面的新加的家族，那么就要配置角色ID为5
**/
enum QueryType {
    TypeCompere = 0; // 主持，默认
    TypeGuild   = 1; // 公会
    TypeChannel = 2; // 厅
    TypeTing    = 3; // 厅天团
    TypeGroup   = 4; // 陪玩业务-团（陪玩的一种特殊角色）
    TypeFamily  = 5; // 语音房家族
    TypeRoom    = 6; // 语音房房间
}

enum Visibility {
    Normal = 0; // 正式分组
    Gray = 1; // 灰度分组
}

struct CompereGroup
{
    1:string group; // 分组结果
    2:i64 version; // 版本号
    3:i64 cover; // 扩展字段
}

struct GroupResult
{
    1:string group; // 分组结果
}

struct QueryCompereGroupReq
{
    1:i64 r_id; // 规则ID 或 活动ID
    2:list<i64> u_id_list; // 请求的主持
    15:map<string, string> expand;
}

struct QueryCompereGroupResp
{
    1:CommonRet header;
    2:map<i64, CompereGroup> group_map;
    15:map<string, string> expand;
}

struct QueryGroupReq
{
    1:i64 r_id; // 规则ID
    2:list<string> members; // 请求的主持 厅-sid_ssid
    3:QueryType query_type; // 请求类型
    4:Source source; // 业务源
    5:Visibility visible; // 0-普通数据源 1-灰度数据源
    15:map<string, string> expand; // map{queryAll: 1}
}

struct BatchQueryGroupReq
{
    1:list<QueryGroupReq> request_list;
}

struct BatchQueryGroupResp
{
    1:CommonRet header;
    2:list<QueryGroupResp> response_list;
}

struct QueryGroupResp
{
    1:CommonRet header;
    2:map<string, GroupResult> group_map;
    15:map<string, string> expand;
}

service FtsGroupService {
    // 测试链接
    void ping();

    // 查询分组
    QueryGroupResp QueryGroup(1:QueryGroupReq req);

    // 批量查询分组
    BatchQueryGroupResp BatchQueryGroup(1:BatchQueryGroupReq req);
}
