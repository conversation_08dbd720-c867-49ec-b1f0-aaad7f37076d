/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_price;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-02-22")
public class PopupMsg implements org.apache.thrift.TBase<PopupMsg, PopupMsg._Fields>, java.io.Serializable, Cloneable, Comparable<PopupMsg> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PopupMsg");

  private static final org.apache.thrift.protocol.TField PLATFORM_FIELD_DESC = new org.apache.thrift.protocol.TField("platform", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField ICON_FIELD_DESC = new org.apache.thrift.protocol.TField("icon", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField TITLE_FIELD_DESC = new org.apache.thrift.protocol.TField("title", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField CONTENT_FIELD_DESC = new org.apache.thrift.protocol.TField("content", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField LINK_FIELD_DESC = new org.apache.thrift.protocol.TField("link", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField BACKGROUND_FIELD_DESC = new org.apache.thrift.protocol.TField("background", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField EXTEND_FIELD_DESC = new org.apache.thrift.protocol.TField("extend", org.apache.thrift.protocol.TType.MAP, (short)7);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new PopupMsgStandardSchemeFactory());
    schemes.put(TupleScheme.class, new PopupMsgTupleSchemeFactory());
  }

  public int platform; // required
  public String icon; // required
  public String title; // required
  public String content; // required
  public String link; // required
  public String background; // required
  public Map<String,String> extend; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    PLATFORM((short)1, "platform"),
    ICON((short)2, "icon"),
    TITLE((short)3, "title"),
    CONTENT((short)4, "content"),
    LINK((short)5, "link"),
    BACKGROUND((short)6, "background"),
    EXTEND((short)7, "extend");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // PLATFORM
          return PLATFORM;
        case 2: // ICON
          return ICON;
        case 3: // TITLE
          return TITLE;
        case 4: // CONTENT
          return CONTENT;
        case 5: // LINK
          return LINK;
        case 6: // BACKGROUND
          return BACKGROUND;
        case 7: // EXTEND
          return EXTEND;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __PLATFORM_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.PLATFORM, new org.apache.thrift.meta_data.FieldMetaData("platform", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ICON, new org.apache.thrift.meta_data.FieldMetaData("icon", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TITLE, new org.apache.thrift.meta_data.FieldMetaData("title", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CONTENT, new org.apache.thrift.meta_data.FieldMetaData("content", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.LINK, new org.apache.thrift.meta_data.FieldMetaData("link", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BACKGROUND, new org.apache.thrift.meta_data.FieldMetaData("background", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXTEND, new org.apache.thrift.meta_data.FieldMetaData("extend", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PopupMsg.class, metaDataMap);
  }

  public PopupMsg() {
  }

  public PopupMsg(
    int platform,
    String icon,
    String title,
    String content,
    String link,
    String background,
    Map<String,String> extend)
  {
    this();
    this.platform = platform;
    setPlatformIsSet(true);
    this.icon = icon;
    this.title = title;
    this.content = content;
    this.link = link;
    this.background = background;
    this.extend = extend;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PopupMsg(PopupMsg other) {
    __isset_bitfield = other.__isset_bitfield;
    this.platform = other.platform;
    if (other.isSetIcon()) {
      this.icon = other.icon;
    }
    if (other.isSetTitle()) {
      this.title = other.title;
    }
    if (other.isSetContent()) {
      this.content = other.content;
    }
    if (other.isSetLink()) {
      this.link = other.link;
    }
    if (other.isSetBackground()) {
      this.background = other.background;
    }
    if (other.isSetExtend()) {
      Map<String,String> __this__extend = new HashMap<String,String>(other.extend);
      this.extend = __this__extend;
    }
  }

  public PopupMsg deepCopy() {
    return new PopupMsg(this);
  }

  @Override
  public void clear() {
    setPlatformIsSet(false);
    this.platform = 0;
    this.icon = null;
    this.title = null;
    this.content = null;
    this.link = null;
    this.background = null;
    this.extend = null;
  }

  public int getPlatform() {
    return this.platform;
  }

  public PopupMsg setPlatform(int platform) {
    this.platform = platform;
    setPlatformIsSet(true);
    return this;
  }

  public void unsetPlatform() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PLATFORM_ISSET_ID);
  }

  /** Returns true if field platform is set (has been assigned a value) and false otherwise */
  public boolean isSetPlatform() {
    return EncodingUtils.testBit(__isset_bitfield, __PLATFORM_ISSET_ID);
  }

  public void setPlatformIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PLATFORM_ISSET_ID, value);
  }

  public String getIcon() {
    return this.icon;
  }

  public PopupMsg setIcon(String icon) {
    this.icon = icon;
    return this;
  }

  public void unsetIcon() {
    this.icon = null;
  }

  /** Returns true if field icon is set (has been assigned a value) and false otherwise */
  public boolean isSetIcon() {
    return this.icon != null;
  }

  public void setIconIsSet(boolean value) {
    if (!value) {
      this.icon = null;
    }
  }

  public String getTitle() {
    return this.title;
  }

  public PopupMsg setTitle(String title) {
    this.title = title;
    return this;
  }

  public void unsetTitle() {
    this.title = null;
  }

  /** Returns true if field title is set (has been assigned a value) and false otherwise */
  public boolean isSetTitle() {
    return this.title != null;
  }

  public void setTitleIsSet(boolean value) {
    if (!value) {
      this.title = null;
    }
  }

  public String getContent() {
    return this.content;
  }

  public PopupMsg setContent(String content) {
    this.content = content;
    return this;
  }

  public void unsetContent() {
    this.content = null;
  }

  /** Returns true if field content is set (has been assigned a value) and false otherwise */
  public boolean isSetContent() {
    return this.content != null;
  }

  public void setContentIsSet(boolean value) {
    if (!value) {
      this.content = null;
    }
  }

  public String getLink() {
    return this.link;
  }

  public PopupMsg setLink(String link) {
    this.link = link;
    return this;
  }

  public void unsetLink() {
    this.link = null;
  }

  /** Returns true if field link is set (has been assigned a value) and false otherwise */
  public boolean isSetLink() {
    return this.link != null;
  }

  public void setLinkIsSet(boolean value) {
    if (!value) {
      this.link = null;
    }
  }

  public String getBackground() {
    return this.background;
  }

  public PopupMsg setBackground(String background) {
    this.background = background;
    return this;
  }

  public void unsetBackground() {
    this.background = null;
  }

  /** Returns true if field background is set (has been assigned a value) and false otherwise */
  public boolean isSetBackground() {
    return this.background != null;
  }

  public void setBackgroundIsSet(boolean value) {
    if (!value) {
      this.background = null;
    }
  }

  public int getExtendSize() {
    return (this.extend == null) ? 0 : this.extend.size();
  }

  public void putToExtend(String key, String val) {
    if (this.extend == null) {
      this.extend = new HashMap<String,String>();
    }
    this.extend.put(key, val);
  }

  public Map<String,String> getExtend() {
    return this.extend;
  }

  public PopupMsg setExtend(Map<String,String> extend) {
    this.extend = extend;
    return this;
  }

  public void unsetExtend() {
    this.extend = null;
  }

  /** Returns true if field extend is set (has been assigned a value) and false otherwise */
  public boolean isSetExtend() {
    return this.extend != null;
  }

  public void setExtendIsSet(boolean value) {
    if (!value) {
      this.extend = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case PLATFORM:
      if (value == null) {
        unsetPlatform();
      } else {
        setPlatform((Integer)value);
      }
      break;

    case ICON:
      if (value == null) {
        unsetIcon();
      } else {
        setIcon((String)value);
      }
      break;

    case TITLE:
      if (value == null) {
        unsetTitle();
      } else {
        setTitle((String)value);
      }
      break;

    case CONTENT:
      if (value == null) {
        unsetContent();
      } else {
        setContent((String)value);
      }
      break;

    case LINK:
      if (value == null) {
        unsetLink();
      } else {
        setLink((String)value);
      }
      break;

    case BACKGROUND:
      if (value == null) {
        unsetBackground();
      } else {
        setBackground((String)value);
      }
      break;

    case EXTEND:
      if (value == null) {
        unsetExtend();
      } else {
        setExtend((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case PLATFORM:
      return getPlatform();

    case ICON:
      return getIcon();

    case TITLE:
      return getTitle();

    case CONTENT:
      return getContent();

    case LINK:
      return getLink();

    case BACKGROUND:
      return getBackground();

    case EXTEND:
      return getExtend();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case PLATFORM:
      return isSetPlatform();
    case ICON:
      return isSetIcon();
    case TITLE:
      return isSetTitle();
    case CONTENT:
      return isSetContent();
    case LINK:
      return isSetLink();
    case BACKGROUND:
      return isSetBackground();
    case EXTEND:
      return isSetExtend();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof PopupMsg)
      return this.equals((PopupMsg)that);
    return false;
  }

  public boolean equals(PopupMsg that) {
    if (that == null)
      return false;

    boolean this_present_platform = true;
    boolean that_present_platform = true;
    if (this_present_platform || that_present_platform) {
      if (!(this_present_platform && that_present_platform))
        return false;
      if (this.platform != that.platform)
        return false;
    }

    boolean this_present_icon = true && this.isSetIcon();
    boolean that_present_icon = true && that.isSetIcon();
    if (this_present_icon || that_present_icon) {
      if (!(this_present_icon && that_present_icon))
        return false;
      if (!this.icon.equals(that.icon))
        return false;
    }

    boolean this_present_title = true && this.isSetTitle();
    boolean that_present_title = true && that.isSetTitle();
    if (this_present_title || that_present_title) {
      if (!(this_present_title && that_present_title))
        return false;
      if (!this.title.equals(that.title))
        return false;
    }

    boolean this_present_content = true && this.isSetContent();
    boolean that_present_content = true && that.isSetContent();
    if (this_present_content || that_present_content) {
      if (!(this_present_content && that_present_content))
        return false;
      if (!this.content.equals(that.content))
        return false;
    }

    boolean this_present_link = true && this.isSetLink();
    boolean that_present_link = true && that.isSetLink();
    if (this_present_link || that_present_link) {
      if (!(this_present_link && that_present_link))
        return false;
      if (!this.link.equals(that.link))
        return false;
    }

    boolean this_present_background = true && this.isSetBackground();
    boolean that_present_background = true && that.isSetBackground();
    if (this_present_background || that_present_background) {
      if (!(this_present_background && that_present_background))
        return false;
      if (!this.background.equals(that.background))
        return false;
    }

    boolean this_present_extend = true && this.isSetExtend();
    boolean that_present_extend = true && that.isSetExtend();
    if (this_present_extend || that_present_extend) {
      if (!(this_present_extend && that_present_extend))
        return false;
      if (!this.extend.equals(that.extend))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_platform = true;
    list.add(present_platform);
    if (present_platform)
      list.add(platform);

    boolean present_icon = true && (isSetIcon());
    list.add(present_icon);
    if (present_icon)
      list.add(icon);

    boolean present_title = true && (isSetTitle());
    list.add(present_title);
    if (present_title)
      list.add(title);

    boolean present_content = true && (isSetContent());
    list.add(present_content);
    if (present_content)
      list.add(content);

    boolean present_link = true && (isSetLink());
    list.add(present_link);
    if (present_link)
      list.add(link);

    boolean present_background = true && (isSetBackground());
    list.add(present_background);
    if (present_background)
      list.add(background);

    boolean present_extend = true && (isSetExtend());
    list.add(present_extend);
    if (present_extend)
      list.add(extend);

    return list.hashCode();
  }

  @Override
  public int compareTo(PopupMsg other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetPlatform()).compareTo(other.isSetPlatform());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPlatform()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.platform, other.platform);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIcon()).compareTo(other.isSetIcon());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIcon()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.icon, other.icon);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTitle()).compareTo(other.isSetTitle());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTitle()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.title, other.title);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetContent()).compareTo(other.isSetContent());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetContent()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.content, other.content);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLink()).compareTo(other.isSetLink());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLink()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.link, other.link);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBackground()).compareTo(other.isSetBackground());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBackground()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.background, other.background);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtend()).compareTo(other.isSetExtend());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtend()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extend, other.extend);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("PopupMsg(");
    boolean first = true;

    sb.append("platform:");
    sb.append(this.platform);
    first = false;
    if (!first) sb.append(", ");
    sb.append("icon:");
    if (this.icon == null) {
      sb.append("null");
    } else {
      sb.append(this.icon);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("title:");
    if (this.title == null) {
      sb.append("null");
    } else {
      sb.append(this.title);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("content:");
    if (this.content == null) {
      sb.append("null");
    } else {
      sb.append(this.content);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("link:");
    if (this.link == null) {
      sb.append("null");
    } else {
      sb.append(this.link);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("background:");
    if (this.background == null) {
      sb.append("null");
    } else {
      sb.append(this.background);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extend:");
    if (this.extend == null) {
      sb.append("null");
    } else {
      sb.append(this.extend);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PopupMsgStandardSchemeFactory implements SchemeFactory {
    public PopupMsgStandardScheme getScheme() {
      return new PopupMsgStandardScheme();
    }
  }

  private static class PopupMsgStandardScheme extends StandardScheme<PopupMsg> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PopupMsg struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // PLATFORM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.platform = iprot.readI32();
              struct.setPlatformIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ICON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.icon = iprot.readString();
              struct.setIconIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // TITLE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.title = iprot.readString();
              struct.setTitleIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CONTENT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.content = iprot.readString();
              struct.setContentIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // LINK
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.link = iprot.readString();
              struct.setLinkIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // BACKGROUND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.background = iprot.readString();
              struct.setBackgroundIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // EXTEND
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map30 = iprot.readMapBegin();
                struct.extend = new HashMap<String,String>(2*_map30.size);
                String _key31;
                String _val32;
                for (int _i33 = 0; _i33 < _map30.size; ++_i33)
                {
                  _key31 = iprot.readString();
                  _val32 = iprot.readString();
                  struct.extend.put(_key31, _val32);
                }
                iprot.readMapEnd();
              }
              struct.setExtendIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PopupMsg struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(PLATFORM_FIELD_DESC);
      oprot.writeI32(struct.platform);
      oprot.writeFieldEnd();
      if (struct.icon != null) {
        oprot.writeFieldBegin(ICON_FIELD_DESC);
        oprot.writeString(struct.icon);
        oprot.writeFieldEnd();
      }
      if (struct.title != null) {
        oprot.writeFieldBegin(TITLE_FIELD_DESC);
        oprot.writeString(struct.title);
        oprot.writeFieldEnd();
      }
      if (struct.content != null) {
        oprot.writeFieldBegin(CONTENT_FIELD_DESC);
        oprot.writeString(struct.content);
        oprot.writeFieldEnd();
      }
      if (struct.link != null) {
        oprot.writeFieldBegin(LINK_FIELD_DESC);
        oprot.writeString(struct.link);
        oprot.writeFieldEnd();
      }
      if (struct.background != null) {
        oprot.writeFieldBegin(BACKGROUND_FIELD_DESC);
        oprot.writeString(struct.background);
        oprot.writeFieldEnd();
      }
      if (struct.extend != null) {
        oprot.writeFieldBegin(EXTEND_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extend.size()));
          for (Map.Entry<String, String> _iter34 : struct.extend.entrySet())
          {
            oprot.writeString(_iter34.getKey());
            oprot.writeString(_iter34.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PopupMsgTupleSchemeFactory implements SchemeFactory {
    public PopupMsgTupleScheme getScheme() {
      return new PopupMsgTupleScheme();
    }
  }

  private static class PopupMsgTupleScheme extends TupleScheme<PopupMsg> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PopupMsg struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetPlatform()) {
        optionals.set(0);
      }
      if (struct.isSetIcon()) {
        optionals.set(1);
      }
      if (struct.isSetTitle()) {
        optionals.set(2);
      }
      if (struct.isSetContent()) {
        optionals.set(3);
      }
      if (struct.isSetLink()) {
        optionals.set(4);
      }
      if (struct.isSetBackground()) {
        optionals.set(5);
      }
      if (struct.isSetExtend()) {
        optionals.set(6);
      }
      oprot.writeBitSet(optionals, 7);
      if (struct.isSetPlatform()) {
        oprot.writeI32(struct.platform);
      }
      if (struct.isSetIcon()) {
        oprot.writeString(struct.icon);
      }
      if (struct.isSetTitle()) {
        oprot.writeString(struct.title);
      }
      if (struct.isSetContent()) {
        oprot.writeString(struct.content);
      }
      if (struct.isSetLink()) {
        oprot.writeString(struct.link);
      }
      if (struct.isSetBackground()) {
        oprot.writeString(struct.background);
      }
      if (struct.isSetExtend()) {
        {
          oprot.writeI32(struct.extend.size());
          for (Map.Entry<String, String> _iter35 : struct.extend.entrySet())
          {
            oprot.writeString(_iter35.getKey());
            oprot.writeString(_iter35.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PopupMsg struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(7);
      if (incoming.get(0)) {
        struct.platform = iprot.readI32();
        struct.setPlatformIsSet(true);
      }
      if (incoming.get(1)) {
        struct.icon = iprot.readString();
        struct.setIconIsSet(true);
      }
      if (incoming.get(2)) {
        struct.title = iprot.readString();
        struct.setTitleIsSet(true);
      }
      if (incoming.get(3)) {
        struct.content = iprot.readString();
        struct.setContentIsSet(true);
      }
      if (incoming.get(4)) {
        struct.link = iprot.readString();
        struct.setLinkIsSet(true);
      }
      if (incoming.get(5)) {
        struct.background = iprot.readString();
        struct.setBackgroundIsSet(true);
      }
      if (incoming.get(6)) {
        {
          org.apache.thrift.protocol.TMap _map36 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extend = new HashMap<String,String>(2*_map36.size);
          String _key37;
          String _val38;
          for (int _i39 = 0; _i39 < _map36.size; ++_i39)
          {
            _key37 = iprot.readString();
            _val38 = iprot.readString();
            struct.extend.put(_key37, _val38);
          }
        }
        struct.setExtendIsSet(true);
      }
    }
  }

}

