/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class QueryMemberRequest implements org.apache.thrift.TBase<QueryMemberRequest, QueryMemberRequest._Fields>, java.io.Serializable, Cloneable, Comparable<QueryMemberRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryMemberRequest");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RANK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rankId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField MEMBER_ID_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("memberIdMap", org.apache.thrift.protocol.TType.MAP, (short)3);
  private static final org.apache.thrift.protocol.TField EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("ext", org.apache.thrift.protocol.TType.MAP, (short)4);
  private static final org.apache.thrift.protocol.TField SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("sign", org.apache.thrift.protocol.TType.STRING, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryMemberRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryMemberRequestTupleSchemeFactory());
  }

  public long actId; // required
  public long rankId; // required
  public Map<String,List<String>> memberIdMap; // required
  public Map<String,String> ext; // required
  public String sign; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    RANK_ID((short)2, "rankId"),
    MEMBER_ID_MAP((short)3, "memberIdMap"),
    EXT((short)4, "ext"),
    SIGN((short)5, "sign");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // RANK_ID
          return RANK_ID;
        case 3: // MEMBER_ID_MAP
          return MEMBER_ID_MAP;
        case 4: // EXT
          return EXT;
        case 5: // SIGN
          return SIGN;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __RANKID_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANK_ID, new org.apache.thrift.meta_data.FieldMetaData("rankId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MEMBER_ID_MAP, new org.apache.thrift.meta_data.FieldMetaData("memberIdMap", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
                new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)))));
    tmpMap.put(_Fields.EXT, new org.apache.thrift.meta_data.FieldMetaData("ext", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.SIGN, new org.apache.thrift.meta_data.FieldMetaData("sign", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryMemberRequest.class, metaDataMap);
  }

  public QueryMemberRequest() {
  }

  public QueryMemberRequest(
    long actId,
    long rankId,
    Map<String,List<String>> memberIdMap,
    Map<String,String> ext,
    String sign)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.rankId = rankId;
    setRankIdIsSet(true);
    this.memberIdMap = memberIdMap;
    this.ext = ext;
    this.sign = sign;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryMemberRequest(QueryMemberRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    this.rankId = other.rankId;
    if (other.isSetMemberIdMap()) {
      Map<String,List<String>> __this__memberIdMap = new HashMap<String,List<String>>(other.memberIdMap.size());
      for (Map.Entry<String, List<String>> other_element : other.memberIdMap.entrySet()) {

        String other_element_key = other_element.getKey();
        List<String> other_element_value = other_element.getValue();

        String __this__memberIdMap_copy_key = other_element_key;

        List<String> __this__memberIdMap_copy_value = new ArrayList<String>(other_element_value);

        __this__memberIdMap.put(__this__memberIdMap_copy_key, __this__memberIdMap_copy_value);
      }
      this.memberIdMap = __this__memberIdMap;
    }
    if (other.isSetExt()) {
      Map<String,String> __this__ext = new HashMap<String,String>(other.ext);
      this.ext = __this__ext;
    }
    if (other.isSetSign()) {
      this.sign = other.sign;
    }
  }

  public QueryMemberRequest deepCopy() {
    return new QueryMemberRequest(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    setRankIdIsSet(false);
    this.rankId = 0;
    this.memberIdMap = null;
    this.ext = null;
    this.sign = null;
  }

  public long getActId() {
    return this.actId;
  }

  public QueryMemberRequest setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getRankId() {
    return this.rankId;
  }

  public QueryMemberRequest setRankId(long rankId) {
    this.rankId = rankId;
    setRankIdIsSet(true);
    return this;
  }

  public void unsetRankId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  /** Returns true if field rankId is set (has been assigned a value) and false otherwise */
  public boolean isSetRankId() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  public void setRankIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKID_ISSET_ID, value);
  }

  public int getMemberIdMapSize() {
    return (this.memberIdMap == null) ? 0 : this.memberIdMap.size();
  }

  public void putToMemberIdMap(String key, List<String> val) {
    if (this.memberIdMap == null) {
      this.memberIdMap = new HashMap<String,List<String>>();
    }
    this.memberIdMap.put(key, val);
  }

  public Map<String,List<String>> getMemberIdMap() {
    return this.memberIdMap;
  }

  public QueryMemberRequest setMemberIdMap(Map<String,List<String>> memberIdMap) {
    this.memberIdMap = memberIdMap;
    return this;
  }

  public void unsetMemberIdMap() {
    this.memberIdMap = null;
  }

  /** Returns true if field memberIdMap is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberIdMap() {
    return this.memberIdMap != null;
  }

  public void setMemberIdMapIsSet(boolean value) {
    if (!value) {
      this.memberIdMap = null;
    }
  }

  public int getExtSize() {
    return (this.ext == null) ? 0 : this.ext.size();
  }

  public void putToExt(String key, String val) {
    if (this.ext == null) {
      this.ext = new HashMap<String,String>();
    }
    this.ext.put(key, val);
  }

  public Map<String,String> getExt() {
    return this.ext;
  }

  public QueryMemberRequest setExt(Map<String,String> ext) {
    this.ext = ext;
    return this;
  }

  public void unsetExt() {
    this.ext = null;
  }

  /** Returns true if field ext is set (has been assigned a value) and false otherwise */
  public boolean isSetExt() {
    return this.ext != null;
  }

  public void setExtIsSet(boolean value) {
    if (!value) {
      this.ext = null;
    }
  }

  public String getSign() {
    return this.sign;
  }

  public QueryMemberRequest setSign(String sign) {
    this.sign = sign;
    return this;
  }

  public void unsetSign() {
    this.sign = null;
  }

  /** Returns true if field sign is set (has been assigned a value) and false otherwise */
  public boolean isSetSign() {
    return this.sign != null;
  }

  public void setSignIsSet(boolean value) {
    if (!value) {
      this.sign = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case RANK_ID:
      if (value == null) {
        unsetRankId();
      } else {
        setRankId((Long)value);
      }
      break;

    case MEMBER_ID_MAP:
      if (value == null) {
        unsetMemberIdMap();
      } else {
        setMemberIdMap((Map<String,List<String>>)value);
      }
      break;

    case EXT:
      if (value == null) {
        unsetExt();
      } else {
        setExt((Map<String,String>)value);
      }
      break;

    case SIGN:
      if (value == null) {
        unsetSign();
      } else {
        setSign((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case RANK_ID:
      return getRankId();

    case MEMBER_ID_MAP:
      return getMemberIdMap();

    case EXT:
      return getExt();

    case SIGN:
      return getSign();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case RANK_ID:
      return isSetRankId();
    case MEMBER_ID_MAP:
      return isSetMemberIdMap();
    case EXT:
      return isSetExt();
    case SIGN:
      return isSetSign();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryMemberRequest)
      return this.equals((QueryMemberRequest)that);
    return false;
  }

  public boolean equals(QueryMemberRequest that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_rankId = true;
    boolean that_present_rankId = true;
    if (this_present_rankId || that_present_rankId) {
      if (!(this_present_rankId && that_present_rankId))
        return false;
      if (this.rankId != that.rankId)
        return false;
    }

    boolean this_present_memberIdMap = true && this.isSetMemberIdMap();
    boolean that_present_memberIdMap = true && that.isSetMemberIdMap();
    if (this_present_memberIdMap || that_present_memberIdMap) {
      if (!(this_present_memberIdMap && that_present_memberIdMap))
        return false;
      if (!this.memberIdMap.equals(that.memberIdMap))
        return false;
    }

    boolean this_present_ext = true && this.isSetExt();
    boolean that_present_ext = true && that.isSetExt();
    if (this_present_ext || that_present_ext) {
      if (!(this_present_ext && that_present_ext))
        return false;
      if (!this.ext.equals(that.ext))
        return false;
    }

    boolean this_present_sign = true && this.isSetSign();
    boolean that_present_sign = true && that.isSetSign();
    if (this_present_sign || that_present_sign) {
      if (!(this_present_sign && that_present_sign))
        return false;
      if (!this.sign.equals(that.sign))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_rankId = true;
    list.add(present_rankId);
    if (present_rankId)
      list.add(rankId);

    boolean present_memberIdMap = true && (isSetMemberIdMap());
    list.add(present_memberIdMap);
    if (present_memberIdMap)
      list.add(memberIdMap);

    boolean present_ext = true && (isSetExt());
    list.add(present_ext);
    if (present_ext)
      list.add(ext);

    boolean present_sign = true && (isSetSign());
    list.add(present_sign);
    if (present_sign)
      list.add(sign);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryMemberRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankId()).compareTo(other.isSetRankId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankId, other.rankId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMemberIdMap()).compareTo(other.isSetMemberIdMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberIdMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberIdMap, other.memberIdMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExt()).compareTo(other.isSetExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ext, other.ext);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSign()).compareTo(other.isSetSign());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSign()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sign, other.sign);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryMemberRequest(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankId:");
    sb.append(this.rankId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("memberIdMap:");
    if (this.memberIdMap == null) {
      sb.append("null");
    } else {
      sb.append(this.memberIdMap);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ext:");
    if (this.ext == null) {
      sb.append("null");
    } else {
      sb.append(this.ext);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("sign:");
    if (this.sign == null) {
      sb.append("null");
    } else {
      sb.append(this.sign);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryMemberRequestStandardSchemeFactory implements SchemeFactory {
    public QueryMemberRequestStandardScheme getScheme() {
      return new QueryMemberRequestStandardScheme();
    }
  }

  private static class QueryMemberRequestStandardScheme extends StandardScheme<QueryMemberRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryMemberRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RANK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankId = iprot.readI64();
              struct.setRankIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // MEMBER_ID_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map164 = iprot.readMapBegin();
                struct.memberIdMap = new HashMap<String,List<String>>(2*_map164.size);
                String _key165;
                List<String> _val166;
                for (int _i167 = 0; _i167 < _map164.size; ++_i167)
                {
                  _key165 = iprot.readString();
                  {
                    org.apache.thrift.protocol.TList _list168 = iprot.readListBegin();
                    _val166 = new ArrayList<String>(_list168.size);
                    String _elem169;
                    for (int _i170 = 0; _i170 < _list168.size; ++_i170)
                    {
                      _elem169 = iprot.readString();
                      _val166.add(_elem169);
                    }
                    iprot.readListEnd();
                  }
                  struct.memberIdMap.put(_key165, _val166);
                }
                iprot.readMapEnd();
              }
              struct.setMemberIdMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map171 = iprot.readMapBegin();
                struct.ext = new HashMap<String,String>(2*_map171.size);
                String _key172;
                String _val173;
                for (int _i174 = 0; _i174 < _map171.size; ++_i174)
                {
                  _key172 = iprot.readString();
                  _val173 = iprot.readString();
                  struct.ext.put(_key172, _val173);
                }
                iprot.readMapEnd();
              }
              struct.setExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SIGN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.sign = iprot.readString();
              struct.setSignIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryMemberRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANK_ID_FIELD_DESC);
      oprot.writeI64(struct.rankId);
      oprot.writeFieldEnd();
      if (struct.memberIdMap != null) {
        oprot.writeFieldBegin(MEMBER_ID_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.LIST, struct.memberIdMap.size()));
          for (Map.Entry<String, List<String>> _iter175 : struct.memberIdMap.entrySet())
          {
            oprot.writeString(_iter175.getKey());
            {
              oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, _iter175.getValue().size()));
              for (String _iter176 : _iter175.getValue())
              {
                oprot.writeString(_iter176);
              }
              oprot.writeListEnd();
            }
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.ext != null) {
        oprot.writeFieldBegin(EXT_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.ext.size()));
          for (Map.Entry<String, String> _iter177 : struct.ext.entrySet())
          {
            oprot.writeString(_iter177.getKey());
            oprot.writeString(_iter177.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.sign != null) {
        oprot.writeFieldBegin(SIGN_FIELD_DESC);
        oprot.writeString(struct.sign);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryMemberRequestTupleSchemeFactory implements SchemeFactory {
    public QueryMemberRequestTupleScheme getScheme() {
      return new QueryMemberRequestTupleScheme();
    }
  }

  private static class QueryMemberRequestTupleScheme extends TupleScheme<QueryMemberRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryMemberRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetRankId()) {
        optionals.set(1);
      }
      if (struct.isSetMemberIdMap()) {
        optionals.set(2);
      }
      if (struct.isSetExt()) {
        optionals.set(3);
      }
      if (struct.isSetSign()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetRankId()) {
        oprot.writeI64(struct.rankId);
      }
      if (struct.isSetMemberIdMap()) {
        {
          oprot.writeI32(struct.memberIdMap.size());
          for (Map.Entry<String, List<String>> _iter178 : struct.memberIdMap.entrySet())
          {
            oprot.writeString(_iter178.getKey());
            {
              oprot.writeI32(_iter178.getValue().size());
              for (String _iter179 : _iter178.getValue())
              {
                oprot.writeString(_iter179);
              }
            }
          }
        }
      }
      if (struct.isSetExt()) {
        {
          oprot.writeI32(struct.ext.size());
          for (Map.Entry<String, String> _iter180 : struct.ext.entrySet())
          {
            oprot.writeString(_iter180.getKey());
            oprot.writeString(_iter180.getValue());
          }
        }
      }
      if (struct.isSetSign()) {
        oprot.writeString(struct.sign);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryMemberRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.rankId = iprot.readI64();
        struct.setRankIdIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map181 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.LIST, iprot.readI32());
          struct.memberIdMap = new HashMap<String,List<String>>(2*_map181.size);
          String _key182;
          List<String> _val183;
          for (int _i184 = 0; _i184 < _map181.size; ++_i184)
          {
            _key182 = iprot.readString();
            {
              org.apache.thrift.protocol.TList _list185 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
              _val183 = new ArrayList<String>(_list185.size);
              String _elem186;
              for (int _i187 = 0; _i187 < _list185.size; ++_i187)
              {
                _elem186 = iprot.readString();
                _val183.add(_elem186);
              }
            }
            struct.memberIdMap.put(_key182, _val183);
          }
        }
        struct.setMemberIdMapIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TMap _map188 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.ext = new HashMap<String,String>(2*_map188.size);
          String _key189;
          String _val190;
          for (int _i191 = 0; _i191 < _map188.size; ++_i191)
          {
            _key189 = iprot.readString();
            _val190 = iprot.readString();
            struct.ext.put(_key189, _val190);
          }
        }
        struct.setExtIsSet(true);
      }
      if (incoming.get(4)) {
        struct.sign = iprot.readString();
        struct.setSignIsSet(true);
      }
    }
  }

}

