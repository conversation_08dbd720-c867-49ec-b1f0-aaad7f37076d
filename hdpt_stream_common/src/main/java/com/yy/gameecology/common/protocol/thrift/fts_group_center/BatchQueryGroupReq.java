/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_group_center;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-12-20")
public class BatchQueryGroupReq implements org.apache.thrift.TBase<BatchQueryGroupReq, BatchQueryGroupReq._Fields>, java.io.Serializable, Cloneable, Comparable<BatchQueryGroupReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("BatchQueryGroupReq");

  private static final org.apache.thrift.protocol.TField REQUEST_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("request_list", org.apache.thrift.protocol.TType.LIST, (short)1);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new BatchQueryGroupReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new BatchQueryGroupReqTupleSchemeFactory());
  }

  public List<QueryGroupReq> request_list; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    REQUEST_LIST((short)1, "request_list");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // REQUEST_LIST
          return REQUEST_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.REQUEST_LIST, new org.apache.thrift.meta_data.FieldMetaData("request_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, QueryGroupReq.class))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(BatchQueryGroupReq.class, metaDataMap);
  }

  public BatchQueryGroupReq() {
  }

  public BatchQueryGroupReq(
    List<QueryGroupReq> request_list)
  {
    this();
    this.request_list = request_list;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public BatchQueryGroupReq(BatchQueryGroupReq other) {
    if (other.isSetRequest_list()) {
      List<QueryGroupReq> __this__request_list = new ArrayList<QueryGroupReq>(other.request_list.size());
      for (QueryGroupReq other_element : other.request_list) {
        __this__request_list.add(new QueryGroupReq(other_element));
      }
      this.request_list = __this__request_list;
    }
  }

  public BatchQueryGroupReq deepCopy() {
    return new BatchQueryGroupReq(this);
  }

  @Override
  public void clear() {
    this.request_list = null;
  }

  public int getRequest_listSize() {
    return (this.request_list == null) ? 0 : this.request_list.size();
  }

  public java.util.Iterator<QueryGroupReq> getRequest_listIterator() {
    return (this.request_list == null) ? null : this.request_list.iterator();
  }

  public void addToRequest_list(QueryGroupReq elem) {
    if (this.request_list == null) {
      this.request_list = new ArrayList<QueryGroupReq>();
    }
    this.request_list.add(elem);
  }

  public List<QueryGroupReq> getRequest_list() {
    return this.request_list;
  }

  public BatchQueryGroupReq setRequest_list(List<QueryGroupReq> request_list) {
    this.request_list = request_list;
    return this;
  }

  public void unsetRequest_list() {
    this.request_list = null;
  }

  /** Returns true if field request_list is set (has been assigned a value) and false otherwise */
  public boolean isSetRequest_list() {
    return this.request_list != null;
  }

  public void setRequest_listIsSet(boolean value) {
    if (!value) {
      this.request_list = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case REQUEST_LIST:
      if (value == null) {
        unsetRequest_list();
      } else {
        setRequest_list((List<QueryGroupReq>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case REQUEST_LIST:
      return getRequest_list();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case REQUEST_LIST:
      return isSetRequest_list();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof BatchQueryGroupReq)
      return this.equals((BatchQueryGroupReq)that);
    return false;
  }

  public boolean equals(BatchQueryGroupReq that) {
    if (that == null)
      return false;

    boolean this_present_request_list = true && this.isSetRequest_list();
    boolean that_present_request_list = true && that.isSetRequest_list();
    if (this_present_request_list || that_present_request_list) {
      if (!(this_present_request_list && that_present_request_list))
        return false;
      if (!this.request_list.equals(that.request_list))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_request_list = true && (isSetRequest_list());
    list.add(present_request_list);
    if (present_request_list)
      list.add(request_list);

    return list.hashCode();
  }

  @Override
  public int compareTo(BatchQueryGroupReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRequest_list()).compareTo(other.isSetRequest_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRequest_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request_list, other.request_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("BatchQueryGroupReq(");
    boolean first = true;

    sb.append("request_list:");
    if (this.request_list == null) {
      sb.append("null");
    } else {
      sb.append(this.request_list);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class BatchQueryGroupReqStandardSchemeFactory implements SchemeFactory {
    public BatchQueryGroupReqStandardScheme getScheme() {
      return new BatchQueryGroupReqStandardScheme();
    }
  }

  private static class BatchQueryGroupReqStandardScheme extends StandardScheme<BatchQueryGroupReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, BatchQueryGroupReq struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // REQUEST_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list56 = iprot.readListBegin();
                struct.request_list = new ArrayList<QueryGroupReq>(_list56.size);
                QueryGroupReq _elem57;
                for (int _i58 = 0; _i58 < _list56.size; ++_i58)
                {
                  _elem57 = new QueryGroupReq();
                  _elem57.read(iprot);
                  struct.request_list.add(_elem57);
                }
                iprot.readListEnd();
              }
              struct.setRequest_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, BatchQueryGroupReq struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.request_list != null) {
        oprot.writeFieldBegin(REQUEST_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.request_list.size()));
          for (QueryGroupReq _iter59 : struct.request_list)
          {
            _iter59.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class BatchQueryGroupReqTupleSchemeFactory implements SchemeFactory {
    public BatchQueryGroupReqTupleScheme getScheme() {
      return new BatchQueryGroupReqTupleScheme();
    }
  }

  private static class BatchQueryGroupReqTupleScheme extends TupleScheme<BatchQueryGroupReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, BatchQueryGroupReq struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRequest_list()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetRequest_list()) {
        {
          oprot.writeI32(struct.request_list.size());
          for (QueryGroupReq _iter60 : struct.request_list)
          {
            _iter60.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, BatchQueryGroupReq struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list61 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.request_list = new ArrayList<QueryGroupReq>(_list61.size);
          QueryGroupReq _elem62;
          for (int _i63 = 0; _i63 < _list61.size; ++_i63)
          {
            _elem62 = new QueryGroupReq();
            _elem62.read(iprot);
            struct.request_list.add(_elem62);
          }
        }
        struct.setRequest_listIsSet(true);
      }
    }
  }

}

