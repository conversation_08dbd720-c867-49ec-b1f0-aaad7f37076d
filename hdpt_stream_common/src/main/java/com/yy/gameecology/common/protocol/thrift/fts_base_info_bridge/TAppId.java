/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

/**
 * 所属业务
 * 
 */
public enum TAppId implements org.apache.thrift.TEnum {
  Da<PERSON>(2),
  VipPk(14),
  Baby(36);

  private final int value;

  private TAppId(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static TAppId findByValue(int value) { 
    switch (value) {
      case 2:
        return Dating;
      case 14:
        return VipPk;
      case 36:
        return Baby;
      default:
        return null;
    }
  }
}
