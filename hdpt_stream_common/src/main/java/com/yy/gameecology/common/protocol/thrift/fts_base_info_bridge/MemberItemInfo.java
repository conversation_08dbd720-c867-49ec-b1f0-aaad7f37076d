/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class MemberItemInfo implements org.apache.thrift.TBase<MemberItemInfo, MemberItemInfo._Fields>, java.io.Serializable, Cloneable, Comparable<MemberItemInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("MemberItemInfo");

  private static final org.apache.thrift.protocol.TField BASE_FIELD_MEMBER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("baseFieldMemberId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField BASE_FIELD_MEMBER_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("baseFieldMemberName", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField BASE_FIELD_MEMBER_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("baseFieldMemberUrl", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("ext", org.apache.thrift.protocol.TType.MAP, (short)4);
  private static final org.apache.thrift.protocol.TField VIEW_EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("viewExt", org.apache.thrift.protocol.TType.MAP, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new MemberItemInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new MemberItemInfoTupleSchemeFactory());
  }

  public String baseFieldMemberId; // required
  public String baseFieldMemberName; // required
  public String baseFieldMemberUrl; // required
  public Map<String,String> ext; // required
  public Map<String,String> viewExt; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    BASE_FIELD_MEMBER_ID((short)1, "baseFieldMemberId"),
    BASE_FIELD_MEMBER_NAME((short)2, "baseFieldMemberName"),
    BASE_FIELD_MEMBER_URL((short)3, "baseFieldMemberUrl"),
    EXT((short)4, "ext"),
    VIEW_EXT((short)5, "viewExt");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BASE_FIELD_MEMBER_ID
          return BASE_FIELD_MEMBER_ID;
        case 2: // BASE_FIELD_MEMBER_NAME
          return BASE_FIELD_MEMBER_NAME;
        case 3: // BASE_FIELD_MEMBER_URL
          return BASE_FIELD_MEMBER_URL;
        case 4: // EXT
          return EXT;
        case 5: // VIEW_EXT
          return VIEW_EXT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BASE_FIELD_MEMBER_ID, new org.apache.thrift.meta_data.FieldMetaData("baseFieldMemberId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BASE_FIELD_MEMBER_NAME, new org.apache.thrift.meta_data.FieldMetaData("baseFieldMemberName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.BASE_FIELD_MEMBER_URL, new org.apache.thrift.meta_data.FieldMetaData("baseFieldMemberUrl", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT, new org.apache.thrift.meta_data.FieldMetaData("ext", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.VIEW_EXT, new org.apache.thrift.meta_data.FieldMetaData("viewExt", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(MemberItemInfo.class, metaDataMap);
  }

  public MemberItemInfo() {
  }

  public MemberItemInfo(
    String baseFieldMemberId,
    String baseFieldMemberName,
    String baseFieldMemberUrl,
    Map<String,String> ext,
    Map<String,String> viewExt)
  {
    this();
    this.baseFieldMemberId = baseFieldMemberId;
    this.baseFieldMemberName = baseFieldMemberName;
    this.baseFieldMemberUrl = baseFieldMemberUrl;
    this.ext = ext;
    this.viewExt = viewExt;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public MemberItemInfo(MemberItemInfo other) {
    if (other.isSetBaseFieldMemberId()) {
      this.baseFieldMemberId = other.baseFieldMemberId;
    }
    if (other.isSetBaseFieldMemberName()) {
      this.baseFieldMemberName = other.baseFieldMemberName;
    }
    if (other.isSetBaseFieldMemberUrl()) {
      this.baseFieldMemberUrl = other.baseFieldMemberUrl;
    }
    if (other.isSetExt()) {
      Map<String,String> __this__ext = new HashMap<String,String>(other.ext);
      this.ext = __this__ext;
    }
    if (other.isSetViewExt()) {
      Map<String,String> __this__viewExt = new HashMap<String,String>(other.viewExt);
      this.viewExt = __this__viewExt;
    }
  }

  public MemberItemInfo deepCopy() {
    return new MemberItemInfo(this);
  }

  @Override
  public void clear() {
    this.baseFieldMemberId = null;
    this.baseFieldMemberName = null;
    this.baseFieldMemberUrl = null;
    this.ext = null;
    this.viewExt = null;
  }

  public String getBaseFieldMemberId() {
    return this.baseFieldMemberId;
  }

  public MemberItemInfo setBaseFieldMemberId(String baseFieldMemberId) {
    this.baseFieldMemberId = baseFieldMemberId;
    return this;
  }

  public void unsetBaseFieldMemberId() {
    this.baseFieldMemberId = null;
  }

  /** Returns true if field baseFieldMemberId is set (has been assigned a value) and false otherwise */
  public boolean isSetBaseFieldMemberId() {
    return this.baseFieldMemberId != null;
  }

  public void setBaseFieldMemberIdIsSet(boolean value) {
    if (!value) {
      this.baseFieldMemberId = null;
    }
  }

  public String getBaseFieldMemberName() {
    return this.baseFieldMemberName;
  }

  public MemberItemInfo setBaseFieldMemberName(String baseFieldMemberName) {
    this.baseFieldMemberName = baseFieldMemberName;
    return this;
  }

  public void unsetBaseFieldMemberName() {
    this.baseFieldMemberName = null;
  }

  /** Returns true if field baseFieldMemberName is set (has been assigned a value) and false otherwise */
  public boolean isSetBaseFieldMemberName() {
    return this.baseFieldMemberName != null;
  }

  public void setBaseFieldMemberNameIsSet(boolean value) {
    if (!value) {
      this.baseFieldMemberName = null;
    }
  }

  public String getBaseFieldMemberUrl() {
    return this.baseFieldMemberUrl;
  }

  public MemberItemInfo setBaseFieldMemberUrl(String baseFieldMemberUrl) {
    this.baseFieldMemberUrl = baseFieldMemberUrl;
    return this;
  }

  public void unsetBaseFieldMemberUrl() {
    this.baseFieldMemberUrl = null;
  }

  /** Returns true if field baseFieldMemberUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetBaseFieldMemberUrl() {
    return this.baseFieldMemberUrl != null;
  }

  public void setBaseFieldMemberUrlIsSet(boolean value) {
    if (!value) {
      this.baseFieldMemberUrl = null;
    }
  }

  public int getExtSize() {
    return (this.ext == null) ? 0 : this.ext.size();
  }

  public void putToExt(String key, String val) {
    if (this.ext == null) {
      this.ext = new HashMap<String,String>();
    }
    this.ext.put(key, val);
  }

  public Map<String,String> getExt() {
    return this.ext;
  }

  public MemberItemInfo setExt(Map<String,String> ext) {
    this.ext = ext;
    return this;
  }

  public void unsetExt() {
    this.ext = null;
  }

  /** Returns true if field ext is set (has been assigned a value) and false otherwise */
  public boolean isSetExt() {
    return this.ext != null;
  }

  public void setExtIsSet(boolean value) {
    if (!value) {
      this.ext = null;
    }
  }

  public int getViewExtSize() {
    return (this.viewExt == null) ? 0 : this.viewExt.size();
  }

  public void putToViewExt(String key, String val) {
    if (this.viewExt == null) {
      this.viewExt = new HashMap<String,String>();
    }
    this.viewExt.put(key, val);
  }

  public Map<String,String> getViewExt() {
    return this.viewExt;
  }

  public MemberItemInfo setViewExt(Map<String,String> viewExt) {
    this.viewExt = viewExt;
    return this;
  }

  public void unsetViewExt() {
    this.viewExt = null;
  }

  /** Returns true if field viewExt is set (has been assigned a value) and false otherwise */
  public boolean isSetViewExt() {
    return this.viewExt != null;
  }

  public void setViewExtIsSet(boolean value) {
    if (!value) {
      this.viewExt = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case BASE_FIELD_MEMBER_ID:
      if (value == null) {
        unsetBaseFieldMemberId();
      } else {
        setBaseFieldMemberId((String)value);
      }
      break;

    case BASE_FIELD_MEMBER_NAME:
      if (value == null) {
        unsetBaseFieldMemberName();
      } else {
        setBaseFieldMemberName((String)value);
      }
      break;

    case BASE_FIELD_MEMBER_URL:
      if (value == null) {
        unsetBaseFieldMemberUrl();
      } else {
        setBaseFieldMemberUrl((String)value);
      }
      break;

    case EXT:
      if (value == null) {
        unsetExt();
      } else {
        setExt((Map<String,String>)value);
      }
      break;

    case VIEW_EXT:
      if (value == null) {
        unsetViewExt();
      } else {
        setViewExt((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case BASE_FIELD_MEMBER_ID:
      return getBaseFieldMemberId();

    case BASE_FIELD_MEMBER_NAME:
      return getBaseFieldMemberName();

    case BASE_FIELD_MEMBER_URL:
      return getBaseFieldMemberUrl();

    case EXT:
      return getExt();

    case VIEW_EXT:
      return getViewExt();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case BASE_FIELD_MEMBER_ID:
      return isSetBaseFieldMemberId();
    case BASE_FIELD_MEMBER_NAME:
      return isSetBaseFieldMemberName();
    case BASE_FIELD_MEMBER_URL:
      return isSetBaseFieldMemberUrl();
    case EXT:
      return isSetExt();
    case VIEW_EXT:
      return isSetViewExt();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof MemberItemInfo)
      return this.equals((MemberItemInfo)that);
    return false;
  }

  public boolean equals(MemberItemInfo that) {
    if (that == null)
      return false;

    boolean this_present_baseFieldMemberId = true && this.isSetBaseFieldMemberId();
    boolean that_present_baseFieldMemberId = true && that.isSetBaseFieldMemberId();
    if (this_present_baseFieldMemberId || that_present_baseFieldMemberId) {
      if (!(this_present_baseFieldMemberId && that_present_baseFieldMemberId))
        return false;
      if (!this.baseFieldMemberId.equals(that.baseFieldMemberId))
        return false;
    }

    boolean this_present_baseFieldMemberName = true && this.isSetBaseFieldMemberName();
    boolean that_present_baseFieldMemberName = true && that.isSetBaseFieldMemberName();
    if (this_present_baseFieldMemberName || that_present_baseFieldMemberName) {
      if (!(this_present_baseFieldMemberName && that_present_baseFieldMemberName))
        return false;
      if (!this.baseFieldMemberName.equals(that.baseFieldMemberName))
        return false;
    }

    boolean this_present_baseFieldMemberUrl = true && this.isSetBaseFieldMemberUrl();
    boolean that_present_baseFieldMemberUrl = true && that.isSetBaseFieldMemberUrl();
    if (this_present_baseFieldMemberUrl || that_present_baseFieldMemberUrl) {
      if (!(this_present_baseFieldMemberUrl && that_present_baseFieldMemberUrl))
        return false;
      if (!this.baseFieldMemberUrl.equals(that.baseFieldMemberUrl))
        return false;
    }

    boolean this_present_ext = true && this.isSetExt();
    boolean that_present_ext = true && that.isSetExt();
    if (this_present_ext || that_present_ext) {
      if (!(this_present_ext && that_present_ext))
        return false;
      if (!this.ext.equals(that.ext))
        return false;
    }

    boolean this_present_viewExt = true && this.isSetViewExt();
    boolean that_present_viewExt = true && that.isSetViewExt();
    if (this_present_viewExt || that_present_viewExt) {
      if (!(this_present_viewExt && that_present_viewExt))
        return false;
      if (!this.viewExt.equals(that.viewExt))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_baseFieldMemberId = true && (isSetBaseFieldMemberId());
    list.add(present_baseFieldMemberId);
    if (present_baseFieldMemberId)
      list.add(baseFieldMemberId);

    boolean present_baseFieldMemberName = true && (isSetBaseFieldMemberName());
    list.add(present_baseFieldMemberName);
    if (present_baseFieldMemberName)
      list.add(baseFieldMemberName);

    boolean present_baseFieldMemberUrl = true && (isSetBaseFieldMemberUrl());
    list.add(present_baseFieldMemberUrl);
    if (present_baseFieldMemberUrl)
      list.add(baseFieldMemberUrl);

    boolean present_ext = true && (isSetExt());
    list.add(present_ext);
    if (present_ext)
      list.add(ext);

    boolean present_viewExt = true && (isSetViewExt());
    list.add(present_viewExt);
    if (present_viewExt)
      list.add(viewExt);

    return list.hashCode();
  }

  @Override
  public int compareTo(MemberItemInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetBaseFieldMemberId()).compareTo(other.isSetBaseFieldMemberId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBaseFieldMemberId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.baseFieldMemberId, other.baseFieldMemberId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBaseFieldMemberName()).compareTo(other.isSetBaseFieldMemberName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBaseFieldMemberName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.baseFieldMemberName, other.baseFieldMemberName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBaseFieldMemberUrl()).compareTo(other.isSetBaseFieldMemberUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBaseFieldMemberUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.baseFieldMemberUrl, other.baseFieldMemberUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExt()).compareTo(other.isSetExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ext, other.ext);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetViewExt()).compareTo(other.isSetViewExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetViewExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.viewExt, other.viewExt);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("MemberItemInfo(");
    boolean first = true;

    sb.append("baseFieldMemberId:");
    if (this.baseFieldMemberId == null) {
      sb.append("null");
    } else {
      sb.append(this.baseFieldMemberId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("baseFieldMemberName:");
    if (this.baseFieldMemberName == null) {
      sb.append("null");
    } else {
      sb.append(this.baseFieldMemberName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("baseFieldMemberUrl:");
    if (this.baseFieldMemberUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.baseFieldMemberUrl);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ext:");
    if (this.ext == null) {
      sb.append("null");
    } else {
      sb.append(this.ext);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("viewExt:");
    if (this.viewExt == null) {
      sb.append("null");
    } else {
      sb.append(this.viewExt);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class MemberItemInfoStandardSchemeFactory implements SchemeFactory {
    public MemberItemInfoStandardScheme getScheme() {
      return new MemberItemInfoStandardScheme();
    }
  }

  private static class MemberItemInfoStandardScheme extends StandardScheme<MemberItemInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, MemberItemInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BASE_FIELD_MEMBER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.baseFieldMemberId = iprot.readString();
              struct.setBaseFieldMemberIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BASE_FIELD_MEMBER_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.baseFieldMemberName = iprot.readString();
              struct.setBaseFieldMemberNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // BASE_FIELD_MEMBER_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.baseFieldMemberUrl = iprot.readString();
              struct.setBaseFieldMemberUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map114 = iprot.readMapBegin();
                struct.ext = new HashMap<String,String>(2*_map114.size);
                String _key115;
                String _val116;
                for (int _i117 = 0; _i117 < _map114.size; ++_i117)
                {
                  _key115 = iprot.readString();
                  _val116 = iprot.readString();
                  struct.ext.put(_key115, _val116);
                }
                iprot.readMapEnd();
              }
              struct.setExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // VIEW_EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map118 = iprot.readMapBegin();
                struct.viewExt = new HashMap<String,String>(2*_map118.size);
                String _key119;
                String _val120;
                for (int _i121 = 0; _i121 < _map118.size; ++_i121)
                {
                  _key119 = iprot.readString();
                  _val120 = iprot.readString();
                  struct.viewExt.put(_key119, _val120);
                }
                iprot.readMapEnd();
              }
              struct.setViewExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, MemberItemInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.baseFieldMemberId != null) {
        oprot.writeFieldBegin(BASE_FIELD_MEMBER_ID_FIELD_DESC);
        oprot.writeString(struct.baseFieldMemberId);
        oprot.writeFieldEnd();
      }
      if (struct.baseFieldMemberName != null) {
        oprot.writeFieldBegin(BASE_FIELD_MEMBER_NAME_FIELD_DESC);
        oprot.writeString(struct.baseFieldMemberName);
        oprot.writeFieldEnd();
      }
      if (struct.baseFieldMemberUrl != null) {
        oprot.writeFieldBegin(BASE_FIELD_MEMBER_URL_FIELD_DESC);
        oprot.writeString(struct.baseFieldMemberUrl);
        oprot.writeFieldEnd();
      }
      if (struct.ext != null) {
        oprot.writeFieldBegin(EXT_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.ext.size()));
          for (Map.Entry<String, String> _iter122 : struct.ext.entrySet())
          {
            oprot.writeString(_iter122.getKey());
            oprot.writeString(_iter122.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.viewExt != null) {
        oprot.writeFieldBegin(VIEW_EXT_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.viewExt.size()));
          for (Map.Entry<String, String> _iter123 : struct.viewExt.entrySet())
          {
            oprot.writeString(_iter123.getKey());
            oprot.writeString(_iter123.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class MemberItemInfoTupleSchemeFactory implements SchemeFactory {
    public MemberItemInfoTupleScheme getScheme() {
      return new MemberItemInfoTupleScheme();
    }
  }

  private static class MemberItemInfoTupleScheme extends TupleScheme<MemberItemInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, MemberItemInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetBaseFieldMemberId()) {
        optionals.set(0);
      }
      if (struct.isSetBaseFieldMemberName()) {
        optionals.set(1);
      }
      if (struct.isSetBaseFieldMemberUrl()) {
        optionals.set(2);
      }
      if (struct.isSetExt()) {
        optionals.set(3);
      }
      if (struct.isSetViewExt()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetBaseFieldMemberId()) {
        oprot.writeString(struct.baseFieldMemberId);
      }
      if (struct.isSetBaseFieldMemberName()) {
        oprot.writeString(struct.baseFieldMemberName);
      }
      if (struct.isSetBaseFieldMemberUrl()) {
        oprot.writeString(struct.baseFieldMemberUrl);
      }
      if (struct.isSetExt()) {
        {
          oprot.writeI32(struct.ext.size());
          for (Map.Entry<String, String> _iter124 : struct.ext.entrySet())
          {
            oprot.writeString(_iter124.getKey());
            oprot.writeString(_iter124.getValue());
          }
        }
      }
      if (struct.isSetViewExt()) {
        {
          oprot.writeI32(struct.viewExt.size());
          for (Map.Entry<String, String> _iter125 : struct.viewExt.entrySet())
          {
            oprot.writeString(_iter125.getKey());
            oprot.writeString(_iter125.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, MemberItemInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.baseFieldMemberId = iprot.readString();
        struct.setBaseFieldMemberIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.baseFieldMemberName = iprot.readString();
        struct.setBaseFieldMemberNameIsSet(true);
      }
      if (incoming.get(2)) {
        struct.baseFieldMemberUrl = iprot.readString();
        struct.setBaseFieldMemberUrlIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TMap _map126 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.ext = new HashMap<String,String>(2*_map126.size);
          String _key127;
          String _val128;
          for (int _i129 = 0; _i129 < _map126.size; ++_i129)
          {
            _key127 = iprot.readString();
            _val128 = iprot.readString();
            struct.ext.put(_key127, _val128);
          }
        }
        struct.setExtIsSet(true);
      }
      if (incoming.get(4)) {
        {
          org.apache.thrift.protocol.TMap _map130 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.viewExt = new HashMap<String,String>(2*_map130.size);
          String _key131;
          String _val132;
          for (int _i133 = 0; _i133 < _map130.size; ++_i133)
          {
            _key131 = iprot.readString();
            _val132 = iprot.readString();
            struct.viewExt.put(_key131, _val132);
          }
        }
        struct.setViewExtIsSet(true);
      }
    }
  }

}

