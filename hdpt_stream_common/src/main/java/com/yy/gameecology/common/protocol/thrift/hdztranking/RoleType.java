/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum RoleType implements org.apache.thrift.TEnum {
  ANY(0),
  USER(100),
  ANCHOR(200),
  WAITER(201),
  HOST(300),
  GUILD(400),
  HALL(401),
  PWTUAN(402);

  private final int value;

  private RoleType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static RoleType findByValue(int value) { 
    switch (value) {
      case 0:
        return ANY;
      case 100:
        return USER;
      case 200:
        return ANCHOR;
      case 201:
        return WAITER;
      case 300:
        return HOST;
      case 400:
        return GUILD;
      case 401:
        return HALL;
      case 402:
        return PWTUAN;
      default:
        return null;
    }
  }
}
