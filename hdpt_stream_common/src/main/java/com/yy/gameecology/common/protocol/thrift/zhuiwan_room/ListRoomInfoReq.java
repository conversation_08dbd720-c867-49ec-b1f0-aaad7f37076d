/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_room;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-06-25")
public class ListRoomInfoReq implements org.apache.thrift.TBase<ListRoomInfoReq, ListRoomInfoReq._Fields>, java.io.Serializable, Cloneable, Comparable<ListRoomInfoReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ListRoomInfoReq");

  private static final org.apache.thrift.protocol.TField SSIDS_FIELD_DESC = new org.apache.thrift.protocol.TField("ssids", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField ROOM_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("roomIds", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ListRoomInfoReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ListRoomInfoReqTupleSchemeFactory());
  }

  public List<Long> ssids; // required
  public List<Integer> roomIds; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SSIDS((short)1, "ssids"),
    ROOM_IDS((short)2, "roomIds");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SSIDS
          return SSIDS;
        case 2: // ROOM_IDS
          return ROOM_IDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SSIDS, new org.apache.thrift.meta_data.FieldMetaData("ssids", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.ROOM_IDS, new org.apache.thrift.meta_data.FieldMetaData("roomIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ListRoomInfoReq.class, metaDataMap);
  }

  public ListRoomInfoReq() {
  }

  public ListRoomInfoReq(
    List<Long> ssids,
    List<Integer> roomIds)
  {
    this();
    this.ssids = ssids;
    this.roomIds = roomIds;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ListRoomInfoReq(ListRoomInfoReq other) {
    if (other.isSetSsids()) {
      List<Long> __this__ssids = new ArrayList<Long>(other.ssids);
      this.ssids = __this__ssids;
    }
    if (other.isSetRoomIds()) {
      List<Integer> __this__roomIds = new ArrayList<Integer>(other.roomIds);
      this.roomIds = __this__roomIds;
    }
  }

  public ListRoomInfoReq deepCopy() {
    return new ListRoomInfoReq(this);
  }

  @Override
  public void clear() {
    this.ssids = null;
    this.roomIds = null;
  }

  public int getSsidsSize() {
    return (this.ssids == null) ? 0 : this.ssids.size();
  }

  public java.util.Iterator<Long> getSsidsIterator() {
    return (this.ssids == null) ? null : this.ssids.iterator();
  }

  public void addToSsids(long elem) {
    if (this.ssids == null) {
      this.ssids = new ArrayList<Long>();
    }
    this.ssids.add(elem);
  }

  public List<Long> getSsids() {
    return this.ssids;
  }

  public ListRoomInfoReq setSsids(List<Long> ssids) {
    this.ssids = ssids;
    return this;
  }

  public void unsetSsids() {
    this.ssids = null;
  }

  /** Returns true if field ssids is set (has been assigned a value) and false otherwise */
  public boolean isSetSsids() {
    return this.ssids != null;
  }

  public void setSsidsIsSet(boolean value) {
    if (!value) {
      this.ssids = null;
    }
  }

  public int getRoomIdsSize() {
    return (this.roomIds == null) ? 0 : this.roomIds.size();
  }

  public java.util.Iterator<Integer> getRoomIdsIterator() {
    return (this.roomIds == null) ? null : this.roomIds.iterator();
  }

  public void addToRoomIds(int elem) {
    if (this.roomIds == null) {
      this.roomIds = new ArrayList<Integer>();
    }
    this.roomIds.add(elem);
  }

  public List<Integer> getRoomIds() {
    return this.roomIds;
  }

  public ListRoomInfoReq setRoomIds(List<Integer> roomIds) {
    this.roomIds = roomIds;
    return this;
  }

  public void unsetRoomIds() {
    this.roomIds = null;
  }

  /** Returns true if field roomIds is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomIds() {
    return this.roomIds != null;
  }

  public void setRoomIdsIsSet(boolean value) {
    if (!value) {
      this.roomIds = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case SSIDS:
      if (value == null) {
        unsetSsids();
      } else {
        setSsids((List<Long>)value);
      }
      break;

    case ROOM_IDS:
      if (value == null) {
        unsetRoomIds();
      } else {
        setRoomIds((List<Integer>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case SSIDS:
      return getSsids();

    case ROOM_IDS:
      return getRoomIds();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SSIDS:
      return isSetSsids();
    case ROOM_IDS:
      return isSetRoomIds();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ListRoomInfoReq)
      return this.equals((ListRoomInfoReq)that);
    return false;
  }

  public boolean equals(ListRoomInfoReq that) {
    if (that == null)
      return false;

    boolean this_present_ssids = true && this.isSetSsids();
    boolean that_present_ssids = true && that.isSetSsids();
    if (this_present_ssids || that_present_ssids) {
      if (!(this_present_ssids && that_present_ssids))
        return false;
      if (!this.ssids.equals(that.ssids))
        return false;
    }

    boolean this_present_roomIds = true && this.isSetRoomIds();
    boolean that_present_roomIds = true && that.isSetRoomIds();
    if (this_present_roomIds || that_present_roomIds) {
      if (!(this_present_roomIds && that_present_roomIds))
        return false;
      if (!this.roomIds.equals(that.roomIds))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_ssids = true && (isSetSsids());
    list.add(present_ssids);
    if (present_ssids)
      list.add(ssids);

    boolean present_roomIds = true && (isSetRoomIds());
    list.add(present_roomIds);
    if (present_roomIds)
      list.add(roomIds);

    return list.hashCode();
  }

  @Override
  public int compareTo(ListRoomInfoReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSsids()).compareTo(other.isSetSsids());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSsids()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssids, other.ssids);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoomIds()).compareTo(other.isSetRoomIds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomIds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomIds, other.roomIds);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ListRoomInfoReq(");
    boolean first = true;

    sb.append("ssids:");
    if (this.ssids == null) {
      sb.append("null");
    } else {
      sb.append(this.ssids);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("roomIds:");
    if (this.roomIds == null) {
      sb.append("null");
    } else {
      sb.append(this.roomIds);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ListRoomInfoReqStandardSchemeFactory implements SchemeFactory {
    public ListRoomInfoReqStandardScheme getScheme() {
      return new ListRoomInfoReqStandardScheme();
    }
  }

  private static class ListRoomInfoReqStandardScheme extends StandardScheme<ListRoomInfoReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ListRoomInfoReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SSIDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.ssids = new ArrayList<Long>(_list0.size);
                long _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = iprot.readI64();
                  struct.ssids.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setSsidsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ROOM_IDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list3 = iprot.readListBegin();
                struct.roomIds = new ArrayList<Integer>(_list3.size);
                int _elem4;
                for (int _i5 = 0; _i5 < _list3.size; ++_i5)
                {
                  _elem4 = iprot.readI32();
                  struct.roomIds.add(_elem4);
                }
                iprot.readListEnd();
              }
              struct.setRoomIdsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ListRoomInfoReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.ssids != null) {
        oprot.writeFieldBegin(SSIDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.ssids.size()));
          for (long _iter6 : struct.ssids)
          {
            oprot.writeI64(_iter6);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.roomIds != null) {
        oprot.writeFieldBegin(ROOM_IDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, struct.roomIds.size()));
          for (int _iter7 : struct.roomIds)
          {
            oprot.writeI32(_iter7);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ListRoomInfoReqTupleSchemeFactory implements SchemeFactory {
    public ListRoomInfoReqTupleScheme getScheme() {
      return new ListRoomInfoReqTupleScheme();
    }
  }

  private static class ListRoomInfoReqTupleScheme extends TupleScheme<ListRoomInfoReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ListRoomInfoReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetSsids()) {
        optionals.set(0);
      }
      if (struct.isSetRoomIds()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetSsids()) {
        {
          oprot.writeI32(struct.ssids.size());
          for (long _iter8 : struct.ssids)
          {
            oprot.writeI64(_iter8);
          }
        }
      }
      if (struct.isSetRoomIds()) {
        {
          oprot.writeI32(struct.roomIds.size());
          for (int _iter9 : struct.roomIds)
          {
            oprot.writeI32(_iter9);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ListRoomInfoReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list10 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.ssids = new ArrayList<Long>(_list10.size);
          long _elem11;
          for (int _i12 = 0; _i12 < _list10.size; ++_i12)
          {
            _elem11 = iprot.readI64();
            struct.ssids.add(_elem11);
          }
        }
        struct.setSsidsIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list13 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I32, iprot.readI32());
          struct.roomIds = new ArrayList<Integer>(_list13.size);
          int _elem14;
          for (int _i15 = 0; _i15 < _list13.size; ++_i15)
          {
            _elem14 = iprot.readI32();
            struct.roomIds.add(_elem14);
          }
        }
        struct.setRoomIdsIsSet(true);
      }
    }
  }

}

