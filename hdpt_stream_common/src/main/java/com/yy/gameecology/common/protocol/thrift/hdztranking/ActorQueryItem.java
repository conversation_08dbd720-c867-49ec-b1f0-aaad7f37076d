/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class ActorQueryItem implements org.apache.thrift.TBase<ActorQueryItem, ActorQueryItem._Fields>, java.io.Serializable, Cloneable, Comparable<ActorQueryItem> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ActorQueryItem");

  private static final org.apache.thrift.protocol.TField RANKING_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rankingId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField ACTOR_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actorId", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField DATE_STR_FIELD_DESC = new org.apache.thrift.protocol.TField("dateStr", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField WITH_STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("withStatus", org.apache.thrift.protocol.TType.BOOL, (short)5);
  private static final org.apache.thrift.protocol.TField ROLE_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("roleType", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField QUERY_PHASE_RANK_FIELD_DESC = new org.apache.thrift.protocol.TField("queryPhaseRank", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ActorQueryItemStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ActorQueryItemTupleSchemeFactory());
  }

  public long rankingId; // required
  public long phaseId; // required
  public String actorId; // required
  public String dateStr; // required
  public boolean withStatus; // required
  public long roleType; // required
  public long queryPhaseRank; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RANKING_ID((short)1, "rankingId"),
    PHASE_ID((short)2, "phaseId"),
    ACTOR_ID((short)3, "actorId"),
    DATE_STR((short)4, "dateStr"),
    WITH_STATUS((short)5, "withStatus"),
    ROLE_TYPE((short)6, "roleType"),
    QUERY_PHASE_RANK((short)7, "queryPhaseRank"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RANKING_ID
          return RANKING_ID;
        case 2: // PHASE_ID
          return PHASE_ID;
        case 3: // ACTOR_ID
          return ACTOR_ID;
        case 4: // DATE_STR
          return DATE_STR;
        case 5: // WITH_STATUS
          return WITH_STATUS;
        case 6: // ROLE_TYPE
          return ROLE_TYPE;
        case 7: // QUERY_PHASE_RANK
          return QUERY_PHASE_RANK;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RANKINGID_ISSET_ID = 0;
  private static final int __PHASEID_ISSET_ID = 1;
  private static final int __WITHSTATUS_ISSET_ID = 2;
  private static final int __ROLETYPE_ISSET_ID = 3;
  private static final int __QUERYPHASERANK_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RANKING_ID, new org.apache.thrift.meta_data.FieldMetaData("rankingId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACTOR_ID, new org.apache.thrift.meta_data.FieldMetaData("actorId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.DATE_STR, new org.apache.thrift.meta_data.FieldMetaData("dateStr", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.WITH_STATUS, new org.apache.thrift.meta_data.FieldMetaData("withStatus", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.ROLE_TYPE, new org.apache.thrift.meta_data.FieldMetaData("roleType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.QUERY_PHASE_RANK, new org.apache.thrift.meta_data.FieldMetaData("queryPhaseRank", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ActorQueryItem.class, metaDataMap);
  }

  public ActorQueryItem() {
  }

  public ActorQueryItem(
    long rankingId,
    long phaseId,
    String actorId,
    String dateStr,
    boolean withStatus,
    long roleType,
    long queryPhaseRank,
    Map<String,String> extData)
  {
    this();
    this.rankingId = rankingId;
    setRankingIdIsSet(true);
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    this.actorId = actorId;
    this.dateStr = dateStr;
    this.withStatus = withStatus;
    setWithStatusIsSet(true);
    this.roleType = roleType;
    setRoleTypeIsSet(true);
    this.queryPhaseRank = queryPhaseRank;
    setQueryPhaseRankIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ActorQueryItem(ActorQueryItem other) {
    __isset_bitfield = other.__isset_bitfield;
    this.rankingId = other.rankingId;
    this.phaseId = other.phaseId;
    if (other.isSetActorId()) {
      this.actorId = other.actorId;
    }
    if (other.isSetDateStr()) {
      this.dateStr = other.dateStr;
    }
    this.withStatus = other.withStatus;
    this.roleType = other.roleType;
    this.queryPhaseRank = other.queryPhaseRank;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public ActorQueryItem deepCopy() {
    return new ActorQueryItem(this);
  }

  @Override
  public void clear() {
    setRankingIdIsSet(false);
    this.rankingId = 0;
    setPhaseIdIsSet(false);
    this.phaseId = 0;
    this.actorId = null;
    this.dateStr = null;
    setWithStatusIsSet(false);
    this.withStatus = false;
    setRoleTypeIsSet(false);
    this.roleType = 0;
    setQueryPhaseRankIsSet(false);
    this.queryPhaseRank = 0;
    this.extData = null;
  }

  public long getRankingId() {
    return this.rankingId;
  }

  public ActorQueryItem setRankingId(long rankingId) {
    this.rankingId = rankingId;
    setRankingIdIsSet(true);
    return this;
  }

  public void unsetRankingId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKINGID_ISSET_ID);
  }

  /** Returns true if field rankingId is set (has been assigned a value) and false otherwise */
  public boolean isSetRankingId() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKINGID_ISSET_ID);
  }

  public void setRankingIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKINGID_ISSET_ID, value);
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public ActorQueryItem setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public String getActorId() {
    return this.actorId;
  }

  public ActorQueryItem setActorId(String actorId) {
    this.actorId = actorId;
    return this;
  }

  public void unsetActorId() {
    this.actorId = null;
  }

  /** Returns true if field actorId is set (has been assigned a value) and false otherwise */
  public boolean isSetActorId() {
    return this.actorId != null;
  }

  public void setActorIdIsSet(boolean value) {
    if (!value) {
      this.actorId = null;
    }
  }

  public String getDateStr() {
    return this.dateStr;
  }

  public ActorQueryItem setDateStr(String dateStr) {
    this.dateStr = dateStr;
    return this;
  }

  public void unsetDateStr() {
    this.dateStr = null;
  }

  /** Returns true if field dateStr is set (has been assigned a value) and false otherwise */
  public boolean isSetDateStr() {
    return this.dateStr != null;
  }

  public void setDateStrIsSet(boolean value) {
    if (!value) {
      this.dateStr = null;
    }
  }

  public boolean isWithStatus() {
    return this.withStatus;
  }

  public ActorQueryItem setWithStatus(boolean withStatus) {
    this.withStatus = withStatus;
    setWithStatusIsSet(true);
    return this;
  }

  public void unsetWithStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __WITHSTATUS_ISSET_ID);
  }

  /** Returns true if field withStatus is set (has been assigned a value) and false otherwise */
  public boolean isSetWithStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __WITHSTATUS_ISSET_ID);
  }

  public void setWithStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __WITHSTATUS_ISSET_ID, value);
  }

  public long getRoleType() {
    return this.roleType;
  }

  public ActorQueryItem setRoleType(long roleType) {
    this.roleType = roleType;
    setRoleTypeIsSet(true);
    return this;
  }

  public void unsetRoleType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLETYPE_ISSET_ID);
  }

  /** Returns true if field roleType is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleType() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLETYPE_ISSET_ID);
  }

  public void setRoleTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLETYPE_ISSET_ID, value);
  }

  public long getQueryPhaseRank() {
    return this.queryPhaseRank;
  }

  public ActorQueryItem setQueryPhaseRank(long queryPhaseRank) {
    this.queryPhaseRank = queryPhaseRank;
    setQueryPhaseRankIsSet(true);
    return this;
  }

  public void unsetQueryPhaseRank() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __QUERYPHASERANK_ISSET_ID);
  }

  /** Returns true if field queryPhaseRank is set (has been assigned a value) and false otherwise */
  public boolean isSetQueryPhaseRank() {
    return EncodingUtils.testBit(__isset_bitfield, __QUERYPHASERANK_ISSET_ID);
  }

  public void setQueryPhaseRankIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __QUERYPHASERANK_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public ActorQueryItem setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RANKING_ID:
      if (value == null) {
        unsetRankingId();
      } else {
        setRankingId((Long)value);
      }
      break;

    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    case ACTOR_ID:
      if (value == null) {
        unsetActorId();
      } else {
        setActorId((String)value);
      }
      break;

    case DATE_STR:
      if (value == null) {
        unsetDateStr();
      } else {
        setDateStr((String)value);
      }
      break;

    case WITH_STATUS:
      if (value == null) {
        unsetWithStatus();
      } else {
        setWithStatus((Boolean)value);
      }
      break;

    case ROLE_TYPE:
      if (value == null) {
        unsetRoleType();
      } else {
        setRoleType((Long)value);
      }
      break;

    case QUERY_PHASE_RANK:
      if (value == null) {
        unsetQueryPhaseRank();
      } else {
        setQueryPhaseRank((Long)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RANKING_ID:
      return getRankingId();

    case PHASE_ID:
      return getPhaseId();

    case ACTOR_ID:
      return getActorId();

    case DATE_STR:
      return getDateStr();

    case WITH_STATUS:
      return isWithStatus();

    case ROLE_TYPE:
      return getRoleType();

    case QUERY_PHASE_RANK:
      return getQueryPhaseRank();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RANKING_ID:
      return isSetRankingId();
    case PHASE_ID:
      return isSetPhaseId();
    case ACTOR_ID:
      return isSetActorId();
    case DATE_STR:
      return isSetDateStr();
    case WITH_STATUS:
      return isSetWithStatus();
    case ROLE_TYPE:
      return isSetRoleType();
    case QUERY_PHASE_RANK:
      return isSetQueryPhaseRank();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ActorQueryItem)
      return this.equals((ActorQueryItem)that);
    return false;
  }

  public boolean equals(ActorQueryItem that) {
    if (that == null)
      return false;

    boolean this_present_rankingId = true;
    boolean that_present_rankingId = true;
    if (this_present_rankingId || that_present_rankingId) {
      if (!(this_present_rankingId && that_present_rankingId))
        return false;
      if (this.rankingId != that.rankingId)
        return false;
    }

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    boolean this_present_actorId = true && this.isSetActorId();
    boolean that_present_actorId = true && that.isSetActorId();
    if (this_present_actorId || that_present_actorId) {
      if (!(this_present_actorId && that_present_actorId))
        return false;
      if (!this.actorId.equals(that.actorId))
        return false;
    }

    boolean this_present_dateStr = true && this.isSetDateStr();
    boolean that_present_dateStr = true && that.isSetDateStr();
    if (this_present_dateStr || that_present_dateStr) {
      if (!(this_present_dateStr && that_present_dateStr))
        return false;
      if (!this.dateStr.equals(that.dateStr))
        return false;
    }

    boolean this_present_withStatus = true;
    boolean that_present_withStatus = true;
    if (this_present_withStatus || that_present_withStatus) {
      if (!(this_present_withStatus && that_present_withStatus))
        return false;
      if (this.withStatus != that.withStatus)
        return false;
    }

    boolean this_present_roleType = true;
    boolean that_present_roleType = true;
    if (this_present_roleType || that_present_roleType) {
      if (!(this_present_roleType && that_present_roleType))
        return false;
      if (this.roleType != that.roleType)
        return false;
    }

    boolean this_present_queryPhaseRank = true;
    boolean that_present_queryPhaseRank = true;
    if (this_present_queryPhaseRank || that_present_queryPhaseRank) {
      if (!(this_present_queryPhaseRank && that_present_queryPhaseRank))
        return false;
      if (this.queryPhaseRank != that.queryPhaseRank)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_rankingId = true;
    list.add(present_rankingId);
    if (present_rankingId)
      list.add(rankingId);

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    boolean present_actorId = true && (isSetActorId());
    list.add(present_actorId);
    if (present_actorId)
      list.add(actorId);

    boolean present_dateStr = true && (isSetDateStr());
    list.add(present_dateStr);
    if (present_dateStr)
      list.add(dateStr);

    boolean present_withStatus = true;
    list.add(present_withStatus);
    if (present_withStatus)
      list.add(withStatus);

    boolean present_roleType = true;
    list.add(present_roleType);
    if (present_roleType)
      list.add(roleType);

    boolean present_queryPhaseRank = true;
    list.add(present_queryPhaseRank);
    if (present_queryPhaseRank)
      list.add(queryPhaseRank);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(ActorQueryItem other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRankingId()).compareTo(other.isSetRankingId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankingId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankingId, other.rankingId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActorId()).compareTo(other.isSetActorId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActorId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actorId, other.actorId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDateStr()).compareTo(other.isSetDateStr());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDateStr()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dateStr, other.dateStr);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetWithStatus()).compareTo(other.isSetWithStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetWithStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.withStatus, other.withStatus);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleType()).compareTo(other.isSetRoleType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleType, other.roleType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetQueryPhaseRank()).compareTo(other.isSetQueryPhaseRank());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetQueryPhaseRank()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.queryPhaseRank, other.queryPhaseRank);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ActorQueryItem(");
    boolean first = true;

    sb.append("rankingId:");
    sb.append(this.rankingId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actorId:");
    if (this.actorId == null) {
      sb.append("null");
    } else {
      sb.append(this.actorId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("dateStr:");
    if (this.dateStr == null) {
      sb.append("null");
    } else {
      sb.append(this.dateStr);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("withStatus:");
    sb.append(this.withStatus);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleType:");
    sb.append(this.roleType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("queryPhaseRank:");
    sb.append(this.queryPhaseRank);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ActorQueryItemStandardSchemeFactory implements SchemeFactory {
    public ActorQueryItemStandardScheme getScheme() {
      return new ActorQueryItemStandardScheme();
    }
  }

  private static class ActorQueryItemStandardScheme extends StandardScheme<ActorQueryItem> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ActorQueryItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RANKING_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankingId = iprot.readI64();
              struct.setRankingIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ACTOR_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.actorId = iprot.readString();
              struct.setActorIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // DATE_STR
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.dateStr = iprot.readString();
              struct.setDateStrIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // WITH_STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.withStatus = iprot.readBool();
              struct.setWithStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // ROLE_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleType = iprot.readI64();
              struct.setRoleTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // QUERY_PHASE_RANK
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.queryPhaseRank = iprot.readI64();
              struct.setQueryPhaseRankIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map166 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map166.size);
                String _key167;
                String _val168;
                for (int _i169 = 0; _i169 < _map166.size; ++_i169)
                {
                  _key167 = iprot.readString();
                  _val168 = iprot.readString();
                  struct.extData.put(_key167, _val168);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ActorQueryItem struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RANKING_ID_FIELD_DESC);
      oprot.writeI64(struct.rankingId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      if (struct.actorId != null) {
        oprot.writeFieldBegin(ACTOR_ID_FIELD_DESC);
        oprot.writeString(struct.actorId);
        oprot.writeFieldEnd();
      }
      if (struct.dateStr != null) {
        oprot.writeFieldBegin(DATE_STR_FIELD_DESC);
        oprot.writeString(struct.dateStr);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(WITH_STATUS_FIELD_DESC);
      oprot.writeBool(struct.withStatus);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ROLE_TYPE_FIELD_DESC);
      oprot.writeI64(struct.roleType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(QUERY_PHASE_RANK_FIELD_DESC);
      oprot.writeI64(struct.queryPhaseRank);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter170 : struct.extData.entrySet())
          {
            oprot.writeString(_iter170.getKey());
            oprot.writeString(_iter170.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ActorQueryItemTupleSchemeFactory implements SchemeFactory {
    public ActorQueryItemTupleScheme getScheme() {
      return new ActorQueryItemTupleScheme();
    }
  }

  private static class ActorQueryItemTupleScheme extends TupleScheme<ActorQueryItem> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ActorQueryItem struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRankingId()) {
        optionals.set(0);
      }
      if (struct.isSetPhaseId()) {
        optionals.set(1);
      }
      if (struct.isSetActorId()) {
        optionals.set(2);
      }
      if (struct.isSetDateStr()) {
        optionals.set(3);
      }
      if (struct.isSetWithStatus()) {
        optionals.set(4);
      }
      if (struct.isSetRoleType()) {
        optionals.set(5);
      }
      if (struct.isSetQueryPhaseRank()) {
        optionals.set(6);
      }
      if (struct.isSetExtData()) {
        optionals.set(7);
      }
      oprot.writeBitSet(optionals, 8);
      if (struct.isSetRankingId()) {
        oprot.writeI64(struct.rankingId);
      }
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
      if (struct.isSetActorId()) {
        oprot.writeString(struct.actorId);
      }
      if (struct.isSetDateStr()) {
        oprot.writeString(struct.dateStr);
      }
      if (struct.isSetWithStatus()) {
        oprot.writeBool(struct.withStatus);
      }
      if (struct.isSetRoleType()) {
        oprot.writeI64(struct.roleType);
      }
      if (struct.isSetQueryPhaseRank()) {
        oprot.writeI64(struct.queryPhaseRank);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter171 : struct.extData.entrySet())
          {
            oprot.writeString(_iter171.getKey());
            oprot.writeString(_iter171.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ActorQueryItem struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(8);
      if (incoming.get(0)) {
        struct.rankingId = iprot.readI64();
        struct.setRankingIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.actorId = iprot.readString();
        struct.setActorIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.dateStr = iprot.readString();
        struct.setDateStrIsSet(true);
      }
      if (incoming.get(4)) {
        struct.withStatus = iprot.readBool();
        struct.setWithStatusIsSet(true);
      }
      if (incoming.get(5)) {
        struct.roleType = iprot.readI64();
        struct.setRoleTypeIsSet(true);
      }
      if (incoming.get(6)) {
        struct.queryPhaseRank = iprot.readI64();
        struct.setQueryPhaseRankIsSet(true);
      }
      if (incoming.get(7)) {
        {
          org.apache.thrift.protocol.TMap _map172 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map172.size);
          String _key173;
          String _val174;
          for (int _i175 = 0; _i175 < _map172.size; ++_i175)
          {
            _key173 = iprot.readString();
            _val174 = iprot.readString();
            struct.extData.put(_key173, _val174);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

