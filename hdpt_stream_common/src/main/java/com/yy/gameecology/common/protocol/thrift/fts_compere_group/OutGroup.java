/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_compere_group;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum OutGroup implements org.apache.thrift.TEnum {
  NONE(0),
  HAT(1),
  TEAM(2),
  S_MALE(3),
  S_FEMALE(4),
  SUPER(5),
  V_MALE(6),
  V_FEMALE(7),
  A_MALE(8),
  A_FEMALE(9),
  MULTI(10),
  NORMAL(11),
  <PERSON>(12),
  Y<PERSON>(13),
  BB_VIDEO(14),
  BB_AUDIO(15);

  private final int value;

  private OutGroup(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static OutGroup findByValue(int value) { 
    switch (value) {
      case 0:
        return NONE;
      case 1:
        return HAT;
      case 2:
        return TEAM;
      case 3:
        return S_MALE;
      case 4:
        return S_FEMALE;
      case 5:
        return SUPER;
      case 6:
        return V_MALE;
      case 7:
        return V_FEMALE;
      case 8:
        return A_MALE;
      case 9:
        return A_FEMALE;
      case 10:
        return MULTI;
      case 11:
        return NORMAL;
      case 12:
        return New;
      case 13:
        return YZ;
      case 14:
        return BB_VIDEO;
      case 15:
        return BB_AUDIO;
      default:
        return null;
    }
  }
}
