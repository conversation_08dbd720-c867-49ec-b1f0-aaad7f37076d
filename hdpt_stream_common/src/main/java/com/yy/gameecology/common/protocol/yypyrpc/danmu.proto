syntax = "proto3";

package com.yy.gameecology.common.protocol.yypyrpc.danmu;

message Result {
  int32 code = 1;       // 0-成功, 其他失败
  string message = 2;   // 一般为失败提示
}

message QueryGameStatusReq {
  int64 sid = 1;
  int64 ssid = 2;
  int64 uid = 3;
}
message QueryGameStatusResp {
  Result result = 1;
  bool playing = 2;   // 是否游戏中
  string appId = 3;   // 游戏appid
  string serial = 4;  // 开播序列号
  int64 anchorUid = 5;// 主播uid
}

message ChannelGameInfo {
  int64 sid = 1;
  int64 ssid = 2;
  bool playing = 3;   // 是否游戏中
  string appId = 4;   // 游戏appid
  string serial = 5;  // 开播序列号
  int64 anchorUid = 6;// 主播uid
}
message QueryAllGameChannelReq {
}
message QueryAllGameChannelResp {
  Result result = 1;
  repeated ChannelGameInfo channelGameInfo = 2;
}
service DanmakuActivityYrpc {
  // 查询频道游戏状态
  rpc queryGameStatus (QueryGameStatusReq) returns (QueryGameStatusResp);
  // 查询所有游戏频道
  rpc queryAllGameChannel (QueryAllGameChannelReq) returns (QueryAllGameChannelResp);
}
// s2s: danmaku_server