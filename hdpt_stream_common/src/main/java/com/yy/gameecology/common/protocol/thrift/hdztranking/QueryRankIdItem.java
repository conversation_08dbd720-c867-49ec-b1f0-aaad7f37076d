/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class QueryRankIdItem implements org.apache.thrift.TBase<QueryRankIdItem, QueryRankIdItem._Fields>, java.io.Serializable, Cloneable, Comparable<QueryRankIdItem> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryRankIdItem");

  private static final org.apache.thrift.protocol.TField MEMBER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("memberId", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField MEMBER_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("memberType", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryRankIdItemStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryRankIdItemTupleSchemeFactory());
  }

  public String memberId; // required
  public String memberType; // required
  public long phaseId; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    MEMBER_ID((short)1, "memberId"),
    MEMBER_TYPE((short)2, "memberType"),
    PHASE_ID((short)3, "phaseId"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // MEMBER_ID
          return MEMBER_ID;
        case 2: // MEMBER_TYPE
          return MEMBER_TYPE;
        case 3: // PHASE_ID
          return PHASE_ID;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __PHASEID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.MEMBER_ID, new org.apache.thrift.meta_data.FieldMetaData("memberId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MEMBER_TYPE, new org.apache.thrift.meta_data.FieldMetaData("memberType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryRankIdItem.class, metaDataMap);
  }

  public QueryRankIdItem() {
  }

  public QueryRankIdItem(
    String memberId,
    String memberType,
    long phaseId,
    Map<String,String> extData)
  {
    this();
    this.memberId = memberId;
    this.memberType = memberType;
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryRankIdItem(QueryRankIdItem other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetMemberId()) {
      this.memberId = other.memberId;
    }
    if (other.isSetMemberType()) {
      this.memberType = other.memberType;
    }
    this.phaseId = other.phaseId;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public QueryRankIdItem deepCopy() {
    return new QueryRankIdItem(this);
  }

  @Override
  public void clear() {
    this.memberId = null;
    this.memberType = null;
    setPhaseIdIsSet(false);
    this.phaseId = 0;
    this.extData = null;
  }

  public String getMemberId() {
    return this.memberId;
  }

  public QueryRankIdItem setMemberId(String memberId) {
    this.memberId = memberId;
    return this;
  }

  public void unsetMemberId() {
    this.memberId = null;
  }

  /** Returns true if field memberId is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberId() {
    return this.memberId != null;
  }

  public void setMemberIdIsSet(boolean value) {
    if (!value) {
      this.memberId = null;
    }
  }

  public String getMemberType() {
    return this.memberType;
  }

  public QueryRankIdItem setMemberType(String memberType) {
    this.memberType = memberType;
    return this;
  }

  public void unsetMemberType() {
    this.memberType = null;
  }

  /** Returns true if field memberType is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberType() {
    return this.memberType != null;
  }

  public void setMemberTypeIsSet(boolean value) {
    if (!value) {
      this.memberType = null;
    }
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public QueryRankIdItem setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public QueryRankIdItem setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case MEMBER_ID:
      if (value == null) {
        unsetMemberId();
      } else {
        setMemberId((String)value);
      }
      break;

    case MEMBER_TYPE:
      if (value == null) {
        unsetMemberType();
      } else {
        setMemberType((String)value);
      }
      break;

    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case MEMBER_ID:
      return getMemberId();

    case MEMBER_TYPE:
      return getMemberType();

    case PHASE_ID:
      return getPhaseId();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case MEMBER_ID:
      return isSetMemberId();
    case MEMBER_TYPE:
      return isSetMemberType();
    case PHASE_ID:
      return isSetPhaseId();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryRankIdItem)
      return this.equals((QueryRankIdItem)that);
    return false;
  }

  public boolean equals(QueryRankIdItem that) {
    if (that == null)
      return false;

    boolean this_present_memberId = true && this.isSetMemberId();
    boolean that_present_memberId = true && that.isSetMemberId();
    if (this_present_memberId || that_present_memberId) {
      if (!(this_present_memberId && that_present_memberId))
        return false;
      if (!this.memberId.equals(that.memberId))
        return false;
    }

    boolean this_present_memberType = true && this.isSetMemberType();
    boolean that_present_memberType = true && that.isSetMemberType();
    if (this_present_memberType || that_present_memberType) {
      if (!(this_present_memberType && that_present_memberType))
        return false;
      if (!this.memberType.equals(that.memberType))
        return false;
    }

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_memberId = true && (isSetMemberId());
    list.add(present_memberId);
    if (present_memberId)
      list.add(memberId);

    boolean present_memberType = true && (isSetMemberType());
    list.add(present_memberType);
    if (present_memberType)
      list.add(memberType);

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryRankIdItem other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetMemberId()).compareTo(other.isSetMemberId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberId, other.memberId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMemberType()).compareTo(other.isSetMemberType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberType, other.memberType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryRankIdItem(");
    boolean first = true;

    sb.append("memberId:");
    if (this.memberId == null) {
      sb.append("null");
    } else {
      sb.append(this.memberId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("memberType:");
    if (this.memberType == null) {
      sb.append("null");
    } else {
      sb.append(this.memberType);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryRankIdItemStandardSchemeFactory implements SchemeFactory {
    public QueryRankIdItemStandardScheme getScheme() {
      return new QueryRankIdItemStandardScheme();
    }
  }

  private static class QueryRankIdItemStandardScheme extends StandardScheme<QueryRankIdItem> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryRankIdItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // MEMBER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.memberId = iprot.readString();
              struct.setMemberIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MEMBER_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.memberType = iprot.readString();
              struct.setMemberTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map360 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map360.size);
                String _key361;
                String _val362;
                for (int _i363 = 0; _i363 < _map360.size; ++_i363)
                {
                  _key361 = iprot.readString();
                  _val362 = iprot.readString();
                  struct.extData.put(_key361, _val362);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryRankIdItem struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.memberId != null) {
        oprot.writeFieldBegin(MEMBER_ID_FIELD_DESC);
        oprot.writeString(struct.memberId);
        oprot.writeFieldEnd();
      }
      if (struct.memberType != null) {
        oprot.writeFieldBegin(MEMBER_TYPE_FIELD_DESC);
        oprot.writeString(struct.memberType);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter364 : struct.extData.entrySet())
          {
            oprot.writeString(_iter364.getKey());
            oprot.writeString(_iter364.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryRankIdItemTupleSchemeFactory implements SchemeFactory {
    public QueryRankIdItemTupleScheme getScheme() {
      return new QueryRankIdItemTupleScheme();
    }
  }

  private static class QueryRankIdItemTupleScheme extends TupleScheme<QueryRankIdItem> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryRankIdItem struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetMemberId()) {
        optionals.set(0);
      }
      if (struct.isSetMemberType()) {
        optionals.set(1);
      }
      if (struct.isSetPhaseId()) {
        optionals.set(2);
      }
      if (struct.isSetExtData()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetMemberId()) {
        oprot.writeString(struct.memberId);
      }
      if (struct.isSetMemberType()) {
        oprot.writeString(struct.memberType);
      }
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter365 : struct.extData.entrySet())
          {
            oprot.writeString(_iter365.getKey());
            oprot.writeString(_iter365.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryRankIdItem struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.memberId = iprot.readString();
        struct.setMemberIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.memberType = iprot.readString();
        struct.setMemberTypeIsSet(true);
      }
      if (incoming.get(2)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TMap _map366 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map366.size);
          String _key367;
          String _val368;
          for (int _i369 = 0; _i369 < _map366.size; ++_i369)
          {
            _key367 = iprot.readString();
            _val368 = iprot.readString();
            struct.extData.put(_key367, _val368);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

