/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_room_manager;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-09-05")
public class GetAntiPoachingListResp implements org.apache.thrift.TBase<GetAntiPoachingListResp, GetAntiPoachingListResp._Fields>, java.io.Serializable, Cloneable, Comparable<GetAntiPoachingListResp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GetAntiPoachingListResp");

  private static final org.apache.thrift.protocol.TField RET_FIELD_DESC = new org.apache.thrift.protocol.TField("ret", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField MSG_FIELD_DESC = new org.apache.thrift.protocol.TField("msg", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField RET_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("retList", org.apache.thrift.protocol.TType.LIST, (short)3);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new GetAntiPoachingListRespStandardSchemeFactory());
    schemes.put(TupleScheme.class, new GetAntiPoachingListRespTupleSchemeFactory());
  }

  public int ret; // required
  public String msg; // required
  public List<AntiPoachingInfo> retList; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RET((short)1, "ret"),
    MSG((short)2, "msg"),
    RET_LIST((short)3, "retList");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RET
          return RET;
        case 2: // MSG
          return MSG;
        case 3: // RET_LIST
          return RET_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RET_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RET, new org.apache.thrift.meta_data.FieldMetaData("ret", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MSG, new org.apache.thrift.meta_data.FieldMetaData("msg", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RET_LIST, new org.apache.thrift.meta_data.FieldMetaData("retList", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AntiPoachingInfo.class))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GetAntiPoachingListResp.class, metaDataMap);
  }

  public GetAntiPoachingListResp() {
  }

  public GetAntiPoachingListResp(
    int ret,
    String msg,
    List<AntiPoachingInfo> retList)
  {
    this();
    this.ret = ret;
    setRetIsSet(true);
    this.msg = msg;
    this.retList = retList;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GetAntiPoachingListResp(GetAntiPoachingListResp other) {
    __isset_bitfield = other.__isset_bitfield;
    this.ret = other.ret;
    if (other.isSetMsg()) {
      this.msg = other.msg;
    }
    if (other.isSetRetList()) {
      List<AntiPoachingInfo> __this__retList = new ArrayList<AntiPoachingInfo>(other.retList.size());
      for (AntiPoachingInfo other_element : other.retList) {
        __this__retList.add(new AntiPoachingInfo(other_element));
      }
      this.retList = __this__retList;
    }
  }

  public GetAntiPoachingListResp deepCopy() {
    return new GetAntiPoachingListResp(this);
  }

  @Override
  public void clear() {
    setRetIsSet(false);
    this.ret = 0;
    this.msg = null;
    this.retList = null;
  }

  public int getRet() {
    return this.ret;
  }

  public GetAntiPoachingListResp setRet(int ret) {
    this.ret = ret;
    setRetIsSet(true);
    return this;
  }

  public void unsetRet() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RET_ISSET_ID);
  }

  /** Returns true if field ret is set (has been assigned a value) and false otherwise */
  public boolean isSetRet() {
    return EncodingUtils.testBit(__isset_bitfield, __RET_ISSET_ID);
  }

  public void setRetIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RET_ISSET_ID, value);
  }

  public String getMsg() {
    return this.msg;
  }

  public GetAntiPoachingListResp setMsg(String msg) {
    this.msg = msg;
    return this;
  }

  public void unsetMsg() {
    this.msg = null;
  }

  /** Returns true if field msg is set (has been assigned a value) and false otherwise */
  public boolean isSetMsg() {
    return this.msg != null;
  }

  public void setMsgIsSet(boolean value) {
    if (!value) {
      this.msg = null;
    }
  }

  public int getRetListSize() {
    return (this.retList == null) ? 0 : this.retList.size();
  }

  public java.util.Iterator<AntiPoachingInfo> getRetListIterator() {
    return (this.retList == null) ? null : this.retList.iterator();
  }

  public void addToRetList(AntiPoachingInfo elem) {
    if (this.retList == null) {
      this.retList = new ArrayList<AntiPoachingInfo>();
    }
    this.retList.add(elem);
  }

  public List<AntiPoachingInfo> getRetList() {
    return this.retList;
  }

  public GetAntiPoachingListResp setRetList(List<AntiPoachingInfo> retList) {
    this.retList = retList;
    return this;
  }

  public void unsetRetList() {
    this.retList = null;
  }

  /** Returns true if field retList is set (has been assigned a value) and false otherwise */
  public boolean isSetRetList() {
    return this.retList != null;
  }

  public void setRetListIsSet(boolean value) {
    if (!value) {
      this.retList = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RET:
      if (value == null) {
        unsetRet();
      } else {
        setRet((Integer)value);
      }
      break;

    case MSG:
      if (value == null) {
        unsetMsg();
      } else {
        setMsg((String)value);
      }
      break;

    case RET_LIST:
      if (value == null) {
        unsetRetList();
      } else {
        setRetList((List<AntiPoachingInfo>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RET:
      return getRet();

    case MSG:
      return getMsg();

    case RET_LIST:
      return getRetList();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RET:
      return isSetRet();
    case MSG:
      return isSetMsg();
    case RET_LIST:
      return isSetRetList();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof GetAntiPoachingListResp)
      return this.equals((GetAntiPoachingListResp)that);
    return false;
  }

  public boolean equals(GetAntiPoachingListResp that) {
    if (that == null)
      return false;

    boolean this_present_ret = true;
    boolean that_present_ret = true;
    if (this_present_ret || that_present_ret) {
      if (!(this_present_ret && that_present_ret))
        return false;
      if (this.ret != that.ret)
        return false;
    }

    boolean this_present_msg = true && this.isSetMsg();
    boolean that_present_msg = true && that.isSetMsg();
    if (this_present_msg || that_present_msg) {
      if (!(this_present_msg && that_present_msg))
        return false;
      if (!this.msg.equals(that.msg))
        return false;
    }

    boolean this_present_retList = true && this.isSetRetList();
    boolean that_present_retList = true && that.isSetRetList();
    if (this_present_retList || that_present_retList) {
      if (!(this_present_retList && that_present_retList))
        return false;
      if (!this.retList.equals(that.retList))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_ret = true;
    list.add(present_ret);
    if (present_ret)
      list.add(ret);

    boolean present_msg = true && (isSetMsg());
    list.add(present_msg);
    if (present_msg)
      list.add(msg);

    boolean present_retList = true && (isSetRetList());
    list.add(present_retList);
    if (present_retList)
      list.add(retList);

    return list.hashCode();
  }

  @Override
  public int compareTo(GetAntiPoachingListResp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRet()).compareTo(other.isSetRet());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRet()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ret, other.ret);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMsg()).compareTo(other.isSetMsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.msg, other.msg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRetList()).compareTo(other.isSetRetList());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRetList()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.retList, other.retList);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("GetAntiPoachingListResp(");
    boolean first = true;

    sb.append("ret:");
    sb.append(this.ret);
    first = false;
    if (!first) sb.append(", ");
    sb.append("msg:");
    if (this.msg == null) {
      sb.append("null");
    } else {
      sb.append(this.msg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("retList:");
    if (this.retList == null) {
      sb.append("null");
    } else {
      sb.append(this.retList);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GetAntiPoachingListRespStandardSchemeFactory implements SchemeFactory {
    public GetAntiPoachingListRespStandardScheme getScheme() {
      return new GetAntiPoachingListRespStandardScheme();
    }
  }

  private static class GetAntiPoachingListRespStandardScheme extends StandardScheme<GetAntiPoachingListResp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GetAntiPoachingListResp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RET
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.ret = iprot.readI32();
              struct.setRetIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.msg = iprot.readString();
              struct.setMsgIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // RET_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.retList = new ArrayList<AntiPoachingInfo>(_list0.size);
                AntiPoachingInfo _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new AntiPoachingInfo();
                  _elem1.read(iprot);
                  struct.retList.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setRetListIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GetAntiPoachingListResp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RET_FIELD_DESC);
      oprot.writeI32(struct.ret);
      oprot.writeFieldEnd();
      if (struct.msg != null) {
        oprot.writeFieldBegin(MSG_FIELD_DESC);
        oprot.writeString(struct.msg);
        oprot.writeFieldEnd();
      }
      if (struct.retList != null) {
        oprot.writeFieldBegin(RET_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.retList.size()));
          for (AntiPoachingInfo _iter3 : struct.retList)
          {
            _iter3.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GetAntiPoachingListRespTupleSchemeFactory implements SchemeFactory {
    public GetAntiPoachingListRespTupleScheme getScheme() {
      return new GetAntiPoachingListRespTupleScheme();
    }
  }

  private static class GetAntiPoachingListRespTupleScheme extends TupleScheme<GetAntiPoachingListResp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GetAntiPoachingListResp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRet()) {
        optionals.set(0);
      }
      if (struct.isSetMsg()) {
        optionals.set(1);
      }
      if (struct.isSetRetList()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetRet()) {
        oprot.writeI32(struct.ret);
      }
      if (struct.isSetMsg()) {
        oprot.writeString(struct.msg);
      }
      if (struct.isSetRetList()) {
        {
          oprot.writeI32(struct.retList.size());
          for (AntiPoachingInfo _iter4 : struct.retList)
          {
            _iter4.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GetAntiPoachingListResp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.ret = iprot.readI32();
        struct.setRetIsSet(true);
      }
      if (incoming.get(1)) {
        struct.msg = iprot.readString();
        struct.setMsgIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TList _list5 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.retList = new ArrayList<AntiPoachingInfo>(_list5.size);
          AntiPoachingInfo _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new AntiPoachingInfo();
            _elem6.read(iprot);
            struct.retList.add(_elem6);
          }
        }
        struct.setRetListIsSet(true);
      }
    }
  }

}

