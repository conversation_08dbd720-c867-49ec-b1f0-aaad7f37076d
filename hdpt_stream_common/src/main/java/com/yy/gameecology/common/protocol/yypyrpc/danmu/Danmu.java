// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: danmu.proto

package com.yy.gameecology.common.protocol.yypyrpc.danmu;

public final class Danmu {
  private Danmu() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.danmu.Result)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 0-成功, 其他失败
     * </pre>
     *
     * <code>int32 code = 1;</code>
     */
    int getCode();

    /**
     * <pre>
     * 一般为失败提示
     * </pre>
     *
     * <code>string message = 2;</code>
     */
    java.lang.String getMessage();
    /**
     * <pre>
     * 一般为失败提示
     * </pre>
     *
     * <code>string message = 2;</code>
     */
    com.google.protobuf.ByteString
        getMessageBytes();
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.Result}
   */
  public  static final class Result extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.Result)
      ResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Result.newBuilder() to construct.
    private Result(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Result() {
      code_ = 0;
      message_ = "";
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Result(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              code_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              message_ = s;
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_;
    /**
     * <pre>
     * 0-成功, 其他失败
     * </pre>
     *
     * <code>int32 code = 1;</code>
     */
    public int getCode() {
      return code_;
    }

    public static final int MESSAGE_FIELD_NUMBER = 2;
    private volatile java.lang.Object message_;
    /**
     * <pre>
     * 一般为失败提示
     * </pre>
     *
     * <code>string message = 2;</code>
     */
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        message_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 一般为失败提示
     * </pre>
     *
     * <code>string message = 2;</code>
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (!getMessageBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, message_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (!getMessageBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, message_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result other = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result) obj;

      boolean result = true;
      result = result && (getCode()
          == other.getCode());
      result = result && getMessage()
          .equals(other.getMessage());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
      hash = (53 * hash) + getMessage().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.Result}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.Result)
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        code_ = 0;

        message_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result build() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result result = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result(this);
        result.code_ = code_;
        result.message_ = message_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (!other.getMessage().isEmpty()) {
          message_ = other.message_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int code_ ;
      /**
       * <pre>
       * 0-成功, 其他失败
       * </pre>
       *
       * <code>int32 code = 1;</code>
       */
      public int getCode() {
        return code_;
      }
      /**
       * <pre>
       * 0-成功, 其他失败
       * </pre>
       *
       * <code>int32 code = 1;</code>
       */
      public Builder setCode(int value) {
        
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0-成功, 其他失败
       * </pre>
       *
       * <code>int32 code = 1;</code>
       */
      public Builder clearCode() {
        
        code_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object message_ = "";
      /**
       * <pre>
       * 一般为失败提示
       * </pre>
       *
       * <code>string message = 2;</code>
       */
      public java.lang.String getMessage() {
        java.lang.Object ref = message_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          message_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 一般为失败提示
       * </pre>
       *
       * <code>string message = 2;</code>
       */
      public com.google.protobuf.ByteString
          getMessageBytes() {
        java.lang.Object ref = message_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          message_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 一般为失败提示
       * </pre>
       *
       * <code>string message = 2;</code>
       */
      public Builder setMessage(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        message_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 一般为失败提示
       * </pre>
       *
       * <code>string message = 2;</code>
       */
      public Builder clearMessage() {
        
        message_ = getDefaultInstance().getMessage();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 一般为失败提示
       * </pre>
       *
       * <code>string message = 2;</code>
       */
      public Builder setMessageBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        message_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.Result)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.Result)
    private static final com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Result>
        PARSER = new com.google.protobuf.AbstractParser<Result>() {
      @java.lang.Override
      public Result parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Result(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Result> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Result> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryGameStatusReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusReq)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 sid = 1;</code>
     */
    long getSid();

    /**
     * <code>int64 ssid = 2;</code>
     */
    long getSsid();

    /**
     * <code>int64 uid = 3;</code>
     */
    long getUid();
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusReq}
   */
  public  static final class QueryGameStatusReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusReq)
      QueryGameStatusReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryGameStatusReq.newBuilder() to construct.
    private QueryGameStatusReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    public QueryGameStatusReq() {
      sid_ = 0L;
      ssid_ = 0L;
      uid_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryGameStatusReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              sid_ = input.readInt64();
              break;
            }
            case 16: {

              ssid_ = input.readInt64();
              break;
            }
            case 24: {

              uid_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq.Builder.class);
    }

    public static final int SID_FIELD_NUMBER = 1;
    private long sid_;
    /**
     * <code>int64 sid = 1;</code>
     */
    public long getSid() {
      return sid_;
    }

    public static final int SSID_FIELD_NUMBER = 2;
    private long ssid_;
    /**
     * <code>int64 ssid = 2;</code>
     */
    public long getSsid() {
      return ssid_;
    }

    public static final int UID_FIELD_NUMBER = 3;
    private long uid_;
    /**
     * <code>int64 uid = 3;</code>
     */
    public long getUid() {
      return uid_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (sid_ != 0L) {
        output.writeInt64(1, sid_);
      }
      if (ssid_ != 0L) {
        output.writeInt64(2, ssid_);
      }
      if (uid_ != 0L) {
        output.writeInt64(3, uid_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (sid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, sid_);
      }
      if (ssid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, ssid_);
      }
      if (uid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, uid_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq other = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq) obj;

      boolean result = true;
      result = result && (getSid()
          == other.getSid());
      result = result && (getSsid()
          == other.getSsid());
      result = result && (getUid()
          == other.getUid());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSid());
      hash = (37 * hash) + SSID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSsid());
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUid());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusReq)
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        sid_ = 0L;

        ssid_ = 0L;

        uid_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq build() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq result = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq(this);
        result.sid_ = sid_;
        result.ssid_ = ssid_;
        result.uid_ = uid_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq.getDefaultInstance()) return this;
        if (other.getSid() != 0L) {
          setSid(other.getSid());
        }
        if (other.getSsid() != 0L) {
          setSsid(other.getSsid());
        }
        if (other.getUid() != 0L) {
          setUid(other.getUid());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long sid_ ;
      /**
       * <code>int64 sid = 1;</code>
       */
      public long getSid() {
        return sid_;
      }
      /**
       * <code>int64 sid = 1;</code>
       */
      public Builder setSid(long value) {
        
        sid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 sid = 1;</code>
       */
      public Builder clearSid() {
        
        sid_ = 0L;
        onChanged();
        return this;
      }

      private long ssid_ ;
      /**
       * <code>int64 ssid = 2;</code>
       */
      public long getSsid() {
        return ssid_;
      }
      /**
       * <code>int64 ssid = 2;</code>
       */
      public Builder setSsid(long value) {
        
        ssid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 ssid = 2;</code>
       */
      public Builder clearSsid() {
        
        ssid_ = 0L;
        onChanged();
        return this;
      }

      private long uid_ ;
      /**
       * <code>int64 uid = 3;</code>
       */
      public long getUid() {
        return uid_;
      }
      /**
       * <code>int64 uid = 3;</code>
       */
      public Builder setUid(long value) {
        
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 uid = 3;</code>
       */
      public Builder clearUid() {
        
        uid_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusReq)
    private static final com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryGameStatusReq>
        PARSER = new com.google.protobuf.AbstractParser<QueryGameStatusReq>() {
      @java.lang.Override
      public QueryGameStatusReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryGameStatusReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryGameStatusReq> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryGameStatusReq> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryGameStatusRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    boolean hasResult();
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result getResult();
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder getResultOrBuilder();

    /**
     * <pre>
     * 是否游戏中
     * </pre>
     *
     * <code>bool playing = 2;</code>
     */
    boolean getPlaying();

    /**
     * <pre>
     * 游戏appid
     * </pre>
     *
     * <code>string appId = 3;</code>
     */
    java.lang.String getAppId();
    /**
     * <pre>
     * 游戏appid
     * </pre>
     *
     * <code>string appId = 3;</code>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    /**
     * <pre>
     * 开播序列号
     * </pre>
     *
     * <code>string serial = 4;</code>
     */
    java.lang.String getSerial();
    /**
     * <pre>
     * 开播序列号
     * </pre>
     *
     * <code>string serial = 4;</code>
     */
    com.google.protobuf.ByteString
        getSerialBytes();

    /**
     * <pre>
     * 主播uid
     * </pre>
     *
     * <code>int64 anchorUid = 5;</code>
     */
    long getAnchorUid();
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusResp}
   */
  public  static final class QueryGameStatusResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusResp)
      QueryGameStatusRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryGameStatusResp.newBuilder() to construct.
    private QueryGameStatusResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryGameStatusResp() {
      playing_ = false;
      appId_ = "";
      serial_ = "";
      anchorUid_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryGameStatusResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder subBuilder = null;
              if (result_ != null) {
                subBuilder = result_.toBuilder();
              }
              result_ = input.readMessage(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(result_);
                result_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              playing_ = input.readBool();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              appId_ = s;
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              serial_ = s;
              break;
            }
            case 40: {

              anchorUid_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp.Builder.class);
    }

    public static final int RESULT_FIELD_NUMBER = 1;
    private com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result result_;
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    public boolean hasResult() {
      return result_ != null;
    }
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result getResult() {
      return result_ == null ? com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.getDefaultInstance() : result_;
    }
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder getResultOrBuilder() {
      return getResult();
    }

    public static final int PLAYING_FIELD_NUMBER = 2;
    private boolean playing_;
    /**
     * <pre>
     * 是否游戏中
     * </pre>
     *
     * <code>bool playing = 2;</code>
     */
    public boolean getPlaying() {
      return playing_;
    }

    public static final int APPID_FIELD_NUMBER = 3;
    private volatile java.lang.Object appId_;
    /**
     * <pre>
     * 游戏appid
     * </pre>
     *
     * <code>string appId = 3;</code>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 游戏appid
     * </pre>
     *
     * <code>string appId = 3;</code>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SERIAL_FIELD_NUMBER = 4;
    private volatile java.lang.Object serial_;
    /**
     * <pre>
     * 开播序列号
     * </pre>
     *
     * <code>string serial = 4;</code>
     */
    public java.lang.String getSerial() {
      java.lang.Object ref = serial_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        serial_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 开播序列号
     * </pre>
     *
     * <code>string serial = 4;</code>
     */
    public com.google.protobuf.ByteString
        getSerialBytes() {
      java.lang.Object ref = serial_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        serial_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ANCHORUID_FIELD_NUMBER = 5;
    private long anchorUid_;
    /**
     * <pre>
     * 主播uid
     * </pre>
     *
     * <code>int64 anchorUid = 5;</code>
     */
    public long getAnchorUid() {
      return anchorUid_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != null) {
        output.writeMessage(1, getResult());
      }
      if (playing_ != false) {
        output.writeBool(2, playing_);
      }
      if (!getAppIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, appId_);
      }
      if (!getSerialBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, serial_);
      }
      if (anchorUid_ != 0L) {
        output.writeInt64(5, anchorUid_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getResult());
      }
      if (playing_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, playing_);
      }
      if (!getAppIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, appId_);
      }
      if (!getSerialBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, serial_);
      }
      if (anchorUid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, anchorUid_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp other = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp) obj;

      boolean result = true;
      result = result && (hasResult() == other.hasResult());
      if (hasResult()) {
        result = result && getResult()
            .equals(other.getResult());
      }
      result = result && (getPlaying()
          == other.getPlaying());
      result = result && getAppId()
          .equals(other.getAppId());
      result = result && getSerial()
          .equals(other.getSerial());
      result = result && (getAnchorUid()
          == other.getAnchorUid());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      hash = (37 * hash) + PLAYING_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getPlaying());
      hash = (37 * hash) + APPID_FIELD_NUMBER;
      hash = (53 * hash) + getAppId().hashCode();
      hash = (37 * hash) + SERIAL_FIELD_NUMBER;
      hash = (53 * hash) + getSerial().hashCode();
      hash = (37 * hash) + ANCHORUID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAnchorUid());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusResp)
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (resultBuilder_ == null) {
          result_ = null;
        } else {
          result_ = null;
          resultBuilder_ = null;
        }
        playing_ = false;

        appId_ = "";

        serial_ = "";

        anchorUid_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp build() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp result = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp(this);
        if (resultBuilder_ == null) {
          result.result_ = result_;
        } else {
          result.result_ = resultBuilder_.build();
        }
        result.playing_ = playing_;
        result.appId_ = appId_;
        result.serial_ = serial_;
        result.anchorUid_ = anchorUid_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp.getDefaultInstance()) return this;
        if (other.hasResult()) {
          mergeResult(other.getResult());
        }
        if (other.getPlaying() != false) {
          setPlaying(other.getPlaying());
        }
        if (!other.getAppId().isEmpty()) {
          appId_ = other.appId_;
          onChanged();
        }
        if (!other.getSerial().isEmpty()) {
          serial_ = other.serial_;
          onChanged();
        }
        if (other.getAnchorUid() != 0L) {
          setAnchorUid(other.getAnchorUid());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result result_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder> resultBuilder_;
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public boolean hasResult() {
        return resultBuilder_ != null || result_ != null;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result getResult() {
        if (resultBuilder_ == null) {
          return result_ == null ? com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.getDefaultInstance() : result_;
        } else {
          return resultBuilder_.getMessage();
        }
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public Builder setResult(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          result_ = value;
          onChanged();
        } else {
          resultBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public Builder setResult(
          com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder builderForValue) {
        if (resultBuilder_ == null) {
          result_ = builderForValue.build();
          onChanged();
        } else {
          resultBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public Builder mergeResult(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result value) {
        if (resultBuilder_ == null) {
          if (result_ != null) {
            result_ =
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.newBuilder(result_).mergeFrom(value).buildPartial();
          } else {
            result_ = value;
          }
          onChanged();
        } else {
          resultBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public Builder clearResult() {
        if (resultBuilder_ == null) {
          result_ = null;
          onChanged();
        } else {
          result_ = null;
          resultBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder getResultBuilder() {
        
        onChanged();
        return getResultFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder getResultOrBuilder() {
        if (resultBuilder_ != null) {
          return resultBuilder_.getMessageOrBuilder();
        } else {
          return result_ == null ?
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.getDefaultInstance() : result_;
        }
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder> 
          getResultFieldBuilder() {
        if (resultBuilder_ == null) {
          resultBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder>(
                  getResult(),
                  getParentForChildren(),
                  isClean());
          result_ = null;
        }
        return resultBuilder_;
      }

      private boolean playing_ ;
      /**
       * <pre>
       * 是否游戏中
       * </pre>
       *
       * <code>bool playing = 2;</code>
       */
      public boolean getPlaying() {
        return playing_;
      }
      /**
       * <pre>
       * 是否游戏中
       * </pre>
       *
       * <code>bool playing = 2;</code>
       */
      public Builder setPlaying(boolean value) {
        
        playing_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否游戏中
       * </pre>
       *
       * <code>bool playing = 2;</code>
       */
      public Builder clearPlaying() {
        
        playing_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object appId_ = "";
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 3;</code>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 3;</code>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 3;</code>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 3;</code>
       */
      public Builder clearAppId() {
        
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 3;</code>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        appId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object serial_ = "";
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 4;</code>
       */
      public java.lang.String getSerial() {
        java.lang.Object ref = serial_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          serial_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 4;</code>
       */
      public com.google.protobuf.ByteString
          getSerialBytes() {
        java.lang.Object ref = serial_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          serial_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 4;</code>
       */
      public Builder setSerial(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        serial_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 4;</code>
       */
      public Builder clearSerial() {
        
        serial_ = getDefaultInstance().getSerial();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 4;</code>
       */
      public Builder setSerialBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        serial_ = value;
        onChanged();
        return this;
      }

      private long anchorUid_ ;
      /**
       * <pre>
       * 主播uid
       * </pre>
       *
       * <code>int64 anchorUid = 5;</code>
       */
      public long getAnchorUid() {
        return anchorUid_;
      }
      /**
       * <pre>
       * 主播uid
       * </pre>
       *
       * <code>int64 anchorUid = 5;</code>
       */
      public Builder setAnchorUid(long value) {
        
        anchorUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 主播uid
       * </pre>
       *
       * <code>int64 anchorUid = 5;</code>
       */
      public Builder clearAnchorUid() {
        
        anchorUid_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryGameStatusResp)
    private static final com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryGameStatusResp>
        PARSER = new com.google.protobuf.AbstractParser<QueryGameStatusResp>() {
      @java.lang.Override
      public QueryGameStatusResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryGameStatusResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryGameStatusResp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryGameStatusResp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryGameStatusResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChannelGameInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 sid = 1;</code>
     */
    long getSid();

    /**
     * <code>int64 ssid = 2;</code>
     */
    long getSsid();

    /**
     * <pre>
     * 是否游戏中
     * </pre>
     *
     * <code>bool playing = 3;</code>
     */
    boolean getPlaying();

    /**
     * <pre>
     * 游戏appid
     * </pre>
     *
     * <code>string appId = 4;</code>
     */
    java.lang.String getAppId();
    /**
     * <pre>
     * 游戏appid
     * </pre>
     *
     * <code>string appId = 4;</code>
     */
    com.google.protobuf.ByteString
        getAppIdBytes();

    /**
     * <pre>
     * 开播序列号
     * </pre>
     *
     * <code>string serial = 5;</code>
     */
    java.lang.String getSerial();
    /**
     * <pre>
     * 开播序列号
     * </pre>
     *
     * <code>string serial = 5;</code>
     */
    com.google.protobuf.ByteString
        getSerialBytes();

    /**
     * <pre>
     * 主播uid
     * </pre>
     *
     * <code>int64 anchorUid = 6;</code>
     */
    long getAnchorUid();
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo}
   */
  public  static final class ChannelGameInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo)
      ChannelGameInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChannelGameInfo.newBuilder() to construct.
    private ChannelGameInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChannelGameInfo() {
      sid_ = 0L;
      ssid_ = 0L;
      playing_ = false;
      appId_ = "";
      serial_ = "";
      anchorUid_ = 0L;
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChannelGameInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              sid_ = input.readInt64();
              break;
            }
            case 16: {

              ssid_ = input.readInt64();
              break;
            }
            case 24: {

              playing_ = input.readBool();
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              appId_ = s;
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              serial_ = s;
              break;
            }
            case 48: {

              anchorUid_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder.class);
    }

    public static final int SID_FIELD_NUMBER = 1;
    private long sid_;
    /**
     * <code>int64 sid = 1;</code>
     */
    public long getSid() {
      return sid_;
    }

    public static final int SSID_FIELD_NUMBER = 2;
    private long ssid_;
    /**
     * <code>int64 ssid = 2;</code>
     */
    public long getSsid() {
      return ssid_;
    }

    public static final int PLAYING_FIELD_NUMBER = 3;
    private boolean playing_;
    /**
     * <pre>
     * 是否游戏中
     * </pre>
     *
     * <code>bool playing = 3;</code>
     */
    public boolean getPlaying() {
      return playing_;
    }

    public static final int APPID_FIELD_NUMBER = 4;
    private volatile java.lang.Object appId_;
    /**
     * <pre>
     * 游戏appid
     * </pre>
     *
     * <code>string appId = 4;</code>
     */
    public java.lang.String getAppId() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appId_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 游戏appid
     * </pre>
     *
     * <code>string appId = 4;</code>
     */
    public com.google.protobuf.ByteString
        getAppIdBytes() {
      java.lang.Object ref = appId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SERIAL_FIELD_NUMBER = 5;
    private volatile java.lang.Object serial_;
    /**
     * <pre>
     * 开播序列号
     * </pre>
     *
     * <code>string serial = 5;</code>
     */
    public java.lang.String getSerial() {
      java.lang.Object ref = serial_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        serial_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 开播序列号
     * </pre>
     *
     * <code>string serial = 5;</code>
     */
    public com.google.protobuf.ByteString
        getSerialBytes() {
      java.lang.Object ref = serial_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        serial_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ANCHORUID_FIELD_NUMBER = 6;
    private long anchorUid_;
    /**
     * <pre>
     * 主播uid
     * </pre>
     *
     * <code>int64 anchorUid = 6;</code>
     */
    public long getAnchorUid() {
      return anchorUid_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (sid_ != 0L) {
        output.writeInt64(1, sid_);
      }
      if (ssid_ != 0L) {
        output.writeInt64(2, ssid_);
      }
      if (playing_ != false) {
        output.writeBool(3, playing_);
      }
      if (!getAppIdBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, appId_);
      }
      if (!getSerialBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, serial_);
      }
      if (anchorUid_ != 0L) {
        output.writeInt64(6, anchorUid_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (sid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, sid_);
      }
      if (ssid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, ssid_);
      }
      if (playing_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, playing_);
      }
      if (!getAppIdBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, appId_);
      }
      if (!getSerialBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, serial_);
      }
      if (anchorUid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, anchorUid_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo other = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo) obj;

      boolean result = true;
      result = result && (getSid()
          == other.getSid());
      result = result && (getSsid()
          == other.getSsid());
      result = result && (getPlaying()
          == other.getPlaying());
      result = result && getAppId()
          .equals(other.getAppId());
      result = result && getSerial()
          .equals(other.getSerial());
      result = result && (getAnchorUid()
          == other.getAnchorUid());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSid());
      hash = (37 * hash) + SSID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSsid());
      hash = (37 * hash) + PLAYING_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getPlaying());
      hash = (37 * hash) + APPID_FIELD_NUMBER;
      hash = (53 * hash) + getAppId().hashCode();
      hash = (37 * hash) + SERIAL_FIELD_NUMBER;
      hash = (53 * hash) + getSerial().hashCode();
      hash = (37 * hash) + ANCHORUID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getAnchorUid());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo)
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        sid_ = 0L;

        ssid_ = 0L;

        playing_ = false;

        appId_ = "";

        serial_ = "";

        anchorUid_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo build() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo result = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo(this);
        result.sid_ = sid_;
        result.ssid_ = ssid_;
        result.playing_ = playing_;
        result.appId_ = appId_;
        result.serial_ = serial_;
        result.anchorUid_ = anchorUid_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.getDefaultInstance()) return this;
        if (other.getSid() != 0L) {
          setSid(other.getSid());
        }
        if (other.getSsid() != 0L) {
          setSsid(other.getSsid());
        }
        if (other.getPlaying() != false) {
          setPlaying(other.getPlaying());
        }
        if (!other.getAppId().isEmpty()) {
          appId_ = other.appId_;
          onChanged();
        }
        if (!other.getSerial().isEmpty()) {
          serial_ = other.serial_;
          onChanged();
        }
        if (other.getAnchorUid() != 0L) {
          setAnchorUid(other.getAnchorUid());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long sid_ ;
      /**
       * <code>int64 sid = 1;</code>
       */
      public long getSid() {
        return sid_;
      }
      /**
       * <code>int64 sid = 1;</code>
       */
      public Builder setSid(long value) {
        
        sid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 sid = 1;</code>
       */
      public Builder clearSid() {
        
        sid_ = 0L;
        onChanged();
        return this;
      }

      private long ssid_ ;
      /**
       * <code>int64 ssid = 2;</code>
       */
      public long getSsid() {
        return ssid_;
      }
      /**
       * <code>int64 ssid = 2;</code>
       */
      public Builder setSsid(long value) {
        
        ssid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 ssid = 2;</code>
       */
      public Builder clearSsid() {
        
        ssid_ = 0L;
        onChanged();
        return this;
      }

      private boolean playing_ ;
      /**
       * <pre>
       * 是否游戏中
       * </pre>
       *
       * <code>bool playing = 3;</code>
       */
      public boolean getPlaying() {
        return playing_;
      }
      /**
       * <pre>
       * 是否游戏中
       * </pre>
       *
       * <code>bool playing = 3;</code>
       */
      public Builder setPlaying(boolean value) {
        
        playing_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否游戏中
       * </pre>
       *
       * <code>bool playing = 3;</code>
       */
      public Builder clearPlaying() {
        
        playing_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object appId_ = "";
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 4;</code>
       */
      public java.lang.String getAppId() {
        java.lang.Object ref = appId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 4;</code>
       */
      public com.google.protobuf.ByteString
          getAppIdBytes() {
        java.lang.Object ref = appId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 4;</code>
       */
      public Builder setAppId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        appId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 4;</code>
       */
      public Builder clearAppId() {
        
        appId_ = getDefaultInstance().getAppId();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 游戏appid
       * </pre>
       *
       * <code>string appId = 4;</code>
       */
      public Builder setAppIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        appId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object serial_ = "";
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 5;</code>
       */
      public java.lang.String getSerial() {
        java.lang.Object ref = serial_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          serial_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 5;</code>
       */
      public com.google.protobuf.ByteString
          getSerialBytes() {
        java.lang.Object ref = serial_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          serial_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 5;</code>
       */
      public Builder setSerial(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        serial_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 5;</code>
       */
      public Builder clearSerial() {
        
        serial_ = getDefaultInstance().getSerial();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 开播序列号
       * </pre>
       *
       * <code>string serial = 5;</code>
       */
      public Builder setSerialBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        serial_ = value;
        onChanged();
        return this;
      }

      private long anchorUid_ ;
      /**
       * <pre>
       * 主播uid
       * </pre>
       *
       * <code>int64 anchorUid = 6;</code>
       */
      public long getAnchorUid() {
        return anchorUid_;
      }
      /**
       * <pre>
       * 主播uid
       * </pre>
       *
       * <code>int64 anchorUid = 6;</code>
       */
      public Builder setAnchorUid(long value) {
        
        anchorUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 主播uid
       * </pre>
       *
       * <code>int64 anchorUid = 6;</code>
       */
      public Builder clearAnchorUid() {
        
        anchorUid_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo)
    private static final com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChannelGameInfo>
        PARSER = new com.google.protobuf.AbstractParser<ChannelGameInfo>() {
      @java.lang.Override
      public ChannelGameInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChannelGameInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChannelGameInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChannelGameInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryAllGameChannelReqOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelReq)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelReq}
   */
  public  static final class QueryAllGameChannelReq extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelReq)
      QueryAllGameChannelReqOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryAllGameChannelReq.newBuilder() to construct.
    private QueryAllGameChannelReq(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryAllGameChannelReq() {
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryAllGameChannelReq(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq other = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq) obj;

      boolean result = true;
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelReq}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelReq)
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReqOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq build() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq result = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelReq)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelReq)
    private static final com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryAllGameChannelReq>
        PARSER = new com.google.protobuf.AbstractParser<QueryAllGameChannelReq>() {
      @java.lang.Override
      public QueryAllGameChannelReq parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryAllGameChannelReq(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryAllGameChannelReq> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryAllGameChannelReq> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelReq getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryAllGameChannelRespOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelResp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    boolean hasResult();
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result getResult();
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder getResultOrBuilder();

    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    java.util.List<com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo> 
        getChannelGameInfoList();
    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo getChannelGameInfo(int index);
    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    int getChannelGameInfoCount();
    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    java.util.List<? extends com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder> 
        getChannelGameInfoOrBuilderList();
    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder getChannelGameInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelResp}
   */
  public  static final class QueryAllGameChannelResp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelResp)
      QueryAllGameChannelRespOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryAllGameChannelResp.newBuilder() to construct.
    private QueryAllGameChannelResp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryAllGameChannelResp() {
      channelGameInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryAllGameChannelResp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder subBuilder = null;
              if (result_ != null) {
                subBuilder = result_.toBuilder();
              }
              result_ = input.readMessage(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(result_);
                result_ = subBuilder.buildPartial();
              }

              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                channelGameInfo_ = new java.util.ArrayList<com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo>();
                mutable_bitField0_ |= 0x00000002;
              }
              channelGameInfo_.add(
                  input.readMessage(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownFieldProto3(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          channelGameInfo_ = java.util.Collections.unmodifiableList(channelGameInfo_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp.Builder.class);
    }

    private int bitField0_;
    public static final int RESULT_FIELD_NUMBER = 1;
    private com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result result_;
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    public boolean hasResult() {
      return result_ != null;
    }
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result getResult() {
      return result_ == null ? com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.getDefaultInstance() : result_;
    }
    /**
     * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder getResultOrBuilder() {
      return getResult();
    }

    public static final int CHANNELGAMEINFO_FIELD_NUMBER = 2;
    private java.util.List<com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo> channelGameInfo_;
    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    public java.util.List<com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo> getChannelGameInfoList() {
      return channelGameInfo_;
    }
    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    public java.util.List<? extends com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder> 
        getChannelGameInfoOrBuilderList() {
      return channelGameInfo_;
    }
    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    public int getChannelGameInfoCount() {
      return channelGameInfo_.size();
    }
    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo getChannelGameInfo(int index) {
      return channelGameInfo_.get(index);
    }
    /**
     * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
     */
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder getChannelGameInfoOrBuilder(
        int index) {
      return channelGameInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != null) {
        output.writeMessage(1, getResult());
      }
      for (int i = 0; i < channelGameInfo_.size(); i++) {
        output.writeMessage(2, channelGameInfo_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getResult());
      }
      for (int i = 0; i < channelGameInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, channelGameInfo_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp)) {
        return super.equals(obj);
      }
      com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp other = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp) obj;

      boolean result = true;
      result = result && (hasResult() == other.hasResult());
      if (hasResult()) {
        result = result && getResult()
            .equals(other.getResult());
      }
      result = result && getChannelGameInfoList()
          .equals(other.getChannelGameInfoList());
      result = result && unknownFields.equals(other.unknownFields);
      return result;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasResult()) {
        hash = (37 * hash) + RESULT_FIELD_NUMBER;
        hash = (53 * hash) + getResult().hashCode();
      }
      if (getChannelGameInfoCount() > 0) {
        hash = (37 * hash) + CHANNELGAMEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getChannelGameInfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelResp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelResp)
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelRespOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp.class, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp.Builder.class);
      }

      // Construct using com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChannelGameInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (resultBuilder_ == null) {
          result_ = null;
        } else {
          result_ = null;
          resultBuilder_ = null;
        }
        if (channelGameInfoBuilder_ == null) {
          channelGameInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          channelGameInfoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_descriptor;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp getDefaultInstanceForType() {
        return com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp.getDefaultInstance();
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp build() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp buildPartial() {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp result = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (resultBuilder_ == null) {
          result.result_ = result_;
        } else {
          result.result_ = resultBuilder_.build();
        }
        if (channelGameInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            channelGameInfo_ = java.util.Collections.unmodifiableList(channelGameInfo_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.channelGameInfo_ = channelGameInfo_;
        } else {
          result.channelGameInfo_ = channelGameInfoBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return (Builder) super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return (Builder) super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return (Builder) super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return (Builder) super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return (Builder) super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp) {
          return mergeFrom((com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp other) {
        if (other == com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp.getDefaultInstance()) return this;
        if (other.hasResult()) {
          mergeResult(other.getResult());
        }
        if (channelGameInfoBuilder_ == null) {
          if (!other.channelGameInfo_.isEmpty()) {
            if (channelGameInfo_.isEmpty()) {
              channelGameInfo_ = other.channelGameInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureChannelGameInfoIsMutable();
              channelGameInfo_.addAll(other.channelGameInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.channelGameInfo_.isEmpty()) {
            if (channelGameInfoBuilder_.isEmpty()) {
              channelGameInfoBuilder_.dispose();
              channelGameInfoBuilder_ = null;
              channelGameInfo_ = other.channelGameInfo_;
              bitField0_ = (bitField0_ & ~0x00000002);
              channelGameInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChannelGameInfoFieldBuilder() : null;
            } else {
              channelGameInfoBuilder_.addAllMessages(other.channelGameInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result result_ = null;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder> resultBuilder_;
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public boolean hasResult() {
        return resultBuilder_ != null || result_ != null;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result getResult() {
        if (resultBuilder_ == null) {
          return result_ == null ? com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.getDefaultInstance() : result_;
        } else {
          return resultBuilder_.getMessage();
        }
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public Builder setResult(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result value) {
        if (resultBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          result_ = value;
          onChanged();
        } else {
          resultBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public Builder setResult(
          com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder builderForValue) {
        if (resultBuilder_ == null) {
          result_ = builderForValue.build();
          onChanged();
        } else {
          resultBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public Builder mergeResult(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result value) {
        if (resultBuilder_ == null) {
          if (result_ != null) {
            result_ =
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.newBuilder(result_).mergeFrom(value).buildPartial();
          } else {
            result_ = value;
          }
          onChanged();
        } else {
          resultBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public Builder clearResult() {
        if (resultBuilder_ == null) {
          result_ = null;
          onChanged();
        } else {
          result_ = null;
          resultBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder getResultBuilder() {
        
        onChanged();
        return getResultFieldBuilder().getBuilder();
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder getResultOrBuilder() {
        if (resultBuilder_ != null) {
          return resultBuilder_.getMessageOrBuilder();
        } else {
          return result_ == null ?
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.getDefaultInstance() : result_;
        }
      }
      /**
       * <code>.com.yy.gameecology.common.protocol.yypyrpc.danmu.Result result = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder> 
          getResultFieldBuilder() {
        if (resultBuilder_ == null) {
          resultBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.Result.Builder, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ResultOrBuilder>(
                  getResult(),
                  getParentForChildren(),
                  isClean());
          result_ = null;
        }
        return resultBuilder_;
      }

      private java.util.List<com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo> channelGameInfo_ =
        java.util.Collections.emptyList();
      private void ensureChannelGameInfoIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          channelGameInfo_ = new java.util.ArrayList<com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo>(channelGameInfo_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder> channelGameInfoBuilder_;

      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public java.util.List<com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo> getChannelGameInfoList() {
        if (channelGameInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(channelGameInfo_);
        } else {
          return channelGameInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public int getChannelGameInfoCount() {
        if (channelGameInfoBuilder_ == null) {
          return channelGameInfo_.size();
        } else {
          return channelGameInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo getChannelGameInfo(int index) {
        if (channelGameInfoBuilder_ == null) {
          return channelGameInfo_.get(index);
        } else {
          return channelGameInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public Builder setChannelGameInfo(
          int index, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo value) {
        if (channelGameInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChannelGameInfoIsMutable();
          channelGameInfo_.set(index, value);
          onChanged();
        } else {
          channelGameInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public Builder setChannelGameInfo(
          int index, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder builderForValue) {
        if (channelGameInfoBuilder_ == null) {
          ensureChannelGameInfoIsMutable();
          channelGameInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          channelGameInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public Builder addChannelGameInfo(com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo value) {
        if (channelGameInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChannelGameInfoIsMutable();
          channelGameInfo_.add(value);
          onChanged();
        } else {
          channelGameInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public Builder addChannelGameInfo(
          int index, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo value) {
        if (channelGameInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChannelGameInfoIsMutable();
          channelGameInfo_.add(index, value);
          onChanged();
        } else {
          channelGameInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public Builder addChannelGameInfo(
          com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder builderForValue) {
        if (channelGameInfoBuilder_ == null) {
          ensureChannelGameInfoIsMutable();
          channelGameInfo_.add(builderForValue.build());
          onChanged();
        } else {
          channelGameInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public Builder addChannelGameInfo(
          int index, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder builderForValue) {
        if (channelGameInfoBuilder_ == null) {
          ensureChannelGameInfoIsMutable();
          channelGameInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          channelGameInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public Builder addAllChannelGameInfo(
          java.lang.Iterable<? extends com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo> values) {
        if (channelGameInfoBuilder_ == null) {
          ensureChannelGameInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, channelGameInfo_);
          onChanged();
        } else {
          channelGameInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public Builder clearChannelGameInfo() {
        if (channelGameInfoBuilder_ == null) {
          channelGameInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          channelGameInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public Builder removeChannelGameInfo(int index) {
        if (channelGameInfoBuilder_ == null) {
          ensureChannelGameInfoIsMutable();
          channelGameInfo_.remove(index);
          onChanged();
        } else {
          channelGameInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder getChannelGameInfoBuilder(
          int index) {
        return getChannelGameInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder getChannelGameInfoOrBuilder(
          int index) {
        if (channelGameInfoBuilder_ == null) {
          return channelGameInfo_.get(index);  } else {
          return channelGameInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public java.util.List<? extends com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder> 
           getChannelGameInfoOrBuilderList() {
        if (channelGameInfoBuilder_ != null) {
          return channelGameInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(channelGameInfo_);
        }
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder addChannelGameInfoBuilder() {
        return getChannelGameInfoFieldBuilder().addBuilder(
            com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder addChannelGameInfoBuilder(
          int index) {
        return getChannelGameInfoFieldBuilder().addBuilder(
            index, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yy.gameecology.common.protocol.yypyrpc.danmu.ChannelGameInfo channelGameInfo = 2;</code>
       */
      public java.util.List<com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder> 
           getChannelGameInfoBuilderList() {
        return getChannelGameInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder> 
          getChannelGameInfoFieldBuilder() {
        if (channelGameInfoBuilder_ == null) {
          channelGameInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfo.Builder, com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.ChannelGameInfoOrBuilder>(
                  channelGameInfo_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          channelGameInfo_ = null;
        }
        return channelGameInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFieldsProto3(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelResp)
    }

    // @@protoc_insertion_point(class_scope:com.yy.gameecology.common.protocol.yypyrpc.danmu.QueryAllGameChannelResp)
    private static final com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp();
    }

    public static com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QueryAllGameChannelResp>
        PARSER = new com.google.protobuf.AbstractParser<QueryAllGameChannelResp>() {
      @java.lang.Override
      public QueryAllGameChannelResp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryAllGameChannelResp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryAllGameChannelResp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryAllGameChannelResp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yy.gameecology.common.protocol.yypyrpc.danmu.Danmu.QueryAllGameChannelResp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013danmu.proto\0220com.yy.gameecology.common" +
      ".protocol.yypyrpc.danmu\"\'\n\006Result\022\014\n\004cod" +
      "e\030\001 \001(\005\022\017\n\007message\030\002 \001(\t\"<\n\022QueryGameSta" +
      "tusReq\022\013\n\003sid\030\001 \001(\003\022\014\n\004ssid\030\002 \001(\003\022\013\n\003uid" +
      "\030\003 \001(\003\"\242\001\n\023QueryGameStatusResp\022H\n\006result" +
      "\030\001 \001(\01328.com.yy.gameecology.common.proto" +
      "col.yypyrpc.danmu.Result\022\017\n\007playing\030\002 \001(" +
      "\010\022\r\n\005appId\030\003 \001(\t\022\016\n\006serial\030\004 \001(\t\022\021\n\tanch" +
      "orUid\030\005 \001(\003\"o\n\017ChannelGameInfo\022\013\n\003sid\030\001 " +
      "\001(\003\022\014\n\004ssid\030\002 \001(\003\022\017\n\007playing\030\003 \001(\010\022\r\n\005ap" +
      "pId\030\004 \001(\t\022\016\n\006serial\030\005 \001(\t\022\021\n\tanchorUid\030\006" +
      " \001(\003\"\030\n\026QueryAllGameChannelReq\"\277\001\n\027Query" +
      "AllGameChannelResp\022H\n\006result\030\001 \001(\01328.com" +
      ".yy.gameecology.common.protocol.yypyrpc." +
      "danmu.Result\022Z\n\017channelGameInfo\030\002 \003(\0132A." +
      "com.yy.gameecology.common.protocol.yypyr" +
      "pc.danmu.ChannelGameInfo2\343\002\n\023DanmakuActi" +
      "vityYrpc\022\236\001\n\017queryGameStatus\022D.com.yy.ga" +
      "meecology.common.protocol.yypyrpc.danmu." +
      "QueryGameStatusReq\032E.com.yy.gameecology." +
      "common.protocol.yypyrpc.danmu.QueryGameS" +
      "tatusResp\022\252\001\n\023queryAllGameChannel\022H.com." +
      "yy.gameecology.common.protocol.yypyrpc.d" +
      "anmu.QueryAllGameChannelReq\032I.com.yy.gam" +
      "eecology.common.protocol.yypyrpc.danmu.Q" +
      "ueryAllGameChannelRespb\006proto3"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
        new com.google.protobuf.Descriptors.FileDescriptor.    InternalDescriptorAssigner() {
          public com.google.protobuf.ExtensionRegistry assignDescriptors(
              com.google.protobuf.Descriptors.FileDescriptor root) {
            descriptor = root;
            return null;
          }
        };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_Result_descriptor,
        new java.lang.String[] { "Code", "Message", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusReq_descriptor,
        new java.lang.String[] { "Sid", "Ssid", "Uid", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryGameStatusResp_descriptor,
        new java.lang.String[] { "Result", "Playing", "AppId", "Serial", "AnchorUid", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_ChannelGameInfo_descriptor,
        new java.lang.String[] { "Sid", "Ssid", "Playing", "AppId", "Serial", "AnchorUid", });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelReq_descriptor,
        new java.lang.String[] { });
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yy_gameecology_common_protocol_yypyrpc_danmu_QueryAllGameChannelResp_descriptor,
        new java.lang.String[] { "Result", "ChannelGameInfo", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
