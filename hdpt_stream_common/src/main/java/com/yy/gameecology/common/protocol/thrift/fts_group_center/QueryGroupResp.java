/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_group_center;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-12-20")
public class QueryGroupResp implements org.apache.thrift.TBase<QueryGroupResp, QueryGroupResp._Fields>, java.io.Serializable, Cloneable, Comparable<QueryGroupResp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryGroupResp");

  private static final org.apache.thrift.protocol.TField HEADER_FIELD_DESC = new org.apache.thrift.protocol.TField("header", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField GROUP_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("group_map", org.apache.thrift.protocol.TType.MAP, (short)2);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.MAP, (short)15);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryGroupRespStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryGroupRespTupleSchemeFactory());
  }

  public CommonRet header; // required
  public Map<String,GroupResult> group_map; // required
  public Map<String,String> expand; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    HEADER((short)1, "header"),
    GROUP_MAP((short)2, "group_map"),
    EXPAND((short)15, "expand");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // HEADER
          return HEADER;
        case 2: // GROUP_MAP
          return GROUP_MAP;
        case 15: // EXPAND
          return EXPAND;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.HEADER, new org.apache.thrift.meta_data.FieldMetaData("header", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CommonRet.class)));
    tmpMap.put(_Fields.GROUP_MAP, new org.apache.thrift.meta_data.FieldMetaData("group_map", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GroupResult.class))));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryGroupResp.class, metaDataMap);
  }

  public QueryGroupResp() {
  }

  public QueryGroupResp(
    CommonRet header,
    Map<String,GroupResult> group_map,
    Map<String,String> expand)
  {
    this();
    this.header = header;
    this.group_map = group_map;
    this.expand = expand;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryGroupResp(QueryGroupResp other) {
    if (other.isSetHeader()) {
      this.header = new CommonRet(other.header);
    }
    if (other.isSetGroup_map()) {
      Map<String,GroupResult> __this__group_map = new HashMap<String,GroupResult>(other.group_map.size());
      for (Map.Entry<String, GroupResult> other_element : other.group_map.entrySet()) {

        String other_element_key = other_element.getKey();
        GroupResult other_element_value = other_element.getValue();

        String __this__group_map_copy_key = other_element_key;

        GroupResult __this__group_map_copy_value = new GroupResult(other_element_value);

        __this__group_map.put(__this__group_map_copy_key, __this__group_map_copy_value);
      }
      this.group_map = __this__group_map;
    }
    if (other.isSetExpand()) {
      Map<String,String> __this__expand = new HashMap<String,String>(other.expand);
      this.expand = __this__expand;
    }
  }

  public QueryGroupResp deepCopy() {
    return new QueryGroupResp(this);
  }

  @Override
  public void clear() {
    this.header = null;
    this.group_map = null;
    this.expand = null;
  }

  public CommonRet getHeader() {
    return this.header;
  }

  public QueryGroupResp setHeader(CommonRet header) {
    this.header = header;
    return this;
  }

  public void unsetHeader() {
    this.header = null;
  }

  /** Returns true if field header is set (has been assigned a value) and false otherwise */
  public boolean isSetHeader() {
    return this.header != null;
  }

  public void setHeaderIsSet(boolean value) {
    if (!value) {
      this.header = null;
    }
  }

  public int getGroup_mapSize() {
    return (this.group_map == null) ? 0 : this.group_map.size();
  }

  public void putToGroup_map(String key, GroupResult val) {
    if (this.group_map == null) {
      this.group_map = new HashMap<String,GroupResult>();
    }
    this.group_map.put(key, val);
  }

  public Map<String,GroupResult> getGroup_map() {
    return this.group_map;
  }

  public QueryGroupResp setGroup_map(Map<String,GroupResult> group_map) {
    this.group_map = group_map;
    return this;
  }

  public void unsetGroup_map() {
    this.group_map = null;
  }

  /** Returns true if field group_map is set (has been assigned a value) and false otherwise */
  public boolean isSetGroup_map() {
    return this.group_map != null;
  }

  public void setGroup_mapIsSet(boolean value) {
    if (!value) {
      this.group_map = null;
    }
  }

  public int getExpandSize() {
    return (this.expand == null) ? 0 : this.expand.size();
  }

  public void putToExpand(String key, String val) {
    if (this.expand == null) {
      this.expand = new HashMap<String,String>();
    }
    this.expand.put(key, val);
  }

  public Map<String,String> getExpand() {
    return this.expand;
  }

  public QueryGroupResp setExpand(Map<String,String> expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case HEADER:
      if (value == null) {
        unsetHeader();
      } else {
        setHeader((CommonRet)value);
      }
      break;

    case GROUP_MAP:
      if (value == null) {
        unsetGroup_map();
      } else {
        setGroup_map((Map<String,GroupResult>)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case HEADER:
      return getHeader();

    case GROUP_MAP:
      return getGroup_map();

    case EXPAND:
      return getExpand();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case HEADER:
      return isSetHeader();
    case GROUP_MAP:
      return isSetGroup_map();
    case EXPAND:
      return isSetExpand();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryGroupResp)
      return this.equals((QueryGroupResp)that);
    return false;
  }

  public boolean equals(QueryGroupResp that) {
    if (that == null)
      return false;

    boolean this_present_header = true && this.isSetHeader();
    boolean that_present_header = true && that.isSetHeader();
    if (this_present_header || that_present_header) {
      if (!(this_present_header && that_present_header))
        return false;
      if (!this.header.equals(that.header))
        return false;
    }

    boolean this_present_group_map = true && this.isSetGroup_map();
    boolean that_present_group_map = true && that.isSetGroup_map();
    if (this_present_group_map || that_present_group_map) {
      if (!(this_present_group_map && that_present_group_map))
        return false;
      if (!this.group_map.equals(that.group_map))
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_header = true && (isSetHeader());
    list.add(present_header);
    if (present_header)
      list.add(header);

    boolean present_group_map = true && (isSetGroup_map());
    list.add(present_group_map);
    if (present_group_map)
      list.add(group_map);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryGroupResp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetHeader()).compareTo(other.isSetHeader());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetHeader()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.header, other.header);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGroup_map()).compareTo(other.isSetGroup_map());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGroup_map()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.group_map, other.group_map);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryGroupResp(");
    boolean first = true;

    sb.append("header:");
    if (this.header == null) {
      sb.append("null");
    } else {
      sb.append(this.header);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("group_map:");
    if (this.group_map == null) {
      sb.append("null");
    } else {
      sb.append(this.group_map);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws TException {
    // check for required fields
    // check for sub-struct validity
    if (header != null) {
      header.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryGroupRespStandardSchemeFactory implements SchemeFactory {
    public QueryGroupRespStandardScheme getScheme() {
      return new QueryGroupRespStandardScheme();
    }
  }

  private static class QueryGroupRespStandardScheme extends StandardScheme<QueryGroupResp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryGroupResp struct) throws TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // HEADER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.header = new CommonRet();
              struct.header.read(iprot);
              struct.setHeaderIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // GROUP_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map72 = iprot.readMapBegin();
                struct.group_map = new HashMap<String,GroupResult>(2*_map72.size);
                String _key73;
                GroupResult _val74;
                for (int _i75 = 0; _i75 < _map72.size; ++_i75)
                {
                  _key73 = iprot.readString();
                  _val74 = new GroupResult();
                  _val74.read(iprot);
                  struct.group_map.put(_key73, _val74);
                }
                iprot.readMapEnd();
              }
              struct.setGroup_mapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map76 = iprot.readMapBegin();
                struct.expand = new HashMap<String,String>(2*_map76.size);
                String _key77;
                String _val78;
                for (int _i79 = 0; _i79 < _map76.size; ++_i79)
                {
                  _key77 = iprot.readString();
                  _val78 = iprot.readString();
                  struct.expand.put(_key77, _val78);
                }
                iprot.readMapEnd();
              }
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryGroupResp struct) throws TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.header != null) {
        oprot.writeFieldBegin(HEADER_FIELD_DESC);
        struct.header.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.group_map != null) {
        oprot.writeFieldBegin(GROUP_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRUCT, struct.group_map.size()));
          for (Map.Entry<String, GroupResult> _iter80 : struct.group_map.entrySet())
          {
            oprot.writeString(_iter80.getKey());
            _iter80.getValue().write(oprot);
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.expand.size()));
          for (Map.Entry<String, String> _iter81 : struct.expand.entrySet())
          {
            oprot.writeString(_iter81.getKey());
            oprot.writeString(_iter81.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryGroupRespTupleSchemeFactory implements SchemeFactory {
    public QueryGroupRespTupleScheme getScheme() {
      return new QueryGroupRespTupleScheme();
    }
  }

  private static class QueryGroupRespTupleScheme extends TupleScheme<QueryGroupResp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryGroupResp struct) throws TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetHeader()) {
        optionals.set(0);
      }
      if (struct.isSetGroup_map()) {
        optionals.set(1);
      }
      if (struct.isSetExpand()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetHeader()) {
        struct.header.write(oprot);
      }
      if (struct.isSetGroup_map()) {
        {
          oprot.writeI32(struct.group_map.size());
          for (Map.Entry<String, GroupResult> _iter82 : struct.group_map.entrySet())
          {
            oprot.writeString(_iter82.getKey());
            _iter82.getValue().write(oprot);
          }
        }
      }
      if (struct.isSetExpand()) {
        {
          oprot.writeI32(struct.expand.size());
          for (Map.Entry<String, String> _iter83 : struct.expand.entrySet())
          {
            oprot.writeString(_iter83.getKey());
            oprot.writeString(_iter83.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryGroupResp struct) throws TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.header = new CommonRet();
        struct.header.read(iprot);
        struct.setHeaderIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map84 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.group_map = new HashMap<String,GroupResult>(2*_map84.size);
          String _key85;
          GroupResult _val86;
          for (int _i87 = 0; _i87 < _map84.size; ++_i87)
          {
            _key85 = iprot.readString();
            _val86 = new GroupResult();
            _val86.read(iprot);
            struct.group_map.put(_key85, _val86);
          }
        }
        struct.setGroup_mapIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map88 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.expand = new HashMap<String,String>(2*_map88.size);
          String _key89;
          String _val90;
          for (int _i91 = 0; _i91 < _map88.size; ++_i91)
          {
            _key89 = iprot.readString();
            _val90 = iprot.readString();
            struct.expand.put(_key89, _val90);
          }
        }
        struct.setExpandIsSet(true);
      }
    }
  }

}

