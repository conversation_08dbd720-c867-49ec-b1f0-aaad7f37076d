/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_price;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-02-22")
public class IssueRequest implements org.apache.thrift.TBase<IssueRequest, IssueRequest._Fields>, java.io.Serializable, Cloneable, Comparable<IssueRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("IssueRequest");

  private static final org.apache.thrift.protocol.TField SEQ_FIELD_DESC = new org.apache.thrift.protocol.TField("seq", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField TASK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("taskId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField RECEIVER_FIELD_DESC = new org.apache.thrift.protocol.TField("receiver", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField SSID_FIELD_DESC = new org.apache.thrift.protocol.TField("ssid", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField ITEM_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("itemType", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField ITEM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("itemId", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField ITEM_NUM_FIELD_DESC = new org.apache.thrift.protocol.TField("itemNum", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField PLATFORM_FIELD_DESC = new org.apache.thrift.protocol.TField("platform", org.apache.thrift.protocol.TType.I32, (short)10);
  private static final org.apache.thrift.protocol.TField TIMESTAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("timestamp", org.apache.thrift.protocol.TType.STRING, (short)11);
  private static final org.apache.thrift.protocol.TField IP_FIELD_DESC = new org.apache.thrift.protocol.TField("ip", org.apache.thrift.protocol.TType.STRING, (short)12);
  private static final org.apache.thrift.protocol.TField MAC_FIELD_DESC = new org.apache.thrift.protocol.TField("mac", org.apache.thrift.protocol.TType.STRING, (short)13);
  private static final org.apache.thrift.protocol.TField EXT_LONG_FIELD_DESC = new org.apache.thrift.protocol.TField("extLong", org.apache.thrift.protocol.TType.I64, (short)14);
  private static final org.apache.thrift.protocol.TField EXT_JSON_FIELD_DESC = new org.apache.thrift.protocol.TField("extJson", org.apache.thrift.protocol.TType.STRING, (short)15);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)16);
  private static final org.apache.thrift.protocol.TField SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("sign", org.apache.thrift.protocol.TType.STRING, (short)17);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new IssueRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new IssueRequestTupleSchemeFactory());
  }

  public String seq; // required
  public long actId; // required
  public long taskId; // required
  public long receiver; // required
  public long sid; // required
  public long ssid; // required
  public String itemType; // required
  public String itemId; // required
  public long itemNum; // required
  /**
   * 
   * @see Platform
   */
  public Platform platform; // required
  public String timestamp; // required
  public String ip; // required
  public String mac; // required
  public long extLong; // required
  public String extJson; // required
  public Map<String,String> extData; // required
  public String sign; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SEQ((short)1, "seq"),
    ACT_ID((short)2, "actId"),
    TASK_ID((short)3, "taskId"),
    RECEIVER((short)4, "receiver"),
    SID((short)5, "sid"),
    SSID((short)6, "ssid"),
    ITEM_TYPE((short)7, "itemType"),
    ITEM_ID((short)8, "itemId"),
    ITEM_NUM((short)9, "itemNum"),
    /**
     * 
     * @see Platform
     */
    PLATFORM((short)10, "platform"),
    TIMESTAMP((short)11, "timestamp"),
    IP((short)12, "ip"),
    MAC((short)13, "mac"),
    EXT_LONG((short)14, "extLong"),
    EXT_JSON((short)15, "extJson"),
    EXT_DATA((short)16, "extData"),
    SIGN((short)17, "sign");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SEQ
          return SEQ;
        case 2: // ACT_ID
          return ACT_ID;
        case 3: // TASK_ID
          return TASK_ID;
        case 4: // RECEIVER
          return RECEIVER;
        case 5: // SID
          return SID;
        case 6: // SSID
          return SSID;
        case 7: // ITEM_TYPE
          return ITEM_TYPE;
        case 8: // ITEM_ID
          return ITEM_ID;
        case 9: // ITEM_NUM
          return ITEM_NUM;
        case 10: // PLATFORM
          return PLATFORM;
        case 11: // TIMESTAMP
          return TIMESTAMP;
        case 12: // IP
          return IP;
        case 13: // MAC
          return MAC;
        case 14: // EXT_LONG
          return EXT_LONG;
        case 15: // EXT_JSON
          return EXT_JSON;
        case 16: // EXT_DATA
          return EXT_DATA;
        case 17: // SIGN
          return SIGN;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __TASKID_ISSET_ID = 1;
  private static final int __RECEIVER_ISSET_ID = 2;
  private static final int __SID_ISSET_ID = 3;
  private static final int __SSID_ISSET_ID = 4;
  private static final int __ITEMNUM_ISSET_ID = 5;
  private static final int __EXTLONG_ISSET_ID = 6;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SEQ, new org.apache.thrift.meta_data.FieldMetaData("seq", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TASK_ID, new org.apache.thrift.meta_data.FieldMetaData("taskId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RECEIVER, new org.apache.thrift.meta_data.FieldMetaData("receiver", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SSID, new org.apache.thrift.meta_data.FieldMetaData("ssid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ITEM_TYPE, new org.apache.thrift.meta_data.FieldMetaData("itemType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ITEM_ID, new org.apache.thrift.meta_data.FieldMetaData("itemId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ITEM_NUM, new org.apache.thrift.meta_data.FieldMetaData("itemNum", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PLATFORM, new org.apache.thrift.meta_data.FieldMetaData("platform", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, Platform.class)));
    tmpMap.put(_Fields.TIMESTAMP, new org.apache.thrift.meta_data.FieldMetaData("timestamp", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.IP, new org.apache.thrift.meta_data.FieldMetaData("ip", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MAC, new org.apache.thrift.meta_data.FieldMetaData("mac", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_LONG, new org.apache.thrift.meta_data.FieldMetaData("extLong", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_JSON, new org.apache.thrift.meta_data.FieldMetaData("extJson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.SIGN, new org.apache.thrift.meta_data.FieldMetaData("sign", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(IssueRequest.class, metaDataMap);
  }

  public IssueRequest() {
  }

  public IssueRequest(
    String seq,
    long actId,
    long taskId,
    long receiver,
    long sid,
    long ssid,
    String itemType,
    String itemId,
    long itemNum,
    Platform platform,
    String timestamp,
    String ip,
    String mac,
    long extLong,
    String extJson,
    Map<String,String> extData,
    String sign)
  {
    this();
    this.seq = seq;
    this.actId = actId;
    setActIdIsSet(true);
    this.taskId = taskId;
    setTaskIdIsSet(true);
    this.receiver = receiver;
    setReceiverIsSet(true);
    this.sid = sid;
    setSidIsSet(true);
    this.ssid = ssid;
    setSsidIsSet(true);
    this.itemType = itemType;
    this.itemId = itemId;
    this.itemNum = itemNum;
    setItemNumIsSet(true);
    this.platform = platform;
    this.timestamp = timestamp;
    this.ip = ip;
    this.mac = mac;
    this.extLong = extLong;
    setExtLongIsSet(true);
    this.extJson = extJson;
    this.extData = extData;
    this.sign = sign;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public IssueRequest(IssueRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetSeq()) {
      this.seq = other.seq;
    }
    this.actId = other.actId;
    this.taskId = other.taskId;
    this.receiver = other.receiver;
    this.sid = other.sid;
    this.ssid = other.ssid;
    if (other.isSetItemType()) {
      this.itemType = other.itemType;
    }
    if (other.isSetItemId()) {
      this.itemId = other.itemId;
    }
    this.itemNum = other.itemNum;
    if (other.isSetPlatform()) {
      this.platform = other.platform;
    }
    if (other.isSetTimestamp()) {
      this.timestamp = other.timestamp;
    }
    if (other.isSetIp()) {
      this.ip = other.ip;
    }
    if (other.isSetMac()) {
      this.mac = other.mac;
    }
    this.extLong = other.extLong;
    if (other.isSetExtJson()) {
      this.extJson = other.extJson;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
    if (other.isSetSign()) {
      this.sign = other.sign;
    }
  }

  public IssueRequest deepCopy() {
    return new IssueRequest(this);
  }

  @Override
  public void clear() {
    this.seq = null;
    setActIdIsSet(false);
    this.actId = 0;
    setTaskIdIsSet(false);
    this.taskId = 0;
    setReceiverIsSet(false);
    this.receiver = 0;
    setSidIsSet(false);
    this.sid = 0;
    setSsidIsSet(false);
    this.ssid = 0;
    this.itemType = null;
    this.itemId = null;
    setItemNumIsSet(false);
    this.itemNum = 0;
    this.platform = null;
    this.timestamp = null;
    this.ip = null;
    this.mac = null;
    setExtLongIsSet(false);
    this.extLong = 0;
    this.extJson = null;
    this.extData = null;
    this.sign = null;
  }

  public String getSeq() {
    return this.seq;
  }

  public IssueRequest setSeq(String seq) {
    this.seq = seq;
    return this;
  }

  public void unsetSeq() {
    this.seq = null;
  }

  /** Returns true if field seq is set (has been assigned a value) and false otherwise */
  public boolean isSetSeq() {
    return this.seq != null;
  }

  public void setSeqIsSet(boolean value) {
    if (!value) {
      this.seq = null;
    }
  }

  public long getActId() {
    return this.actId;
  }

  public IssueRequest setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getTaskId() {
    return this.taskId;
  }

  public IssueRequest setTaskId(long taskId) {
    this.taskId = taskId;
    setTaskIdIsSet(true);
    return this;
  }

  public void unsetTaskId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TASKID_ISSET_ID);
  }

  /** Returns true if field taskId is set (has been assigned a value) and false otherwise */
  public boolean isSetTaskId() {
    return EncodingUtils.testBit(__isset_bitfield, __TASKID_ISSET_ID);
  }

  public void setTaskIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TASKID_ISSET_ID, value);
  }

  public long getReceiver() {
    return this.receiver;
  }

  public IssueRequest setReceiver(long receiver) {
    this.receiver = receiver;
    setReceiverIsSet(true);
    return this;
  }

  public void unsetReceiver() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RECEIVER_ISSET_ID);
  }

  /** Returns true if field receiver is set (has been assigned a value) and false otherwise */
  public boolean isSetReceiver() {
    return EncodingUtils.testBit(__isset_bitfield, __RECEIVER_ISSET_ID);
  }

  public void setReceiverIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RECEIVER_ISSET_ID, value);
  }

  public long getSid() {
    return this.sid;
  }

  public IssueRequest setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getSsid() {
    return this.ssid;
  }

  public IssueRequest setSsid(long ssid) {
    this.ssid = ssid;
    setSsidIsSet(true);
    return this;
  }

  public void unsetSsid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  /** Returns true if field ssid is set (has been assigned a value) and false otherwise */
  public boolean isSetSsid() {
    return EncodingUtils.testBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  public void setSsidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SSID_ISSET_ID, value);
  }

  public String getItemType() {
    return this.itemType;
  }

  public IssueRequest setItemType(String itemType) {
    this.itemType = itemType;
    return this;
  }

  public void unsetItemType() {
    this.itemType = null;
  }

  /** Returns true if field itemType is set (has been assigned a value) and false otherwise */
  public boolean isSetItemType() {
    return this.itemType != null;
  }

  public void setItemTypeIsSet(boolean value) {
    if (!value) {
      this.itemType = null;
    }
  }

  public String getItemId() {
    return this.itemId;
  }

  public IssueRequest setItemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

  public void unsetItemId() {
    this.itemId = null;
  }

  /** Returns true if field itemId is set (has been assigned a value) and false otherwise */
  public boolean isSetItemId() {
    return this.itemId != null;
  }

  public void setItemIdIsSet(boolean value) {
    if (!value) {
      this.itemId = null;
    }
  }

  public long getItemNum() {
    return this.itemNum;
  }

  public IssueRequest setItemNum(long itemNum) {
    this.itemNum = itemNum;
    setItemNumIsSet(true);
    return this;
  }

  public void unsetItemNum() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ITEMNUM_ISSET_ID);
  }

  /** Returns true if field itemNum is set (has been assigned a value) and false otherwise */
  public boolean isSetItemNum() {
    return EncodingUtils.testBit(__isset_bitfield, __ITEMNUM_ISSET_ID);
  }

  public void setItemNumIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ITEMNUM_ISSET_ID, value);
  }

  /**
   * 
   * @see Platform
   */
  public Platform getPlatform() {
    return this.platform;
  }

  /**
   * 
   * @see Platform
   */
  public IssueRequest setPlatform(Platform platform) {
    this.platform = platform;
    return this;
  }

  public void unsetPlatform() {
    this.platform = null;
  }

  /** Returns true if field platform is set (has been assigned a value) and false otherwise */
  public boolean isSetPlatform() {
    return this.platform != null;
  }

  public void setPlatformIsSet(boolean value) {
    if (!value) {
      this.platform = null;
    }
  }

  public String getTimestamp() {
    return this.timestamp;
  }

  public IssueRequest setTimestamp(String timestamp) {
    this.timestamp = timestamp;
    return this;
  }

  public void unsetTimestamp() {
    this.timestamp = null;
  }

  /** Returns true if field timestamp is set (has been assigned a value) and false otherwise */
  public boolean isSetTimestamp() {
    return this.timestamp != null;
  }

  public void setTimestampIsSet(boolean value) {
    if (!value) {
      this.timestamp = null;
    }
  }

  public String getIp() {
    return this.ip;
  }

  public IssueRequest setIp(String ip) {
    this.ip = ip;
    return this;
  }

  public void unsetIp() {
    this.ip = null;
  }

  /** Returns true if field ip is set (has been assigned a value) and false otherwise */
  public boolean isSetIp() {
    return this.ip != null;
  }

  public void setIpIsSet(boolean value) {
    if (!value) {
      this.ip = null;
    }
  }

  public String getMac() {
    return this.mac;
  }

  public IssueRequest setMac(String mac) {
    this.mac = mac;
    return this;
  }

  public void unsetMac() {
    this.mac = null;
  }

  /** Returns true if field mac is set (has been assigned a value) and false otherwise */
  public boolean isSetMac() {
    return this.mac != null;
  }

  public void setMacIsSet(boolean value) {
    if (!value) {
      this.mac = null;
    }
  }

  public long getExtLong() {
    return this.extLong;
  }

  public IssueRequest setExtLong(long extLong) {
    this.extLong = extLong;
    setExtLongIsSet(true);
    return this;
  }

  public void unsetExtLong() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __EXTLONG_ISSET_ID);
  }

  /** Returns true if field extLong is set (has been assigned a value) and false otherwise */
  public boolean isSetExtLong() {
    return EncodingUtils.testBit(__isset_bitfield, __EXTLONG_ISSET_ID);
  }

  public void setExtLongIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __EXTLONG_ISSET_ID, value);
  }

  public String getExtJson() {
    return this.extJson;
  }

  public IssueRequest setExtJson(String extJson) {
    this.extJson = extJson;
    return this;
  }

  public void unsetExtJson() {
    this.extJson = null;
  }

  /** Returns true if field extJson is set (has been assigned a value) and false otherwise */
  public boolean isSetExtJson() {
    return this.extJson != null;
  }

  public void setExtJsonIsSet(boolean value) {
    if (!value) {
      this.extJson = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public IssueRequest setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public String getSign() {
    return this.sign;
  }

  public IssueRequest setSign(String sign) {
    this.sign = sign;
    return this;
  }

  public void unsetSign() {
    this.sign = null;
  }

  /** Returns true if field sign is set (has been assigned a value) and false otherwise */
  public boolean isSetSign() {
    return this.sign != null;
  }

  public void setSignIsSet(boolean value) {
    if (!value) {
      this.sign = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case SEQ:
      if (value == null) {
        unsetSeq();
      } else {
        setSeq((String)value);
      }
      break;

    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case TASK_ID:
      if (value == null) {
        unsetTaskId();
      } else {
        setTaskId((Long)value);
      }
      break;

    case RECEIVER:
      if (value == null) {
        unsetReceiver();
      } else {
        setReceiver((Long)value);
      }
      break;

    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case SSID:
      if (value == null) {
        unsetSsid();
      } else {
        setSsid((Long)value);
      }
      break;

    case ITEM_TYPE:
      if (value == null) {
        unsetItemType();
      } else {
        setItemType((String)value);
      }
      break;

    case ITEM_ID:
      if (value == null) {
        unsetItemId();
      } else {
        setItemId((String)value);
      }
      break;

    case ITEM_NUM:
      if (value == null) {
        unsetItemNum();
      } else {
        setItemNum((Long)value);
      }
      break;

    case PLATFORM:
      if (value == null) {
        unsetPlatform();
      } else {
        setPlatform((Platform)value);
      }
      break;

    case TIMESTAMP:
      if (value == null) {
        unsetTimestamp();
      } else {
        setTimestamp((String)value);
      }
      break;

    case IP:
      if (value == null) {
        unsetIp();
      } else {
        setIp((String)value);
      }
      break;

    case MAC:
      if (value == null) {
        unsetMac();
      } else {
        setMac((String)value);
      }
      break;

    case EXT_LONG:
      if (value == null) {
        unsetExtLong();
      } else {
        setExtLong((Long)value);
      }
      break;

    case EXT_JSON:
      if (value == null) {
        unsetExtJson();
      } else {
        setExtJson((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    case SIGN:
      if (value == null) {
        unsetSign();
      } else {
        setSign((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case SEQ:
      return getSeq();

    case ACT_ID:
      return getActId();

    case TASK_ID:
      return getTaskId();

    case RECEIVER:
      return getReceiver();

    case SID:
      return getSid();

    case SSID:
      return getSsid();

    case ITEM_TYPE:
      return getItemType();

    case ITEM_ID:
      return getItemId();

    case ITEM_NUM:
      return getItemNum();

    case PLATFORM:
      return getPlatform();

    case TIMESTAMP:
      return getTimestamp();

    case IP:
      return getIp();

    case MAC:
      return getMac();

    case EXT_LONG:
      return getExtLong();

    case EXT_JSON:
      return getExtJson();

    case EXT_DATA:
      return getExtData();

    case SIGN:
      return getSign();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SEQ:
      return isSetSeq();
    case ACT_ID:
      return isSetActId();
    case TASK_ID:
      return isSetTaskId();
    case RECEIVER:
      return isSetReceiver();
    case SID:
      return isSetSid();
    case SSID:
      return isSetSsid();
    case ITEM_TYPE:
      return isSetItemType();
    case ITEM_ID:
      return isSetItemId();
    case ITEM_NUM:
      return isSetItemNum();
    case PLATFORM:
      return isSetPlatform();
    case TIMESTAMP:
      return isSetTimestamp();
    case IP:
      return isSetIp();
    case MAC:
      return isSetMac();
    case EXT_LONG:
      return isSetExtLong();
    case EXT_JSON:
      return isSetExtJson();
    case EXT_DATA:
      return isSetExtData();
    case SIGN:
      return isSetSign();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof IssueRequest)
      return this.equals((IssueRequest)that);
    return false;
  }

  public boolean equals(IssueRequest that) {
    if (that == null)
      return false;

    boolean this_present_seq = true && this.isSetSeq();
    boolean that_present_seq = true && that.isSetSeq();
    if (this_present_seq || that_present_seq) {
      if (!(this_present_seq && that_present_seq))
        return false;
      if (!this.seq.equals(that.seq))
        return false;
    }

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_taskId = true;
    boolean that_present_taskId = true;
    if (this_present_taskId || that_present_taskId) {
      if (!(this_present_taskId && that_present_taskId))
        return false;
      if (this.taskId != that.taskId)
        return false;
    }

    boolean this_present_receiver = true;
    boolean that_present_receiver = true;
    if (this_present_receiver || that_present_receiver) {
      if (!(this_present_receiver && that_present_receiver))
        return false;
      if (this.receiver != that.receiver)
        return false;
    }

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_ssid = true;
    boolean that_present_ssid = true;
    if (this_present_ssid || that_present_ssid) {
      if (!(this_present_ssid && that_present_ssid))
        return false;
      if (this.ssid != that.ssid)
        return false;
    }

    boolean this_present_itemType = true && this.isSetItemType();
    boolean that_present_itemType = true && that.isSetItemType();
    if (this_present_itemType || that_present_itemType) {
      if (!(this_present_itemType && that_present_itemType))
        return false;
      if (!this.itemType.equals(that.itemType))
        return false;
    }

    boolean this_present_itemId = true && this.isSetItemId();
    boolean that_present_itemId = true && that.isSetItemId();
    if (this_present_itemId || that_present_itemId) {
      if (!(this_present_itemId && that_present_itemId))
        return false;
      if (!this.itemId.equals(that.itemId))
        return false;
    }

    boolean this_present_itemNum = true;
    boolean that_present_itemNum = true;
    if (this_present_itemNum || that_present_itemNum) {
      if (!(this_present_itemNum && that_present_itemNum))
        return false;
      if (this.itemNum != that.itemNum)
        return false;
    }

    boolean this_present_platform = true && this.isSetPlatform();
    boolean that_present_platform = true && that.isSetPlatform();
    if (this_present_platform || that_present_platform) {
      if (!(this_present_platform && that_present_platform))
        return false;
      if (!this.platform.equals(that.platform))
        return false;
    }

    boolean this_present_timestamp = true && this.isSetTimestamp();
    boolean that_present_timestamp = true && that.isSetTimestamp();
    if (this_present_timestamp || that_present_timestamp) {
      if (!(this_present_timestamp && that_present_timestamp))
        return false;
      if (!this.timestamp.equals(that.timestamp))
        return false;
    }

    boolean this_present_ip = true && this.isSetIp();
    boolean that_present_ip = true && that.isSetIp();
    if (this_present_ip || that_present_ip) {
      if (!(this_present_ip && that_present_ip))
        return false;
      if (!this.ip.equals(that.ip))
        return false;
    }

    boolean this_present_mac = true && this.isSetMac();
    boolean that_present_mac = true && that.isSetMac();
    if (this_present_mac || that_present_mac) {
      if (!(this_present_mac && that_present_mac))
        return false;
      if (!this.mac.equals(that.mac))
        return false;
    }

    boolean this_present_extLong = true;
    boolean that_present_extLong = true;
    if (this_present_extLong || that_present_extLong) {
      if (!(this_present_extLong && that_present_extLong))
        return false;
      if (this.extLong != that.extLong)
        return false;
    }

    boolean this_present_extJson = true && this.isSetExtJson();
    boolean that_present_extJson = true && that.isSetExtJson();
    if (this_present_extJson || that_present_extJson) {
      if (!(this_present_extJson && that_present_extJson))
        return false;
      if (!this.extJson.equals(that.extJson))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    boolean this_present_sign = true && this.isSetSign();
    boolean that_present_sign = true && that.isSetSign();
    if (this_present_sign || that_present_sign) {
      if (!(this_present_sign && that_present_sign))
        return false;
      if (!this.sign.equals(that.sign))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_seq = true && (isSetSeq());
    list.add(present_seq);
    if (present_seq)
      list.add(seq);

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_taskId = true;
    list.add(present_taskId);
    if (present_taskId)
      list.add(taskId);

    boolean present_receiver = true;
    list.add(present_receiver);
    if (present_receiver)
      list.add(receiver);

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_ssid = true;
    list.add(present_ssid);
    if (present_ssid)
      list.add(ssid);

    boolean present_itemType = true && (isSetItemType());
    list.add(present_itemType);
    if (present_itemType)
      list.add(itemType);

    boolean present_itemId = true && (isSetItemId());
    list.add(present_itemId);
    if (present_itemId)
      list.add(itemId);

    boolean present_itemNum = true;
    list.add(present_itemNum);
    if (present_itemNum)
      list.add(itemNum);

    boolean present_platform = true && (isSetPlatform());
    list.add(present_platform);
    if (present_platform)
      list.add(platform.getValue());

    boolean present_timestamp = true && (isSetTimestamp());
    list.add(present_timestamp);
    if (present_timestamp)
      list.add(timestamp);

    boolean present_ip = true && (isSetIp());
    list.add(present_ip);
    if (present_ip)
      list.add(ip);

    boolean present_mac = true && (isSetMac());
    list.add(present_mac);
    if (present_mac)
      list.add(mac);

    boolean present_extLong = true;
    list.add(present_extLong);
    if (present_extLong)
      list.add(extLong);

    boolean present_extJson = true && (isSetExtJson());
    list.add(present_extJson);
    if (present_extJson)
      list.add(extJson);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    boolean present_sign = true && (isSetSign());
    list.add(present_sign);
    if (present_sign)
      list.add(sign);

    return list.hashCode();
  }

  @Override
  public int compareTo(IssueRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSeq()).compareTo(other.isSetSeq());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeq()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.seq, other.seq);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTaskId()).compareTo(other.isSetTaskId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTaskId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.taskId, other.taskId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReceiver()).compareTo(other.isSetReceiver());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReceiver()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.receiver, other.receiver);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSsid()).compareTo(other.isSetSsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssid, other.ssid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemType()).compareTo(other.isSetItemType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemType, other.itemType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemId()).compareTo(other.isSetItemId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemId, other.itemId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemNum()).compareTo(other.isSetItemNum());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemNum()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemNum, other.itemNum);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPlatform()).compareTo(other.isSetPlatform());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPlatform()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.platform, other.platform);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimestamp()).compareTo(other.isSetTimestamp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimestamp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timestamp, other.timestamp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIp()).compareTo(other.isSetIp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ip, other.ip);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMac()).compareTo(other.isSetMac());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMac()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mac, other.mac);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtLong()).compareTo(other.isSetExtLong());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtLong()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extLong, other.extLong);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtJson()).compareTo(other.isSetExtJson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtJson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extJson, other.extJson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSign()).compareTo(other.isSetSign());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSign()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sign, other.sign);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("IssueRequest(");
    boolean first = true;

    sb.append("seq:");
    if (this.seq == null) {
      sb.append("null");
    } else {
      sb.append(this.seq);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("taskId:");
    sb.append(this.taskId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("receiver:");
    sb.append(this.receiver);
    first = false;
    if (!first) sb.append(", ");
    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ssid:");
    sb.append(this.ssid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemType:");
    if (this.itemType == null) {
      sb.append("null");
    } else {
      sb.append(this.itemType);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemId:");
    if (this.itemId == null) {
      sb.append("null");
    } else {
      sb.append(this.itemId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemNum:");
    sb.append(this.itemNum);
    first = false;
    if (!first) sb.append(", ");
    sb.append("platform:");
    if (this.platform == null) {
      sb.append("null");
    } else {
      sb.append(this.platform);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("timestamp:");
    if (this.timestamp == null) {
      sb.append("null");
    } else {
      sb.append(this.timestamp);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ip:");
    if (this.ip == null) {
      sb.append("null");
    } else {
      sb.append(this.ip);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("mac:");
    if (this.mac == null) {
      sb.append("null");
    } else {
      sb.append(this.mac);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extLong:");
    sb.append(this.extLong);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extJson:");
    if (this.extJson == null) {
      sb.append("null");
    } else {
      sb.append(this.extJson);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("sign:");
    if (this.sign == null) {
      sb.append("null");
    } else {
      sb.append(this.sign);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class IssueRequestStandardSchemeFactory implements SchemeFactory {
    public IssueRequestStandardScheme getScheme() {
      return new IssueRequestStandardScheme();
    }
  }

  private static class IssueRequestStandardScheme extends StandardScheme<IssueRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, IssueRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SEQ
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.seq = iprot.readString();
              struct.setSeqIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // TASK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.taskId = iprot.readI64();
              struct.setTaskIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // RECEIVER
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.receiver = iprot.readI64();
              struct.setReceiverIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SSID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ssid = iprot.readI64();
              struct.setSsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // ITEM_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.itemType = iprot.readString();
              struct.setItemTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // ITEM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.itemId = iprot.readString();
              struct.setItemIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // ITEM_NUM
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.itemNum = iprot.readI64();
              struct.setItemNumIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // PLATFORM
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.platform = Platform.findByValue(iprot.readI32());
              struct.setPlatformIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // TIMESTAMP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.timestamp = iprot.readString();
              struct.setTimestampIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // IP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ip = iprot.readString();
              struct.setIpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // MAC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.mac = iprot.readString();
              struct.setMacIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // EXT_LONG
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.extLong = iprot.readI64();
              struct.setExtLongIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // EXT_JSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extJson = iprot.readString();
              struct.setExtJsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map10 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map10.size);
                String _key11;
                String _val12;
                for (int _i13 = 0; _i13 < _map10.size; ++_i13)
                {
                  _key11 = iprot.readString();
                  _val12 = iprot.readString();
                  struct.extData.put(_key11, _val12);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 17: // SIGN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.sign = iprot.readString();
              struct.setSignIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, IssueRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.seq != null) {
        oprot.writeFieldBegin(SEQ_FIELD_DESC);
        oprot.writeString(struct.seq);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TASK_ID_FIELD_DESC);
      oprot.writeI64(struct.taskId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RECEIVER_FIELD_DESC);
      oprot.writeI64(struct.receiver);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SSID_FIELD_DESC);
      oprot.writeI64(struct.ssid);
      oprot.writeFieldEnd();
      if (struct.itemType != null) {
        oprot.writeFieldBegin(ITEM_TYPE_FIELD_DESC);
        oprot.writeString(struct.itemType);
        oprot.writeFieldEnd();
      }
      if (struct.itemId != null) {
        oprot.writeFieldBegin(ITEM_ID_FIELD_DESC);
        oprot.writeString(struct.itemId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(ITEM_NUM_FIELD_DESC);
      oprot.writeI64(struct.itemNum);
      oprot.writeFieldEnd();
      if (struct.platform != null) {
        oprot.writeFieldBegin(PLATFORM_FIELD_DESC);
        oprot.writeI32(struct.platform.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.timestamp != null) {
        oprot.writeFieldBegin(TIMESTAMP_FIELD_DESC);
        oprot.writeString(struct.timestamp);
        oprot.writeFieldEnd();
      }
      if (struct.ip != null) {
        oprot.writeFieldBegin(IP_FIELD_DESC);
        oprot.writeString(struct.ip);
        oprot.writeFieldEnd();
      }
      if (struct.mac != null) {
        oprot.writeFieldBegin(MAC_FIELD_DESC);
        oprot.writeString(struct.mac);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(EXT_LONG_FIELD_DESC);
      oprot.writeI64(struct.extLong);
      oprot.writeFieldEnd();
      if (struct.extJson != null) {
        oprot.writeFieldBegin(EXT_JSON_FIELD_DESC);
        oprot.writeString(struct.extJson);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter14 : struct.extData.entrySet())
          {
            oprot.writeString(_iter14.getKey());
            oprot.writeString(_iter14.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.sign != null) {
        oprot.writeFieldBegin(SIGN_FIELD_DESC);
        oprot.writeString(struct.sign);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class IssueRequestTupleSchemeFactory implements SchemeFactory {
    public IssueRequestTupleScheme getScheme() {
      return new IssueRequestTupleScheme();
    }
  }

  private static class IssueRequestTupleScheme extends TupleScheme<IssueRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, IssueRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetSeq()) {
        optionals.set(0);
      }
      if (struct.isSetActId()) {
        optionals.set(1);
      }
      if (struct.isSetTaskId()) {
        optionals.set(2);
      }
      if (struct.isSetReceiver()) {
        optionals.set(3);
      }
      if (struct.isSetSid()) {
        optionals.set(4);
      }
      if (struct.isSetSsid()) {
        optionals.set(5);
      }
      if (struct.isSetItemType()) {
        optionals.set(6);
      }
      if (struct.isSetItemId()) {
        optionals.set(7);
      }
      if (struct.isSetItemNum()) {
        optionals.set(8);
      }
      if (struct.isSetPlatform()) {
        optionals.set(9);
      }
      if (struct.isSetTimestamp()) {
        optionals.set(10);
      }
      if (struct.isSetIp()) {
        optionals.set(11);
      }
      if (struct.isSetMac()) {
        optionals.set(12);
      }
      if (struct.isSetExtLong()) {
        optionals.set(13);
      }
      if (struct.isSetExtJson()) {
        optionals.set(14);
      }
      if (struct.isSetExtData()) {
        optionals.set(15);
      }
      if (struct.isSetSign()) {
        optionals.set(16);
      }
      oprot.writeBitSet(optionals, 17);
      if (struct.isSetSeq()) {
        oprot.writeString(struct.seq);
      }
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetTaskId()) {
        oprot.writeI64(struct.taskId);
      }
      if (struct.isSetReceiver()) {
        oprot.writeI64(struct.receiver);
      }
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetSsid()) {
        oprot.writeI64(struct.ssid);
      }
      if (struct.isSetItemType()) {
        oprot.writeString(struct.itemType);
      }
      if (struct.isSetItemId()) {
        oprot.writeString(struct.itemId);
      }
      if (struct.isSetItemNum()) {
        oprot.writeI64(struct.itemNum);
      }
      if (struct.isSetPlatform()) {
        oprot.writeI32(struct.platform.getValue());
      }
      if (struct.isSetTimestamp()) {
        oprot.writeString(struct.timestamp);
      }
      if (struct.isSetIp()) {
        oprot.writeString(struct.ip);
      }
      if (struct.isSetMac()) {
        oprot.writeString(struct.mac);
      }
      if (struct.isSetExtLong()) {
        oprot.writeI64(struct.extLong);
      }
      if (struct.isSetExtJson()) {
        oprot.writeString(struct.extJson);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter15 : struct.extData.entrySet())
          {
            oprot.writeString(_iter15.getKey());
            oprot.writeString(_iter15.getValue());
          }
        }
      }
      if (struct.isSetSign()) {
        oprot.writeString(struct.sign);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, IssueRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(17);
      if (incoming.get(0)) {
        struct.seq = iprot.readString();
        struct.setSeqIsSet(true);
      }
      if (incoming.get(1)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.taskId = iprot.readI64();
        struct.setTaskIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.receiver = iprot.readI64();
        struct.setReceiverIsSet(true);
      }
      if (incoming.get(4)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(5)) {
        struct.ssid = iprot.readI64();
        struct.setSsidIsSet(true);
      }
      if (incoming.get(6)) {
        struct.itemType = iprot.readString();
        struct.setItemTypeIsSet(true);
      }
      if (incoming.get(7)) {
        struct.itemId = iprot.readString();
        struct.setItemIdIsSet(true);
      }
      if (incoming.get(8)) {
        struct.itemNum = iprot.readI64();
        struct.setItemNumIsSet(true);
      }
      if (incoming.get(9)) {
        struct.platform = Platform.findByValue(iprot.readI32());
        struct.setPlatformIsSet(true);
      }
      if (incoming.get(10)) {
        struct.timestamp = iprot.readString();
        struct.setTimestampIsSet(true);
      }
      if (incoming.get(11)) {
        struct.ip = iprot.readString();
        struct.setIpIsSet(true);
      }
      if (incoming.get(12)) {
        struct.mac = iprot.readString();
        struct.setMacIsSet(true);
      }
      if (incoming.get(13)) {
        struct.extLong = iprot.readI64();
        struct.setExtLongIsSet(true);
      }
      if (incoming.get(14)) {
        struct.extJson = iprot.readString();
        struct.setExtJsonIsSet(true);
      }
      if (incoming.get(15)) {
        {
          org.apache.thrift.protocol.TMap _map16 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map16.size);
          String _key17;
          String _val18;
          for (int _i19 = 0; _i19 < _map16.size; ++_i19)
          {
            _key17 = iprot.readString();
            _val18 = iprot.readString();
            struct.extData.put(_key17, _val18);
          }
        }
        struct.setExtDataIsSet(true);
      }
      if (incoming.get(16)) {
        struct.sign = iprot.readString();
        struct.setSignIsSet(true);
      }
    }
  }

}

