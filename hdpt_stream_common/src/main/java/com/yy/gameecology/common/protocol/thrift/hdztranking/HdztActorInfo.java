/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class HdztActorInfo implements org.apache.thrift.TBase<HdztActorInfo, HdztActorInfo._Fields>, java.io.Serializable, Cloneable, Comparable<HdztActorInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("HdztActorInfo");

  private static final org.apache.thrift.protocol.TField ROLE_FIELD_DESC = new org.apache.thrift.protocol.TField("role", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField BUSI_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("busiId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("name", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("type", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField REMARK_FIELD_DESC = new org.apache.thrift.protocol.TField("remark", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField EXTJSON_FIELD_DESC = new org.apache.thrift.protocol.TField("extjson", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField CTIME_FIELD_DESC = new org.apache.thrift.protocol.TField("ctime", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField UTIME_FIELD_DESC = new org.apache.thrift.protocol.TField("utime", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new HdztActorInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new HdztActorInfoTupleSchemeFactory());
  }

  public long role; // required
  public long busiId; // required
  public String name; // required
  public long type; // required
  public String remark; // required
  public String extjson; // required
  public long ctime; // required
  public long utime; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ROLE((short)1, "role"),
    BUSI_ID((short)2, "busiId"),
    NAME((short)3, "name"),
    TYPE((short)4, "type"),
    REMARK((short)5, "remark"),
    EXTJSON((short)6, "extjson"),
    CTIME((short)7, "ctime"),
    UTIME((short)8, "utime"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROLE
          return ROLE;
        case 2: // BUSI_ID
          return BUSI_ID;
        case 3: // NAME
          return NAME;
        case 4: // TYPE
          return TYPE;
        case 5: // REMARK
          return REMARK;
        case 6: // EXTJSON
          return EXTJSON;
        case 7: // CTIME
          return CTIME;
        case 8: // UTIME
          return UTIME;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ROLE_ISSET_ID = 0;
  private static final int __BUSIID_ISSET_ID = 1;
  private static final int __TYPE_ISSET_ID = 2;
  private static final int __CTIME_ISSET_ID = 3;
  private static final int __UTIME_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROLE, new org.apache.thrift.meta_data.FieldMetaData("role", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.BUSI_ID, new org.apache.thrift.meta_data.FieldMetaData("busiId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.NAME, new org.apache.thrift.meta_data.FieldMetaData("name", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TYPE, new org.apache.thrift.meta_data.FieldMetaData("type", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.REMARK, new org.apache.thrift.meta_data.FieldMetaData("remark", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXTJSON, new org.apache.thrift.meta_data.FieldMetaData("extjson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CTIME, new org.apache.thrift.meta_data.FieldMetaData("ctime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.UTIME, new org.apache.thrift.meta_data.FieldMetaData("utime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(HdztActorInfo.class, metaDataMap);
  }

  public HdztActorInfo() {
  }

  public HdztActorInfo(
    long role,
    long busiId,
    String name,
    long type,
    String remark,
    String extjson,
    long ctime,
    long utime,
    Map<String,String> extData)
  {
    this();
    this.role = role;
    setRoleIsSet(true);
    this.busiId = busiId;
    setBusiIdIsSet(true);
    this.name = name;
    this.type = type;
    setTypeIsSet(true);
    this.remark = remark;
    this.extjson = extjson;
    this.ctime = ctime;
    setCtimeIsSet(true);
    this.utime = utime;
    setUtimeIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public HdztActorInfo(HdztActorInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.role = other.role;
    this.busiId = other.busiId;
    if (other.isSetName()) {
      this.name = other.name;
    }
    this.type = other.type;
    if (other.isSetRemark()) {
      this.remark = other.remark;
    }
    if (other.isSetExtjson()) {
      this.extjson = other.extjson;
    }
    this.ctime = other.ctime;
    this.utime = other.utime;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public HdztActorInfo deepCopy() {
    return new HdztActorInfo(this);
  }

  @Override
  public void clear() {
    setRoleIsSet(false);
    this.role = 0;
    setBusiIdIsSet(false);
    this.busiId = 0;
    this.name = null;
    setTypeIsSet(false);
    this.type = 0;
    this.remark = null;
    this.extjson = null;
    setCtimeIsSet(false);
    this.ctime = 0;
    setUtimeIsSet(false);
    this.utime = 0;
    this.extData = null;
  }

  public long getRole() {
    return this.role;
  }

  public HdztActorInfo setRole(long role) {
    this.role = role;
    setRoleIsSet(true);
    return this;
  }

  public void unsetRole() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLE_ISSET_ID);
  }

  /** Returns true if field role is set (has been assigned a value) and false otherwise */
  public boolean isSetRole() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLE_ISSET_ID);
  }

  public void setRoleIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLE_ISSET_ID, value);
  }

  public long getBusiId() {
    return this.busiId;
  }

  public HdztActorInfo setBusiId(long busiId) {
    this.busiId = busiId;
    setBusiIdIsSet(true);
    return this;
  }

  public void unsetBusiId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BUSIID_ISSET_ID);
  }

  /** Returns true if field busiId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusiId() {
    return EncodingUtils.testBit(__isset_bitfield, __BUSIID_ISSET_ID);
  }

  public void setBusiIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BUSIID_ISSET_ID, value);
  }

  public String getName() {
    return this.name;
  }

  public HdztActorInfo setName(String name) {
    this.name = name;
    return this;
  }

  public void unsetName() {
    this.name = null;
  }

  /** Returns true if field name is set (has been assigned a value) and false otherwise */
  public boolean isSetName() {
    return this.name != null;
  }

  public void setNameIsSet(boolean value) {
    if (!value) {
      this.name = null;
    }
  }

  public long getType() {
    return this.type;
  }

  public HdztActorInfo setType(long type) {
    this.type = type;
    setTypeIsSet(true);
    return this;
  }

  public void unsetType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TYPE_ISSET_ID);
  }

  /** Returns true if field type is set (has been assigned a value) and false otherwise */
  public boolean isSetType() {
    return EncodingUtils.testBit(__isset_bitfield, __TYPE_ISSET_ID);
  }

  public void setTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TYPE_ISSET_ID, value);
  }

  public String getRemark() {
    return this.remark;
  }

  public HdztActorInfo setRemark(String remark) {
    this.remark = remark;
    return this;
  }

  public void unsetRemark() {
    this.remark = null;
  }

  /** Returns true if field remark is set (has been assigned a value) and false otherwise */
  public boolean isSetRemark() {
    return this.remark != null;
  }

  public void setRemarkIsSet(boolean value) {
    if (!value) {
      this.remark = null;
    }
  }

  public String getExtjson() {
    return this.extjson;
  }

  public HdztActorInfo setExtjson(String extjson) {
    this.extjson = extjson;
    return this;
  }

  public void unsetExtjson() {
    this.extjson = null;
  }

  /** Returns true if field extjson is set (has been assigned a value) and false otherwise */
  public boolean isSetExtjson() {
    return this.extjson != null;
  }

  public void setExtjsonIsSet(boolean value) {
    if (!value) {
      this.extjson = null;
    }
  }

  public long getCtime() {
    return this.ctime;
  }

  public HdztActorInfo setCtime(long ctime) {
    this.ctime = ctime;
    setCtimeIsSet(true);
    return this;
  }

  public void unsetCtime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CTIME_ISSET_ID);
  }

  /** Returns true if field ctime is set (has been assigned a value) and false otherwise */
  public boolean isSetCtime() {
    return EncodingUtils.testBit(__isset_bitfield, __CTIME_ISSET_ID);
  }

  public void setCtimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CTIME_ISSET_ID, value);
  }

  public long getUtime() {
    return this.utime;
  }

  public HdztActorInfo setUtime(long utime) {
    this.utime = utime;
    setUtimeIsSet(true);
    return this;
  }

  public void unsetUtime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __UTIME_ISSET_ID);
  }

  /** Returns true if field utime is set (has been assigned a value) and false otherwise */
  public boolean isSetUtime() {
    return EncodingUtils.testBit(__isset_bitfield, __UTIME_ISSET_ID);
  }

  public void setUtimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __UTIME_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public HdztActorInfo setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ROLE:
      if (value == null) {
        unsetRole();
      } else {
        setRole((Long)value);
      }
      break;

    case BUSI_ID:
      if (value == null) {
        unsetBusiId();
      } else {
        setBusiId((Long)value);
      }
      break;

    case NAME:
      if (value == null) {
        unsetName();
      } else {
        setName((String)value);
      }
      break;

    case TYPE:
      if (value == null) {
        unsetType();
      } else {
        setType((Long)value);
      }
      break;

    case REMARK:
      if (value == null) {
        unsetRemark();
      } else {
        setRemark((String)value);
      }
      break;

    case EXTJSON:
      if (value == null) {
        unsetExtjson();
      } else {
        setExtjson((String)value);
      }
      break;

    case CTIME:
      if (value == null) {
        unsetCtime();
      } else {
        setCtime((Long)value);
      }
      break;

    case UTIME:
      if (value == null) {
        unsetUtime();
      } else {
        setUtime((Long)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ROLE:
      return getRole();

    case BUSI_ID:
      return getBusiId();

    case NAME:
      return getName();

    case TYPE:
      return getType();

    case REMARK:
      return getRemark();

    case EXTJSON:
      return getExtjson();

    case CTIME:
      return getCtime();

    case UTIME:
      return getUtime();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ROLE:
      return isSetRole();
    case BUSI_ID:
      return isSetBusiId();
    case NAME:
      return isSetName();
    case TYPE:
      return isSetType();
    case REMARK:
      return isSetRemark();
    case EXTJSON:
      return isSetExtjson();
    case CTIME:
      return isSetCtime();
    case UTIME:
      return isSetUtime();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof HdztActorInfo)
      return this.equals((HdztActorInfo)that);
    return false;
  }

  public boolean equals(HdztActorInfo that) {
    if (that == null)
      return false;

    boolean this_present_role = true;
    boolean that_present_role = true;
    if (this_present_role || that_present_role) {
      if (!(this_present_role && that_present_role))
        return false;
      if (this.role != that.role)
        return false;
    }

    boolean this_present_busiId = true;
    boolean that_present_busiId = true;
    if (this_present_busiId || that_present_busiId) {
      if (!(this_present_busiId && that_present_busiId))
        return false;
      if (this.busiId != that.busiId)
        return false;
    }

    boolean this_present_name = true && this.isSetName();
    boolean that_present_name = true && that.isSetName();
    if (this_present_name || that_present_name) {
      if (!(this_present_name && that_present_name))
        return false;
      if (!this.name.equals(that.name))
        return false;
    }

    boolean this_present_type = true;
    boolean that_present_type = true;
    if (this_present_type || that_present_type) {
      if (!(this_present_type && that_present_type))
        return false;
      if (this.type != that.type)
        return false;
    }

    boolean this_present_remark = true && this.isSetRemark();
    boolean that_present_remark = true && that.isSetRemark();
    if (this_present_remark || that_present_remark) {
      if (!(this_present_remark && that_present_remark))
        return false;
      if (!this.remark.equals(that.remark))
        return false;
    }

    boolean this_present_extjson = true && this.isSetExtjson();
    boolean that_present_extjson = true && that.isSetExtjson();
    if (this_present_extjson || that_present_extjson) {
      if (!(this_present_extjson && that_present_extjson))
        return false;
      if (!this.extjson.equals(that.extjson))
        return false;
    }

    boolean this_present_ctime = true;
    boolean that_present_ctime = true;
    if (this_present_ctime || that_present_ctime) {
      if (!(this_present_ctime && that_present_ctime))
        return false;
      if (this.ctime != that.ctime)
        return false;
    }

    boolean this_present_utime = true;
    boolean that_present_utime = true;
    if (this_present_utime || that_present_utime) {
      if (!(this_present_utime && that_present_utime))
        return false;
      if (this.utime != that.utime)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_role = true;
    list.add(present_role);
    if (present_role)
      list.add(role);

    boolean present_busiId = true;
    list.add(present_busiId);
    if (present_busiId)
      list.add(busiId);

    boolean present_name = true && (isSetName());
    list.add(present_name);
    if (present_name)
      list.add(name);

    boolean present_type = true;
    list.add(present_type);
    if (present_type)
      list.add(type);

    boolean present_remark = true && (isSetRemark());
    list.add(present_remark);
    if (present_remark)
      list.add(remark);

    boolean present_extjson = true && (isSetExtjson());
    list.add(present_extjson);
    if (present_extjson)
      list.add(extjson);

    boolean present_ctime = true;
    list.add(present_ctime);
    if (present_ctime)
      list.add(ctime);

    boolean present_utime = true;
    list.add(present_utime);
    if (present_utime)
      list.add(utime);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(HdztActorInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRole()).compareTo(other.isSetRole());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRole()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.role, other.role);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetBusiId()).compareTo(other.isSetBusiId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusiId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.busiId, other.busiId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetName()).compareTo(other.isSetName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.name, other.name);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetType()).compareTo(other.isSetType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.type, other.type);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRemark()).compareTo(other.isSetRemark());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRemark()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.remark, other.remark);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtjson()).compareTo(other.isSetExtjson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtjson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extjson, other.extjson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCtime()).compareTo(other.isSetCtime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCtime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ctime, other.ctime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUtime()).compareTo(other.isSetUtime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUtime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.utime, other.utime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("HdztActorInfo(");
    boolean first = true;

    sb.append("role:");
    sb.append(this.role);
    first = false;
    if (!first) sb.append(", ");
    sb.append("busiId:");
    sb.append(this.busiId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("name:");
    if (this.name == null) {
      sb.append("null");
    } else {
      sb.append(this.name);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("type:");
    sb.append(this.type);
    first = false;
    if (!first) sb.append(", ");
    sb.append("remark:");
    if (this.remark == null) {
      sb.append("null");
    } else {
      sb.append(this.remark);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extjson:");
    if (this.extjson == null) {
      sb.append("null");
    } else {
      sb.append(this.extjson);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ctime:");
    sb.append(this.ctime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("utime:");
    sb.append(this.utime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class HdztActorInfoStandardSchemeFactory implements SchemeFactory {
    public HdztActorInfoStandardScheme getScheme() {
      return new HdztActorInfoStandardScheme();
    }
  }

  private static class HdztActorInfoStandardScheme extends StandardScheme<HdztActorInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, HdztActorInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROLE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.role = iprot.readI64();
              struct.setRoleIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // BUSI_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.busiId = iprot.readI64();
              struct.setBusiIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.name = iprot.readString();
              struct.setNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.type = iprot.readI64();
              struct.setTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // REMARK
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.remark = iprot.readString();
              struct.setRemarkIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // EXTJSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.extjson = iprot.readString();
              struct.setExtjsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // CTIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ctime = iprot.readI64();
              struct.setCtimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // UTIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.utime = iprot.readI64();
              struct.setUtimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map706 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map706.size);
                String _key707;
                String _val708;
                for (int _i709 = 0; _i709 < _map706.size; ++_i709)
                {
                  _key707 = iprot.readString();
                  _val708 = iprot.readString();
                  struct.extData.put(_key707, _val708);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, HdztActorInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ROLE_FIELD_DESC);
      oprot.writeI64(struct.role);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(BUSI_ID_FIELD_DESC);
      oprot.writeI64(struct.busiId);
      oprot.writeFieldEnd();
      if (struct.name != null) {
        oprot.writeFieldBegin(NAME_FIELD_DESC);
        oprot.writeString(struct.name);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(TYPE_FIELD_DESC);
      oprot.writeI64(struct.type);
      oprot.writeFieldEnd();
      if (struct.remark != null) {
        oprot.writeFieldBegin(REMARK_FIELD_DESC);
        oprot.writeString(struct.remark);
        oprot.writeFieldEnd();
      }
      if (struct.extjson != null) {
        oprot.writeFieldBegin(EXTJSON_FIELD_DESC);
        oprot.writeString(struct.extjson);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CTIME_FIELD_DESC);
      oprot.writeI64(struct.ctime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(UTIME_FIELD_DESC);
      oprot.writeI64(struct.utime);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter710 : struct.extData.entrySet())
          {
            oprot.writeString(_iter710.getKey());
            oprot.writeString(_iter710.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class HdztActorInfoTupleSchemeFactory implements SchemeFactory {
    public HdztActorInfoTupleScheme getScheme() {
      return new HdztActorInfoTupleScheme();
    }
  }

  private static class HdztActorInfoTupleScheme extends TupleScheme<HdztActorInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, HdztActorInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRole()) {
        optionals.set(0);
      }
      if (struct.isSetBusiId()) {
        optionals.set(1);
      }
      if (struct.isSetName()) {
        optionals.set(2);
      }
      if (struct.isSetType()) {
        optionals.set(3);
      }
      if (struct.isSetRemark()) {
        optionals.set(4);
      }
      if (struct.isSetExtjson()) {
        optionals.set(5);
      }
      if (struct.isSetCtime()) {
        optionals.set(6);
      }
      if (struct.isSetUtime()) {
        optionals.set(7);
      }
      if (struct.isSetExtData()) {
        optionals.set(8);
      }
      oprot.writeBitSet(optionals, 9);
      if (struct.isSetRole()) {
        oprot.writeI64(struct.role);
      }
      if (struct.isSetBusiId()) {
        oprot.writeI64(struct.busiId);
      }
      if (struct.isSetName()) {
        oprot.writeString(struct.name);
      }
      if (struct.isSetType()) {
        oprot.writeI64(struct.type);
      }
      if (struct.isSetRemark()) {
        oprot.writeString(struct.remark);
      }
      if (struct.isSetExtjson()) {
        oprot.writeString(struct.extjson);
      }
      if (struct.isSetCtime()) {
        oprot.writeI64(struct.ctime);
      }
      if (struct.isSetUtime()) {
        oprot.writeI64(struct.utime);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter711 : struct.extData.entrySet())
          {
            oprot.writeString(_iter711.getKey());
            oprot.writeString(_iter711.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, HdztActorInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(9);
      if (incoming.get(0)) {
        struct.role = iprot.readI64();
        struct.setRoleIsSet(true);
      }
      if (incoming.get(1)) {
        struct.busiId = iprot.readI64();
        struct.setBusiIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.name = iprot.readString();
        struct.setNameIsSet(true);
      }
      if (incoming.get(3)) {
        struct.type = iprot.readI64();
        struct.setTypeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.remark = iprot.readString();
        struct.setRemarkIsSet(true);
      }
      if (incoming.get(5)) {
        struct.extjson = iprot.readString();
        struct.setExtjsonIsSet(true);
      }
      if (incoming.get(6)) {
        struct.ctime = iprot.readI64();
        struct.setCtimeIsSet(true);
      }
      if (incoming.get(7)) {
        struct.utime = iprot.readI64();
        struct.setUtimeIsSet(true);
      }
      if (incoming.get(8)) {
        {
          org.apache.thrift.protocol.TMap _map712 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map712.size);
          String _key713;
          String _val714;
          for (int _i715 = 0; _i715 < _map712.size; ++_i715)
          {
            _key713 = iprot.readString();
            _val714 = iprot.readString();
            struct.extData.put(_key713, _val714);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

