/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_compere_group;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-08-06")
public class QueryGroupReq implements org.apache.thrift.TBase<QueryGroupReq, QueryGroupReq._Fields>, java.io.Serializable, Cloneable, Comparable<QueryGroupReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryGroupReq");

  private static final org.apache.thrift.protocol.TField R_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("r_id", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField MEMBERS_FIELD_DESC = new org.apache.thrift.protocol.TField("members", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField QUERY_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("query_type", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField SOURCE_FIELD_DESC = new org.apache.thrift.protocol.TField("source", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField VISIBLE_FIELD_DESC = new org.apache.thrift.protocol.TField("visible", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.MAP, (short)15);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryGroupReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryGroupReqTupleSchemeFactory());
  }

  public long r_id; // required
  public List<String> members; // required
  /**
   * 
   * @see QueryType
   */
  public QueryType query_type; // required
  /**
   * 
   * @see Source
   */
  public Source source; // required
  /**
   * 
   * @see Visibility
   */
  public Visibility visible; // required
  public Map<String,String> expand; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    R_ID((short)1, "r_id"),
    MEMBERS((short)2, "members"),
    /**
     * 
     * @see QueryType
     */
    QUERY_TYPE((short)3, "query_type"),
    /**
     * 
     * @see Source
     */
    SOURCE((short)4, "source"),
    /**
     * 
     * @see Visibility
     */
    VISIBLE((short)5, "visible"),
    EXPAND((short)15, "expand");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // R_ID
          return R_ID;
        case 2: // MEMBERS
          return MEMBERS;
        case 3: // QUERY_TYPE
          return QUERY_TYPE;
        case 4: // SOURCE
          return SOURCE;
        case 5: // VISIBLE
          return VISIBLE;
        case 15: // EXPAND
          return EXPAND;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __R_ID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.R_ID, new org.apache.thrift.meta_data.FieldMetaData("r_id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MEMBERS, new org.apache.thrift.meta_data.FieldMetaData("members", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.QUERY_TYPE, new org.apache.thrift.meta_data.FieldMetaData("query_type", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, QueryType.class)));
    tmpMap.put(_Fields.SOURCE, new org.apache.thrift.meta_data.FieldMetaData("source", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, Source.class)));
    tmpMap.put(_Fields.VISIBLE, new org.apache.thrift.meta_data.FieldMetaData("visible", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, Visibility.class)));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryGroupReq.class, metaDataMap);
  }

  public QueryGroupReq() {
  }

  public QueryGroupReq(
    long r_id,
    List<String> members,
    QueryType query_type,
    Source source,
    Visibility visible,
    Map<String,String> expand)
  {
    this();
    this.r_id = r_id;
    setR_idIsSet(true);
    this.members = members;
    this.query_type = query_type;
    this.source = source;
    this.visible = visible;
    this.expand = expand;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryGroupReq(QueryGroupReq other) {
    __isset_bitfield = other.__isset_bitfield;
    this.r_id = other.r_id;
    if (other.isSetMembers()) {
      List<String> __this__members = new ArrayList<String>(other.members);
      this.members = __this__members;
    }
    if (other.isSetQuery_type()) {
      this.query_type = other.query_type;
    }
    if (other.isSetSource()) {
      this.source = other.source;
    }
    if (other.isSetVisible()) {
      this.visible = other.visible;
    }
    if (other.isSetExpand()) {
      Map<String,String> __this__expand = new HashMap<String,String>(other.expand);
      this.expand = __this__expand;
    }
  }

  public QueryGroupReq deepCopy() {
    return new QueryGroupReq(this);
  }

  @Override
  public void clear() {
    setR_idIsSet(false);
    this.r_id = 0;
    this.members = null;
    this.query_type = null;
    this.source = null;
    this.visible = null;
    this.expand = null;
  }

  public long getR_id() {
    return this.r_id;
  }

  public QueryGroupReq setR_id(long r_id) {
    this.r_id = r_id;
    setR_idIsSet(true);
    return this;
  }

  public void unsetR_id() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __R_ID_ISSET_ID);
  }

  /** Returns true if field r_id is set (has been assigned a value) and false otherwise */
  public boolean isSetR_id() {
    return EncodingUtils.testBit(__isset_bitfield, __R_ID_ISSET_ID);
  }

  public void setR_idIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __R_ID_ISSET_ID, value);
  }

  public int getMembersSize() {
    return (this.members == null) ? 0 : this.members.size();
  }

  public java.util.Iterator<String> getMembersIterator() {
    return (this.members == null) ? null : this.members.iterator();
  }

  public void addToMembers(String elem) {
    if (this.members == null) {
      this.members = new ArrayList<String>();
    }
    this.members.add(elem);
  }

  public List<String> getMembers() {
    return this.members;
  }

  public QueryGroupReq setMembers(List<String> members) {
    this.members = members;
    return this;
  }

  public void unsetMembers() {
    this.members = null;
  }

  /** Returns true if field members is set (has been assigned a value) and false otherwise */
  public boolean isSetMembers() {
    return this.members != null;
  }

  public void setMembersIsSet(boolean value) {
    if (!value) {
      this.members = null;
    }
  }

  /**
   * 
   * @see QueryType
   */
  public QueryType getQuery_type() {
    return this.query_type;
  }

  /**
   * 
   * @see QueryType
   */
  public QueryGroupReq setQuery_type(QueryType query_type) {
    this.query_type = query_type;
    return this;
  }

  public void unsetQuery_type() {
    this.query_type = null;
  }

  /** Returns true if field query_type is set (has been assigned a value) and false otherwise */
  public boolean isSetQuery_type() {
    return this.query_type != null;
  }

  public void setQuery_typeIsSet(boolean value) {
    if (!value) {
      this.query_type = null;
    }
  }

  /**
   * 
   * @see Source
   */
  public Source getSource() {
    return this.source;
  }

  /**
   * 
   * @see Source
   */
  public QueryGroupReq setSource(Source source) {
    this.source = source;
    return this;
  }

  public void unsetSource() {
    this.source = null;
  }

  /** Returns true if field source is set (has been assigned a value) and false otherwise */
  public boolean isSetSource() {
    return this.source != null;
  }

  public void setSourceIsSet(boolean value) {
    if (!value) {
      this.source = null;
    }
  }

  /**
   * 
   * @see Visibility
   */
  public Visibility getVisible() {
    return this.visible;
  }

  /**
   * 
   * @see Visibility
   */
  public QueryGroupReq setVisible(Visibility visible) {
    this.visible = visible;
    return this;
  }

  public void unsetVisible() {
    this.visible = null;
  }

  /** Returns true if field visible is set (has been assigned a value) and false otherwise */
  public boolean isSetVisible() {
    return this.visible != null;
  }

  public void setVisibleIsSet(boolean value) {
    if (!value) {
      this.visible = null;
    }
  }

  public int getExpandSize() {
    return (this.expand == null) ? 0 : this.expand.size();
  }

  public void putToExpand(String key, String val) {
    if (this.expand == null) {
      this.expand = new HashMap<String,String>();
    }
    this.expand.put(key, val);
  }

  public Map<String,String> getExpand() {
    return this.expand;
  }

  public QueryGroupReq setExpand(Map<String,String> expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case R_ID:
      if (value == null) {
        unsetR_id();
      } else {
        setR_id((Long)value);
      }
      break;

    case MEMBERS:
      if (value == null) {
        unsetMembers();
      } else {
        setMembers((List<String>)value);
      }
      break;

    case QUERY_TYPE:
      if (value == null) {
        unsetQuery_type();
      } else {
        setQuery_type((QueryType)value);
      }
      break;

    case SOURCE:
      if (value == null) {
        unsetSource();
      } else {
        setSource((Source)value);
      }
      break;

    case VISIBLE:
      if (value == null) {
        unsetVisible();
      } else {
        setVisible((Visibility)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case R_ID:
      return getR_id();

    case MEMBERS:
      return getMembers();

    case QUERY_TYPE:
      return getQuery_type();

    case SOURCE:
      return getSource();

    case VISIBLE:
      return getVisible();

    case EXPAND:
      return getExpand();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case R_ID:
      return isSetR_id();
    case MEMBERS:
      return isSetMembers();
    case QUERY_TYPE:
      return isSetQuery_type();
    case SOURCE:
      return isSetSource();
    case VISIBLE:
      return isSetVisible();
    case EXPAND:
      return isSetExpand();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryGroupReq)
      return this.equals((QueryGroupReq)that);
    return false;
  }

  public boolean equals(QueryGroupReq that) {
    if (that == null)
      return false;

    boolean this_present_r_id = true;
    boolean that_present_r_id = true;
    if (this_present_r_id || that_present_r_id) {
      if (!(this_present_r_id && that_present_r_id))
        return false;
      if (this.r_id != that.r_id)
        return false;
    }

    boolean this_present_members = true && this.isSetMembers();
    boolean that_present_members = true && that.isSetMembers();
    if (this_present_members || that_present_members) {
      if (!(this_present_members && that_present_members))
        return false;
      if (!this.members.equals(that.members))
        return false;
    }

    boolean this_present_query_type = true && this.isSetQuery_type();
    boolean that_present_query_type = true && that.isSetQuery_type();
    if (this_present_query_type || that_present_query_type) {
      if (!(this_present_query_type && that_present_query_type))
        return false;
      if (!this.query_type.equals(that.query_type))
        return false;
    }

    boolean this_present_source = true && this.isSetSource();
    boolean that_present_source = true && that.isSetSource();
    if (this_present_source || that_present_source) {
      if (!(this_present_source && that_present_source))
        return false;
      if (!this.source.equals(that.source))
        return false;
    }

    boolean this_present_visible = true && this.isSetVisible();
    boolean that_present_visible = true && that.isSetVisible();
    if (this_present_visible || that_present_visible) {
      if (!(this_present_visible && that_present_visible))
        return false;
      if (!this.visible.equals(that.visible))
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_r_id = true;
    list.add(present_r_id);
    if (present_r_id)
      list.add(r_id);

    boolean present_members = true && (isSetMembers());
    list.add(present_members);
    if (present_members)
      list.add(members);

    boolean present_query_type = true && (isSetQuery_type());
    list.add(present_query_type);
    if (present_query_type)
      list.add(query_type.getValue());

    boolean present_source = true && (isSetSource());
    list.add(present_source);
    if (present_source)
      list.add(source.getValue());

    boolean present_visible = true && (isSetVisible());
    list.add(present_visible);
    if (present_visible)
      list.add(visible.getValue());

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryGroupReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetR_id()).compareTo(other.isSetR_id());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetR_id()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.r_id, other.r_id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMembers()).compareTo(other.isSetMembers());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMembers()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.members, other.members);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetQuery_type()).compareTo(other.isSetQuery_type());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetQuery_type()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.query_type, other.query_type);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSource()).compareTo(other.isSetSource());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSource()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.source, other.source);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetVisible()).compareTo(other.isSetVisible());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetVisible()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.visible, other.visible);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryGroupReq(");
    boolean first = true;

    sb.append("r_id:");
    sb.append(this.r_id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("members:");
    if (this.members == null) {
      sb.append("null");
    } else {
      sb.append(this.members);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("query_type:");
    if (this.query_type == null) {
      sb.append("null");
    } else {
      sb.append(this.query_type);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("source:");
    if (this.source == null) {
      sb.append("null");
    } else {
      sb.append(this.source);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("visible:");
    if (this.visible == null) {
      sb.append("null");
    } else {
      sb.append(this.visible);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryGroupReqStandardSchemeFactory implements SchemeFactory {
    public QueryGroupReqStandardScheme getScheme() {
      return new QueryGroupReqStandardScheme();
    }
  }

  private static class QueryGroupReqStandardScheme extends StandardScheme<QueryGroupReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryGroupReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // R_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.r_id = iprot.readI64();
              struct.setR_idIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MEMBERS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list38 = iprot.readListBegin();
                struct.members = new ArrayList<String>(_list38.size);
                String _elem39;
                for (int _i40 = 0; _i40 < _list38.size; ++_i40)
                {
                  _elem39 = iprot.readString();
                  struct.members.add(_elem39);
                }
                iprot.readListEnd();
              }
              struct.setMembersIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // QUERY_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.query_type = com.yy.gameecology.common.protocol.thrift.fts_compere_group.QueryType.findByValue(iprot.readI32());
              struct.setQuery_typeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SOURCE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.source = com.yy.gameecology.common.protocol.thrift.fts_compere_group.Source.findByValue(iprot.readI32());
              struct.setSourceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // VISIBLE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.visible = com.yy.gameecology.common.protocol.thrift.fts_compere_group.Visibility.findByValue(iprot.readI32());
              struct.setVisibleIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map41 = iprot.readMapBegin();
                struct.expand = new HashMap<String,String>(2*_map41.size);
                String _key42;
                String _val43;
                for (int _i44 = 0; _i44 < _map41.size; ++_i44)
                {
                  _key42 = iprot.readString();
                  _val43 = iprot.readString();
                  struct.expand.put(_key42, _val43);
                }
                iprot.readMapEnd();
              }
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryGroupReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(R_ID_FIELD_DESC);
      oprot.writeI64(struct.r_id);
      oprot.writeFieldEnd();
      if (struct.members != null) {
        oprot.writeFieldBegin(MEMBERS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.members.size()));
          for (String _iter45 : struct.members)
          {
            oprot.writeString(_iter45);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.query_type != null) {
        oprot.writeFieldBegin(QUERY_TYPE_FIELD_DESC);
        oprot.writeI32(struct.query_type.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.source != null) {
        oprot.writeFieldBegin(SOURCE_FIELD_DESC);
        oprot.writeI32(struct.source.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.visible != null) {
        oprot.writeFieldBegin(VISIBLE_FIELD_DESC);
        oprot.writeI32(struct.visible.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.expand.size()));
          for (Map.Entry<String, String> _iter46 : struct.expand.entrySet())
          {
            oprot.writeString(_iter46.getKey());
            oprot.writeString(_iter46.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryGroupReqTupleSchemeFactory implements SchemeFactory {
    public QueryGroupReqTupleScheme getScheme() {
      return new QueryGroupReqTupleScheme();
    }
  }

  private static class QueryGroupReqTupleScheme extends TupleScheme<QueryGroupReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryGroupReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetR_id()) {
        optionals.set(0);
      }
      if (struct.isSetMembers()) {
        optionals.set(1);
      }
      if (struct.isSetQuery_type()) {
        optionals.set(2);
      }
      if (struct.isSetSource()) {
        optionals.set(3);
      }
      if (struct.isSetVisible()) {
        optionals.set(4);
      }
      if (struct.isSetExpand()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetR_id()) {
        oprot.writeI64(struct.r_id);
      }
      if (struct.isSetMembers()) {
        {
          oprot.writeI32(struct.members.size());
          for (String _iter47 : struct.members)
          {
            oprot.writeString(_iter47);
          }
        }
      }
      if (struct.isSetQuery_type()) {
        oprot.writeI32(struct.query_type.getValue());
      }
      if (struct.isSetSource()) {
        oprot.writeI32(struct.source.getValue());
      }
      if (struct.isSetVisible()) {
        oprot.writeI32(struct.visible.getValue());
      }
      if (struct.isSetExpand()) {
        {
          oprot.writeI32(struct.expand.size());
          for (Map.Entry<String, String> _iter48 : struct.expand.entrySet())
          {
            oprot.writeString(_iter48.getKey());
            oprot.writeString(_iter48.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryGroupReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.r_id = iprot.readI64();
        struct.setR_idIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list49 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.members = new ArrayList<String>(_list49.size);
          String _elem50;
          for (int _i51 = 0; _i51 < _list49.size; ++_i51)
          {
            _elem50 = iprot.readString();
            struct.members.add(_elem50);
          }
        }
        struct.setMembersIsSet(true);
      }
      if (incoming.get(2)) {
        struct.query_type = com.yy.gameecology.common.protocol.thrift.fts_compere_group.QueryType.findByValue(iprot.readI32());
        struct.setQuery_typeIsSet(true);
      }
      if (incoming.get(3)) {
        struct.source = com.yy.gameecology.common.protocol.thrift.fts_compere_group.Source.findByValue(iprot.readI32());
        struct.setSourceIsSet(true);
      }
      if (incoming.get(4)) {
        struct.visible = com.yy.gameecology.common.protocol.thrift.fts_compere_group.Visibility.findByValue(iprot.readI32());
        struct.setVisibleIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TMap _map52 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.expand = new HashMap<String,String>(2*_map52.size);
          String _key53;
          String _val54;
          for (int _i55 = 0; _i55 < _map52.size; ++_i55)
          {
            _key53 = iprot.readString();
            _val54 = iprot.readString();
            struct.expand.put(_key53, _val54);
          }
        }
        struct.setExpandIsSet(true);
      }
    }
  }

}

