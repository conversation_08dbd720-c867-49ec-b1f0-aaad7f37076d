/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class LBSInfo implements org.apache.thrift.TBase<LBSInfo, LBSInfo._Fields>, java.io.Serializable, Cloneable, Comparable<LBSInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("LBSInfo");

  private static final org.apache.thrift.protocol.TField CITY_FIELD_DESC = new org.apache.thrift.protocol.TField("city", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField LAT_FIELD_DESC = new org.apache.thrift.protocol.TField("lat", org.apache.thrift.protocol.TType.DOUBLE, (short)2);
  private static final org.apache.thrift.protocol.TField LNG_FIELD_DESC = new org.apache.thrift.protocol.TField("lng", org.apache.thrift.protocol.TType.DOUBLE, (short)3);
  private static final org.apache.thrift.protocol.TField PROVINCE_FIELD_DESC = new org.apache.thrift.protocol.TField("province", org.apache.thrift.protocol.TType.STRING, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new LBSInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new LBSInfoTupleSchemeFactory());
  }

  public String city; // required
  public double lat; // required
  public double lng; // required
  public String province; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    CITY((short)1, "city"),
    LAT((short)2, "lat"),
    LNG((short)3, "lng"),
    PROVINCE((short)4, "province");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CITY
          return CITY;
        case 2: // LAT
          return LAT;
        case 3: // LNG
          return LNG;
        case 4: // PROVINCE
          return PROVINCE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __LAT_ISSET_ID = 0;
  private static final int __LNG_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CITY, new org.apache.thrift.meta_data.FieldMetaData("city", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.LAT, new org.apache.thrift.meta_data.FieldMetaData("lat", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    tmpMap.put(_Fields.LNG, new org.apache.thrift.meta_data.FieldMetaData("lng", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.DOUBLE)));
    tmpMap.put(_Fields.PROVINCE, new org.apache.thrift.meta_data.FieldMetaData("province", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(LBSInfo.class, metaDataMap);
  }

  public LBSInfo() {
  }

  public LBSInfo(
    String city,
    double lat,
    double lng,
    String province)
  {
    this();
    this.city = city;
    this.lat = lat;
    setLatIsSet(true);
    this.lng = lng;
    setLngIsSet(true);
    this.province = province;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public LBSInfo(LBSInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetCity()) {
      this.city = other.city;
    }
    this.lat = other.lat;
    this.lng = other.lng;
    if (other.isSetProvince()) {
      this.province = other.province;
    }
  }

  public LBSInfo deepCopy() {
    return new LBSInfo(this);
  }

  @Override
  public void clear() {
    this.city = null;
    setLatIsSet(false);
    this.lat = 0.0;
    setLngIsSet(false);
    this.lng = 0.0;
    this.province = null;
  }

  public String getCity() {
    return this.city;
  }

  public LBSInfo setCity(String city) {
    this.city = city;
    return this;
  }

  public void unsetCity() {
    this.city = null;
  }

  /** Returns true if field city is set (has been assigned a value) and false otherwise */
  public boolean isSetCity() {
    return this.city != null;
  }

  public void setCityIsSet(boolean value) {
    if (!value) {
      this.city = null;
    }
  }

  public double getLat() {
    return this.lat;
  }

  public LBSInfo setLat(double lat) {
    this.lat = lat;
    setLatIsSet(true);
    return this;
  }

  public void unsetLat() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LAT_ISSET_ID);
  }

  /** Returns true if field lat is set (has been assigned a value) and false otherwise */
  public boolean isSetLat() {
    return EncodingUtils.testBit(__isset_bitfield, __LAT_ISSET_ID);
  }

  public void setLatIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LAT_ISSET_ID, value);
  }

  public double getLng() {
    return this.lng;
  }

  public LBSInfo setLng(double lng) {
    this.lng = lng;
    setLngIsSet(true);
    return this;
  }

  public void unsetLng() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LNG_ISSET_ID);
  }

  /** Returns true if field lng is set (has been assigned a value) and false otherwise */
  public boolean isSetLng() {
    return EncodingUtils.testBit(__isset_bitfield, __LNG_ISSET_ID);
  }

  public void setLngIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LNG_ISSET_ID, value);
  }

  public String getProvince() {
    return this.province;
  }

  public LBSInfo setProvince(String province) {
    this.province = province;
    return this;
  }

  public void unsetProvince() {
    this.province = null;
  }

  /** Returns true if field province is set (has been assigned a value) and false otherwise */
  public boolean isSetProvince() {
    return this.province != null;
  }

  public void setProvinceIsSet(boolean value) {
    if (!value) {
      this.province = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case CITY:
      if (value == null) {
        unsetCity();
      } else {
        setCity((String)value);
      }
      break;

    case LAT:
      if (value == null) {
        unsetLat();
      } else {
        setLat((Double)value);
      }
      break;

    case LNG:
      if (value == null) {
        unsetLng();
      } else {
        setLng((Double)value);
      }
      break;

    case PROVINCE:
      if (value == null) {
        unsetProvince();
      } else {
        setProvince((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case CITY:
      return getCity();

    case LAT:
      return getLat();

    case LNG:
      return getLng();

    case PROVINCE:
      return getProvince();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case CITY:
      return isSetCity();
    case LAT:
      return isSetLat();
    case LNG:
      return isSetLng();
    case PROVINCE:
      return isSetProvince();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof LBSInfo)
      return this.equals((LBSInfo)that);
    return false;
  }

  public boolean equals(LBSInfo that) {
    if (that == null)
      return false;

    boolean this_present_city = true && this.isSetCity();
    boolean that_present_city = true && that.isSetCity();
    if (this_present_city || that_present_city) {
      if (!(this_present_city && that_present_city))
        return false;
      if (!this.city.equals(that.city))
        return false;
    }

    boolean this_present_lat = true;
    boolean that_present_lat = true;
    if (this_present_lat || that_present_lat) {
      if (!(this_present_lat && that_present_lat))
        return false;
      if (this.lat != that.lat)
        return false;
    }

    boolean this_present_lng = true;
    boolean that_present_lng = true;
    if (this_present_lng || that_present_lng) {
      if (!(this_present_lng && that_present_lng))
        return false;
      if (this.lng != that.lng)
        return false;
    }

    boolean this_present_province = true && this.isSetProvince();
    boolean that_present_province = true && that.isSetProvince();
    if (this_present_province || that_present_province) {
      if (!(this_present_province && that_present_province))
        return false;
      if (!this.province.equals(that.province))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_city = true && (isSetCity());
    list.add(present_city);
    if (present_city)
      list.add(city);

    boolean present_lat = true;
    list.add(present_lat);
    if (present_lat)
      list.add(lat);

    boolean present_lng = true;
    list.add(present_lng);
    if (present_lng)
      list.add(lng);

    boolean present_province = true && (isSetProvince());
    list.add(present_province);
    if (present_province)
      list.add(province);

    return list.hashCode();
  }

  @Override
  public int compareTo(LBSInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetCity()).compareTo(other.isSetCity());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCity()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.city, other.city);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLat()).compareTo(other.isSetLat());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLat()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lat, other.lat);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLng()).compareTo(other.isSetLng());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLng()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lng, other.lng);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetProvince()).compareTo(other.isSetProvince());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetProvince()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.province, other.province);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("LBSInfo(");
    boolean first = true;

    sb.append("city:");
    if (this.city == null) {
      sb.append("null");
    } else {
      sb.append(this.city);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("lat:");
    sb.append(this.lat);
    first = false;
    if (!first) sb.append(", ");
    sb.append("lng:");
    sb.append(this.lng);
    first = false;
    if (!first) sb.append(", ");
    sb.append("province:");
    if (this.province == null) {
      sb.append("null");
    } else {
      sb.append(this.province);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class LBSInfoStandardSchemeFactory implements SchemeFactory {
    public LBSInfoStandardScheme getScheme() {
      return new LBSInfoStandardScheme();
    }
  }

  private static class LBSInfoStandardScheme extends StandardScheme<LBSInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, LBSInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CITY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.city = iprot.readString();
              struct.setCityIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // LAT
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.lat = iprot.readDouble();
              struct.setLatIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // LNG
            if (schemeField.type == org.apache.thrift.protocol.TType.DOUBLE) {
              struct.lng = iprot.readDouble();
              struct.setLngIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PROVINCE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.province = iprot.readString();
              struct.setProvinceIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, LBSInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.city != null) {
        oprot.writeFieldBegin(CITY_FIELD_DESC);
        oprot.writeString(struct.city);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(LAT_FIELD_DESC);
      oprot.writeDouble(struct.lat);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(LNG_FIELD_DESC);
      oprot.writeDouble(struct.lng);
      oprot.writeFieldEnd();
      if (struct.province != null) {
        oprot.writeFieldBegin(PROVINCE_FIELD_DESC);
        oprot.writeString(struct.province);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class LBSInfoTupleSchemeFactory implements SchemeFactory {
    public LBSInfoTupleScheme getScheme() {
      return new LBSInfoTupleScheme();
    }
  }

  private static class LBSInfoTupleScheme extends TupleScheme<LBSInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, LBSInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetCity()) {
        optionals.set(0);
      }
      if (struct.isSetLat()) {
        optionals.set(1);
      }
      if (struct.isSetLng()) {
        optionals.set(2);
      }
      if (struct.isSetProvince()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetCity()) {
        oprot.writeString(struct.city);
      }
      if (struct.isSetLat()) {
        oprot.writeDouble(struct.lat);
      }
      if (struct.isSetLng()) {
        oprot.writeDouble(struct.lng);
      }
      if (struct.isSetProvince()) {
        oprot.writeString(struct.province);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, LBSInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.city = iprot.readString();
        struct.setCityIsSet(true);
      }
      if (incoming.get(1)) {
        struct.lat = iprot.readDouble();
        struct.setLatIsSet(true);
      }
      if (incoming.get(2)) {
        struct.lng = iprot.readDouble();
        struct.setLngIsSet(true);
      }
      if (incoming.get(3)) {
        struct.province = iprot.readString();
        struct.setProvinceIsSet(true);
      }
    }
  }

}

