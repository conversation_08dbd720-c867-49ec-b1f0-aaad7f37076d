/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_price;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum Platform implements org.apache.thrift.TEnum {
  UNKNOWN(0),
  ANDROID(100),
  IOS(200),
  PC(300),
  SERVER(400);

  private final int value;

  private Platform(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static Platform findByValue(int value) { 
    switch (value) {
      case 0:
        return UNKNOWN;
      case 100:
        return ANDROID;
      case 200:
        return IOS;
      case 300:
        return PC;
      case 400:
        return SERVER;
      default:
        return null;
    }
  }
}
