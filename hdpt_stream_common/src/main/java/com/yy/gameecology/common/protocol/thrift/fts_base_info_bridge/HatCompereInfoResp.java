/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class HatCompereInfoResp implements org.apache.thrift.TBase<HatCompereInfoResp, HatCompereInfoResp._Fields>, java.io.Serializable, Cloneable, Comparable<HatCompereInfoResp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("HatCompereInfoResp");

  private static final org.apache.thrift.protocol.TField RET_FIELD_DESC = new org.apache.thrift.protocol.TField("ret", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField UID_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("uid_list", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new HatCompereInfoRespStandardSchemeFactory());
    schemes.put(TupleScheme.class, new HatCompereInfoRespTupleSchemeFactory());
  }

  public CommonRet ret; // required
  public List<Long> uid_list; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RET((short)1, "ret"),
    UID_LIST((short)2, "uid_list");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RET
          return RET;
        case 2: // UID_LIST
          return UID_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RET, new org.apache.thrift.meta_data.FieldMetaData("ret", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CommonRet.class)));
    tmpMap.put(_Fields.UID_LIST, new org.apache.thrift.meta_data.FieldMetaData("uid_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(HatCompereInfoResp.class, metaDataMap);
  }

  public HatCompereInfoResp() {
  }

  public HatCompereInfoResp(
    CommonRet ret,
    List<Long> uid_list)
  {
    this();
    this.ret = ret;
    this.uid_list = uid_list;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public HatCompereInfoResp(HatCompereInfoResp other) {
    if (other.isSetRet()) {
      this.ret = new CommonRet(other.ret);
    }
    if (other.isSetUid_list()) {
      List<Long> __this__uid_list = new ArrayList<Long>(other.uid_list);
      this.uid_list = __this__uid_list;
    }
  }

  public HatCompereInfoResp deepCopy() {
    return new HatCompereInfoResp(this);
  }

  @Override
  public void clear() {
    this.ret = null;
    this.uid_list = null;
  }

  public CommonRet getRet() {
    return this.ret;
  }

  public HatCompereInfoResp setRet(CommonRet ret) {
    this.ret = ret;
    return this;
  }

  public void unsetRet() {
    this.ret = null;
  }

  /** Returns true if field ret is set (has been assigned a value) and false otherwise */
  public boolean isSetRet() {
    return this.ret != null;
  }

  public void setRetIsSet(boolean value) {
    if (!value) {
      this.ret = null;
    }
  }

  public int getUid_listSize() {
    return (this.uid_list == null) ? 0 : this.uid_list.size();
  }

  public java.util.Iterator<Long> getUid_listIterator() {
    return (this.uid_list == null) ? null : this.uid_list.iterator();
  }

  public void addToUid_list(long elem) {
    if (this.uid_list == null) {
      this.uid_list = new ArrayList<Long>();
    }
    this.uid_list.add(elem);
  }

  public List<Long> getUid_list() {
    return this.uid_list;
  }

  public HatCompereInfoResp setUid_list(List<Long> uid_list) {
    this.uid_list = uid_list;
    return this;
  }

  public void unsetUid_list() {
    this.uid_list = null;
  }

  /** Returns true if field uid_list is set (has been assigned a value) and false otherwise */
  public boolean isSetUid_list() {
    return this.uid_list != null;
  }

  public void setUid_listIsSet(boolean value) {
    if (!value) {
      this.uid_list = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RET:
      if (value == null) {
        unsetRet();
      } else {
        setRet((CommonRet)value);
      }
      break;

    case UID_LIST:
      if (value == null) {
        unsetUid_list();
      } else {
        setUid_list((List<Long>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RET:
      return getRet();

    case UID_LIST:
      return getUid_list();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RET:
      return isSetRet();
    case UID_LIST:
      return isSetUid_list();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof HatCompereInfoResp)
      return this.equals((HatCompereInfoResp)that);
    return false;
  }

  public boolean equals(HatCompereInfoResp that) {
    if (that == null)
      return false;

    boolean this_present_ret = true && this.isSetRet();
    boolean that_present_ret = true && that.isSetRet();
    if (this_present_ret || that_present_ret) {
      if (!(this_present_ret && that_present_ret))
        return false;
      if (!this.ret.equals(that.ret))
        return false;
    }

    boolean this_present_uid_list = true && this.isSetUid_list();
    boolean that_present_uid_list = true && that.isSetUid_list();
    if (this_present_uid_list || that_present_uid_list) {
      if (!(this_present_uid_list && that_present_uid_list))
        return false;
      if (!this.uid_list.equals(that.uid_list))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_ret = true && (isSetRet());
    list.add(present_ret);
    if (present_ret)
      list.add(ret);

    boolean present_uid_list = true && (isSetUid_list());
    list.add(present_uid_list);
    if (present_uid_list)
      list.add(uid_list);

    return list.hashCode();
  }

  @Override
  public int compareTo(HatCompereInfoResp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRet()).compareTo(other.isSetRet());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRet()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ret, other.ret);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUid_list()).compareTo(other.isSetUid_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid_list, other.uid_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("HatCompereInfoResp(");
    boolean first = true;

    sb.append("ret:");
    if (this.ret == null) {
      sb.append("null");
    } else {
      sb.append(this.ret);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("uid_list:");
    if (this.uid_list == null) {
      sb.append("null");
    } else {
      sb.append(this.uid_list);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (ret != null) {
      ret.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class HatCompereInfoRespStandardSchemeFactory implements SchemeFactory {
    public HatCompereInfoRespStandardScheme getScheme() {
      return new HatCompereInfoRespStandardScheme();
    }
  }

  private static class HatCompereInfoRespStandardScheme extends StandardScheme<HatCompereInfoResp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, HatCompereInfoResp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RET
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.ret = new CommonRet();
              struct.ret.read(iprot);
              struct.setRetIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // UID_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list210 = iprot.readListBegin();
                struct.uid_list = new ArrayList<Long>(_list210.size);
                long _elem211;
                for (int _i212 = 0; _i212 < _list210.size; ++_i212)
                {
                  _elem211 = iprot.readI64();
                  struct.uid_list.add(_elem211);
                }
                iprot.readListEnd();
              }
              struct.setUid_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, HatCompereInfoResp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.ret != null) {
        oprot.writeFieldBegin(RET_FIELD_DESC);
        struct.ret.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.uid_list != null) {
        oprot.writeFieldBegin(UID_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.uid_list.size()));
          for (long _iter213 : struct.uid_list)
          {
            oprot.writeI64(_iter213);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class HatCompereInfoRespTupleSchemeFactory implements SchemeFactory {
    public HatCompereInfoRespTupleScheme getScheme() {
      return new HatCompereInfoRespTupleScheme();
    }
  }

  private static class HatCompereInfoRespTupleScheme extends TupleScheme<HatCompereInfoResp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, HatCompereInfoResp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRet()) {
        optionals.set(0);
      }
      if (struct.isSetUid_list()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRet()) {
        struct.ret.write(oprot);
      }
      if (struct.isSetUid_list()) {
        {
          oprot.writeI32(struct.uid_list.size());
          for (long _iter214 : struct.uid_list)
          {
            oprot.writeI64(_iter214);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, HatCompereInfoResp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.ret = new CommonRet();
        struct.ret.read(iprot);
        struct.setRetIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list215 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.uid_list = new ArrayList<Long>(_list215.size);
          long _elem216;
          for (int _i217 = 0; _i217 < _list215.size; ++_i217)
          {
            _elem216 = iprot.readI64();
            struct.uid_list.add(_elem216);
          }
        }
        struct.setUid_listIsSet(true);
      }
    }
  }

}

