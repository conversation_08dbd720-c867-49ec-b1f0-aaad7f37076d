/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class ChannelInfo implements org.apache.thrift.TBase<ChannelInfo, ChannelInfo._Fields>, java.io.Serializable, Cloneable, Comparable<ChannelInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ChannelInfo");

  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField SSID_FIELD_DESC = new org.apache.thrift.protocol.TField("ssid", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField COMPERE_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("compere_uid", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField PLAY_MODE_FIELD_DESC = new org.apache.thrift.protocol.TField("play_mode", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField GUEST_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("guest_list", org.apache.thrift.protocol.TType.LIST, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ChannelInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ChannelInfoTupleSchemeFactory());
  }

  public long sid; // required
  public long ssid; // required
  public long compere_uid; // required
  public long play_mode; // required
  public List<Long> guest_list; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SID((short)1, "sid"),
    SSID((short)2, "ssid"),
    COMPERE_UID((short)3, "compere_uid"),
    PLAY_MODE((short)4, "play_mode"),
    GUEST_LIST((short)5, "guest_list");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SID
          return SID;
        case 2: // SSID
          return SSID;
        case 3: // COMPERE_UID
          return COMPERE_UID;
        case 4: // PLAY_MODE
          return PLAY_MODE;
        case 5: // GUEST_LIST
          return GUEST_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SID_ISSET_ID = 0;
  private static final int __SSID_ISSET_ID = 1;
  private static final int __COMPERE_UID_ISSET_ID = 2;
  private static final int __PLAY_MODE_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SSID, new org.apache.thrift.meta_data.FieldMetaData("ssid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.COMPERE_UID, new org.apache.thrift.meta_data.FieldMetaData("compere_uid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PLAY_MODE, new org.apache.thrift.meta_data.FieldMetaData("play_mode", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.GUEST_LIST, new org.apache.thrift.meta_data.FieldMetaData("guest_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ChannelInfo.class, metaDataMap);
  }

  public ChannelInfo() {
  }

  public ChannelInfo(
    long sid,
    long ssid,
    long compere_uid,
    long play_mode,
    List<Long> guest_list)
  {
    this();
    this.sid = sid;
    setSidIsSet(true);
    this.ssid = ssid;
    setSsidIsSet(true);
    this.compere_uid = compere_uid;
    setCompere_uidIsSet(true);
    this.play_mode = play_mode;
    setPlay_modeIsSet(true);
    this.guest_list = guest_list;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ChannelInfo(ChannelInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.sid = other.sid;
    this.ssid = other.ssid;
    this.compere_uid = other.compere_uid;
    this.play_mode = other.play_mode;
    if (other.isSetGuest_list()) {
      List<Long> __this__guest_list = new ArrayList<Long>(other.guest_list);
      this.guest_list = __this__guest_list;
    }
  }

  public ChannelInfo deepCopy() {
    return new ChannelInfo(this);
  }

  @Override
  public void clear() {
    setSidIsSet(false);
    this.sid = 0;
    setSsidIsSet(false);
    this.ssid = 0;
    setCompere_uidIsSet(false);
    this.compere_uid = 0;
    setPlay_modeIsSet(false);
    this.play_mode = 0;
    this.guest_list = null;
  }

  public long getSid() {
    return this.sid;
  }

  public ChannelInfo setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getSsid() {
    return this.ssid;
  }

  public ChannelInfo setSsid(long ssid) {
    this.ssid = ssid;
    setSsidIsSet(true);
    return this;
  }

  public void unsetSsid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  /** Returns true if field ssid is set (has been assigned a value) and false otherwise */
  public boolean isSetSsid() {
    return EncodingUtils.testBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  public void setSsidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SSID_ISSET_ID, value);
  }

  public long getCompere_uid() {
    return this.compere_uid;
  }

  public ChannelInfo setCompere_uid(long compere_uid) {
    this.compere_uid = compere_uid;
    setCompere_uidIsSet(true);
    return this;
  }

  public void unsetCompere_uid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COMPERE_UID_ISSET_ID);
  }

  /** Returns true if field compere_uid is set (has been assigned a value) and false otherwise */
  public boolean isSetCompere_uid() {
    return EncodingUtils.testBit(__isset_bitfield, __COMPERE_UID_ISSET_ID);
  }

  public void setCompere_uidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COMPERE_UID_ISSET_ID, value);
  }

  public long getPlay_mode() {
    return this.play_mode;
  }

  public ChannelInfo setPlay_mode(long play_mode) {
    this.play_mode = play_mode;
    setPlay_modeIsSet(true);
    return this;
  }

  public void unsetPlay_mode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PLAY_MODE_ISSET_ID);
  }

  /** Returns true if field play_mode is set (has been assigned a value) and false otherwise */
  public boolean isSetPlay_mode() {
    return EncodingUtils.testBit(__isset_bitfield, __PLAY_MODE_ISSET_ID);
  }

  public void setPlay_modeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PLAY_MODE_ISSET_ID, value);
  }

  public int getGuest_listSize() {
    return (this.guest_list == null) ? 0 : this.guest_list.size();
  }

  public java.util.Iterator<Long> getGuest_listIterator() {
    return (this.guest_list == null) ? null : this.guest_list.iterator();
  }

  public void addToGuest_list(long elem) {
    if (this.guest_list == null) {
      this.guest_list = new ArrayList<Long>();
    }
    this.guest_list.add(elem);
  }

  public List<Long> getGuest_list() {
    return this.guest_list;
  }

  public ChannelInfo setGuest_list(List<Long> guest_list) {
    this.guest_list = guest_list;
    return this;
  }

  public void unsetGuest_list() {
    this.guest_list = null;
  }

  /** Returns true if field guest_list is set (has been assigned a value) and false otherwise */
  public boolean isSetGuest_list() {
    return this.guest_list != null;
  }

  public void setGuest_listIsSet(boolean value) {
    if (!value) {
      this.guest_list = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case SSID:
      if (value == null) {
        unsetSsid();
      } else {
        setSsid((Long)value);
      }
      break;

    case COMPERE_UID:
      if (value == null) {
        unsetCompere_uid();
      } else {
        setCompere_uid((Long)value);
      }
      break;

    case PLAY_MODE:
      if (value == null) {
        unsetPlay_mode();
      } else {
        setPlay_mode((Long)value);
      }
      break;

    case GUEST_LIST:
      if (value == null) {
        unsetGuest_list();
      } else {
        setGuest_list((List<Long>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case SID:
      return getSid();

    case SSID:
      return getSsid();

    case COMPERE_UID:
      return getCompere_uid();

    case PLAY_MODE:
      return getPlay_mode();

    case GUEST_LIST:
      return getGuest_list();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SID:
      return isSetSid();
    case SSID:
      return isSetSsid();
    case COMPERE_UID:
      return isSetCompere_uid();
    case PLAY_MODE:
      return isSetPlay_mode();
    case GUEST_LIST:
      return isSetGuest_list();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ChannelInfo)
      return this.equals((ChannelInfo)that);
    return false;
  }

  public boolean equals(ChannelInfo that) {
    if (that == null)
      return false;

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_ssid = true;
    boolean that_present_ssid = true;
    if (this_present_ssid || that_present_ssid) {
      if (!(this_present_ssid && that_present_ssid))
        return false;
      if (this.ssid != that.ssid)
        return false;
    }

    boolean this_present_compere_uid = true;
    boolean that_present_compere_uid = true;
    if (this_present_compere_uid || that_present_compere_uid) {
      if (!(this_present_compere_uid && that_present_compere_uid))
        return false;
      if (this.compere_uid != that.compere_uid)
        return false;
    }

    boolean this_present_play_mode = true;
    boolean that_present_play_mode = true;
    if (this_present_play_mode || that_present_play_mode) {
      if (!(this_present_play_mode && that_present_play_mode))
        return false;
      if (this.play_mode != that.play_mode)
        return false;
    }

    boolean this_present_guest_list = true && this.isSetGuest_list();
    boolean that_present_guest_list = true && that.isSetGuest_list();
    if (this_present_guest_list || that_present_guest_list) {
      if (!(this_present_guest_list && that_present_guest_list))
        return false;
      if (!this.guest_list.equals(that.guest_list))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_ssid = true;
    list.add(present_ssid);
    if (present_ssid)
      list.add(ssid);

    boolean present_compere_uid = true;
    list.add(present_compere_uid);
    if (present_compere_uid)
      list.add(compere_uid);

    boolean present_play_mode = true;
    list.add(present_play_mode);
    if (present_play_mode)
      list.add(play_mode);

    boolean present_guest_list = true && (isSetGuest_list());
    list.add(present_guest_list);
    if (present_guest_list)
      list.add(guest_list);

    return list.hashCode();
  }

  @Override
  public int compareTo(ChannelInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSsid()).compareTo(other.isSetSsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssid, other.ssid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCompere_uid()).compareTo(other.isSetCompere_uid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCompere_uid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.compere_uid, other.compere_uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPlay_mode()).compareTo(other.isSetPlay_mode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPlay_mode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.play_mode, other.play_mode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGuest_list()).compareTo(other.isSetGuest_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGuest_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.guest_list, other.guest_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ChannelInfo(");
    boolean first = true;

    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ssid:");
    sb.append(this.ssid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("compere_uid:");
    sb.append(this.compere_uid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("play_mode:");
    sb.append(this.play_mode);
    first = false;
    if (!first) sb.append(", ");
    sb.append("guest_list:");
    if (this.guest_list == null) {
      sb.append("null");
    } else {
      sb.append(this.guest_list);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ChannelInfoStandardSchemeFactory implements SchemeFactory {
    public ChannelInfoStandardScheme getScheme() {
      return new ChannelInfoStandardScheme();
    }
  }

  private static class ChannelInfoStandardScheme extends StandardScheme<ChannelInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ChannelInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SSID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ssid = iprot.readI64();
              struct.setSsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // COMPERE_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.compere_uid = iprot.readI64();
              struct.setCompere_uidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PLAY_MODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.play_mode = iprot.readI64();
              struct.setPlay_modeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // GUEST_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list54 = iprot.readListBegin();
                struct.guest_list = new ArrayList<Long>(_list54.size);
                long _elem55;
                for (int _i56 = 0; _i56 < _list54.size; ++_i56)
                {
                  _elem55 = iprot.readI64();
                  struct.guest_list.add(_elem55);
                }
                iprot.readListEnd();
              }
              struct.setGuest_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ChannelInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SSID_FIELD_DESC);
      oprot.writeI64(struct.ssid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(COMPERE_UID_FIELD_DESC);
      oprot.writeI64(struct.compere_uid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PLAY_MODE_FIELD_DESC);
      oprot.writeI64(struct.play_mode);
      oprot.writeFieldEnd();
      if (struct.guest_list != null) {
        oprot.writeFieldBegin(GUEST_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.guest_list.size()));
          for (long _iter57 : struct.guest_list)
          {
            oprot.writeI64(_iter57);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ChannelInfoTupleSchemeFactory implements SchemeFactory {
    public ChannelInfoTupleScheme getScheme() {
      return new ChannelInfoTupleScheme();
    }
  }

  private static class ChannelInfoTupleScheme extends TupleScheme<ChannelInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ChannelInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetSid()) {
        optionals.set(0);
      }
      if (struct.isSetSsid()) {
        optionals.set(1);
      }
      if (struct.isSetCompere_uid()) {
        optionals.set(2);
      }
      if (struct.isSetPlay_mode()) {
        optionals.set(3);
      }
      if (struct.isSetGuest_list()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetSsid()) {
        oprot.writeI64(struct.ssid);
      }
      if (struct.isSetCompere_uid()) {
        oprot.writeI64(struct.compere_uid);
      }
      if (struct.isSetPlay_mode()) {
        oprot.writeI64(struct.play_mode);
      }
      if (struct.isSetGuest_list()) {
        {
          oprot.writeI32(struct.guest_list.size());
          for (long _iter58 : struct.guest_list)
          {
            oprot.writeI64(_iter58);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ChannelInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.ssid = iprot.readI64();
        struct.setSsidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.compere_uid = iprot.readI64();
        struct.setCompere_uidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.play_mode = iprot.readI64();
        struct.setPlay_modeIsSet(true);
      }
      if (incoming.get(4)) {
        {
          org.apache.thrift.protocol.TList _list59 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.guest_list = new ArrayList<Long>(_list59.size);
          long _elem60;
          for (int _i61 = 0; _i61 < _list59.size; ++_i61)
          {
            _elem60 = iprot.readI64();
            struct.guest_list.add(_elem60);
          }
        }
        struct.setGuest_listIsSet(true);
      }
    }
  }

}

