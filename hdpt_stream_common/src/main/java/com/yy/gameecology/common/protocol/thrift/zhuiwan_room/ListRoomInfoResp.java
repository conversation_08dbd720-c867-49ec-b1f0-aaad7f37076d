/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_room;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-06-25")
public class ListRoomInfoResp implements org.apache.thrift.TBase<ListRoomInfoResp, ListRoomInfoResp._Fields>, java.io.Serializable, Cloneable, Comparable<ListRoomInfoResp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ListRoomInfoResp");

  private static final org.apache.thrift.protocol.TField ROOM_INFOS_FIELD_DESC = new org.apache.thrift.protocol.TField("roomInfos", org.apache.thrift.protocol.TType.LIST, (short)1);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ListRoomInfoRespStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ListRoomInfoRespTupleSchemeFactory());
  }

  public List<RoomInfo> roomInfos; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ROOM_INFOS((short)1, "roomInfos");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROOM_INFOS
          return ROOM_INFOS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROOM_INFOS, new org.apache.thrift.meta_data.FieldMetaData("roomInfos", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRUCT            , "RoomInfo"))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ListRoomInfoResp.class, metaDataMap);
  }

  public ListRoomInfoResp() {
  }

  public ListRoomInfoResp(
    List<RoomInfo> roomInfos)
  {
    this();
    this.roomInfos = roomInfos;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ListRoomInfoResp(ListRoomInfoResp other) {
    if (other.isSetRoomInfos()) {
      List<RoomInfo> __this__roomInfos = new ArrayList<RoomInfo>(other.roomInfos.size());
      for (RoomInfo other_element : other.roomInfos) {
        __this__roomInfos.add(other_element);
      }
      this.roomInfos = __this__roomInfos;
    }
  }

  public ListRoomInfoResp deepCopy() {
    return new ListRoomInfoResp(this);
  }

  @Override
  public void clear() {
    this.roomInfos = null;
  }

  public int getRoomInfosSize() {
    return (this.roomInfos == null) ? 0 : this.roomInfos.size();
  }

  public java.util.Iterator<RoomInfo> getRoomInfosIterator() {
    return (this.roomInfos == null) ? null : this.roomInfos.iterator();
  }

  public void addToRoomInfos(RoomInfo elem) {
    if (this.roomInfos == null) {
      this.roomInfos = new ArrayList<RoomInfo>();
    }
    this.roomInfos.add(elem);
  }

  public List<RoomInfo> getRoomInfos() {
    return this.roomInfos;
  }

  public ListRoomInfoResp setRoomInfos(List<RoomInfo> roomInfos) {
    this.roomInfos = roomInfos;
    return this;
  }

  public void unsetRoomInfos() {
    this.roomInfos = null;
  }

  /** Returns true if field roomInfos is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomInfos() {
    return this.roomInfos != null;
  }

  public void setRoomInfosIsSet(boolean value) {
    if (!value) {
      this.roomInfos = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ROOM_INFOS:
      if (value == null) {
        unsetRoomInfos();
      } else {
        setRoomInfos((List<RoomInfo>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ROOM_INFOS:
      return getRoomInfos();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ROOM_INFOS:
      return isSetRoomInfos();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ListRoomInfoResp)
      return this.equals((ListRoomInfoResp)that);
    return false;
  }

  public boolean equals(ListRoomInfoResp that) {
    if (that == null)
      return false;

    boolean this_present_roomInfos = true && this.isSetRoomInfos();
    boolean that_present_roomInfos = true && that.isSetRoomInfos();
    if (this_present_roomInfos || that_present_roomInfos) {
      if (!(this_present_roomInfos && that_present_roomInfos))
        return false;
      if (!this.roomInfos.equals(that.roomInfos))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_roomInfos = true && (isSetRoomInfos());
    list.add(present_roomInfos);
    if (present_roomInfos)
      list.add(roomInfos);

    return list.hashCode();
  }

  @Override
  public int compareTo(ListRoomInfoResp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRoomInfos()).compareTo(other.isSetRoomInfos());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomInfos()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomInfos, other.roomInfos);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ListRoomInfoResp(");
    boolean first = true;

    sb.append("roomInfos:");
    if (this.roomInfos == null) {
      sb.append("null");
    } else {
      sb.append(this.roomInfos);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ListRoomInfoRespStandardSchemeFactory implements SchemeFactory {
    public ListRoomInfoRespStandardScheme getScheme() {
      return new ListRoomInfoRespStandardScheme();
    }
  }

  private static class ListRoomInfoRespStandardScheme extends StandardScheme<ListRoomInfoResp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ListRoomInfoResp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROOM_INFOS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list16 = iprot.readListBegin();
                struct.roomInfos = new ArrayList<RoomInfo>(_list16.size);
                RoomInfo _elem17;
                for (int _i18 = 0; _i18 < _list16.size; ++_i18)
                {
                  _elem17 = new RoomInfo();
                  _elem17.read(iprot);
                  struct.roomInfos.add(_elem17);
                }
                iprot.readListEnd();
              }
              struct.setRoomInfosIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ListRoomInfoResp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.roomInfos != null) {
        oprot.writeFieldBegin(ROOM_INFOS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.roomInfos.size()));
          for (RoomInfo _iter19 : struct.roomInfos)
          {
            _iter19.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ListRoomInfoRespTupleSchemeFactory implements SchemeFactory {
    public ListRoomInfoRespTupleScheme getScheme() {
      return new ListRoomInfoRespTupleScheme();
    }
  }

  private static class ListRoomInfoRespTupleScheme extends TupleScheme<ListRoomInfoResp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ListRoomInfoResp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRoomInfos()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetRoomInfos()) {
        {
          oprot.writeI32(struct.roomInfos.size());
          for (RoomInfo _iter20 : struct.roomInfos)
          {
            _iter20.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ListRoomInfoResp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list21 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.roomInfos = new ArrayList<RoomInfo>(_list21.size);
          RoomInfo _elem22;
          for (int _i23 = 0; _i23 < _list21.size; ++_i23)
          {
            _elem22 = new RoomInfo();
            _elem22.read(iprot);
            struct.roomInfos.add(_elem22);
          }
        }
        struct.setRoomInfosIsSet(true);
      }
    }
  }

}

