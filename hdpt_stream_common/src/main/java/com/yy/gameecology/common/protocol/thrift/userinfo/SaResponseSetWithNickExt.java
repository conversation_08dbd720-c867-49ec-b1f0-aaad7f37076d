/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.userinfo;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * @param     rescode            返回码
 * @param     keyValue           保留字段
 * @param     keyIndex           columns中各字段在dataSet中对应的索引
 * @param     dataset            内层StringList表示一行查询结果,外层list表示多行查询结果
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-08-18")
public class SaResponseSetWithNickExt implements org.apache.thrift.TBase<SaResponseSetWithNickExt, SaResponseSetWithNickExt._Fields>, java.io.Serializable, Cloneable, Comparable<SaResponseSetWithNickExt> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaResponseSetWithNickExt");

  private static final org.apache.thrift.protocol.TField RESCODE_FIELD_DESC = new org.apache.thrift.protocol.TField("rescode", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField KEY_VALUE_FIELD_DESC = new org.apache.thrift.protocol.TField("keyValue", org.apache.thrift.protocol.TType.MAP, (short)2);
  private static final org.apache.thrift.protocol.TField KEY_INDEX_FIELD_DESC = new org.apache.thrift.protocol.TField("keyIndex", org.apache.thrift.protocol.TType.MAP, (short)3);
  private static final org.apache.thrift.protocol.TField DATA_SET_FIELD_DESC = new org.apache.thrift.protocol.TField("dataSet", org.apache.thrift.protocol.TType.LIST, (short)4);
  private static final org.apache.thrift.protocol.TField NICK_EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("nickExt", org.apache.thrift.protocol.TType.STRING, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaResponseSetWithNickExtStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaResponseSetWithNickExtTupleSchemeFactory());
  }

  public int rescode; // required
  public Map<String,String> keyValue; // required
  public Map<String,Integer> keyIndex; // required
  public List<StringList> dataSet; // required
  public String nickExt; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RESCODE((short)1, "rescode"),
    KEY_VALUE((short)2, "keyValue"),
    KEY_INDEX((short)3, "keyIndex"),
    DATA_SET((short)4, "dataSet"),
    NICK_EXT((short)5, "nickExt");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RESCODE
          return RESCODE;
        case 2: // KEY_VALUE
          return KEY_VALUE;
        case 3: // KEY_INDEX
          return KEY_INDEX;
        case 4: // DATA_SET
          return DATA_SET;
        case 5: // NICK_EXT
          return NICK_EXT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RESCODE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RESCODE, new org.apache.thrift.meta_data.FieldMetaData("rescode", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.KEY_VALUE, new org.apache.thrift.meta_data.FieldMetaData("keyValue", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.KEY_INDEX, new org.apache.thrift.meta_data.FieldMetaData("keyIndex", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32))));
    tmpMap.put(_Fields.DATA_SET, new org.apache.thrift.meta_data.FieldMetaData("dataSet", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, StringList.class))));
    tmpMap.put(_Fields.NICK_EXT, new org.apache.thrift.meta_data.FieldMetaData("nickExt", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaResponseSetWithNickExt.class, metaDataMap);
  }

  public SaResponseSetWithNickExt() {
  }

  public SaResponseSetWithNickExt(
    int rescode,
    Map<String,String> keyValue,
    Map<String,Integer> keyIndex,
    List<StringList> dataSet,
    String nickExt)
  {
    this();
    this.rescode = rescode;
    setRescodeIsSet(true);
    this.keyValue = keyValue;
    this.keyIndex = keyIndex;
    this.dataSet = dataSet;
    this.nickExt = nickExt;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaResponseSetWithNickExt(SaResponseSetWithNickExt other) {
    __isset_bitfield = other.__isset_bitfield;
    this.rescode = other.rescode;
    if (other.isSetKeyValue()) {
      Map<String,String> __this__keyValue = new HashMap<String,String>(other.keyValue);
      this.keyValue = __this__keyValue;
    }
    if (other.isSetKeyIndex()) {
      Map<String,Integer> __this__keyIndex = new HashMap<String,Integer>(other.keyIndex);
      this.keyIndex = __this__keyIndex;
    }
    if (other.isSetDataSet()) {
      List<StringList> __this__dataSet = new ArrayList<StringList>(other.dataSet.size());
      for (StringList other_element : other.dataSet) {
        __this__dataSet.add(new StringList(other_element));
      }
      this.dataSet = __this__dataSet;
    }
    if (other.isSetNickExt()) {
      this.nickExt = other.nickExt;
    }
  }

  public SaResponseSetWithNickExt deepCopy() {
    return new SaResponseSetWithNickExt(this);
  }

  @Override
  public void clear() {
    setRescodeIsSet(false);
    this.rescode = 0;
    this.keyValue = null;
    this.keyIndex = null;
    this.dataSet = null;
    this.nickExt = null;
  }

  public int getRescode() {
    return this.rescode;
  }

  public SaResponseSetWithNickExt setRescode(int rescode) {
    this.rescode = rescode;
    setRescodeIsSet(true);
    return this;
  }

  public void unsetRescode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RESCODE_ISSET_ID);
  }

  /** Returns true if field rescode is set (has been assigned a value) and false otherwise */
  public boolean isSetRescode() {
    return EncodingUtils.testBit(__isset_bitfield, __RESCODE_ISSET_ID);
  }

  public void setRescodeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RESCODE_ISSET_ID, value);
  }

  public int getKeyValueSize() {
    return (this.keyValue == null) ? 0 : this.keyValue.size();
  }

  public void putToKeyValue(String key, String val) {
    if (this.keyValue == null) {
      this.keyValue = new HashMap<String,String>();
    }
    this.keyValue.put(key, val);
  }

  public Map<String,String> getKeyValue() {
    return this.keyValue;
  }

  public SaResponseSetWithNickExt setKeyValue(Map<String,String> keyValue) {
    this.keyValue = keyValue;
    return this;
  }

  public void unsetKeyValue() {
    this.keyValue = null;
  }

  /** Returns true if field keyValue is set (has been assigned a value) and false otherwise */
  public boolean isSetKeyValue() {
    return this.keyValue != null;
  }

  public void setKeyValueIsSet(boolean value) {
    if (!value) {
      this.keyValue = null;
    }
  }

  public int getKeyIndexSize() {
    return (this.keyIndex == null) ? 0 : this.keyIndex.size();
  }

  public void putToKeyIndex(String key, int val) {
    if (this.keyIndex == null) {
      this.keyIndex = new HashMap<String,Integer>();
    }
    this.keyIndex.put(key, val);
  }

  public Map<String,Integer> getKeyIndex() {
    return this.keyIndex;
  }

  public SaResponseSetWithNickExt setKeyIndex(Map<String,Integer> keyIndex) {
    this.keyIndex = keyIndex;
    return this;
  }

  public void unsetKeyIndex() {
    this.keyIndex = null;
  }

  /** Returns true if field keyIndex is set (has been assigned a value) and false otherwise */
  public boolean isSetKeyIndex() {
    return this.keyIndex != null;
  }

  public void setKeyIndexIsSet(boolean value) {
    if (!value) {
      this.keyIndex = null;
    }
  }

  public int getDataSetSize() {
    return (this.dataSet == null) ? 0 : this.dataSet.size();
  }

  public java.util.Iterator<StringList> getDataSetIterator() {
    return (this.dataSet == null) ? null : this.dataSet.iterator();
  }

  public void addToDataSet(StringList elem) {
    if (this.dataSet == null) {
      this.dataSet = new ArrayList<StringList>();
    }
    this.dataSet.add(elem);
  }

  public List<StringList> getDataSet() {
    return this.dataSet;
  }

  public SaResponseSetWithNickExt setDataSet(List<StringList> dataSet) {
    this.dataSet = dataSet;
    return this;
  }

  public void unsetDataSet() {
    this.dataSet = null;
  }

  /** Returns true if field dataSet is set (has been assigned a value) and false otherwise */
  public boolean isSetDataSet() {
    return this.dataSet != null;
  }

  public void setDataSetIsSet(boolean value) {
    if (!value) {
      this.dataSet = null;
    }
  }

  public String getNickExt() {
    return this.nickExt;
  }

  public SaResponseSetWithNickExt setNickExt(String nickExt) {
    this.nickExt = nickExt;
    return this;
  }

  public void unsetNickExt() {
    this.nickExt = null;
  }

  /** Returns true if field nickExt is set (has been assigned a value) and false otherwise */
  public boolean isSetNickExt() {
    return this.nickExt != null;
  }

  public void setNickExtIsSet(boolean value) {
    if (!value) {
      this.nickExt = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RESCODE:
      if (value == null) {
        unsetRescode();
      } else {
        setRescode((Integer)value);
      }
      break;

    case KEY_VALUE:
      if (value == null) {
        unsetKeyValue();
      } else {
        setKeyValue((Map<String,String>)value);
      }
      break;

    case KEY_INDEX:
      if (value == null) {
        unsetKeyIndex();
      } else {
        setKeyIndex((Map<String,Integer>)value);
      }
      break;

    case DATA_SET:
      if (value == null) {
        unsetDataSet();
      } else {
        setDataSet((List<StringList>)value);
      }
      break;

    case NICK_EXT:
      if (value == null) {
        unsetNickExt();
      } else {
        setNickExt((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RESCODE:
      return getRescode();

    case KEY_VALUE:
      return getKeyValue();

    case KEY_INDEX:
      return getKeyIndex();

    case DATA_SET:
      return getDataSet();

    case NICK_EXT:
      return getNickExt();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RESCODE:
      return isSetRescode();
    case KEY_VALUE:
      return isSetKeyValue();
    case KEY_INDEX:
      return isSetKeyIndex();
    case DATA_SET:
      return isSetDataSet();
    case NICK_EXT:
      return isSetNickExt();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaResponseSetWithNickExt)
      return this.equals((SaResponseSetWithNickExt)that);
    return false;
  }

  public boolean equals(SaResponseSetWithNickExt that) {
    if (that == null)
      return false;

    boolean this_present_rescode = true;
    boolean that_present_rescode = true;
    if (this_present_rescode || that_present_rescode) {
      if (!(this_present_rescode && that_present_rescode))
        return false;
      if (this.rescode != that.rescode)
        return false;
    }

    boolean this_present_keyValue = true && this.isSetKeyValue();
    boolean that_present_keyValue = true && that.isSetKeyValue();
    if (this_present_keyValue || that_present_keyValue) {
      if (!(this_present_keyValue && that_present_keyValue))
        return false;
      if (!this.keyValue.equals(that.keyValue))
        return false;
    }

    boolean this_present_keyIndex = true && this.isSetKeyIndex();
    boolean that_present_keyIndex = true && that.isSetKeyIndex();
    if (this_present_keyIndex || that_present_keyIndex) {
      if (!(this_present_keyIndex && that_present_keyIndex))
        return false;
      if (!this.keyIndex.equals(that.keyIndex))
        return false;
    }

    boolean this_present_dataSet = true && this.isSetDataSet();
    boolean that_present_dataSet = true && that.isSetDataSet();
    if (this_present_dataSet || that_present_dataSet) {
      if (!(this_present_dataSet && that_present_dataSet))
        return false;
      if (!this.dataSet.equals(that.dataSet))
        return false;
    }

    boolean this_present_nickExt = true && this.isSetNickExt();
    boolean that_present_nickExt = true && that.isSetNickExt();
    if (this_present_nickExt || that_present_nickExt) {
      if (!(this_present_nickExt && that_present_nickExt))
        return false;
      if (!this.nickExt.equals(that.nickExt))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_rescode = true;
    list.add(present_rescode);
    if (present_rescode)
      list.add(rescode);

    boolean present_keyValue = true && (isSetKeyValue());
    list.add(present_keyValue);
    if (present_keyValue)
      list.add(keyValue);

    boolean present_keyIndex = true && (isSetKeyIndex());
    list.add(present_keyIndex);
    if (present_keyIndex)
      list.add(keyIndex);

    boolean present_dataSet = true && (isSetDataSet());
    list.add(present_dataSet);
    if (present_dataSet)
      list.add(dataSet);

    boolean present_nickExt = true && (isSetNickExt());
    list.add(present_nickExt);
    if (present_nickExt)
      list.add(nickExt);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaResponseSetWithNickExt other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRescode()).compareTo(other.isSetRescode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRescode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rescode, other.rescode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKeyValue()).compareTo(other.isSetKeyValue());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKeyValue()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.keyValue, other.keyValue);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKeyIndex()).compareTo(other.isSetKeyIndex());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKeyIndex()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.keyIndex, other.keyIndex);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDataSet()).compareTo(other.isSetDataSet());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDataSet()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dataSet, other.dataSet);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetNickExt()).compareTo(other.isSetNickExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNickExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nickExt, other.nickExt);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaResponseSetWithNickExt(");
    boolean first = true;

    sb.append("rescode:");
    sb.append(this.rescode);
    first = false;
    if (!first) sb.append(", ");
    sb.append("keyValue:");
    if (this.keyValue == null) {
      sb.append("null");
    } else {
      sb.append(this.keyValue);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("keyIndex:");
    if (this.keyIndex == null) {
      sb.append("null");
    } else {
      sb.append(this.keyIndex);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("dataSet:");
    if (this.dataSet == null) {
      sb.append("null");
    } else {
      sb.append(this.dataSet);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("nickExt:");
    if (this.nickExt == null) {
      sb.append("null");
    } else {
      sb.append(this.nickExt);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaResponseSetWithNickExtStandardSchemeFactory implements SchemeFactory {
    public SaResponseSetWithNickExtStandardScheme getScheme() {
      return new SaResponseSetWithNickExtStandardScheme();
    }
  }

  private static class SaResponseSetWithNickExtStandardScheme extends StandardScheme<SaResponseSetWithNickExt> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaResponseSetWithNickExt struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RESCODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rescode = iprot.readI32();
              struct.setRescodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // KEY_VALUE
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map420 = iprot.readMapBegin();
                struct.keyValue = new HashMap<String,String>(2*_map420.size);
                String _key421;
                String _val422;
                for (int _i423 = 0; _i423 < _map420.size; ++_i423)
                {
                  _key421 = iprot.readString();
                  _val422 = iprot.readString();
                  struct.keyValue.put(_key421, _val422);
                }
                iprot.readMapEnd();
              }
              struct.setKeyValueIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // KEY_INDEX
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map424 = iprot.readMapBegin();
                struct.keyIndex = new HashMap<String,Integer>(2*_map424.size);
                String _key425;
                int _val426;
                for (int _i427 = 0; _i427 < _map424.size; ++_i427)
                {
                  _key425 = iprot.readString();
                  _val426 = iprot.readI32();
                  struct.keyIndex.put(_key425, _val426);
                }
                iprot.readMapEnd();
              }
              struct.setKeyIndexIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // DATA_SET
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list428 = iprot.readListBegin();
                struct.dataSet = new ArrayList<StringList>(_list428.size);
                StringList _elem429;
                for (int _i430 = 0; _i430 < _list428.size; ++_i430)
                {
                  _elem429 = new StringList();
                  _elem429.read(iprot);
                  struct.dataSet.add(_elem429);
                }
                iprot.readListEnd();
              }
              struct.setDataSetIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // NICK_EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.nickExt = iprot.readString();
              struct.setNickExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaResponseSetWithNickExt struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RESCODE_FIELD_DESC);
      oprot.writeI32(struct.rescode);
      oprot.writeFieldEnd();
      if (struct.keyValue != null) {
        oprot.writeFieldBegin(KEY_VALUE_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.keyValue.size()));
          for (Map.Entry<String, String> _iter431 : struct.keyValue.entrySet())
          {
            oprot.writeString(_iter431.getKey());
            oprot.writeString(_iter431.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.keyIndex != null) {
        oprot.writeFieldBegin(KEY_INDEX_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32, struct.keyIndex.size()));
          for (Map.Entry<String, Integer> _iter432 : struct.keyIndex.entrySet())
          {
            oprot.writeString(_iter432.getKey());
            oprot.writeI32(_iter432.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.dataSet != null) {
        oprot.writeFieldBegin(DATA_SET_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.dataSet.size()));
          for (StringList _iter433 : struct.dataSet)
          {
            _iter433.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.nickExt != null) {
        oprot.writeFieldBegin(NICK_EXT_FIELD_DESC);
        oprot.writeString(struct.nickExt);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaResponseSetWithNickExtTupleSchemeFactory implements SchemeFactory {
    public SaResponseSetWithNickExtTupleScheme getScheme() {
      return new SaResponseSetWithNickExtTupleScheme();
    }
  }

  private static class SaResponseSetWithNickExtTupleScheme extends TupleScheme<SaResponseSetWithNickExt> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaResponseSetWithNickExt struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRescode()) {
        optionals.set(0);
      }
      if (struct.isSetKeyValue()) {
        optionals.set(1);
      }
      if (struct.isSetKeyIndex()) {
        optionals.set(2);
      }
      if (struct.isSetDataSet()) {
        optionals.set(3);
      }
      if (struct.isSetNickExt()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetRescode()) {
        oprot.writeI32(struct.rescode);
      }
      if (struct.isSetKeyValue()) {
        {
          oprot.writeI32(struct.keyValue.size());
          for (Map.Entry<String, String> _iter434 : struct.keyValue.entrySet())
          {
            oprot.writeString(_iter434.getKey());
            oprot.writeString(_iter434.getValue());
          }
        }
      }
      if (struct.isSetKeyIndex()) {
        {
          oprot.writeI32(struct.keyIndex.size());
          for (Map.Entry<String, Integer> _iter435 : struct.keyIndex.entrySet())
          {
            oprot.writeString(_iter435.getKey());
            oprot.writeI32(_iter435.getValue());
          }
        }
      }
      if (struct.isSetDataSet()) {
        {
          oprot.writeI32(struct.dataSet.size());
          for (StringList _iter436 : struct.dataSet)
          {
            _iter436.write(oprot);
          }
        }
      }
      if (struct.isSetNickExt()) {
        oprot.writeString(struct.nickExt);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaResponseSetWithNickExt struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.rescode = iprot.readI32();
        struct.setRescodeIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map437 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.keyValue = new HashMap<String,String>(2*_map437.size);
          String _key438;
          String _val439;
          for (int _i440 = 0; _i440 < _map437.size; ++_i440)
          {
            _key438 = iprot.readString();
            _val439 = iprot.readString();
            struct.keyValue.put(_key438, _val439);
          }
        }
        struct.setKeyValueIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map441 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I32, iprot.readI32());
          struct.keyIndex = new HashMap<String,Integer>(2*_map441.size);
          String _key442;
          int _val443;
          for (int _i444 = 0; _i444 < _map441.size; ++_i444)
          {
            _key442 = iprot.readString();
            _val443 = iprot.readI32();
            struct.keyIndex.put(_key442, _val443);
          }
        }
        struct.setKeyIndexIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TList _list445 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.dataSet = new ArrayList<StringList>(_list445.size);
          StringList _elem446;
          for (int _i447 = 0; _i447 < _list445.size; ++_i447)
          {
            _elem446 = new StringList();
            _elem446.read(iprot);
            struct.dataSet.add(_elem446);
          }
        }
        struct.setDataSetIsSet(true);
      }
      if (incoming.get(4)) {
        struct.nickExt = iprot.readString();
        struct.setNickExtIsSet(true);
      }
    }
  }

}

