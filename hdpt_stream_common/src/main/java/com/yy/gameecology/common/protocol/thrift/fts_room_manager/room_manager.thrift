namespace java com.yy.gameecology.common.protocol.thrift.fts_room_manager

// 测试环境s2s: fts_room_manager_test
// 正式环境s2s: fts_room_manager

struct GetAntiPoachingListReq
{

}

struct AntiPoachingInfo
{
    1:i64            sid;
    2:i64            ssid;
    3:bool           isEffects;    // 是否选中玩法特效  true-勾选 false-未勾选
    4:bool           isBroadcast;  // 是否选中玩法广播  true-勾选 false-未勾选
    5:bool           isBillboard;  // 是否选中频道榜单  true-勾选 false-未勾选
}

struct GetAntiPoachingListResp
{
    1:i32                      ret,     // 0--成功  1或其他--错误
    2:string                   msg,     // 出错时的信息
    3:list<AntiPoachingInfo>   retList; // 厅防挖列表
}

service FtsRoomManagerService
{
    void ping();

    // 获取防挖列表(全量) 建议本地缓存+定时更新
    GetAntiPoachingListResp GetAntiPoachingList(1:GetAntiPoachingListReq req);
}
