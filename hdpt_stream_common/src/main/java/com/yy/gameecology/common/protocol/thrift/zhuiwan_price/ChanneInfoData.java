/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_price;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-02-22")
public class ChanneInfoData implements org.apache.thrift.TBase<ChanneInfoData, ChanneInfoData._Fields>, java.io.Serializable, Cloneable, Comparable<ChanneInfoData> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ChanneInfoData");

  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField ASID_FIELD_DESC = new org.apache.thrift.protocol.TField("asid", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField PROMOTE_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("promoteCount", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField DOWNLOAD_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("downloadUrl", org.apache.thrift.protocol.TType.STRING, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ChanneInfoDataStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ChanneInfoDataTupleSchemeFactory());
  }

  public long sid; // required
  public long asid; // required
  public long promoteCount; // required
  public String downloadUrl; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SID((short)1, "sid"),
    ASID((short)2, "asid"),
    PROMOTE_COUNT((short)3, "promoteCount"),
    DOWNLOAD_URL((short)4, "downloadUrl");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SID
          return SID;
        case 2: // ASID
          return ASID;
        case 3: // PROMOTE_COUNT
          return PROMOTE_COUNT;
        case 4: // DOWNLOAD_URL
          return DOWNLOAD_URL;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SID_ISSET_ID = 0;
  private static final int __ASID_ISSET_ID = 1;
  private static final int __PROMOTECOUNT_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ASID, new org.apache.thrift.meta_data.FieldMetaData("asid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PROMOTE_COUNT, new org.apache.thrift.meta_data.FieldMetaData("promoteCount", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DOWNLOAD_URL, new org.apache.thrift.meta_data.FieldMetaData("downloadUrl", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ChanneInfoData.class, metaDataMap);
  }

  public ChanneInfoData() {
  }

  public ChanneInfoData(
    long sid,
    long asid,
    long promoteCount,
    String downloadUrl)
  {
    this();
    this.sid = sid;
    setSidIsSet(true);
    this.asid = asid;
    setAsidIsSet(true);
    this.promoteCount = promoteCount;
    setPromoteCountIsSet(true);
    this.downloadUrl = downloadUrl;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ChanneInfoData(ChanneInfoData other) {
    __isset_bitfield = other.__isset_bitfield;
    this.sid = other.sid;
    this.asid = other.asid;
    this.promoteCount = other.promoteCount;
    if (other.isSetDownloadUrl()) {
      this.downloadUrl = other.downloadUrl;
    }
  }

  public ChanneInfoData deepCopy() {
    return new ChanneInfoData(this);
  }

  @Override
  public void clear() {
    setSidIsSet(false);
    this.sid = 0;
    setAsidIsSet(false);
    this.asid = 0;
    setPromoteCountIsSet(false);
    this.promoteCount = 0;
    this.downloadUrl = null;
  }

  public long getSid() {
    return this.sid;
  }

  public ChanneInfoData setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getAsid() {
    return this.asid;
  }

  public ChanneInfoData setAsid(long asid) {
    this.asid = asid;
    setAsidIsSet(true);
    return this;
  }

  public void unsetAsid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ASID_ISSET_ID);
  }

  /** Returns true if field asid is set (has been assigned a value) and false otherwise */
  public boolean isSetAsid() {
    return EncodingUtils.testBit(__isset_bitfield, __ASID_ISSET_ID);
  }

  public void setAsidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ASID_ISSET_ID, value);
  }

  public long getPromoteCount() {
    return this.promoteCount;
  }

  public ChanneInfoData setPromoteCount(long promoteCount) {
    this.promoteCount = promoteCount;
    setPromoteCountIsSet(true);
    return this;
  }

  public void unsetPromoteCount() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PROMOTECOUNT_ISSET_ID);
  }

  /** Returns true if field promoteCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPromoteCount() {
    return EncodingUtils.testBit(__isset_bitfield, __PROMOTECOUNT_ISSET_ID);
  }

  public void setPromoteCountIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PROMOTECOUNT_ISSET_ID, value);
  }

  public String getDownloadUrl() {
    return this.downloadUrl;
  }

  public ChanneInfoData setDownloadUrl(String downloadUrl) {
    this.downloadUrl = downloadUrl;
    return this;
  }

  public void unsetDownloadUrl() {
    this.downloadUrl = null;
  }

  /** Returns true if field downloadUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetDownloadUrl() {
    return this.downloadUrl != null;
  }

  public void setDownloadUrlIsSet(boolean value) {
    if (!value) {
      this.downloadUrl = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case ASID:
      if (value == null) {
        unsetAsid();
      } else {
        setAsid((Long)value);
      }
      break;

    case PROMOTE_COUNT:
      if (value == null) {
        unsetPromoteCount();
      } else {
        setPromoteCount((Long)value);
      }
      break;

    case DOWNLOAD_URL:
      if (value == null) {
        unsetDownloadUrl();
      } else {
        setDownloadUrl((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case SID:
      return getSid();

    case ASID:
      return getAsid();

    case PROMOTE_COUNT:
      return getPromoteCount();

    case DOWNLOAD_URL:
      return getDownloadUrl();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SID:
      return isSetSid();
    case ASID:
      return isSetAsid();
    case PROMOTE_COUNT:
      return isSetPromoteCount();
    case DOWNLOAD_URL:
      return isSetDownloadUrl();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ChanneInfoData)
      return this.equals((ChanneInfoData)that);
    return false;
  }

  public boolean equals(ChanneInfoData that) {
    if (that == null)
      return false;

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_asid = true;
    boolean that_present_asid = true;
    if (this_present_asid || that_present_asid) {
      if (!(this_present_asid && that_present_asid))
        return false;
      if (this.asid != that.asid)
        return false;
    }

    boolean this_present_promoteCount = true;
    boolean that_present_promoteCount = true;
    if (this_present_promoteCount || that_present_promoteCount) {
      if (!(this_present_promoteCount && that_present_promoteCount))
        return false;
      if (this.promoteCount != that.promoteCount)
        return false;
    }

    boolean this_present_downloadUrl = true && this.isSetDownloadUrl();
    boolean that_present_downloadUrl = true && that.isSetDownloadUrl();
    if (this_present_downloadUrl || that_present_downloadUrl) {
      if (!(this_present_downloadUrl && that_present_downloadUrl))
        return false;
      if (!this.downloadUrl.equals(that.downloadUrl))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_asid = true;
    list.add(present_asid);
    if (present_asid)
      list.add(asid);

    boolean present_promoteCount = true;
    list.add(present_promoteCount);
    if (present_promoteCount)
      list.add(promoteCount);

    boolean present_downloadUrl = true && (isSetDownloadUrl());
    list.add(present_downloadUrl);
    if (present_downloadUrl)
      list.add(downloadUrl);

    return list.hashCode();
  }

  @Override
  public int compareTo(ChanneInfoData other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAsid()).compareTo(other.isSetAsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.asid, other.asid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPromoteCount()).compareTo(other.isSetPromoteCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPromoteCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.promoteCount, other.promoteCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDownloadUrl()).compareTo(other.isSetDownloadUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDownloadUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.downloadUrl, other.downloadUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ChanneInfoData(");
    boolean first = true;

    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("asid:");
    sb.append(this.asid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("promoteCount:");
    sb.append(this.promoteCount);
    first = false;
    if (!first) sb.append(", ");
    sb.append("downloadUrl:");
    if (this.downloadUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.downloadUrl);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ChanneInfoDataStandardSchemeFactory implements SchemeFactory {
    public ChanneInfoDataStandardScheme getScheme() {
      return new ChanneInfoDataStandardScheme();
    }
  }

  private static class ChanneInfoDataStandardScheme extends StandardScheme<ChanneInfoData> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ChanneInfoData struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ASID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.asid = iprot.readI64();
              struct.setAsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PROMOTE_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.promoteCount = iprot.readI64();
              struct.setPromoteCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // DOWNLOAD_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.downloadUrl = iprot.readString();
              struct.setDownloadUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ChanneInfoData struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ASID_FIELD_DESC);
      oprot.writeI64(struct.asid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PROMOTE_COUNT_FIELD_DESC);
      oprot.writeI64(struct.promoteCount);
      oprot.writeFieldEnd();
      if (struct.downloadUrl != null) {
        oprot.writeFieldBegin(DOWNLOAD_URL_FIELD_DESC);
        oprot.writeString(struct.downloadUrl);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ChanneInfoDataTupleSchemeFactory implements SchemeFactory {
    public ChanneInfoDataTupleScheme getScheme() {
      return new ChanneInfoDataTupleScheme();
    }
  }

  private static class ChanneInfoDataTupleScheme extends TupleScheme<ChanneInfoData> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ChanneInfoData struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetSid()) {
        optionals.set(0);
      }
      if (struct.isSetAsid()) {
        optionals.set(1);
      }
      if (struct.isSetPromoteCount()) {
        optionals.set(2);
      }
      if (struct.isSetDownloadUrl()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetAsid()) {
        oprot.writeI64(struct.asid);
      }
      if (struct.isSetPromoteCount()) {
        oprot.writeI64(struct.promoteCount);
      }
      if (struct.isSetDownloadUrl()) {
        oprot.writeString(struct.downloadUrl);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ChanneInfoData struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.asid = iprot.readI64();
        struct.setAsidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.promoteCount = iprot.readI64();
        struct.setPromoteCountIsSet(true);
      }
      if (incoming.get(3)) {
        struct.downloadUrl = iprot.readString();
        struct.setDownloadUrlIsSet(true);
      }
    }
  }

}

