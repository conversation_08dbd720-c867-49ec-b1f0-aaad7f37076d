namespace java com.yy.gameecology.common.protocol.thrift.zhuiwan_room

struct ListRoomInfoReq {
    1: list<i64> ssids;     // 根据ssid批量查询
    2: list<i32> roomIds;   // 根据roomId批量查询
}
struct ListRoomInfoResp {
    1: list<RoomInfo> roomInfos;
}
struct RoomInfo {
    1: i32 roomId;      // 房间号
    2: string title;    // 标题
    3: string cover;    // 封面
    4: i64 ow;          // 房主
    5: i64 sid;
    6: i64 ssid;
	7: i64 familyId;    // 家族id
}

service RoomNoService {

    void ping();

    // 批量查询
    ListRoomInfoResp listRoomInfo(1: ListRoomInfoReq req);

}