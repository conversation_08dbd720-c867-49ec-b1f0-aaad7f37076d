/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class SubscribeResp implements org.apache.thrift.TBase<SubscribeResp, SubscribeResp._Fields>, java.io.Serializable, Cloneable, Comparable<SubscribeResp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SubscribeResp");

  private static final org.apache.thrift.protocol.TField RET_FIELD_DESC = new org.apache.thrift.protocol.TField("ret", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField RET_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("retMap", org.apache.thrift.protocol.TType.MAP, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SubscribeRespStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SubscribeRespTupleSchemeFactory());
  }

  public CommonRet ret; // required
  public Map<Long,Boolean> retMap; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RET((short)1, "ret"),
    RET_MAP((short)2, "retMap");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RET
          return RET;
        case 2: // RET_MAP
          return RET_MAP;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RET, new org.apache.thrift.meta_data.FieldMetaData("ret", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, CommonRet.class)));
    tmpMap.put(_Fields.RET_MAP, new org.apache.thrift.meta_data.FieldMetaData("retMap", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SubscribeResp.class, metaDataMap);
  }

  public SubscribeResp() {
  }

  public SubscribeResp(
    CommonRet ret,
    Map<Long,Boolean> retMap)
  {
    this();
    this.ret = ret;
    this.retMap = retMap;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SubscribeResp(SubscribeResp other) {
    if (other.isSetRet()) {
      this.ret = new CommonRet(other.ret);
    }
    if (other.isSetRetMap()) {
      Map<Long,Boolean> __this__retMap = new HashMap<Long,Boolean>(other.retMap);
      this.retMap = __this__retMap;
    }
  }

  public SubscribeResp deepCopy() {
    return new SubscribeResp(this);
  }

  @Override
  public void clear() {
    this.ret = null;
    this.retMap = null;
  }

  public CommonRet getRet() {
    return this.ret;
  }

  public SubscribeResp setRet(CommonRet ret) {
    this.ret = ret;
    return this;
  }

  public void unsetRet() {
    this.ret = null;
  }

  /** Returns true if field ret is set (has been assigned a value) and false otherwise */
  public boolean isSetRet() {
    return this.ret != null;
  }

  public void setRetIsSet(boolean value) {
    if (!value) {
      this.ret = null;
    }
  }

  public int getRetMapSize() {
    return (this.retMap == null) ? 0 : this.retMap.size();
  }

  public void putToRetMap(long key, boolean val) {
    if (this.retMap == null) {
      this.retMap = new HashMap<Long,Boolean>();
    }
    this.retMap.put(key, val);
  }

  public Map<Long,Boolean> getRetMap() {
    return this.retMap;
  }

  public SubscribeResp setRetMap(Map<Long,Boolean> retMap) {
    this.retMap = retMap;
    return this;
  }

  public void unsetRetMap() {
    this.retMap = null;
  }

  /** Returns true if field retMap is set (has been assigned a value) and false otherwise */
  public boolean isSetRetMap() {
    return this.retMap != null;
  }

  public void setRetMapIsSet(boolean value) {
    if (!value) {
      this.retMap = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RET:
      if (value == null) {
        unsetRet();
      } else {
        setRet((CommonRet)value);
      }
      break;

    case RET_MAP:
      if (value == null) {
        unsetRetMap();
      } else {
        setRetMap((Map<Long,Boolean>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RET:
      return getRet();

    case RET_MAP:
      return getRetMap();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RET:
      return isSetRet();
    case RET_MAP:
      return isSetRetMap();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SubscribeResp)
      return this.equals((SubscribeResp)that);
    return false;
  }

  public boolean equals(SubscribeResp that) {
    if (that == null)
      return false;

    boolean this_present_ret = true && this.isSetRet();
    boolean that_present_ret = true && that.isSetRet();
    if (this_present_ret || that_present_ret) {
      if (!(this_present_ret && that_present_ret))
        return false;
      if (!this.ret.equals(that.ret))
        return false;
    }

    boolean this_present_retMap = true && this.isSetRetMap();
    boolean that_present_retMap = true && that.isSetRetMap();
    if (this_present_retMap || that_present_retMap) {
      if (!(this_present_retMap && that_present_retMap))
        return false;
      if (!this.retMap.equals(that.retMap))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_ret = true && (isSetRet());
    list.add(present_ret);
    if (present_ret)
      list.add(ret);

    boolean present_retMap = true && (isSetRetMap());
    list.add(present_retMap);
    if (present_retMap)
      list.add(retMap);

    return list.hashCode();
  }

  @Override
  public int compareTo(SubscribeResp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRet()).compareTo(other.isSetRet());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRet()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ret, other.ret);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRetMap()).compareTo(other.isSetRetMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRetMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.retMap, other.retMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SubscribeResp(");
    boolean first = true;

    sb.append("ret:");
    if (this.ret == null) {
      sb.append("null");
    } else {
      sb.append(this.ret);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("retMap:");
    if (this.retMap == null) {
      sb.append("null");
    } else {
      sb.append(this.retMap);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (ret != null) {
      ret.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SubscribeRespStandardSchemeFactory implements SchemeFactory {
    public SubscribeRespStandardScheme getScheme() {
      return new SubscribeRespStandardScheme();
    }
  }

  private static class SubscribeRespStandardScheme extends StandardScheme<SubscribeResp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SubscribeResp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RET
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.ret = new CommonRet();
              struct.ret.read(iprot);
              struct.setRetIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RET_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map104 = iprot.readMapBegin();
                struct.retMap = new HashMap<Long,Boolean>(2*_map104.size);
                long _key105;
                boolean _val106;
                for (int _i107 = 0; _i107 < _map104.size; ++_i107)
                {
                  _key105 = iprot.readI64();
                  _val106 = iprot.readBool();
                  struct.retMap.put(_key105, _val106);
                }
                iprot.readMapEnd();
              }
              struct.setRetMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SubscribeResp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.ret != null) {
        oprot.writeFieldBegin(RET_FIELD_DESC);
        struct.ret.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.retMap != null) {
        oprot.writeFieldBegin(RET_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.BOOL, struct.retMap.size()));
          for (Map.Entry<Long, Boolean> _iter108 : struct.retMap.entrySet())
          {
            oprot.writeI64(_iter108.getKey());
            oprot.writeBool(_iter108.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SubscribeRespTupleSchemeFactory implements SchemeFactory {
    public SubscribeRespTupleScheme getScheme() {
      return new SubscribeRespTupleScheme();
    }
  }

  private static class SubscribeRespTupleScheme extends TupleScheme<SubscribeResp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SubscribeResp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRet()) {
        optionals.set(0);
      }
      if (struct.isSetRetMap()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRet()) {
        struct.ret.write(oprot);
      }
      if (struct.isSetRetMap()) {
        {
          oprot.writeI32(struct.retMap.size());
          for (Map.Entry<Long, Boolean> _iter109 : struct.retMap.entrySet())
          {
            oprot.writeI64(_iter109.getKey());
            oprot.writeBool(_iter109.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SubscribeResp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.ret = new CommonRet();
        struct.ret.read(iprot);
        struct.setRetIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map110 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.BOOL, iprot.readI32());
          struct.retMap = new HashMap<Long,Boolean>(2*_map110.size);
          long _key111;
          boolean _val112;
          for (int _i113 = 0; _i113 < _map110.size; ++_i113)
          {
            _key111 = iprot.readI64();
            _val112 = iprot.readBool();
            struct.retMap.put(_key111, _val112);
          }
        }
        struct.setRetMapIsSet(true);
      }
    }
  }

}

