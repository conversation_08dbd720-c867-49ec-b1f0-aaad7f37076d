/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class QueryEnrollmentInfoRequest implements org.apache.thrift.TBase<QueryEnrollmentInfoRequest, QueryEnrollmentInfoRequest._Fields>, java.io.Serializable, Cloneable, Comparable<QueryEnrollmentInfoRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryEnrollmentInfoRequest");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField MEMBER_IDS_FIELD_DESC = new org.apache.thrift.protocol.TField("memberIds", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField PAGE_FIELD_DESC = new org.apache.thrift.protocol.TField("page", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField PAGE_SIZE_FIELD_DESC = new org.apache.thrift.protocol.TField("pageSize", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField ROLE_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("roleType", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField NOCACHE_FIELD_DESC = new org.apache.thrift.protocol.TField("nocache", org.apache.thrift.protocol.TType.BOOL, (short)6);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryEnrollmentInfoRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryEnrollmentInfoRequestTupleSchemeFactory());
  }

  public long actId; // required
  public List<String> memberIds; // required
  public int page; // required
  public int pageSize; // required
  public long roleType; // required
  public boolean nocache; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    MEMBER_IDS((short)2, "memberIds"),
    PAGE((short)3, "page"),
    PAGE_SIZE((short)4, "pageSize"),
    ROLE_TYPE((short)5, "roleType"),
    NOCACHE((short)6, "nocache"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // MEMBER_IDS
          return MEMBER_IDS;
        case 3: // PAGE
          return PAGE;
        case 4: // PAGE_SIZE
          return PAGE_SIZE;
        case 5: // ROLE_TYPE
          return ROLE_TYPE;
        case 6: // NOCACHE
          return NOCACHE;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __PAGE_ISSET_ID = 1;
  private static final int __PAGESIZE_ISSET_ID = 2;
  private static final int __ROLETYPE_ISSET_ID = 3;
  private static final int __NOCACHE_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MEMBER_IDS, new org.apache.thrift.meta_data.FieldMetaData("memberIds", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.PAGE, new org.apache.thrift.meta_data.FieldMetaData("page", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.PAGE_SIZE, new org.apache.thrift.meta_data.FieldMetaData("pageSize", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ROLE_TYPE, new org.apache.thrift.meta_data.FieldMetaData("roleType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.NOCACHE, new org.apache.thrift.meta_data.FieldMetaData("nocache", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryEnrollmentInfoRequest.class, metaDataMap);
  }

  public QueryEnrollmentInfoRequest() {
  }

  public QueryEnrollmentInfoRequest(
    long actId,
    List<String> memberIds,
    int page,
    int pageSize,
    long roleType,
    boolean nocache,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.memberIds = memberIds;
    this.page = page;
    setPageIsSet(true);
    this.pageSize = pageSize;
    setPageSizeIsSet(true);
    this.roleType = roleType;
    setRoleTypeIsSet(true);
    this.nocache = nocache;
    setNocacheIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryEnrollmentInfoRequest(QueryEnrollmentInfoRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    if (other.isSetMemberIds()) {
      List<String> __this__memberIds = new ArrayList<String>(other.memberIds);
      this.memberIds = __this__memberIds;
    }
    this.page = other.page;
    this.pageSize = other.pageSize;
    this.roleType = other.roleType;
    this.nocache = other.nocache;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public QueryEnrollmentInfoRequest deepCopy() {
    return new QueryEnrollmentInfoRequest(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    this.memberIds = null;
    setPageIsSet(false);
    this.page = 0;
    setPageSizeIsSet(false);
    this.pageSize = 0;
    setRoleTypeIsSet(false);
    this.roleType = 0;
    setNocacheIsSet(false);
    this.nocache = false;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public QueryEnrollmentInfoRequest setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public int getMemberIdsSize() {
    return (this.memberIds == null) ? 0 : this.memberIds.size();
  }

  public java.util.Iterator<String> getMemberIdsIterator() {
    return (this.memberIds == null) ? null : this.memberIds.iterator();
  }

  public void addToMemberIds(String elem) {
    if (this.memberIds == null) {
      this.memberIds = new ArrayList<String>();
    }
    this.memberIds.add(elem);
  }

  public List<String> getMemberIds() {
    return this.memberIds;
  }

  public QueryEnrollmentInfoRequest setMemberIds(List<String> memberIds) {
    this.memberIds = memberIds;
    return this;
  }

  public void unsetMemberIds() {
    this.memberIds = null;
  }

  /** Returns true if field memberIds is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberIds() {
    return this.memberIds != null;
  }

  public void setMemberIdsIsSet(boolean value) {
    if (!value) {
      this.memberIds = null;
    }
  }

  public int getPage() {
    return this.page;
  }

  public QueryEnrollmentInfoRequest setPage(int page) {
    this.page = page;
    setPageIsSet(true);
    return this;
  }

  public void unsetPage() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGE_ISSET_ID);
  }

  /** Returns true if field page is set (has been assigned a value) and false otherwise */
  public boolean isSetPage() {
    return EncodingUtils.testBit(__isset_bitfield, __PAGE_ISSET_ID);
  }

  public void setPageIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGE_ISSET_ID, value);
  }

  public int getPageSize() {
    return this.pageSize;
  }

  public QueryEnrollmentInfoRequest setPageSize(int pageSize) {
    this.pageSize = pageSize;
    setPageSizeIsSet(true);
    return this;
  }

  public void unsetPageSize() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
  }

  /** Returns true if field pageSize is set (has been assigned a value) and false otherwise */
  public boolean isSetPageSize() {
    return EncodingUtils.testBit(__isset_bitfield, __PAGESIZE_ISSET_ID);
  }

  public void setPageSizeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PAGESIZE_ISSET_ID, value);
  }

  public long getRoleType() {
    return this.roleType;
  }

  public QueryEnrollmentInfoRequest setRoleType(long roleType) {
    this.roleType = roleType;
    setRoleTypeIsSet(true);
    return this;
  }

  public void unsetRoleType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLETYPE_ISSET_ID);
  }

  /** Returns true if field roleType is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleType() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLETYPE_ISSET_ID);
  }

  public void setRoleTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLETYPE_ISSET_ID, value);
  }

  public boolean isNocache() {
    return this.nocache;
  }

  public QueryEnrollmentInfoRequest setNocache(boolean nocache) {
    this.nocache = nocache;
    setNocacheIsSet(true);
    return this;
  }

  public void unsetNocache() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __NOCACHE_ISSET_ID);
  }

  /** Returns true if field nocache is set (has been assigned a value) and false otherwise */
  public boolean isSetNocache() {
    return EncodingUtils.testBit(__isset_bitfield, __NOCACHE_ISSET_ID);
  }

  public void setNocacheIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __NOCACHE_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public QueryEnrollmentInfoRequest setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case MEMBER_IDS:
      if (value == null) {
        unsetMemberIds();
      } else {
        setMemberIds((List<String>)value);
      }
      break;

    case PAGE:
      if (value == null) {
        unsetPage();
      } else {
        setPage((Integer)value);
      }
      break;

    case PAGE_SIZE:
      if (value == null) {
        unsetPageSize();
      } else {
        setPageSize((Integer)value);
      }
      break;

    case ROLE_TYPE:
      if (value == null) {
        unsetRoleType();
      } else {
        setRoleType((Long)value);
      }
      break;

    case NOCACHE:
      if (value == null) {
        unsetNocache();
      } else {
        setNocache((Boolean)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case MEMBER_IDS:
      return getMemberIds();

    case PAGE:
      return getPage();

    case PAGE_SIZE:
      return getPageSize();

    case ROLE_TYPE:
      return getRoleType();

    case NOCACHE:
      return isNocache();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case MEMBER_IDS:
      return isSetMemberIds();
    case PAGE:
      return isSetPage();
    case PAGE_SIZE:
      return isSetPageSize();
    case ROLE_TYPE:
      return isSetRoleType();
    case NOCACHE:
      return isSetNocache();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryEnrollmentInfoRequest)
      return this.equals((QueryEnrollmentInfoRequest)that);
    return false;
  }

  public boolean equals(QueryEnrollmentInfoRequest that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_memberIds = true && this.isSetMemberIds();
    boolean that_present_memberIds = true && that.isSetMemberIds();
    if (this_present_memberIds || that_present_memberIds) {
      if (!(this_present_memberIds && that_present_memberIds))
        return false;
      if (!this.memberIds.equals(that.memberIds))
        return false;
    }

    boolean this_present_page = true;
    boolean that_present_page = true;
    if (this_present_page || that_present_page) {
      if (!(this_present_page && that_present_page))
        return false;
      if (this.page != that.page)
        return false;
    }

    boolean this_present_pageSize = true;
    boolean that_present_pageSize = true;
    if (this_present_pageSize || that_present_pageSize) {
      if (!(this_present_pageSize && that_present_pageSize))
        return false;
      if (this.pageSize != that.pageSize)
        return false;
    }

    boolean this_present_roleType = true;
    boolean that_present_roleType = true;
    if (this_present_roleType || that_present_roleType) {
      if (!(this_present_roleType && that_present_roleType))
        return false;
      if (this.roleType != that.roleType)
        return false;
    }

    boolean this_present_nocache = true;
    boolean that_present_nocache = true;
    if (this_present_nocache || that_present_nocache) {
      if (!(this_present_nocache && that_present_nocache))
        return false;
      if (this.nocache != that.nocache)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_memberIds = true && (isSetMemberIds());
    list.add(present_memberIds);
    if (present_memberIds)
      list.add(memberIds);

    boolean present_page = true;
    list.add(present_page);
    if (present_page)
      list.add(page);

    boolean present_pageSize = true;
    list.add(present_pageSize);
    if (present_pageSize)
      list.add(pageSize);

    boolean present_roleType = true;
    list.add(present_roleType);
    if (present_roleType)
      list.add(roleType);

    boolean present_nocache = true;
    list.add(present_nocache);
    if (present_nocache)
      list.add(nocache);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryEnrollmentInfoRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMemberIds()).compareTo(other.isSetMemberIds());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberIds()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberIds, other.memberIds);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPage()).compareTo(other.isSetPage());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPage()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.page, other.page);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPageSize()).compareTo(other.isSetPageSize());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPageSize()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pageSize, other.pageSize);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleType()).compareTo(other.isSetRoleType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleType, other.roleType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetNocache()).compareTo(other.isSetNocache());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNocache()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nocache, other.nocache);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryEnrollmentInfoRequest(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("memberIds:");
    if (this.memberIds == null) {
      sb.append("null");
    } else {
      sb.append(this.memberIds);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("page:");
    sb.append(this.page);
    first = false;
    if (!first) sb.append(", ");
    sb.append("pageSize:");
    sb.append(this.pageSize);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleType:");
    sb.append(this.roleType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("nocache:");
    sb.append(this.nocache);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryEnrollmentInfoRequestStandardSchemeFactory implements SchemeFactory {
    public QueryEnrollmentInfoRequestStandardScheme getScheme() {
      return new QueryEnrollmentInfoRequestStandardScheme();
    }
  }

  private static class QueryEnrollmentInfoRequestStandardScheme extends StandardScheme<QueryEnrollmentInfoRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryEnrollmentInfoRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MEMBER_IDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list426 = iprot.readListBegin();
                struct.memberIds = new ArrayList<String>(_list426.size);
                String _elem427;
                for (int _i428 = 0; _i428 < _list426.size; ++_i428)
                {
                  _elem427 = iprot.readString();
                  struct.memberIds.add(_elem427);
                }
                iprot.readListEnd();
              }
              struct.setMemberIdsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PAGE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.page = iprot.readI32();
              struct.setPageIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PAGE_SIZE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pageSize = iprot.readI32();
              struct.setPageSizeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ROLE_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleType = iprot.readI64();
              struct.setRoleTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // NOCACHE
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.nocache = iprot.readBool();
              struct.setNocacheIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map429 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map429.size);
                String _key430;
                String _val431;
                for (int _i432 = 0; _i432 < _map429.size; ++_i432)
                {
                  _key430 = iprot.readString();
                  _val431 = iprot.readString();
                  struct.extData.put(_key430, _val431);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryEnrollmentInfoRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      if (struct.memberIds != null) {
        oprot.writeFieldBegin(MEMBER_IDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.memberIds.size()));
          for (String _iter433 : struct.memberIds)
          {
            oprot.writeString(_iter433);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(PAGE_FIELD_DESC);
      oprot.writeI32(struct.page);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PAGE_SIZE_FIELD_DESC);
      oprot.writeI32(struct.pageSize);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ROLE_TYPE_FIELD_DESC);
      oprot.writeI64(struct.roleType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(NOCACHE_FIELD_DESC);
      oprot.writeBool(struct.nocache);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter434 : struct.extData.entrySet())
          {
            oprot.writeString(_iter434.getKey());
            oprot.writeString(_iter434.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryEnrollmentInfoRequestTupleSchemeFactory implements SchemeFactory {
    public QueryEnrollmentInfoRequestTupleScheme getScheme() {
      return new QueryEnrollmentInfoRequestTupleScheme();
    }
  }

  private static class QueryEnrollmentInfoRequestTupleScheme extends TupleScheme<QueryEnrollmentInfoRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryEnrollmentInfoRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetMemberIds()) {
        optionals.set(1);
      }
      if (struct.isSetPage()) {
        optionals.set(2);
      }
      if (struct.isSetPageSize()) {
        optionals.set(3);
      }
      if (struct.isSetRoleType()) {
        optionals.set(4);
      }
      if (struct.isSetNocache()) {
        optionals.set(5);
      }
      if (struct.isSetExtData()) {
        optionals.set(6);
      }
      oprot.writeBitSet(optionals, 7);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetMemberIds()) {
        {
          oprot.writeI32(struct.memberIds.size());
          for (String _iter435 : struct.memberIds)
          {
            oprot.writeString(_iter435);
          }
        }
      }
      if (struct.isSetPage()) {
        oprot.writeI32(struct.page);
      }
      if (struct.isSetPageSize()) {
        oprot.writeI32(struct.pageSize);
      }
      if (struct.isSetRoleType()) {
        oprot.writeI64(struct.roleType);
      }
      if (struct.isSetNocache()) {
        oprot.writeBool(struct.nocache);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter436 : struct.extData.entrySet())
          {
            oprot.writeString(_iter436.getKey());
            oprot.writeString(_iter436.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryEnrollmentInfoRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(7);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list437 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.memberIds = new ArrayList<String>(_list437.size);
          String _elem438;
          for (int _i439 = 0; _i439 < _list437.size; ++_i439)
          {
            _elem438 = iprot.readString();
            struct.memberIds.add(_elem438);
          }
        }
        struct.setMemberIdsIsSet(true);
      }
      if (incoming.get(2)) {
        struct.page = iprot.readI32();
        struct.setPageIsSet(true);
      }
      if (incoming.get(3)) {
        struct.pageSize = iprot.readI32();
        struct.setPageSizeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.roleType = iprot.readI64();
        struct.setRoleTypeIsSet(true);
      }
      if (incoming.get(5)) {
        struct.nocache = iprot.readBool();
        struct.setNocacheIsSet(true);
      }
      if (incoming.get(6)) {
        {
          org.apache.thrift.protocol.TMap _map440 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map440.size);
          String _key441;
          String _val442;
          for (int _i443 = 0; _i443 < _map440.size; ++_i443)
          {
            _key441 = iprot.readString();
            _val442 = iprot.readString();
            struct.extData.put(_key441, _val442);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

