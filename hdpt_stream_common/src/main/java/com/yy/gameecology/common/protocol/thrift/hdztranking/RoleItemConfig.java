/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class RoleItemConfig implements org.apache.thrift.TBase<RoleItemConfig, RoleItemConfig._Fields>, java.io.Serializable, Cloneable, Comparable<RoleItemConfig> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RoleItemConfig");

  private static final org.apache.thrift.protocol.TField ROLES_FIELD_DESC = new org.apache.thrift.protocol.TField("roles", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField ROLES_EXTJSON_FIELD_DESC = new org.apache.thrift.protocol.TField("rolesExtjson", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField ITEMS_FIELD_DESC = new org.apache.thrift.protocol.TField("items", org.apache.thrift.protocol.TType.LIST, (short)3);
  private static final org.apache.thrift.protocol.TField ITEM_ALIAS_FIELD_DESC = new org.apache.thrift.protocol.TField("itemAlias", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField ITEMS_EXTJSON_FIELD_DESC = new org.apache.thrift.protocol.TField("itemsExtjson", org.apache.thrift.protocol.TType.STRING, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RoleItemConfigStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RoleItemConfigTupleSchemeFactory());
  }

  public List<List<RoleDetail>> roles; // required
  public String rolesExtjson; // required
  public List<String> items; // required
  public String itemAlias; // required
  public String itemsExtjson; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ROLES((short)1, "roles"),
    ROLES_EXTJSON((short)2, "rolesExtjson"),
    ITEMS((short)3, "items"),
    ITEM_ALIAS((short)4, "itemAlias"),
    ITEMS_EXTJSON((short)5, "itemsExtjson");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROLES
          return ROLES;
        case 2: // ROLES_EXTJSON
          return ROLES_EXTJSON;
        case 3: // ITEMS
          return ITEMS;
        case 4: // ITEM_ALIAS
          return ITEM_ALIAS;
        case 5: // ITEMS_EXTJSON
          return ITEMS_EXTJSON;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROLES, new org.apache.thrift.meta_data.FieldMetaData("roles", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
                new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RoleDetail.class)))));
    tmpMap.put(_Fields.ROLES_EXTJSON, new org.apache.thrift.meta_data.FieldMetaData("rolesExtjson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ITEMS, new org.apache.thrift.meta_data.FieldMetaData("items", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.ITEM_ALIAS, new org.apache.thrift.meta_data.FieldMetaData("itemAlias", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ITEMS_EXTJSON, new org.apache.thrift.meta_data.FieldMetaData("itemsExtjson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RoleItemConfig.class, metaDataMap);
  }

  public RoleItemConfig() {
  }

  public RoleItemConfig(
    List<List<RoleDetail>> roles,
    String rolesExtjson,
    List<String> items,
    String itemAlias,
    String itemsExtjson)
  {
    this();
    this.roles = roles;
    this.rolesExtjson = rolesExtjson;
    this.items = items;
    this.itemAlias = itemAlias;
    this.itemsExtjson = itemsExtjson;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RoleItemConfig(RoleItemConfig other) {
    if (other.isSetRoles()) {
      List<List<RoleDetail>> __this__roles = new ArrayList<List<RoleDetail>>(other.roles.size());
      for (List<RoleDetail> other_element : other.roles) {
        List<RoleDetail> __this__roles_copy = new ArrayList<RoleDetail>(other_element.size());
        for (RoleDetail other_element_element : other_element) {
          __this__roles_copy.add(new RoleDetail(other_element_element));
        }
        __this__roles.add(__this__roles_copy);
      }
      this.roles = __this__roles;
    }
    if (other.isSetRolesExtjson()) {
      this.rolesExtjson = other.rolesExtjson;
    }
    if (other.isSetItems()) {
      List<String> __this__items = new ArrayList<String>(other.items);
      this.items = __this__items;
    }
    if (other.isSetItemAlias()) {
      this.itemAlias = other.itemAlias;
    }
    if (other.isSetItemsExtjson()) {
      this.itemsExtjson = other.itemsExtjson;
    }
  }

  public RoleItemConfig deepCopy() {
    return new RoleItemConfig(this);
  }

  @Override
  public void clear() {
    this.roles = null;
    this.rolesExtjson = null;
    this.items = null;
    this.itemAlias = null;
    this.itemsExtjson = null;
  }

  public int getRolesSize() {
    return (this.roles == null) ? 0 : this.roles.size();
  }

  public java.util.Iterator<List<RoleDetail>> getRolesIterator() {
    return (this.roles == null) ? null : this.roles.iterator();
  }

  public void addToRoles(List<RoleDetail> elem) {
    if (this.roles == null) {
      this.roles = new ArrayList<List<RoleDetail>>();
    }
    this.roles.add(elem);
  }

  public List<List<RoleDetail>> getRoles() {
    return this.roles;
  }

  public RoleItemConfig setRoles(List<List<RoleDetail>> roles) {
    this.roles = roles;
    return this;
  }

  public void unsetRoles() {
    this.roles = null;
  }

  /** Returns true if field roles is set (has been assigned a value) and false otherwise */
  public boolean isSetRoles() {
    return this.roles != null;
  }

  public void setRolesIsSet(boolean value) {
    if (!value) {
      this.roles = null;
    }
  }

  public String getRolesExtjson() {
    return this.rolesExtjson;
  }

  public RoleItemConfig setRolesExtjson(String rolesExtjson) {
    this.rolesExtjson = rolesExtjson;
    return this;
  }

  public void unsetRolesExtjson() {
    this.rolesExtjson = null;
  }

  /** Returns true if field rolesExtjson is set (has been assigned a value) and false otherwise */
  public boolean isSetRolesExtjson() {
    return this.rolesExtjson != null;
  }

  public void setRolesExtjsonIsSet(boolean value) {
    if (!value) {
      this.rolesExtjson = null;
    }
  }

  public int getItemsSize() {
    return (this.items == null) ? 0 : this.items.size();
  }

  public java.util.Iterator<String> getItemsIterator() {
    return (this.items == null) ? null : this.items.iterator();
  }

  public void addToItems(String elem) {
    if (this.items == null) {
      this.items = new ArrayList<String>();
    }
    this.items.add(elem);
  }

  public List<String> getItems() {
    return this.items;
  }

  public RoleItemConfig setItems(List<String> items) {
    this.items = items;
    return this;
  }

  public void unsetItems() {
    this.items = null;
  }

  /** Returns true if field items is set (has been assigned a value) and false otherwise */
  public boolean isSetItems() {
    return this.items != null;
  }

  public void setItemsIsSet(boolean value) {
    if (!value) {
      this.items = null;
    }
  }

  public String getItemAlias() {
    return this.itemAlias;
  }

  public RoleItemConfig setItemAlias(String itemAlias) {
    this.itemAlias = itemAlias;
    return this;
  }

  public void unsetItemAlias() {
    this.itemAlias = null;
  }

  /** Returns true if field itemAlias is set (has been assigned a value) and false otherwise */
  public boolean isSetItemAlias() {
    return this.itemAlias != null;
  }

  public void setItemAliasIsSet(boolean value) {
    if (!value) {
      this.itemAlias = null;
    }
  }

  public String getItemsExtjson() {
    return this.itemsExtjson;
  }

  public RoleItemConfig setItemsExtjson(String itemsExtjson) {
    this.itemsExtjson = itemsExtjson;
    return this;
  }

  public void unsetItemsExtjson() {
    this.itemsExtjson = null;
  }

  /** Returns true if field itemsExtjson is set (has been assigned a value) and false otherwise */
  public boolean isSetItemsExtjson() {
    return this.itemsExtjson != null;
  }

  public void setItemsExtjsonIsSet(boolean value) {
    if (!value) {
      this.itemsExtjson = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ROLES:
      if (value == null) {
        unsetRoles();
      } else {
        setRoles((List<List<RoleDetail>>)value);
      }
      break;

    case ROLES_EXTJSON:
      if (value == null) {
        unsetRolesExtjson();
      } else {
        setRolesExtjson((String)value);
      }
      break;

    case ITEMS:
      if (value == null) {
        unsetItems();
      } else {
        setItems((List<String>)value);
      }
      break;

    case ITEM_ALIAS:
      if (value == null) {
        unsetItemAlias();
      } else {
        setItemAlias((String)value);
      }
      break;

    case ITEMS_EXTJSON:
      if (value == null) {
        unsetItemsExtjson();
      } else {
        setItemsExtjson((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ROLES:
      return getRoles();

    case ROLES_EXTJSON:
      return getRolesExtjson();

    case ITEMS:
      return getItems();

    case ITEM_ALIAS:
      return getItemAlias();

    case ITEMS_EXTJSON:
      return getItemsExtjson();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ROLES:
      return isSetRoles();
    case ROLES_EXTJSON:
      return isSetRolesExtjson();
    case ITEMS:
      return isSetItems();
    case ITEM_ALIAS:
      return isSetItemAlias();
    case ITEMS_EXTJSON:
      return isSetItemsExtjson();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RoleItemConfig)
      return this.equals((RoleItemConfig)that);
    return false;
  }

  public boolean equals(RoleItemConfig that) {
    if (that == null)
      return false;

    boolean this_present_roles = true && this.isSetRoles();
    boolean that_present_roles = true && that.isSetRoles();
    if (this_present_roles || that_present_roles) {
      if (!(this_present_roles && that_present_roles))
        return false;
      if (!this.roles.equals(that.roles))
        return false;
    }

    boolean this_present_rolesExtjson = true && this.isSetRolesExtjson();
    boolean that_present_rolesExtjson = true && that.isSetRolesExtjson();
    if (this_present_rolesExtjson || that_present_rolesExtjson) {
      if (!(this_present_rolesExtjson && that_present_rolesExtjson))
        return false;
      if (!this.rolesExtjson.equals(that.rolesExtjson))
        return false;
    }

    boolean this_present_items = true && this.isSetItems();
    boolean that_present_items = true && that.isSetItems();
    if (this_present_items || that_present_items) {
      if (!(this_present_items && that_present_items))
        return false;
      if (!this.items.equals(that.items))
        return false;
    }

    boolean this_present_itemAlias = true && this.isSetItemAlias();
    boolean that_present_itemAlias = true && that.isSetItemAlias();
    if (this_present_itemAlias || that_present_itemAlias) {
      if (!(this_present_itemAlias && that_present_itemAlias))
        return false;
      if (!this.itemAlias.equals(that.itemAlias))
        return false;
    }

    boolean this_present_itemsExtjson = true && this.isSetItemsExtjson();
    boolean that_present_itemsExtjson = true && that.isSetItemsExtjson();
    if (this_present_itemsExtjson || that_present_itemsExtjson) {
      if (!(this_present_itemsExtjson && that_present_itemsExtjson))
        return false;
      if (!this.itemsExtjson.equals(that.itemsExtjson))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_roles = true && (isSetRoles());
    list.add(present_roles);
    if (present_roles)
      list.add(roles);

    boolean present_rolesExtjson = true && (isSetRolesExtjson());
    list.add(present_rolesExtjson);
    if (present_rolesExtjson)
      list.add(rolesExtjson);

    boolean present_items = true && (isSetItems());
    list.add(present_items);
    if (present_items)
      list.add(items);

    boolean present_itemAlias = true && (isSetItemAlias());
    list.add(present_itemAlias);
    if (present_itemAlias)
      list.add(itemAlias);

    boolean present_itemsExtjson = true && (isSetItemsExtjson());
    list.add(present_itemsExtjson);
    if (present_itemsExtjson)
      list.add(itemsExtjson);

    return list.hashCode();
  }

  @Override
  public int compareTo(RoleItemConfig other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRoles()).compareTo(other.isSetRoles());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoles()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roles, other.roles);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRolesExtjson()).compareTo(other.isSetRolesExtjson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRolesExtjson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rolesExtjson, other.rolesExtjson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItems()).compareTo(other.isSetItems());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItems()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.items, other.items);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemAlias()).compareTo(other.isSetItemAlias());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemAlias()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemAlias, other.itemAlias);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemsExtjson()).compareTo(other.isSetItemsExtjson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemsExtjson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemsExtjson, other.itemsExtjson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RoleItemConfig(");
    boolean first = true;

    sb.append("roles:");
    if (this.roles == null) {
      sb.append("null");
    } else {
      sb.append(this.roles);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rolesExtjson:");
    if (this.rolesExtjson == null) {
      sb.append("null");
    } else {
      sb.append(this.rolesExtjson);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("items:");
    if (this.items == null) {
      sb.append("null");
    } else {
      sb.append(this.items);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemAlias:");
    if (this.itemAlias == null) {
      sb.append("null");
    } else {
      sb.append(this.itemAlias);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemsExtjson:");
    if (this.itemsExtjson == null) {
      sb.append("null");
    } else {
      sb.append(this.itemsExtjson);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RoleItemConfigStandardSchemeFactory implements SchemeFactory {
    public RoleItemConfigStandardScheme getScheme() {
      return new RoleItemConfigStandardScheme();
    }
  }

  private static class RoleItemConfigStandardScheme extends StandardScheme<RoleItemConfig> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RoleItemConfig struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROLES
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list112 = iprot.readListBegin();
                struct.roles = new ArrayList<List<RoleDetail>>(_list112.size);
                List<RoleDetail> _elem113;
                for (int _i114 = 0; _i114 < _list112.size; ++_i114)
                {
                  {
                    org.apache.thrift.protocol.TList _list115 = iprot.readListBegin();
                    _elem113 = new ArrayList<RoleDetail>(_list115.size);
                    RoleDetail _elem116;
                    for (int _i117 = 0; _i117 < _list115.size; ++_i117)
                    {
                      _elem116 = new RoleDetail();
                      _elem116.read(iprot);
                      _elem113.add(_elem116);
                    }
                    iprot.readListEnd();
                  }
                  struct.roles.add(_elem113);
                }
                iprot.readListEnd();
              }
              struct.setRolesIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ROLES_EXTJSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.rolesExtjson = iprot.readString();
              struct.setRolesExtjsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ITEMS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list118 = iprot.readListBegin();
                struct.items = new ArrayList<String>(_list118.size);
                String _elem119;
                for (int _i120 = 0; _i120 < _list118.size; ++_i120)
                {
                  _elem119 = iprot.readString();
                  struct.items.add(_elem119);
                }
                iprot.readListEnd();
              }
              struct.setItemsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ITEM_ALIAS
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.itemAlias = iprot.readString();
              struct.setItemAliasIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ITEMS_EXTJSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.itemsExtjson = iprot.readString();
              struct.setItemsExtjsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RoleItemConfig struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.roles != null) {
        oprot.writeFieldBegin(ROLES_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.LIST, struct.roles.size()));
          for (List<RoleDetail> _iter121 : struct.roles)
          {
            {
              oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, _iter121.size()));
              for (RoleDetail _iter122 : _iter121)
              {
                _iter122.write(oprot);
              }
              oprot.writeListEnd();
            }
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.rolesExtjson != null) {
        oprot.writeFieldBegin(ROLES_EXTJSON_FIELD_DESC);
        oprot.writeString(struct.rolesExtjson);
        oprot.writeFieldEnd();
      }
      if (struct.items != null) {
        oprot.writeFieldBegin(ITEMS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.items.size()));
          for (String _iter123 : struct.items)
          {
            oprot.writeString(_iter123);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.itemAlias != null) {
        oprot.writeFieldBegin(ITEM_ALIAS_FIELD_DESC);
        oprot.writeString(struct.itemAlias);
        oprot.writeFieldEnd();
      }
      if (struct.itemsExtjson != null) {
        oprot.writeFieldBegin(ITEMS_EXTJSON_FIELD_DESC);
        oprot.writeString(struct.itemsExtjson);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RoleItemConfigTupleSchemeFactory implements SchemeFactory {
    public RoleItemConfigTupleScheme getScheme() {
      return new RoleItemConfigTupleScheme();
    }
  }

  private static class RoleItemConfigTupleScheme extends TupleScheme<RoleItemConfig> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RoleItemConfig struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRoles()) {
        optionals.set(0);
      }
      if (struct.isSetRolesExtjson()) {
        optionals.set(1);
      }
      if (struct.isSetItems()) {
        optionals.set(2);
      }
      if (struct.isSetItemAlias()) {
        optionals.set(3);
      }
      if (struct.isSetItemsExtjson()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetRoles()) {
        {
          oprot.writeI32(struct.roles.size());
          for (List<RoleDetail> _iter124 : struct.roles)
          {
            {
              oprot.writeI32(_iter124.size());
              for (RoleDetail _iter125 : _iter124)
              {
                _iter125.write(oprot);
              }
            }
          }
        }
      }
      if (struct.isSetRolesExtjson()) {
        oprot.writeString(struct.rolesExtjson);
      }
      if (struct.isSetItems()) {
        {
          oprot.writeI32(struct.items.size());
          for (String _iter126 : struct.items)
          {
            oprot.writeString(_iter126);
          }
        }
      }
      if (struct.isSetItemAlias()) {
        oprot.writeString(struct.itemAlias);
      }
      if (struct.isSetItemsExtjson()) {
        oprot.writeString(struct.itemsExtjson);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RoleItemConfig struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list127 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.LIST, iprot.readI32());
          struct.roles = new ArrayList<List<RoleDetail>>(_list127.size);
          List<RoleDetail> _elem128;
          for (int _i129 = 0; _i129 < _list127.size; ++_i129)
          {
            {
              org.apache.thrift.protocol.TList _list130 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
              _elem128 = new ArrayList<RoleDetail>(_list130.size);
              RoleDetail _elem131;
              for (int _i132 = 0; _i132 < _list130.size; ++_i132)
              {
                _elem131 = new RoleDetail();
                _elem131.read(iprot);
                _elem128.add(_elem131);
              }
            }
            struct.roles.add(_elem128);
          }
        }
        struct.setRolesIsSet(true);
      }
      if (incoming.get(1)) {
        struct.rolesExtjson = iprot.readString();
        struct.setRolesExtjsonIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TList _list133 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.items = new ArrayList<String>(_list133.size);
          String _elem134;
          for (int _i135 = 0; _i135 < _list133.size; ++_i135)
          {
            _elem134 = iprot.readString();
            struct.items.add(_elem134);
          }
        }
        struct.setItemsIsSet(true);
      }
      if (incoming.get(3)) {
        struct.itemAlias = iprot.readString();
        struct.setItemAliasIsSet(true);
      }
      if (incoming.get(4)) {
        struct.itemsExtjson = iprot.readString();
        struct.setItemsExtjsonIsSet(true);
      }
    }
  }

}

