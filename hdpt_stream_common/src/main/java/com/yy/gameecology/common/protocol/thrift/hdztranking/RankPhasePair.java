/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class RankPhasePair implements org.apache.thrift.TBase<RankPhasePair, RankPhasePair._Fields>, java.io.Serializable, Cloneable, Comparable<RankPhasePair> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RankPhasePair");

  private static final org.apache.thrift.protocol.TField RANK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rankId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RankPhasePairStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RankPhasePairTupleSchemeFactory());
  }

  public long rankId; // required
  public long phaseId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RANK_ID((short)1, "rankId"),
    PHASE_ID((short)2, "phaseId");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RANK_ID
          return RANK_ID;
        case 2: // PHASE_ID
          return PHASE_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RANKID_ISSET_ID = 0;
  private static final int __PHASEID_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RANK_ID, new org.apache.thrift.meta_data.FieldMetaData("rankId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RankPhasePair.class, metaDataMap);
  }

  public RankPhasePair() {
  }

  public RankPhasePair(
    long rankId,
    long phaseId)
  {
    this();
    this.rankId = rankId;
    setRankIdIsSet(true);
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RankPhasePair(RankPhasePair other) {
    __isset_bitfield = other.__isset_bitfield;
    this.rankId = other.rankId;
    this.phaseId = other.phaseId;
  }

  public RankPhasePair deepCopy() {
    return new RankPhasePair(this);
  }

  @Override
  public void clear() {
    setRankIdIsSet(false);
    this.rankId = 0;
    setPhaseIdIsSet(false);
    this.phaseId = 0;
  }

  public long getRankId() {
    return this.rankId;
  }

  public RankPhasePair setRankId(long rankId) {
    this.rankId = rankId;
    setRankIdIsSet(true);
    return this;
  }

  public void unsetRankId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  /** Returns true if field rankId is set (has been assigned a value) and false otherwise */
  public boolean isSetRankId() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  public void setRankIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKID_ISSET_ID, value);
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public RankPhasePair setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RANK_ID:
      if (value == null) {
        unsetRankId();
      } else {
        setRankId((Long)value);
      }
      break;

    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RANK_ID:
      return getRankId();

    case PHASE_ID:
      return getPhaseId();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RANK_ID:
      return isSetRankId();
    case PHASE_ID:
      return isSetPhaseId();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RankPhasePair)
      return this.equals((RankPhasePair)that);
    return false;
  }

  public boolean equals(RankPhasePair that) {
    if (that == null)
      return false;

    boolean this_present_rankId = true;
    boolean that_present_rankId = true;
    if (this_present_rankId || that_present_rankId) {
      if (!(this_present_rankId && that_present_rankId))
        return false;
      if (this.rankId != that.rankId)
        return false;
    }

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_rankId = true;
    list.add(present_rankId);
    if (present_rankId)
      list.add(rankId);

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    return list.hashCode();
  }

  @Override
  public int compareTo(RankPhasePair other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRankId()).compareTo(other.isSetRankId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankId, other.rankId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RankPhasePair(");
    boolean first = true;

    sb.append("rankId:");
    sb.append(this.rankId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RankPhasePairStandardSchemeFactory implements SchemeFactory {
    public RankPhasePairStandardScheme getScheme() {
      return new RankPhasePairStandardScheme();
    }
  }

  private static class RankPhasePairStandardScheme extends StandardScheme<RankPhasePair> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RankPhasePair struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RANK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankId = iprot.readI64();
              struct.setRankIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RankPhasePair struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RANK_ID_FIELD_DESC);
      oprot.writeI64(struct.rankId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RankPhasePairTupleSchemeFactory implements SchemeFactory {
    public RankPhasePairTupleScheme getScheme() {
      return new RankPhasePairTupleScheme();
    }
  }

  private static class RankPhasePairTupleScheme extends TupleScheme<RankPhasePair> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RankPhasePair struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRankId()) {
        optionals.set(0);
      }
      if (struct.isSetPhaseId()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRankId()) {
        oprot.writeI64(struct.rankId);
      }
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RankPhasePair struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.rankId = iprot.readI64();
        struct.setRankIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
    }
  }

}

