/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_group_center;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

/**
 * 这个可以随便加，但是对应到boss后台那里配置角色ID的时候，需要配置对应的数字，
 * 比如下面的新加的家族，那么就要配置角色ID为5
 * 
 */
public enum QueryType implements TEnum {
  TypeCompere(0),
  TypeGuild(1),
  TypeChannel(2),
  TypeTing(3),
  TypeGroup(4),
  TypeFamily(5),
  TypeRoom(6);

  private final int value;

  private QueryType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static QueryType findByValue(int value) { 
    switch (value) {
      case 0:
        return TypeCompere;
      case 1:
        return TypeGuild;
      case 2:
        return TypeChannel;
      case 3:
        return TypeTing;
      case 4:
        return TypeGroup;
      case 5:
        return TypeFamily;
      case 6:
        return TypeRoom;
      default:
        return null;
    }
  }
}
