/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class BatchGetLBSInfoRsp implements org.apache.thrift.TBase<BatchGetLBSInfoRsp, BatchGetLBSInfoRsp._Fields>, java.io.Serializable, Cloneable, Comparable<BatchGetLBSInfoRsp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("BatchGetLBSInfoRsp");

  private static final org.apache.thrift.protocol.TField RET_FIELD_DESC = new org.apache.thrift.protocol.TField("ret", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField LBS_INFO_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("lbs_info_map", org.apache.thrift.protocol.TType.MAP, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new BatchGetLBSInfoRspStandardSchemeFactory());
    schemes.put(TupleScheme.class, new BatchGetLBSInfoRspTupleSchemeFactory());
  }

  public int ret; // required
  public Map<Long,LBSInfo> lbs_info_map; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RET((short)1, "ret"),
    LBS_INFO_MAP((short)2, "lbs_info_map");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RET
          return RET;
        case 2: // LBS_INFO_MAP
          return LBS_INFO_MAP;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RET_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RET, new org.apache.thrift.meta_data.FieldMetaData("ret", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.LBS_INFO_MAP, new org.apache.thrift.meta_data.FieldMetaData("lbs_info_map", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, LBSInfo.class))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(BatchGetLBSInfoRsp.class, metaDataMap);
  }

  public BatchGetLBSInfoRsp() {
  }

  public BatchGetLBSInfoRsp(
    int ret,
    Map<Long,LBSInfo> lbs_info_map)
  {
    this();
    this.ret = ret;
    setRetIsSet(true);
    this.lbs_info_map = lbs_info_map;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public BatchGetLBSInfoRsp(BatchGetLBSInfoRsp other) {
    __isset_bitfield = other.__isset_bitfield;
    this.ret = other.ret;
    if (other.isSetLbs_info_map()) {
      Map<Long,LBSInfo> __this__lbs_info_map = new HashMap<Long,LBSInfo>(other.lbs_info_map.size());
      for (Map.Entry<Long, LBSInfo> other_element : other.lbs_info_map.entrySet()) {

        Long other_element_key = other_element.getKey();
        LBSInfo other_element_value = other_element.getValue();

        Long __this__lbs_info_map_copy_key = other_element_key;

        LBSInfo __this__lbs_info_map_copy_value = new LBSInfo(other_element_value);

        __this__lbs_info_map.put(__this__lbs_info_map_copy_key, __this__lbs_info_map_copy_value);
      }
      this.lbs_info_map = __this__lbs_info_map;
    }
  }

  public BatchGetLBSInfoRsp deepCopy() {
    return new BatchGetLBSInfoRsp(this);
  }

  @Override
  public void clear() {
    setRetIsSet(false);
    this.ret = 0;
    this.lbs_info_map = null;
  }

  public int getRet() {
    return this.ret;
  }

  public BatchGetLBSInfoRsp setRet(int ret) {
    this.ret = ret;
    setRetIsSet(true);
    return this;
  }

  public void unsetRet() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RET_ISSET_ID);
  }

  /** Returns true if field ret is set (has been assigned a value) and false otherwise */
  public boolean isSetRet() {
    return EncodingUtils.testBit(__isset_bitfield, __RET_ISSET_ID);
  }

  public void setRetIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RET_ISSET_ID, value);
  }

  public int getLbs_info_mapSize() {
    return (this.lbs_info_map == null) ? 0 : this.lbs_info_map.size();
  }

  public void putToLbs_info_map(long key, LBSInfo val) {
    if (this.lbs_info_map == null) {
      this.lbs_info_map = new HashMap<Long,LBSInfo>();
    }
    this.lbs_info_map.put(key, val);
  }

  public Map<Long,LBSInfo> getLbs_info_map() {
    return this.lbs_info_map;
  }

  public BatchGetLBSInfoRsp setLbs_info_map(Map<Long,LBSInfo> lbs_info_map) {
    this.lbs_info_map = lbs_info_map;
    return this;
  }

  public void unsetLbs_info_map() {
    this.lbs_info_map = null;
  }

  /** Returns true if field lbs_info_map is set (has been assigned a value) and false otherwise */
  public boolean isSetLbs_info_map() {
    return this.lbs_info_map != null;
  }

  public void setLbs_info_mapIsSet(boolean value) {
    if (!value) {
      this.lbs_info_map = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RET:
      if (value == null) {
        unsetRet();
      } else {
        setRet((Integer)value);
      }
      break;

    case LBS_INFO_MAP:
      if (value == null) {
        unsetLbs_info_map();
      } else {
        setLbs_info_map((Map<Long,LBSInfo>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RET:
      return getRet();

    case LBS_INFO_MAP:
      return getLbs_info_map();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RET:
      return isSetRet();
    case LBS_INFO_MAP:
      return isSetLbs_info_map();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof BatchGetLBSInfoRsp)
      return this.equals((BatchGetLBSInfoRsp)that);
    return false;
  }

  public boolean equals(BatchGetLBSInfoRsp that) {
    if (that == null)
      return false;

    boolean this_present_ret = true;
    boolean that_present_ret = true;
    if (this_present_ret || that_present_ret) {
      if (!(this_present_ret && that_present_ret))
        return false;
      if (this.ret != that.ret)
        return false;
    }

    boolean this_present_lbs_info_map = true && this.isSetLbs_info_map();
    boolean that_present_lbs_info_map = true && that.isSetLbs_info_map();
    if (this_present_lbs_info_map || that_present_lbs_info_map) {
      if (!(this_present_lbs_info_map && that_present_lbs_info_map))
        return false;
      if (!this.lbs_info_map.equals(that.lbs_info_map))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_ret = true;
    list.add(present_ret);
    if (present_ret)
      list.add(ret);

    boolean present_lbs_info_map = true && (isSetLbs_info_map());
    list.add(present_lbs_info_map);
    if (present_lbs_info_map)
      list.add(lbs_info_map);

    return list.hashCode();
  }

  @Override
  public int compareTo(BatchGetLBSInfoRsp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRet()).compareTo(other.isSetRet());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRet()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ret, other.ret);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLbs_info_map()).compareTo(other.isSetLbs_info_map());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLbs_info_map()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lbs_info_map, other.lbs_info_map);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("BatchGetLBSInfoRsp(");
    boolean first = true;

    sb.append("ret:");
    sb.append(this.ret);
    first = false;
    if (!first) sb.append(", ");
    sb.append("lbs_info_map:");
    if (this.lbs_info_map == null) {
      sb.append("null");
    } else {
      sb.append(this.lbs_info_map);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class BatchGetLBSInfoRspStandardSchemeFactory implements SchemeFactory {
    public BatchGetLBSInfoRspStandardScheme getScheme() {
      return new BatchGetLBSInfoRspStandardScheme();
    }
  }

  private static class BatchGetLBSInfoRspStandardScheme extends StandardScheme<BatchGetLBSInfoRsp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, BatchGetLBSInfoRsp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RET
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.ret = iprot.readI32();
              struct.setRetIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // LBS_INFO_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map200 = iprot.readMapBegin();
                struct.lbs_info_map = new HashMap<Long,LBSInfo>(2*_map200.size);
                long _key201;
                LBSInfo _val202;
                for (int _i203 = 0; _i203 < _map200.size; ++_i203)
                {
                  _key201 = iprot.readI64();
                  _val202 = new LBSInfo();
                  _val202.read(iprot);
                  struct.lbs_info_map.put(_key201, _val202);
                }
                iprot.readMapEnd();
              }
              struct.setLbs_info_mapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, BatchGetLBSInfoRsp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RET_FIELD_DESC);
      oprot.writeI32(struct.ret);
      oprot.writeFieldEnd();
      if (struct.lbs_info_map != null) {
        oprot.writeFieldBegin(LBS_INFO_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRUCT, struct.lbs_info_map.size()));
          for (Map.Entry<Long, LBSInfo> _iter204 : struct.lbs_info_map.entrySet())
          {
            oprot.writeI64(_iter204.getKey());
            _iter204.getValue().write(oprot);
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class BatchGetLBSInfoRspTupleSchemeFactory implements SchemeFactory {
    public BatchGetLBSInfoRspTupleScheme getScheme() {
      return new BatchGetLBSInfoRspTupleScheme();
    }
  }

  private static class BatchGetLBSInfoRspTupleScheme extends TupleScheme<BatchGetLBSInfoRsp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, BatchGetLBSInfoRsp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRet()) {
        optionals.set(0);
      }
      if (struct.isSetLbs_info_map()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetRet()) {
        oprot.writeI32(struct.ret);
      }
      if (struct.isSetLbs_info_map()) {
        {
          oprot.writeI32(struct.lbs_info_map.size());
          for (Map.Entry<Long, LBSInfo> _iter205 : struct.lbs_info_map.entrySet())
          {
            oprot.writeI64(_iter205.getKey());
            _iter205.getValue().write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, BatchGetLBSInfoRsp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.ret = iprot.readI32();
        struct.setRetIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map206 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.lbs_info_map = new HashMap<Long,LBSInfo>(2*_map206.size);
          long _key207;
          LBSInfo _val208;
          for (int _i209 = 0; _i209 < _map206.size; ++_i209)
          {
            _key207 = iprot.readI64();
            _val208 = new LBSInfo();
            _val208.read(iprot);
            struct.lbs_info_map.put(_key207, _val208);
          }
        }
        struct.setLbs_info_mapIsSet(true);
      }
    }
  }

}

