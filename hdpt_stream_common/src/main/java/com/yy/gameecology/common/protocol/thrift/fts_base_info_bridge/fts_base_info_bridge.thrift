namespace java com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge
// s2s name
// fts_base_info_bridge_test
// fts_base_info_bridge

struct CommonRet
{
    1:i64 code; // 0-success
    2:string message;
}

// 用户资料
struct UserInfo
{
    1:i64 uid; //uid
    2:string avatar_url; //用户头像
    3:string nick; // 交友昵称，如果不存在则为YY昵称
    4:i64 noble_id; // 贵族等级 100-107 骑士-守护神
    5:i64 ecology_noble_id; // 超神贵族
    6:i64 imid; // YY号
}

struct UserInfoReq
{
    1:list<i64> uid_list;
    2:string expand; // 扩展字段
}

struct UserInfoResp
{
    1:CommonRet ret;
    2:map<i64, UserInfo> ret_map;
}

// 主持签约信息
struct CompereSign
{
    1:i64 uid; // uid
    2:i64 sid; // sign sid
    3:i64 asid; // sign asid
}

struct CompereSignReq
{
    1:list<i64> uid_list;
    2:string expand;
}

struct CompereSignResp
{
    1:CommonRet ret;
    2:map<i64, CompereSign> ret_map;
}

// 主持在线信息
struct CompereOnline
{
    1:i64 uid; // uid
    2:i64 sid; // sid > 0 && ssid > 0 主持在线
    3:i64 ssid; //
}

struct CompereOnlineReq
{
    1:list<i64> uid_list;
    2:string expand;
}

struct CompereOnlineResp
{
    1:CommonRet ret;
    2:map<i64, CompereOnline> ret_map;
}

// 查询频道的信息
struct ChannelId
{
    1:i64 sid;
    2:i64 ssid;
}

struct ChannelInfo
{
    1:i64 sid;
    2:i64 ssid;
    3:i64 compere_uid; // 在麦主持
    4:i64 play_mode; // 频道玩法
    5:list<i64> guest_list; // 针对多人玩法，嘉宾列表
}

struct ChannelInfoReq
{
    1:list<ChannelId> channel_list; // 频道信息
    2:string expand;
}

struct ChannelInfoResp
{
    1:CommonRet ret;
    2:map<string, ChannelInfo> ret_map; // map => key = sid:ssid value = ChannelInfo
}

// 查询所有在线频道
struct OnlineChannel
{
    1:i64 sid;
    2:i64 ssid;
    3:i64 play_mode; // 8, 18, 19 为多人玩法
    4:i64 compere_uid;
    5:list<i64> guest_list; // 多人玩法下这个
}

struct OnlineChannelReq
{

}

struct OnlineChannelResp
{
    1:CommonRet ret;
    2:list<OnlineChannel> ret_list;
}

// 订阅信息
struct SubscribeReq
{
    1:i64 uid = 1; // 请求的uid
    2:list<i64> compere_uids = 2; // 主持uid
}

struct SubscribeResp
{
    1:CommonRet ret;
    2:map<i64, bool> retMap;
}

// 用户资料
struct MemberItemInfo
{
    1:string baseFieldMemberId; //uid/sid(可能和key不一样，例如陪玩运营频道，则key是原来的频道号,MemberItemInfo是对应的运营频道信息)
    2:string baseFieldMemberName; //名称 业务个性化名称或者YY平台名称
    3:string baseFieldMemberUrl; //头像 业务个性化头像或者YY平台头像
    4:map<string,string> ext; // 扩展字段
    5:map<string,string> viewExt; // 扩展字段直接透传给前端，可加业务个性化信息
}


struct QueryMemberResponse
{
    1:map<string,map<string,MemberItemInfo>> memberMap; // 第一层key 分组id, 第二层key memberId
    2:map<string,string> ext; // 扩展字段
    3:i32 code;                 // 结果码，0-成功，非零-失败或其他含义
    4:string reason;            // 成功或失败的提示信息
}

struct QueryMemberRequest
{
    1:i64 actId; // 活动标识
    2:i64 rankId;
    3:map<string,list<string>> memberIdMap; // key 分组id ; value memberId
    4:map<string,string> ext; // 扩展字段
    5:string sign; // 请求参数签名（使用 busiId 和 actId 下的组合key，签名算法待定，目前尚未启用）
}

struct LBSInfo
{
    1:string    city (go.tag = "json:\"city\"");
    2:double    lat (go.tag = "json:\"lat\"");
    3:double    lng (go.tag = "json:\"lng\"");
    4:string    province (go.tag = "json:\"province\"");
}

struct BatchGetLBSInfoReq
{
    1:list<i64> uids;
}

struct BatchGetLBSInfoRsp
{
    1:i32   ret; // 0-成功，其他失败
    2:map<i64, LBSInfo>  lbs_info_map;
}

struct ChannelFightReq
{
    1:i64  sid;
	2:i64  ssid;
}

struct ChannelFightRsp
{
   1:i64  ret;      // 0 -成功  1 -失败
   2:i64  serialNo; // 0 -非乱斗中
   3:i64  sid;
   4:i64  ssid;
   5:i64  compereUid;
   6:i64  revenue; // 营收流水 1Y币=1000
}

struct HatCompereInfoReq
{

}

struct HatCompereInfoResp
{
    1:CommonRet ret;
    2:list<i64> uid_list; // 所有帽子主持UID
}


/**
 * 所属业务
 *
 */
enum TAppId {
    Dating=2,       // 交友
    VipPk=14,       // 约战
    Baby=36,        // 宝贝
}

struct TTingBind {
	1: i64 id; //厅主持绑定id
	2: i32 appid; //业务id
	3: i64 sid; //公会id
	4: i64 tingMgrUid; //厅管uid
	5: i64 tid; //厅id
	6: i64 anchorUid; //主持uid
	7: string expand; //扩展信息
	8: i64 createTime; //创建日期
}

struct TTing {
	1: i64 id;  // 厅id
	2: i32 appid; //业务ud
	3: i64 sid; //公会id
	4: i64 owUid; //ow uid
	5: i64 tingMgrUid; //厅管uid
	6: i32 weight; //分成百分比(0-100)
	7: string expand; //扩展信息
	8: i64 createTime; //创建日期
	9: i64 updateTime; //更新日期
}

struct TingBindReq
{
    1:TAppId appid;
    2:list<i64> uid_list; // 主播UID
}

struct TingBindResp
{
    1:CommonRet ret;
    2:map<i64, TTingBind> ret_map;
}

service FtsBaseInfoBridgeService {
    // 测试链接
    void ping();

   // 批量查询用户资料
   UserInfoResp QueryUserInfo(1:UserInfoReq req);

   // 批量查询主持签约信息
   CompereSignResp QueryCompereSign(1:CompereSignReq req);

   // 主持在线信息
   CompereOnlineResp QueryCompereOnline(1:CompereOnlineReq req);

   // 查询频道信息
   ChannelInfoResp QueryChannelInfo(1:ChannelInfoReq req);

   // 查询在线频道
   OnlineChannelResp QueryOnlineChannel(1:OnlineChannelReq req);

   // 查询订阅
   SubscribeResp QuerySubscribe(1:SubscribeReq req);

   // 读取业务公会、主播、用户独有信息，通常用于榜单、浮层用户信息展示
   QueryMemberResponse queryMember(1:QueryMemberRequest request);

   // 查询位置信息
   BatchGetLBSInfoRsp BatchGetLBSInfo(1:BatchGetLBSInfoReq req);
   
   // 查询乱斗信息
   ChannelFightRsp QueryChannelFight(1:ChannelFightReq req);

   // 查询所有帽子主持
   HatCompereInfoResp QueryHatCompere(1:HatCompereInfoReq req);

   // 查询厅信息
   TingBindResp QueryTingBindByAnchorUID(1:TingBindReq req);

  /**
   * 批量查询厅
   * @param appid 业务ID
   * @param tids 厅id集合
   * @return map<厅id,厅信息>
   */
   map<i64, TTing> batchQueryTingsByTids(1: TAppId appid, 2: list<i64> tids);
}
