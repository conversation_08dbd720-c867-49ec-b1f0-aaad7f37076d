/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_blacklist;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2025-05-28")
public class BlackListItem implements org.apache.thrift.TBase<BlackListItem, BlackListItem._Fields>, java.io.Serializable, Cloneable, Comparable<BlackListItem> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("BlackListItem");

  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField SSID_FIELD_DESC = new org.apache.thrift.protocol.TField("ssid", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField ROOM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roomId", org.apache.thrift.protocol.TType.I64, (short)3);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new BlackListItemStandardSchemeFactory());
    schemes.put(TupleScheme.class, new BlackListItemTupleSchemeFactory());
  }

  public long sid; // required
  public long ssid; // required
  public long roomId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    SID((short)1, "sid"),
    SSID((short)2, "ssid"),
    ROOM_ID((short)3, "roomId");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // SID
          return SID;
        case 2: // SSID
          return SSID;
        case 3: // ROOM_ID
          return ROOM_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __SID_ISSET_ID = 0;
  private static final int __SSID_ISSET_ID = 1;
  private static final int __ROOMID_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SSID, new org.apache.thrift.meta_data.FieldMetaData("ssid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROOM_ID, new org.apache.thrift.meta_data.FieldMetaData("roomId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(BlackListItem.class, metaDataMap);
  }

  public BlackListItem() {
  }

  public BlackListItem(
    long sid,
    long ssid,
    long roomId)
  {
    this();
    this.sid = sid;
    setSidIsSet(true);
    this.ssid = ssid;
    setSsidIsSet(true);
    this.roomId = roomId;
    setRoomIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public BlackListItem(BlackListItem other) {
    __isset_bitfield = other.__isset_bitfield;
    this.sid = other.sid;
    this.ssid = other.ssid;
    this.roomId = other.roomId;
  }

  public BlackListItem deepCopy() {
    return new BlackListItem(this);
  }

  @Override
  public void clear() {
    setSidIsSet(false);
    this.sid = 0;
    setSsidIsSet(false);
    this.ssid = 0;
    setRoomIdIsSet(false);
    this.roomId = 0;
  }

  public long getSid() {
    return this.sid;
  }

  public BlackListItem setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getSsid() {
    return this.ssid;
  }

  public BlackListItem setSsid(long ssid) {
    this.ssid = ssid;
    setSsidIsSet(true);
    return this;
  }

  public void unsetSsid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  /** Returns true if field ssid is set (has been assigned a value) and false otherwise */
  public boolean isSetSsid() {
    return EncodingUtils.testBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  public void setSsidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SSID_ISSET_ID, value);
  }

  public long getRoomId() {
    return this.roomId;
  }

  public BlackListItem setRoomId(long roomId) {
    this.roomId = roomId;
    setRoomIdIsSet(true);
    return this;
  }

  public void unsetRoomId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROOMID_ISSET_ID);
  }

  /** Returns true if field roomId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomId() {
    return EncodingUtils.testBit(__isset_bitfield, __ROOMID_ISSET_ID);
  }

  public void setRoomIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROOMID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case SSID:
      if (value == null) {
        unsetSsid();
      } else {
        setSsid((Long)value);
      }
      break;

    case ROOM_ID:
      if (value == null) {
        unsetRoomId();
      } else {
        setRoomId((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case SID:
      return getSid();

    case SSID:
      return getSsid();

    case ROOM_ID:
      return getRoomId();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case SID:
      return isSetSid();
    case SSID:
      return isSetSsid();
    case ROOM_ID:
      return isSetRoomId();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof BlackListItem)
      return this.equals((BlackListItem)that);
    return false;
  }

  public boolean equals(BlackListItem that) {
    if (that == null)
      return false;

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_ssid = true;
    boolean that_present_ssid = true;
    if (this_present_ssid || that_present_ssid) {
      if (!(this_present_ssid && that_present_ssid))
        return false;
      if (this.ssid != that.ssid)
        return false;
    }

    boolean this_present_roomId = true;
    boolean that_present_roomId = true;
    if (this_present_roomId || that_present_roomId) {
      if (!(this_present_roomId && that_present_roomId))
        return false;
      if (this.roomId != that.roomId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_ssid = true;
    list.add(present_ssid);
    if (present_ssid)
      list.add(ssid);

    boolean present_roomId = true;
    list.add(present_roomId);
    if (present_roomId)
      list.add(roomId);

    return list.hashCode();
  }

  @Override
  public int compareTo(BlackListItem other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSsid()).compareTo(other.isSetSsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssid, other.ssid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoomId()).compareTo(other.isSetRoomId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomId, other.roomId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("BlackListItem(");
    boolean first = true;

    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ssid:");
    sb.append(this.ssid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roomId:");
    sb.append(this.roomId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class BlackListItemStandardSchemeFactory implements SchemeFactory {
    public BlackListItemStandardScheme getScheme() {
      return new BlackListItemStandardScheme();
    }
  }

  private static class BlackListItemStandardScheme extends StandardScheme<BlackListItem> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, BlackListItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SSID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ssid = iprot.readI64();
              struct.setSsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ROOM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roomId = iprot.readI64();
              struct.setRoomIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, BlackListItem struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SSID_FIELD_DESC);
      oprot.writeI64(struct.ssid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ROOM_ID_FIELD_DESC);
      oprot.writeI64(struct.roomId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class BlackListItemTupleSchemeFactory implements SchemeFactory {
    public BlackListItemTupleScheme getScheme() {
      return new BlackListItemTupleScheme();
    }
  }

  private static class BlackListItemTupleScheme extends TupleScheme<BlackListItem> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, BlackListItem struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetSid()) {
        optionals.set(0);
      }
      if (struct.isSetSsid()) {
        optionals.set(1);
      }
      if (struct.isSetRoomId()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetSsid()) {
        oprot.writeI64(struct.ssid);
      }
      if (struct.isSetRoomId()) {
        oprot.writeI64(struct.roomId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, BlackListItem struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.ssid = iprot.readI64();
        struct.setSsidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.roomId = iprot.readI64();
        struct.setRoomIdIsSet(true);
      }
    }
  }

}

