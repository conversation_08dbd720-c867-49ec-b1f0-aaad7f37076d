/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class UpdateRankingRequest implements org.apache.thrift.TBase<UpdateRankingRequest, UpdateRankingRequest._Fields>, java.io.Serializable, Cloneable, Comparable<UpdateRankingRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("UpdateRankingRequest");

  private static final org.apache.thrift.protocol.TField BUSI_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("busiId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField SEQ_FIELD_DESC = new org.apache.thrift.protocol.TField("seq", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField ACTORS_FIELD_DESC = new org.apache.thrift.protocol.TField("actors", org.apache.thrift.protocol.TType.MAP, (short)4);
  private static final org.apache.thrift.protocol.TField ITEM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("itemId", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("count", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("score", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField TIMESTAMP_FIELD_DESC = new org.apache.thrift.protocol.TField("timestamp", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField ROLE_COUNTS_FIELD_DESC = new org.apache.thrift.protocol.TField("roleCounts", org.apache.thrift.protocol.TType.MAP, (short)9);
  private static final org.apache.thrift.protocol.TField ROLE_SCORES_FIELD_DESC = new org.apache.thrift.protocol.TField("roleScores", org.apache.thrift.protocol.TType.MAP, (short)10);
  private static final org.apache.thrift.protocol.TField RANK_COUNTS_FIELD_DESC = new org.apache.thrift.protocol.TField("rankCounts", org.apache.thrift.protocol.TType.MAP, (short)11);
  private static final org.apache.thrift.protocol.TField RANK_SCORES_FIELD_DESC = new org.apache.thrift.protocol.TField("rankScores", org.apache.thrift.protocol.TType.MAP, (short)12);
  private static final org.apache.thrift.protocol.TField IP_FIELD_DESC = new org.apache.thrift.protocol.TField("ip", org.apache.thrift.protocol.TType.STRING, (short)96);
  private static final org.apache.thrift.protocol.TField MAC_FIELD_DESC = new org.apache.thrift.protocol.TField("mac", org.apache.thrift.protocol.TType.STRING, (short)97);
  private static final org.apache.thrift.protocol.TField EXT_LONG_FIELD_DESC = new org.apache.thrift.protocol.TField("extLong", org.apache.thrift.protocol.TType.I64, (short)98);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);
  private static final org.apache.thrift.protocol.TField SIGN_FIELD_DESC = new org.apache.thrift.protocol.TField("sign", org.apache.thrift.protocol.TType.STRING, (short)100);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new UpdateRankingRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new UpdateRankingRequestTupleSchemeFactory());
  }

  public long busiId; // required
  public long actId; // required
  public String seq; // required
  public Map<Long,String> actors; // required
  public String itemId; // required
  public long count; // required
  public long score; // required
  public long timestamp; // required
  public Map<String,Long> roleCounts; // required
  public Map<String,Long> roleScores; // required
  public Map<Long,Long> rankCounts; // required
  public Map<Long,Long> rankScores; // required
  public String ip; // required
  public String mac; // required
  public long extLong; // required
  public Map<String,String> extData; // required
  public String sign; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    BUSI_ID((short)1, "busiId"),
    ACT_ID((short)2, "actId"),
    SEQ((short)3, "seq"),
    ACTORS((short)4, "actors"),
    ITEM_ID((short)5, "itemId"),
    COUNT((short)6, "count"),
    SCORE((short)7, "score"),
    TIMESTAMP((short)8, "timestamp"),
    ROLE_COUNTS((short)9, "roleCounts"),
    ROLE_SCORES((short)10, "roleScores"),
    RANK_COUNTS((short)11, "rankCounts"),
    RANK_SCORES((short)12, "rankScores"),
    IP((short)96, "ip"),
    MAC((short)97, "mac"),
    EXT_LONG((short)98, "extLong"),
    EXT_DATA((short)99, "extData"),
    SIGN((short)100, "sign");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BUSI_ID
          return BUSI_ID;
        case 2: // ACT_ID
          return ACT_ID;
        case 3: // SEQ
          return SEQ;
        case 4: // ACTORS
          return ACTORS;
        case 5: // ITEM_ID
          return ITEM_ID;
        case 6: // COUNT
          return COUNT;
        case 7: // SCORE
          return SCORE;
        case 8: // TIMESTAMP
          return TIMESTAMP;
        case 9: // ROLE_COUNTS
          return ROLE_COUNTS;
        case 10: // ROLE_SCORES
          return ROLE_SCORES;
        case 11: // RANK_COUNTS
          return RANK_COUNTS;
        case 12: // RANK_SCORES
          return RANK_SCORES;
        case 96: // IP
          return IP;
        case 97: // MAC
          return MAC;
        case 98: // EXT_LONG
          return EXT_LONG;
        case 99: // EXT_DATA
          return EXT_DATA;
        case 100: // SIGN
          return SIGN;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BUSIID_ISSET_ID = 0;
  private static final int __ACTID_ISSET_ID = 1;
  private static final int __COUNT_ISSET_ID = 2;
  private static final int __SCORE_ISSET_ID = 3;
  private static final int __TIMESTAMP_ISSET_ID = 4;
  private static final int __EXTLONG_ISSET_ID = 5;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BUSI_ID, new org.apache.thrift.meta_data.FieldMetaData("busiId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SEQ, new org.apache.thrift.meta_data.FieldMetaData("seq", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ACTORS, new org.apache.thrift.meta_data.FieldMetaData("actors", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.ITEM_ID, new org.apache.thrift.meta_data.FieldMetaData("itemId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COUNT, new org.apache.thrift.meta_data.FieldMetaData("count", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SCORE, new org.apache.thrift.meta_data.FieldMetaData("score", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TIMESTAMP, new org.apache.thrift.meta_data.FieldMetaData("timestamp", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROLE_COUNTS, new org.apache.thrift.meta_data.FieldMetaData("roleCounts", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.ROLE_SCORES, new org.apache.thrift.meta_data.FieldMetaData("roleScores", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.RANK_COUNTS, new org.apache.thrift.meta_data.FieldMetaData("rankCounts", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.RANK_SCORES, new org.apache.thrift.meta_data.FieldMetaData("rankScores", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.IP, new org.apache.thrift.meta_data.FieldMetaData("ip", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MAC, new org.apache.thrift.meta_data.FieldMetaData("mac", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_LONG, new org.apache.thrift.meta_data.FieldMetaData("extLong", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.SIGN, new org.apache.thrift.meta_data.FieldMetaData("sign", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(UpdateRankingRequest.class, metaDataMap);
  }

  public UpdateRankingRequest() {
  }

  public UpdateRankingRequest(
    long busiId,
    long actId,
    String seq,
    Map<Long,String> actors,
    String itemId,
    long count,
    long score,
    long timestamp,
    Map<String,Long> roleCounts,
    Map<String,Long> roleScores,
    Map<Long,Long> rankCounts,
    Map<Long,Long> rankScores,
    String ip,
    String mac,
    long extLong,
    Map<String,String> extData,
    String sign)
  {
    this();
    this.busiId = busiId;
    setBusiIdIsSet(true);
    this.actId = actId;
    setActIdIsSet(true);
    this.seq = seq;
    this.actors = actors;
    this.itemId = itemId;
    this.count = count;
    setCountIsSet(true);
    this.score = score;
    setScoreIsSet(true);
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    this.roleCounts = roleCounts;
    this.roleScores = roleScores;
    this.rankCounts = rankCounts;
    this.rankScores = rankScores;
    this.ip = ip;
    this.mac = mac;
    this.extLong = extLong;
    setExtLongIsSet(true);
    this.extData = extData;
    this.sign = sign;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public UpdateRankingRequest(UpdateRankingRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.busiId = other.busiId;
    this.actId = other.actId;
    if (other.isSetSeq()) {
      this.seq = other.seq;
    }
    if (other.isSetActors()) {
      Map<Long,String> __this__actors = new HashMap<Long,String>(other.actors);
      this.actors = __this__actors;
    }
    if (other.isSetItemId()) {
      this.itemId = other.itemId;
    }
    this.count = other.count;
    this.score = other.score;
    this.timestamp = other.timestamp;
    if (other.isSetRoleCounts()) {
      Map<String,Long> __this__roleCounts = new HashMap<String,Long>(other.roleCounts);
      this.roleCounts = __this__roleCounts;
    }
    if (other.isSetRoleScores()) {
      Map<String,Long> __this__roleScores = new HashMap<String,Long>(other.roleScores);
      this.roleScores = __this__roleScores;
    }
    if (other.isSetRankCounts()) {
      Map<Long,Long> __this__rankCounts = new HashMap<Long,Long>(other.rankCounts);
      this.rankCounts = __this__rankCounts;
    }
    if (other.isSetRankScores()) {
      Map<Long,Long> __this__rankScores = new HashMap<Long,Long>(other.rankScores);
      this.rankScores = __this__rankScores;
    }
    if (other.isSetIp()) {
      this.ip = other.ip;
    }
    if (other.isSetMac()) {
      this.mac = other.mac;
    }
    this.extLong = other.extLong;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
    if (other.isSetSign()) {
      this.sign = other.sign;
    }
  }

  public UpdateRankingRequest deepCopy() {
    return new UpdateRankingRequest(this);
  }

  @Override
  public void clear() {
    setBusiIdIsSet(false);
    this.busiId = 0;
    setActIdIsSet(false);
    this.actId = 0;
    this.seq = null;
    this.actors = null;
    this.itemId = null;
    setCountIsSet(false);
    this.count = 0;
    setScoreIsSet(false);
    this.score = 0;
    setTimestampIsSet(false);
    this.timestamp = 0;
    this.roleCounts = null;
    this.roleScores = null;
    this.rankCounts = null;
    this.rankScores = null;
    this.ip = null;
    this.mac = null;
    setExtLongIsSet(false);
    this.extLong = 0;
    this.extData = null;
    this.sign = null;
  }

  public long getBusiId() {
    return this.busiId;
  }

  public UpdateRankingRequest setBusiId(long busiId) {
    this.busiId = busiId;
    setBusiIdIsSet(true);
    return this;
  }

  public void unsetBusiId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __BUSIID_ISSET_ID);
  }

  /** Returns true if field busiId is set (has been assigned a value) and false otherwise */
  public boolean isSetBusiId() {
    return EncodingUtils.testBit(__isset_bitfield, __BUSIID_ISSET_ID);
  }

  public void setBusiIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __BUSIID_ISSET_ID, value);
  }

  public long getActId() {
    return this.actId;
  }

  public UpdateRankingRequest setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public String getSeq() {
    return this.seq;
  }

  public UpdateRankingRequest setSeq(String seq) {
    this.seq = seq;
    return this;
  }

  public void unsetSeq() {
    this.seq = null;
  }

  /** Returns true if field seq is set (has been assigned a value) and false otherwise */
  public boolean isSetSeq() {
    return this.seq != null;
  }

  public void setSeqIsSet(boolean value) {
    if (!value) {
      this.seq = null;
    }
  }

  public int getActorsSize() {
    return (this.actors == null) ? 0 : this.actors.size();
  }

  public void putToActors(long key, String val) {
    if (this.actors == null) {
      this.actors = new HashMap<Long,String>();
    }
    this.actors.put(key, val);
  }

  public Map<Long,String> getActors() {
    return this.actors;
  }

  public UpdateRankingRequest setActors(Map<Long,String> actors) {
    this.actors = actors;
    return this;
  }

  public void unsetActors() {
    this.actors = null;
  }

  /** Returns true if field actors is set (has been assigned a value) and false otherwise */
  public boolean isSetActors() {
    return this.actors != null;
  }

  public void setActorsIsSet(boolean value) {
    if (!value) {
      this.actors = null;
    }
  }

  public String getItemId() {
    return this.itemId;
  }

  public UpdateRankingRequest setItemId(String itemId) {
    this.itemId = itemId;
    return this;
  }

  public void unsetItemId() {
    this.itemId = null;
  }

  /** Returns true if field itemId is set (has been assigned a value) and false otherwise */
  public boolean isSetItemId() {
    return this.itemId != null;
  }

  public void setItemIdIsSet(boolean value) {
    if (!value) {
      this.itemId = null;
    }
  }

  public long getCount() {
    return this.count;
  }

  public UpdateRankingRequest setCount(long count) {
    this.count = count;
    setCountIsSet(true);
    return this;
  }

  public void unsetCount() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUNT_ISSET_ID);
  }

  /** Returns true if field count is set (has been assigned a value) and false otherwise */
  public boolean isSetCount() {
    return EncodingUtils.testBit(__isset_bitfield, __COUNT_ISSET_ID);
  }

  public void setCountIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUNT_ISSET_ID, value);
  }

  public long getScore() {
    return this.score;
  }

  public UpdateRankingRequest setScore(long score) {
    this.score = score;
    setScoreIsSet(true);
    return this;
  }

  public void unsetScore() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  /** Returns true if field score is set (has been assigned a value) and false otherwise */
  public boolean isSetScore() {
    return EncodingUtils.testBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  public void setScoreIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SCORE_ISSET_ID, value);
  }

  public long getTimestamp() {
    return this.timestamp;
  }

  public UpdateRankingRequest setTimestamp(long timestamp) {
    this.timestamp = timestamp;
    setTimestampIsSet(true);
    return this;
  }

  public void unsetTimestamp() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  /** Returns true if field timestamp is set (has been assigned a value) and false otherwise */
  public boolean isSetTimestamp() {
    return EncodingUtils.testBit(__isset_bitfield, __TIMESTAMP_ISSET_ID);
  }

  public void setTimestampIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TIMESTAMP_ISSET_ID, value);
  }

  public int getRoleCountsSize() {
    return (this.roleCounts == null) ? 0 : this.roleCounts.size();
  }

  public void putToRoleCounts(String key, long val) {
    if (this.roleCounts == null) {
      this.roleCounts = new HashMap<String,Long>();
    }
    this.roleCounts.put(key, val);
  }

  public Map<String,Long> getRoleCounts() {
    return this.roleCounts;
  }

  public UpdateRankingRequest setRoleCounts(Map<String,Long> roleCounts) {
    this.roleCounts = roleCounts;
    return this;
  }

  public void unsetRoleCounts() {
    this.roleCounts = null;
  }

  /** Returns true if field roleCounts is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleCounts() {
    return this.roleCounts != null;
  }

  public void setRoleCountsIsSet(boolean value) {
    if (!value) {
      this.roleCounts = null;
    }
  }

  public int getRoleScoresSize() {
    return (this.roleScores == null) ? 0 : this.roleScores.size();
  }

  public void putToRoleScores(String key, long val) {
    if (this.roleScores == null) {
      this.roleScores = new HashMap<String,Long>();
    }
    this.roleScores.put(key, val);
  }

  public Map<String,Long> getRoleScores() {
    return this.roleScores;
  }

  public UpdateRankingRequest setRoleScores(Map<String,Long> roleScores) {
    this.roleScores = roleScores;
    return this;
  }

  public void unsetRoleScores() {
    this.roleScores = null;
  }

  /** Returns true if field roleScores is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleScores() {
    return this.roleScores != null;
  }

  public void setRoleScoresIsSet(boolean value) {
    if (!value) {
      this.roleScores = null;
    }
  }

  public int getRankCountsSize() {
    return (this.rankCounts == null) ? 0 : this.rankCounts.size();
  }

  public void putToRankCounts(long key, long val) {
    if (this.rankCounts == null) {
      this.rankCounts = new HashMap<Long,Long>();
    }
    this.rankCounts.put(key, val);
  }

  public Map<Long,Long> getRankCounts() {
    return this.rankCounts;
  }

  public UpdateRankingRequest setRankCounts(Map<Long,Long> rankCounts) {
    this.rankCounts = rankCounts;
    return this;
  }

  public void unsetRankCounts() {
    this.rankCounts = null;
  }

  /** Returns true if field rankCounts is set (has been assigned a value) and false otherwise */
  public boolean isSetRankCounts() {
    return this.rankCounts != null;
  }

  public void setRankCountsIsSet(boolean value) {
    if (!value) {
      this.rankCounts = null;
    }
  }

  public int getRankScoresSize() {
    return (this.rankScores == null) ? 0 : this.rankScores.size();
  }

  public void putToRankScores(long key, long val) {
    if (this.rankScores == null) {
      this.rankScores = new HashMap<Long,Long>();
    }
    this.rankScores.put(key, val);
  }

  public Map<Long,Long> getRankScores() {
    return this.rankScores;
  }

  public UpdateRankingRequest setRankScores(Map<Long,Long> rankScores) {
    this.rankScores = rankScores;
    return this;
  }

  public void unsetRankScores() {
    this.rankScores = null;
  }

  /** Returns true if field rankScores is set (has been assigned a value) and false otherwise */
  public boolean isSetRankScores() {
    return this.rankScores != null;
  }

  public void setRankScoresIsSet(boolean value) {
    if (!value) {
      this.rankScores = null;
    }
  }

  public String getIp() {
    return this.ip;
  }

  public UpdateRankingRequest setIp(String ip) {
    this.ip = ip;
    return this;
  }

  public void unsetIp() {
    this.ip = null;
  }

  /** Returns true if field ip is set (has been assigned a value) and false otherwise */
  public boolean isSetIp() {
    return this.ip != null;
  }

  public void setIpIsSet(boolean value) {
    if (!value) {
      this.ip = null;
    }
  }

  public String getMac() {
    return this.mac;
  }

  public UpdateRankingRequest setMac(String mac) {
    this.mac = mac;
    return this;
  }

  public void unsetMac() {
    this.mac = null;
  }

  /** Returns true if field mac is set (has been assigned a value) and false otherwise */
  public boolean isSetMac() {
    return this.mac != null;
  }

  public void setMacIsSet(boolean value) {
    if (!value) {
      this.mac = null;
    }
  }

  public long getExtLong() {
    return this.extLong;
  }

  public UpdateRankingRequest setExtLong(long extLong) {
    this.extLong = extLong;
    setExtLongIsSet(true);
    return this;
  }

  public void unsetExtLong() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __EXTLONG_ISSET_ID);
  }

  /** Returns true if field extLong is set (has been assigned a value) and false otherwise */
  public boolean isSetExtLong() {
    return EncodingUtils.testBit(__isset_bitfield, __EXTLONG_ISSET_ID);
  }

  public void setExtLongIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __EXTLONG_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public UpdateRankingRequest setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public String getSign() {
    return this.sign;
  }

  public UpdateRankingRequest setSign(String sign) {
    this.sign = sign;
    return this;
  }

  public void unsetSign() {
    this.sign = null;
  }

  /** Returns true if field sign is set (has been assigned a value) and false otherwise */
  public boolean isSetSign() {
    return this.sign != null;
  }

  public void setSignIsSet(boolean value) {
    if (!value) {
      this.sign = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case BUSI_ID:
      if (value == null) {
        unsetBusiId();
      } else {
        setBusiId((Long)value);
      }
      break;

    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case SEQ:
      if (value == null) {
        unsetSeq();
      } else {
        setSeq((String)value);
      }
      break;

    case ACTORS:
      if (value == null) {
        unsetActors();
      } else {
        setActors((Map<Long,String>)value);
      }
      break;

    case ITEM_ID:
      if (value == null) {
        unsetItemId();
      } else {
        setItemId((String)value);
      }
      break;

    case COUNT:
      if (value == null) {
        unsetCount();
      } else {
        setCount((Long)value);
      }
      break;

    case SCORE:
      if (value == null) {
        unsetScore();
      } else {
        setScore((Long)value);
      }
      break;

    case TIMESTAMP:
      if (value == null) {
        unsetTimestamp();
      } else {
        setTimestamp((Long)value);
      }
      break;

    case ROLE_COUNTS:
      if (value == null) {
        unsetRoleCounts();
      } else {
        setRoleCounts((Map<String,Long>)value);
      }
      break;

    case ROLE_SCORES:
      if (value == null) {
        unsetRoleScores();
      } else {
        setRoleScores((Map<String,Long>)value);
      }
      break;

    case RANK_COUNTS:
      if (value == null) {
        unsetRankCounts();
      } else {
        setRankCounts((Map<Long,Long>)value);
      }
      break;

    case RANK_SCORES:
      if (value == null) {
        unsetRankScores();
      } else {
        setRankScores((Map<Long,Long>)value);
      }
      break;

    case IP:
      if (value == null) {
        unsetIp();
      } else {
        setIp((String)value);
      }
      break;

    case MAC:
      if (value == null) {
        unsetMac();
      } else {
        setMac((String)value);
      }
      break;

    case EXT_LONG:
      if (value == null) {
        unsetExtLong();
      } else {
        setExtLong((Long)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    case SIGN:
      if (value == null) {
        unsetSign();
      } else {
        setSign((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case BUSI_ID:
      return getBusiId();

    case ACT_ID:
      return getActId();

    case SEQ:
      return getSeq();

    case ACTORS:
      return getActors();

    case ITEM_ID:
      return getItemId();

    case COUNT:
      return getCount();

    case SCORE:
      return getScore();

    case TIMESTAMP:
      return getTimestamp();

    case ROLE_COUNTS:
      return getRoleCounts();

    case ROLE_SCORES:
      return getRoleScores();

    case RANK_COUNTS:
      return getRankCounts();

    case RANK_SCORES:
      return getRankScores();

    case IP:
      return getIp();

    case MAC:
      return getMac();

    case EXT_LONG:
      return getExtLong();

    case EXT_DATA:
      return getExtData();

    case SIGN:
      return getSign();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case BUSI_ID:
      return isSetBusiId();
    case ACT_ID:
      return isSetActId();
    case SEQ:
      return isSetSeq();
    case ACTORS:
      return isSetActors();
    case ITEM_ID:
      return isSetItemId();
    case COUNT:
      return isSetCount();
    case SCORE:
      return isSetScore();
    case TIMESTAMP:
      return isSetTimestamp();
    case ROLE_COUNTS:
      return isSetRoleCounts();
    case ROLE_SCORES:
      return isSetRoleScores();
    case RANK_COUNTS:
      return isSetRankCounts();
    case RANK_SCORES:
      return isSetRankScores();
    case IP:
      return isSetIp();
    case MAC:
      return isSetMac();
    case EXT_LONG:
      return isSetExtLong();
    case EXT_DATA:
      return isSetExtData();
    case SIGN:
      return isSetSign();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof UpdateRankingRequest)
      return this.equals((UpdateRankingRequest)that);
    return false;
  }

  public boolean equals(UpdateRankingRequest that) {
    if (that == null)
      return false;

    boolean this_present_busiId = true;
    boolean that_present_busiId = true;
    if (this_present_busiId || that_present_busiId) {
      if (!(this_present_busiId && that_present_busiId))
        return false;
      if (this.busiId != that.busiId)
        return false;
    }

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_seq = true && this.isSetSeq();
    boolean that_present_seq = true && that.isSetSeq();
    if (this_present_seq || that_present_seq) {
      if (!(this_present_seq && that_present_seq))
        return false;
      if (!this.seq.equals(that.seq))
        return false;
    }

    boolean this_present_actors = true && this.isSetActors();
    boolean that_present_actors = true && that.isSetActors();
    if (this_present_actors || that_present_actors) {
      if (!(this_present_actors && that_present_actors))
        return false;
      if (!this.actors.equals(that.actors))
        return false;
    }

    boolean this_present_itemId = true && this.isSetItemId();
    boolean that_present_itemId = true && that.isSetItemId();
    if (this_present_itemId || that_present_itemId) {
      if (!(this_present_itemId && that_present_itemId))
        return false;
      if (!this.itemId.equals(that.itemId))
        return false;
    }

    boolean this_present_count = true;
    boolean that_present_count = true;
    if (this_present_count || that_present_count) {
      if (!(this_present_count && that_present_count))
        return false;
      if (this.count != that.count)
        return false;
    }

    boolean this_present_score = true;
    boolean that_present_score = true;
    if (this_present_score || that_present_score) {
      if (!(this_present_score && that_present_score))
        return false;
      if (this.score != that.score)
        return false;
    }

    boolean this_present_timestamp = true;
    boolean that_present_timestamp = true;
    if (this_present_timestamp || that_present_timestamp) {
      if (!(this_present_timestamp && that_present_timestamp))
        return false;
      if (this.timestamp != that.timestamp)
        return false;
    }

    boolean this_present_roleCounts = true && this.isSetRoleCounts();
    boolean that_present_roleCounts = true && that.isSetRoleCounts();
    if (this_present_roleCounts || that_present_roleCounts) {
      if (!(this_present_roleCounts && that_present_roleCounts))
        return false;
      if (!this.roleCounts.equals(that.roleCounts))
        return false;
    }

    boolean this_present_roleScores = true && this.isSetRoleScores();
    boolean that_present_roleScores = true && that.isSetRoleScores();
    if (this_present_roleScores || that_present_roleScores) {
      if (!(this_present_roleScores && that_present_roleScores))
        return false;
      if (!this.roleScores.equals(that.roleScores))
        return false;
    }

    boolean this_present_rankCounts = true && this.isSetRankCounts();
    boolean that_present_rankCounts = true && that.isSetRankCounts();
    if (this_present_rankCounts || that_present_rankCounts) {
      if (!(this_present_rankCounts && that_present_rankCounts))
        return false;
      if (!this.rankCounts.equals(that.rankCounts))
        return false;
    }

    boolean this_present_rankScores = true && this.isSetRankScores();
    boolean that_present_rankScores = true && that.isSetRankScores();
    if (this_present_rankScores || that_present_rankScores) {
      if (!(this_present_rankScores && that_present_rankScores))
        return false;
      if (!this.rankScores.equals(that.rankScores))
        return false;
    }

    boolean this_present_ip = true && this.isSetIp();
    boolean that_present_ip = true && that.isSetIp();
    if (this_present_ip || that_present_ip) {
      if (!(this_present_ip && that_present_ip))
        return false;
      if (!this.ip.equals(that.ip))
        return false;
    }

    boolean this_present_mac = true && this.isSetMac();
    boolean that_present_mac = true && that.isSetMac();
    if (this_present_mac || that_present_mac) {
      if (!(this_present_mac && that_present_mac))
        return false;
      if (!this.mac.equals(that.mac))
        return false;
    }

    boolean this_present_extLong = true;
    boolean that_present_extLong = true;
    if (this_present_extLong || that_present_extLong) {
      if (!(this_present_extLong && that_present_extLong))
        return false;
      if (this.extLong != that.extLong)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    boolean this_present_sign = true && this.isSetSign();
    boolean that_present_sign = true && that.isSetSign();
    if (this_present_sign || that_present_sign) {
      if (!(this_present_sign && that_present_sign))
        return false;
      if (!this.sign.equals(that.sign))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_busiId = true;
    list.add(present_busiId);
    if (present_busiId)
      list.add(busiId);

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_seq = true && (isSetSeq());
    list.add(present_seq);
    if (present_seq)
      list.add(seq);

    boolean present_actors = true && (isSetActors());
    list.add(present_actors);
    if (present_actors)
      list.add(actors);

    boolean present_itemId = true && (isSetItemId());
    list.add(present_itemId);
    if (present_itemId)
      list.add(itemId);

    boolean present_count = true;
    list.add(present_count);
    if (present_count)
      list.add(count);

    boolean present_score = true;
    list.add(present_score);
    if (present_score)
      list.add(score);

    boolean present_timestamp = true;
    list.add(present_timestamp);
    if (present_timestamp)
      list.add(timestamp);

    boolean present_roleCounts = true && (isSetRoleCounts());
    list.add(present_roleCounts);
    if (present_roleCounts)
      list.add(roleCounts);

    boolean present_roleScores = true && (isSetRoleScores());
    list.add(present_roleScores);
    if (present_roleScores)
      list.add(roleScores);

    boolean present_rankCounts = true && (isSetRankCounts());
    list.add(present_rankCounts);
    if (present_rankCounts)
      list.add(rankCounts);

    boolean present_rankScores = true && (isSetRankScores());
    list.add(present_rankScores);
    if (present_rankScores)
      list.add(rankScores);

    boolean present_ip = true && (isSetIp());
    list.add(present_ip);
    if (present_ip)
      list.add(ip);

    boolean present_mac = true && (isSetMac());
    list.add(present_mac);
    if (present_mac)
      list.add(mac);

    boolean present_extLong = true;
    list.add(present_extLong);
    if (present_extLong)
      list.add(extLong);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    boolean present_sign = true && (isSetSign());
    list.add(present_sign);
    if (present_sign)
      list.add(sign);

    return list.hashCode();
  }

  @Override
  public int compareTo(UpdateRankingRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetBusiId()).compareTo(other.isSetBusiId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBusiId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.busiId, other.busiId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSeq()).compareTo(other.isSetSeq());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeq()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.seq, other.seq);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActors()).compareTo(other.isSetActors());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActors()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actors, other.actors);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetItemId()).compareTo(other.isSetItemId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetItemId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.itemId, other.itemId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCount()).compareTo(other.isSetCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.count, other.count);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetScore()).compareTo(other.isSetScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.score, other.score);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimestamp()).compareTo(other.isSetTimestamp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimestamp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timestamp, other.timestamp);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleCounts()).compareTo(other.isSetRoleCounts());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleCounts()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleCounts, other.roleCounts);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleScores()).compareTo(other.isSetRoleScores());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleScores()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleScores, other.roleScores);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankCounts()).compareTo(other.isSetRankCounts());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankCounts()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankCounts, other.rankCounts);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankScores()).compareTo(other.isSetRankScores());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankScores()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankScores, other.rankScores);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetIp()).compareTo(other.isSetIp());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetIp()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ip, other.ip);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMac()).compareTo(other.isSetMac());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMac()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mac, other.mac);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtLong()).compareTo(other.isSetExtLong());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtLong()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extLong, other.extLong);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSign()).compareTo(other.isSetSign());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSign()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sign, other.sign);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("UpdateRankingRequest(");
    boolean first = true;

    sb.append("busiId:");
    sb.append(this.busiId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("seq:");
    if (this.seq == null) {
      sb.append("null");
    } else {
      sb.append(this.seq);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("actors:");
    if (this.actors == null) {
      sb.append("null");
    } else {
      sb.append(this.actors);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("itemId:");
    if (this.itemId == null) {
      sb.append("null");
    } else {
      sb.append(this.itemId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("count:");
    sb.append(this.count);
    first = false;
    if (!first) sb.append(", ");
    sb.append("score:");
    sb.append(this.score);
    first = false;
    if (!first) sb.append(", ");
    sb.append("timestamp:");
    sb.append(this.timestamp);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleCounts:");
    if (this.roleCounts == null) {
      sb.append("null");
    } else {
      sb.append(this.roleCounts);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleScores:");
    if (this.roleScores == null) {
      sb.append("null");
    } else {
      sb.append(this.roleScores);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankCounts:");
    if (this.rankCounts == null) {
      sb.append("null");
    } else {
      sb.append(this.rankCounts);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankScores:");
    if (this.rankScores == null) {
      sb.append("null");
    } else {
      sb.append(this.rankScores);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ip:");
    if (this.ip == null) {
      sb.append("null");
    } else {
      sb.append(this.ip);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("mac:");
    if (this.mac == null) {
      sb.append("null");
    } else {
      sb.append(this.mac);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extLong:");
    sb.append(this.extLong);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("sign:");
    if (this.sign == null) {
      sb.append("null");
    } else {
      sb.append(this.sign);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class UpdateRankingRequestStandardSchemeFactory implements SchemeFactory {
    public UpdateRankingRequestStandardScheme getScheme() {
      return new UpdateRankingRequestStandardScheme();
    }
  }

  private static class UpdateRankingRequestStandardScheme extends StandardScheme<UpdateRankingRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, UpdateRankingRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BUSI_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.busiId = iprot.readI64();
              struct.setBusiIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SEQ
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.seq = iprot.readString();
              struct.setSeqIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ACTORS
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map222 = iprot.readMapBegin();
                struct.actors = new HashMap<Long,String>(2*_map222.size);
                long _key223;
                String _val224;
                for (int _i225 = 0; _i225 < _map222.size; ++_i225)
                {
                  _key223 = iprot.readI64();
                  _val224 = iprot.readString();
                  struct.actors.put(_key223, _val224);
                }
                iprot.readMapEnd();
              }
              struct.setActorsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ITEM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.itemId = iprot.readString();
              struct.setItemIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.count = iprot.readI64();
              struct.setCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.score = iprot.readI64();
              struct.setScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // TIMESTAMP
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.timestamp = iprot.readI64();
              struct.setTimestampIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // ROLE_COUNTS
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map226 = iprot.readMapBegin();
                struct.roleCounts = new HashMap<String,Long>(2*_map226.size);
                String _key227;
                long _val228;
                for (int _i229 = 0; _i229 < _map226.size; ++_i229)
                {
                  _key227 = iprot.readString();
                  _val228 = iprot.readI64();
                  struct.roleCounts.put(_key227, _val228);
                }
                iprot.readMapEnd();
              }
              struct.setRoleCountsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // ROLE_SCORES
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map230 = iprot.readMapBegin();
                struct.roleScores = new HashMap<String,Long>(2*_map230.size);
                String _key231;
                long _val232;
                for (int _i233 = 0; _i233 < _map230.size; ++_i233)
                {
                  _key231 = iprot.readString();
                  _val232 = iprot.readI64();
                  struct.roleScores.put(_key231, _val232);
                }
                iprot.readMapEnd();
              }
              struct.setRoleScoresIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // RANK_COUNTS
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map234 = iprot.readMapBegin();
                struct.rankCounts = new HashMap<Long,Long>(2*_map234.size);
                long _key235;
                long _val236;
                for (int _i237 = 0; _i237 < _map234.size; ++_i237)
                {
                  _key235 = iprot.readI64();
                  _val236 = iprot.readI64();
                  struct.rankCounts.put(_key235, _val236);
                }
                iprot.readMapEnd();
              }
              struct.setRankCountsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // RANK_SCORES
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map238 = iprot.readMapBegin();
                struct.rankScores = new HashMap<Long,Long>(2*_map238.size);
                long _key239;
                long _val240;
                for (int _i241 = 0; _i241 < _map238.size; ++_i241)
                {
                  _key239 = iprot.readI64();
                  _val240 = iprot.readI64();
                  struct.rankScores.put(_key239, _val240);
                }
                iprot.readMapEnd();
              }
              struct.setRankScoresIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 96: // IP
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ip = iprot.readString();
              struct.setIpIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 97: // MAC
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.mac = iprot.readString();
              struct.setMacIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 98: // EXT_LONG
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.extLong = iprot.readI64();
              struct.setExtLongIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map242 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map242.size);
                String _key243;
                String _val244;
                for (int _i245 = 0; _i245 < _map242.size; ++_i245)
                {
                  _key243 = iprot.readString();
                  _val244 = iprot.readString();
                  struct.extData.put(_key243, _val244);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 100: // SIGN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.sign = iprot.readString();
              struct.setSignIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, UpdateRankingRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(BUSI_ID_FIELD_DESC);
      oprot.writeI64(struct.busiId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      if (struct.seq != null) {
        oprot.writeFieldBegin(SEQ_FIELD_DESC);
        oprot.writeString(struct.seq);
        oprot.writeFieldEnd();
      }
      if (struct.actors != null) {
        oprot.writeFieldBegin(ACTORS_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRING, struct.actors.size()));
          for (Map.Entry<Long, String> _iter246 : struct.actors.entrySet())
          {
            oprot.writeI64(_iter246.getKey());
            oprot.writeString(_iter246.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.itemId != null) {
        oprot.writeFieldBegin(ITEM_ID_FIELD_DESC);
        oprot.writeString(struct.itemId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(COUNT_FIELD_DESC);
      oprot.writeI64(struct.count);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SCORE_FIELD_DESC);
      oprot.writeI64(struct.score);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TIMESTAMP_FIELD_DESC);
      oprot.writeI64(struct.timestamp);
      oprot.writeFieldEnd();
      if (struct.roleCounts != null) {
        oprot.writeFieldBegin(ROLE_COUNTS_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I64, struct.roleCounts.size()));
          for (Map.Entry<String, Long> _iter247 : struct.roleCounts.entrySet())
          {
            oprot.writeString(_iter247.getKey());
            oprot.writeI64(_iter247.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.roleScores != null) {
        oprot.writeFieldBegin(ROLE_SCORES_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I64, struct.roleScores.size()));
          for (Map.Entry<String, Long> _iter248 : struct.roleScores.entrySet())
          {
            oprot.writeString(_iter248.getKey());
            oprot.writeI64(_iter248.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.rankCounts != null) {
        oprot.writeFieldBegin(RANK_COUNTS_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, struct.rankCounts.size()));
          for (Map.Entry<Long, Long> _iter249 : struct.rankCounts.entrySet())
          {
            oprot.writeI64(_iter249.getKey());
            oprot.writeI64(_iter249.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.rankScores != null) {
        oprot.writeFieldBegin(RANK_SCORES_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, struct.rankScores.size()));
          for (Map.Entry<Long, Long> _iter250 : struct.rankScores.entrySet())
          {
            oprot.writeI64(_iter250.getKey());
            oprot.writeI64(_iter250.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.ip != null) {
        oprot.writeFieldBegin(IP_FIELD_DESC);
        oprot.writeString(struct.ip);
        oprot.writeFieldEnd();
      }
      if (struct.mac != null) {
        oprot.writeFieldBegin(MAC_FIELD_DESC);
        oprot.writeString(struct.mac);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(EXT_LONG_FIELD_DESC);
      oprot.writeI64(struct.extLong);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter251 : struct.extData.entrySet())
          {
            oprot.writeString(_iter251.getKey());
            oprot.writeString(_iter251.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.sign != null) {
        oprot.writeFieldBegin(SIGN_FIELD_DESC);
        oprot.writeString(struct.sign);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class UpdateRankingRequestTupleSchemeFactory implements SchemeFactory {
    public UpdateRankingRequestTupleScheme getScheme() {
      return new UpdateRankingRequestTupleScheme();
    }
  }

  private static class UpdateRankingRequestTupleScheme extends TupleScheme<UpdateRankingRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, UpdateRankingRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetBusiId()) {
        optionals.set(0);
      }
      if (struct.isSetActId()) {
        optionals.set(1);
      }
      if (struct.isSetSeq()) {
        optionals.set(2);
      }
      if (struct.isSetActors()) {
        optionals.set(3);
      }
      if (struct.isSetItemId()) {
        optionals.set(4);
      }
      if (struct.isSetCount()) {
        optionals.set(5);
      }
      if (struct.isSetScore()) {
        optionals.set(6);
      }
      if (struct.isSetTimestamp()) {
        optionals.set(7);
      }
      if (struct.isSetRoleCounts()) {
        optionals.set(8);
      }
      if (struct.isSetRoleScores()) {
        optionals.set(9);
      }
      if (struct.isSetRankCounts()) {
        optionals.set(10);
      }
      if (struct.isSetRankScores()) {
        optionals.set(11);
      }
      if (struct.isSetIp()) {
        optionals.set(12);
      }
      if (struct.isSetMac()) {
        optionals.set(13);
      }
      if (struct.isSetExtLong()) {
        optionals.set(14);
      }
      if (struct.isSetExtData()) {
        optionals.set(15);
      }
      if (struct.isSetSign()) {
        optionals.set(16);
      }
      oprot.writeBitSet(optionals, 17);
      if (struct.isSetBusiId()) {
        oprot.writeI64(struct.busiId);
      }
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetSeq()) {
        oprot.writeString(struct.seq);
      }
      if (struct.isSetActors()) {
        {
          oprot.writeI32(struct.actors.size());
          for (Map.Entry<Long, String> _iter252 : struct.actors.entrySet())
          {
            oprot.writeI64(_iter252.getKey());
            oprot.writeString(_iter252.getValue());
          }
        }
      }
      if (struct.isSetItemId()) {
        oprot.writeString(struct.itemId);
      }
      if (struct.isSetCount()) {
        oprot.writeI64(struct.count);
      }
      if (struct.isSetScore()) {
        oprot.writeI64(struct.score);
      }
      if (struct.isSetTimestamp()) {
        oprot.writeI64(struct.timestamp);
      }
      if (struct.isSetRoleCounts()) {
        {
          oprot.writeI32(struct.roleCounts.size());
          for (Map.Entry<String, Long> _iter253 : struct.roleCounts.entrySet())
          {
            oprot.writeString(_iter253.getKey());
            oprot.writeI64(_iter253.getValue());
          }
        }
      }
      if (struct.isSetRoleScores()) {
        {
          oprot.writeI32(struct.roleScores.size());
          for (Map.Entry<String, Long> _iter254 : struct.roleScores.entrySet())
          {
            oprot.writeString(_iter254.getKey());
            oprot.writeI64(_iter254.getValue());
          }
        }
      }
      if (struct.isSetRankCounts()) {
        {
          oprot.writeI32(struct.rankCounts.size());
          for (Map.Entry<Long, Long> _iter255 : struct.rankCounts.entrySet())
          {
            oprot.writeI64(_iter255.getKey());
            oprot.writeI64(_iter255.getValue());
          }
        }
      }
      if (struct.isSetRankScores()) {
        {
          oprot.writeI32(struct.rankScores.size());
          for (Map.Entry<Long, Long> _iter256 : struct.rankScores.entrySet())
          {
            oprot.writeI64(_iter256.getKey());
            oprot.writeI64(_iter256.getValue());
          }
        }
      }
      if (struct.isSetIp()) {
        oprot.writeString(struct.ip);
      }
      if (struct.isSetMac()) {
        oprot.writeString(struct.mac);
      }
      if (struct.isSetExtLong()) {
        oprot.writeI64(struct.extLong);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter257 : struct.extData.entrySet())
          {
            oprot.writeString(_iter257.getKey());
            oprot.writeString(_iter257.getValue());
          }
        }
      }
      if (struct.isSetSign()) {
        oprot.writeString(struct.sign);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, UpdateRankingRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(17);
      if (incoming.get(0)) {
        struct.busiId = iprot.readI64();
        struct.setBusiIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.seq = iprot.readString();
        struct.setSeqIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TMap _map258 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.actors = new HashMap<Long,String>(2*_map258.size);
          long _key259;
          String _val260;
          for (int _i261 = 0; _i261 < _map258.size; ++_i261)
          {
            _key259 = iprot.readI64();
            _val260 = iprot.readString();
            struct.actors.put(_key259, _val260);
          }
        }
        struct.setActorsIsSet(true);
      }
      if (incoming.get(4)) {
        struct.itemId = iprot.readString();
        struct.setItemIdIsSet(true);
      }
      if (incoming.get(5)) {
        struct.count = iprot.readI64();
        struct.setCountIsSet(true);
      }
      if (incoming.get(6)) {
        struct.score = iprot.readI64();
        struct.setScoreIsSet(true);
      }
      if (incoming.get(7)) {
        struct.timestamp = iprot.readI64();
        struct.setTimestampIsSet(true);
      }
      if (incoming.get(8)) {
        {
          org.apache.thrift.protocol.TMap _map262 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.roleCounts = new HashMap<String,Long>(2*_map262.size);
          String _key263;
          long _val264;
          for (int _i265 = 0; _i265 < _map262.size; ++_i265)
          {
            _key263 = iprot.readString();
            _val264 = iprot.readI64();
            struct.roleCounts.put(_key263, _val264);
          }
        }
        struct.setRoleCountsIsSet(true);
      }
      if (incoming.get(9)) {
        {
          org.apache.thrift.protocol.TMap _map266 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.roleScores = new HashMap<String,Long>(2*_map266.size);
          String _key267;
          long _val268;
          for (int _i269 = 0; _i269 < _map266.size; ++_i269)
          {
            _key267 = iprot.readString();
            _val268 = iprot.readI64();
            struct.roleScores.put(_key267, _val268);
          }
        }
        struct.setRoleScoresIsSet(true);
      }
      if (incoming.get(10)) {
        {
          org.apache.thrift.protocol.TMap _map270 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.rankCounts = new HashMap<Long,Long>(2*_map270.size);
          long _key271;
          long _val272;
          for (int _i273 = 0; _i273 < _map270.size; ++_i273)
          {
            _key271 = iprot.readI64();
            _val272 = iprot.readI64();
            struct.rankCounts.put(_key271, _val272);
          }
        }
        struct.setRankCountsIsSet(true);
      }
      if (incoming.get(11)) {
        {
          org.apache.thrift.protocol.TMap _map274 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.rankScores = new HashMap<Long,Long>(2*_map274.size);
          long _key275;
          long _val276;
          for (int _i277 = 0; _i277 < _map274.size; ++_i277)
          {
            _key275 = iprot.readI64();
            _val276 = iprot.readI64();
            struct.rankScores.put(_key275, _val276);
          }
        }
        struct.setRankScoresIsSet(true);
      }
      if (incoming.get(12)) {
        struct.ip = iprot.readString();
        struct.setIpIsSet(true);
      }
      if (incoming.get(13)) {
        struct.mac = iprot.readString();
        struct.setMacIsSet(true);
      }
      if (incoming.get(14)) {
        struct.extLong = iprot.readI64();
        struct.setExtLongIsSet(true);
      }
      if (incoming.get(15)) {
        {
          org.apache.thrift.protocol.TMap _map278 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map278.size);
          String _key279;
          String _val280;
          for (int _i281 = 0; _i281 < _map278.size; ++_i281)
          {
            _key279 = iprot.readString();
            _val280 = iprot.readString();
            struct.extData.put(_key279, _val280);
          }
        }
        struct.setExtDataIsSet(true);
      }
      if (incoming.get(16)) {
        struct.sign = iprot.readString();
        struct.setSignIsSet(true);
      }
    }
  }

}

