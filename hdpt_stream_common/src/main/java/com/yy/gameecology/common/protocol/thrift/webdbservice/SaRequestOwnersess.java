/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 通过用户uid查询用户拥有的频道信息
 * @param     appkey          客户端的标识
 * @param     ownerid         用户uid
 * @param     apptype         频道apptype(默认apptype为空或0,表示不指定apptype)
 * @param     optype          操作类型(默认optype为空或0, 若optype为1表示仅查询用户拥有的频道数量)
 * @columns                   需要查询的字段集合，可以查询如下字段
 *                               - "sid": 频道长位id，
 *                               - "apptype": 频道apptype
 *                               - "name": 频道名字，
 *                               - "create_time", 创建时间
 *                               - "asid": 频道短位id
 * @SaResponseSet              用户拥有的频道信息结果集，如果没有查到结果则dataSet为空
 *                             若optype=1，keyValue中存放<ownerid,频道数量>的结果
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class SaRequestOwnersess implements org.apache.thrift.TBase<SaRequestOwnersess, SaRequestOwnersess._Fields>, java.io.Serializable, Cloneable, Comparable<SaRequestOwnersess> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaRequestOwnersess");

  private static final org.apache.thrift.protocol.TField AUTH_MSG_FIELD_DESC = new org.apache.thrift.protocol.TField("authMsg", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField APPKEY_FIELD_DESC = new org.apache.thrift.protocol.TField("appkey", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField OWNERID_FIELD_DESC = new org.apache.thrift.protocol.TField("ownerid", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField APPTYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("apptype", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField OPTYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("optype", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField COLUMNS_FIELD_DESC = new org.apache.thrift.protocol.TField("columns", org.apache.thrift.protocol.TType.LIST, (short)6);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaRequestOwnersessStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaRequestOwnersessTupleSchemeFactory());
  }

  public AuthorizeMsg authMsg; // required
  public String appkey; // required
  public String ownerid; // required
  public String apptype; // required
  public String optype; // required
  public List<String> columns; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    AUTH_MSG((short)1, "authMsg"),
    APPKEY((short)2, "appkey"),
    OWNERID((short)3, "ownerid"),
    APPTYPE((short)4, "apptype"),
    OPTYPE((short)5, "optype"),
    COLUMNS((short)6, "columns");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // AUTH_MSG
          return AUTH_MSG;
        case 2: // APPKEY
          return APPKEY;
        case 3: // OWNERID
          return OWNERID;
        case 4: // APPTYPE
          return APPTYPE;
        case 5: // OPTYPE
          return OPTYPE;
        case 6: // COLUMNS
          return COLUMNS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.AUTH_MSG, new org.apache.thrift.meta_data.FieldMetaData("authMsg", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AuthorizeMsg.class)));
    tmpMap.put(_Fields.APPKEY, new org.apache.thrift.meta_data.FieldMetaData("appkey", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.OWNERID, new org.apache.thrift.meta_data.FieldMetaData("ownerid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.APPTYPE, new org.apache.thrift.meta_data.FieldMetaData("apptype", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.OPTYPE, new org.apache.thrift.meta_data.FieldMetaData("optype", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COLUMNS, new org.apache.thrift.meta_data.FieldMetaData("columns", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaRequestOwnersess.class, metaDataMap);
  }

  public SaRequestOwnersess() {
  }

  public SaRequestOwnersess(
    AuthorizeMsg authMsg,
    String appkey,
    String ownerid,
    String apptype,
    String optype,
    List<String> columns)
  {
    this();
    this.authMsg = authMsg;
    this.appkey = appkey;
    this.ownerid = ownerid;
    this.apptype = apptype;
    this.optype = optype;
    this.columns = columns;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaRequestOwnersess(SaRequestOwnersess other) {
    if (other.isSetAuthMsg()) {
      this.authMsg = new AuthorizeMsg(other.authMsg);
    }
    if (other.isSetAppkey()) {
      this.appkey = other.appkey;
    }
    if (other.isSetOwnerid()) {
      this.ownerid = other.ownerid;
    }
    if (other.isSetApptype()) {
      this.apptype = other.apptype;
    }
    if (other.isSetOptype()) {
      this.optype = other.optype;
    }
    if (other.isSetColumns()) {
      List<String> __this__columns = new ArrayList<String>(other.columns);
      this.columns = __this__columns;
    }
  }

  public SaRequestOwnersess deepCopy() {
    return new SaRequestOwnersess(this);
  }

  @Override
  public void clear() {
    this.authMsg = null;
    this.appkey = null;
    this.ownerid = null;
    this.apptype = null;
    this.optype = null;
    this.columns = null;
  }

  public AuthorizeMsg getAuthMsg() {
    return this.authMsg;
  }

  public SaRequestOwnersess setAuthMsg(AuthorizeMsg authMsg) {
    this.authMsg = authMsg;
    return this;
  }

  public void unsetAuthMsg() {
    this.authMsg = null;
  }

  /** Returns true if field authMsg is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthMsg() {
    return this.authMsg != null;
  }

  public void setAuthMsgIsSet(boolean value) {
    if (!value) {
      this.authMsg = null;
    }
  }

  public String getAppkey() {
    return this.appkey;
  }

  public SaRequestOwnersess setAppkey(String appkey) {
    this.appkey = appkey;
    return this;
  }

  public void unsetAppkey() {
    this.appkey = null;
  }

  /** Returns true if field appkey is set (has been assigned a value) and false otherwise */
  public boolean isSetAppkey() {
    return this.appkey != null;
  }

  public void setAppkeyIsSet(boolean value) {
    if (!value) {
      this.appkey = null;
    }
  }

  public String getOwnerid() {
    return this.ownerid;
  }

  public SaRequestOwnersess setOwnerid(String ownerid) {
    this.ownerid = ownerid;
    return this;
  }

  public void unsetOwnerid() {
    this.ownerid = null;
  }

  /** Returns true if field ownerid is set (has been assigned a value) and false otherwise */
  public boolean isSetOwnerid() {
    return this.ownerid != null;
  }

  public void setOwneridIsSet(boolean value) {
    if (!value) {
      this.ownerid = null;
    }
  }

  public String getApptype() {
    return this.apptype;
  }

  public SaRequestOwnersess setApptype(String apptype) {
    this.apptype = apptype;
    return this;
  }

  public void unsetApptype() {
    this.apptype = null;
  }

  /** Returns true if field apptype is set (has been assigned a value) and false otherwise */
  public boolean isSetApptype() {
    return this.apptype != null;
  }

  public void setApptypeIsSet(boolean value) {
    if (!value) {
      this.apptype = null;
    }
  }

  public String getOptype() {
    return this.optype;
  }

  public SaRequestOwnersess setOptype(String optype) {
    this.optype = optype;
    return this;
  }

  public void unsetOptype() {
    this.optype = null;
  }

  /** Returns true if field optype is set (has been assigned a value) and false otherwise */
  public boolean isSetOptype() {
    return this.optype != null;
  }

  public void setOptypeIsSet(boolean value) {
    if (!value) {
      this.optype = null;
    }
  }

  public int getColumnsSize() {
    return (this.columns == null) ? 0 : this.columns.size();
  }

  public java.util.Iterator<String> getColumnsIterator() {
    return (this.columns == null) ? null : this.columns.iterator();
  }

  public void addToColumns(String elem) {
    if (this.columns == null) {
      this.columns = new ArrayList<String>();
    }
    this.columns.add(elem);
  }

  public List<String> getColumns() {
    return this.columns;
  }

  public SaRequestOwnersess setColumns(List<String> columns) {
    this.columns = columns;
    return this;
  }

  public void unsetColumns() {
    this.columns = null;
  }

  /** Returns true if field columns is set (has been assigned a value) and false otherwise */
  public boolean isSetColumns() {
    return this.columns != null;
  }

  public void setColumnsIsSet(boolean value) {
    if (!value) {
      this.columns = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case AUTH_MSG:
      if (value == null) {
        unsetAuthMsg();
      } else {
        setAuthMsg((AuthorizeMsg)value);
      }
      break;

    case APPKEY:
      if (value == null) {
        unsetAppkey();
      } else {
        setAppkey((String)value);
      }
      break;

    case OWNERID:
      if (value == null) {
        unsetOwnerid();
      } else {
        setOwnerid((String)value);
      }
      break;

    case APPTYPE:
      if (value == null) {
        unsetApptype();
      } else {
        setApptype((String)value);
      }
      break;

    case OPTYPE:
      if (value == null) {
        unsetOptype();
      } else {
        setOptype((String)value);
      }
      break;

    case COLUMNS:
      if (value == null) {
        unsetColumns();
      } else {
        setColumns((List<String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case AUTH_MSG:
      return getAuthMsg();

    case APPKEY:
      return getAppkey();

    case OWNERID:
      return getOwnerid();

    case APPTYPE:
      return getApptype();

    case OPTYPE:
      return getOptype();

    case COLUMNS:
      return getColumns();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case AUTH_MSG:
      return isSetAuthMsg();
    case APPKEY:
      return isSetAppkey();
    case OWNERID:
      return isSetOwnerid();
    case APPTYPE:
      return isSetApptype();
    case OPTYPE:
      return isSetOptype();
    case COLUMNS:
      return isSetColumns();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaRequestOwnersess)
      return this.equals((SaRequestOwnersess)that);
    return false;
  }

  public boolean equals(SaRequestOwnersess that) {
    if (that == null)
      return false;

    boolean this_present_authMsg = true && this.isSetAuthMsg();
    boolean that_present_authMsg = true && that.isSetAuthMsg();
    if (this_present_authMsg || that_present_authMsg) {
      if (!(this_present_authMsg && that_present_authMsg))
        return false;
      if (!this.authMsg.equals(that.authMsg))
        return false;
    }

    boolean this_present_appkey = true && this.isSetAppkey();
    boolean that_present_appkey = true && that.isSetAppkey();
    if (this_present_appkey || that_present_appkey) {
      if (!(this_present_appkey && that_present_appkey))
        return false;
      if (!this.appkey.equals(that.appkey))
        return false;
    }

    boolean this_present_ownerid = true && this.isSetOwnerid();
    boolean that_present_ownerid = true && that.isSetOwnerid();
    if (this_present_ownerid || that_present_ownerid) {
      if (!(this_present_ownerid && that_present_ownerid))
        return false;
      if (!this.ownerid.equals(that.ownerid))
        return false;
    }

    boolean this_present_apptype = true && this.isSetApptype();
    boolean that_present_apptype = true && that.isSetApptype();
    if (this_present_apptype || that_present_apptype) {
      if (!(this_present_apptype && that_present_apptype))
        return false;
      if (!this.apptype.equals(that.apptype))
        return false;
    }

    boolean this_present_optype = true && this.isSetOptype();
    boolean that_present_optype = true && that.isSetOptype();
    if (this_present_optype || that_present_optype) {
      if (!(this_present_optype && that_present_optype))
        return false;
      if (!this.optype.equals(that.optype))
        return false;
    }

    boolean this_present_columns = true && this.isSetColumns();
    boolean that_present_columns = true && that.isSetColumns();
    if (this_present_columns || that_present_columns) {
      if (!(this_present_columns && that_present_columns))
        return false;
      if (!this.columns.equals(that.columns))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_authMsg = true && (isSetAuthMsg());
    list.add(present_authMsg);
    if (present_authMsg)
      list.add(authMsg);

    boolean present_appkey = true && (isSetAppkey());
    list.add(present_appkey);
    if (present_appkey)
      list.add(appkey);

    boolean present_ownerid = true && (isSetOwnerid());
    list.add(present_ownerid);
    if (present_ownerid)
      list.add(ownerid);

    boolean present_apptype = true && (isSetApptype());
    list.add(present_apptype);
    if (present_apptype)
      list.add(apptype);

    boolean present_optype = true && (isSetOptype());
    list.add(present_optype);
    if (present_optype)
      list.add(optype);

    boolean present_columns = true && (isSetColumns());
    list.add(present_columns);
    if (present_columns)
      list.add(columns);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaRequestOwnersess other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAuthMsg()).compareTo(other.isSetAuthMsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthMsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.authMsg, other.authMsg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppkey()).compareTo(other.isSetAppkey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppkey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appkey, other.appkey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOwnerid()).compareTo(other.isSetOwnerid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOwnerid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ownerid, other.ownerid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetApptype()).compareTo(other.isSetApptype());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetApptype()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.apptype, other.apptype);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOptype()).compareTo(other.isSetOptype());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOptype()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.optype, other.optype);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetColumns()).compareTo(other.isSetColumns());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetColumns()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.columns, other.columns);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaRequestOwnersess(");
    boolean first = true;

    sb.append("authMsg:");
    if (this.authMsg == null) {
      sb.append("null");
    } else {
      sb.append(this.authMsg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appkey:");
    if (this.appkey == null) {
      sb.append("null");
    } else {
      sb.append(this.appkey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ownerid:");
    if (this.ownerid == null) {
      sb.append("null");
    } else {
      sb.append(this.ownerid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("apptype:");
    if (this.apptype == null) {
      sb.append("null");
    } else {
      sb.append(this.apptype);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("optype:");
    if (this.optype == null) {
      sb.append("null");
    } else {
      sb.append(this.optype);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("columns:");
    if (this.columns == null) {
      sb.append("null");
    } else {
      sb.append(this.columns);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (authMsg != null) {
      authMsg.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaRequestOwnersessStandardSchemeFactory implements SchemeFactory {
    public SaRequestOwnersessStandardScheme getScheme() {
      return new SaRequestOwnersessStandardScheme();
    }
  }

  private static class SaRequestOwnersessStandardScheme extends StandardScheme<SaRequestOwnersess> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaRequestOwnersess struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // AUTH_MSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.authMsg = new AuthorizeMsg();
              struct.authMsg.read(iprot);
              struct.setAuthMsgIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPKEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appkey = iprot.readString();
              struct.setAppkeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // OWNERID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.ownerid = iprot.readString();
              struct.setOwneridIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // APPTYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.apptype = iprot.readString();
              struct.setApptypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // OPTYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.optype = iprot.readString();
              struct.setOptypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // COLUMNS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list88 = iprot.readListBegin();
                struct.columns = new ArrayList<String>(_list88.size);
                String _elem89;
                for (int _i90 = 0; _i90 < _list88.size; ++_i90)
                {
                  _elem89 = iprot.readString();
                  struct.columns.add(_elem89);
                }
                iprot.readListEnd();
              }
              struct.setColumnsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaRequestOwnersess struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.authMsg != null) {
        oprot.writeFieldBegin(AUTH_MSG_FIELD_DESC);
        struct.authMsg.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.appkey != null) {
        oprot.writeFieldBegin(APPKEY_FIELD_DESC);
        oprot.writeString(struct.appkey);
        oprot.writeFieldEnd();
      }
      if (struct.ownerid != null) {
        oprot.writeFieldBegin(OWNERID_FIELD_DESC);
        oprot.writeString(struct.ownerid);
        oprot.writeFieldEnd();
      }
      if (struct.apptype != null) {
        oprot.writeFieldBegin(APPTYPE_FIELD_DESC);
        oprot.writeString(struct.apptype);
        oprot.writeFieldEnd();
      }
      if (struct.optype != null) {
        oprot.writeFieldBegin(OPTYPE_FIELD_DESC);
        oprot.writeString(struct.optype);
        oprot.writeFieldEnd();
      }
      if (struct.columns != null) {
        oprot.writeFieldBegin(COLUMNS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.columns.size()));
          for (String _iter91 : struct.columns)
          {
            oprot.writeString(_iter91);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaRequestOwnersessTupleSchemeFactory implements SchemeFactory {
    public SaRequestOwnersessTupleScheme getScheme() {
      return new SaRequestOwnersessTupleScheme();
    }
  }

  private static class SaRequestOwnersessTupleScheme extends TupleScheme<SaRequestOwnersess> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaRequestOwnersess struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAuthMsg()) {
        optionals.set(0);
      }
      if (struct.isSetAppkey()) {
        optionals.set(1);
      }
      if (struct.isSetOwnerid()) {
        optionals.set(2);
      }
      if (struct.isSetApptype()) {
        optionals.set(3);
      }
      if (struct.isSetOptype()) {
        optionals.set(4);
      }
      if (struct.isSetColumns()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetAuthMsg()) {
        struct.authMsg.write(oprot);
      }
      if (struct.isSetAppkey()) {
        oprot.writeString(struct.appkey);
      }
      if (struct.isSetOwnerid()) {
        oprot.writeString(struct.ownerid);
      }
      if (struct.isSetApptype()) {
        oprot.writeString(struct.apptype);
      }
      if (struct.isSetOptype()) {
        oprot.writeString(struct.optype);
      }
      if (struct.isSetColumns()) {
        {
          oprot.writeI32(struct.columns.size());
          for (String _iter92 : struct.columns)
          {
            oprot.writeString(_iter92);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaRequestOwnersess struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.authMsg = new AuthorizeMsg();
        struct.authMsg.read(iprot);
        struct.setAuthMsgIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appkey = iprot.readString();
        struct.setAppkeyIsSet(true);
      }
      if (incoming.get(2)) {
        struct.ownerid = iprot.readString();
        struct.setOwneridIsSet(true);
      }
      if (incoming.get(3)) {
        struct.apptype = iprot.readString();
        struct.setApptypeIsSet(true);
      }
      if (incoming.get(4)) {
        struct.optype = iprot.readString();
        struct.setOptypeIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TList _list93 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.columns = new ArrayList<String>(_list93.size);
          String _elem94;
          for (int _i95 = 0; _i95 < _list93.size; ++_i95)
          {
            _elem94 = iprot.readString();
            struct.columns.add(_elem94);
          }
        }
        struct.setColumnsIsSet(true);
      }
    }
  }

}

