/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum BusiId implements org.apache.thrift.TEnum {
  HDZT(1),
  HDZT_RANKING(2),
  HDZT_AWARD(3),
  YY_PAY_CENTER(100),
  YY_BASE_PLATFORM(101),
  GAME_ECOLOGY(200),
  GAME_GUILD(300),
  GAME_BABY(400),
  MAKE_FRIEND(500),
  YUE_ZHAN(600),
  YU_LE(700),
  ZHUI_WAN(800),
  PEI_WAN(900);

  private final int value;

  private BusiId(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static BusiId findByValue(int value) { 
    switch (value) {
      case 1:
        return HDZT;
      case 2:
        return HDZT_RANKING;
      case 3:
        return HDZT_AWARD;
      case 100:
        return YY_PAY_CENTER;
      case 101:
        return YY_BASE_PLATFORM;
      case 200:
        return GAME_ECOLOGY;
      case 300:
        return GAME_GUILD;
      case 400:
        return GAME_BABY;
      case 500:
        return MAKE_FRIEND;
      case 600:
        return YUE_ZHAN;
      case 700:
        return YU_LE;
      case 800:
        return ZHUI_WAN;
      case 900:
        return PEI_WAN;
      default:
        return null;
    }
  }
}
