namespace java com.yy.gameecology.common.protocol.thrift.zhuiwan

enum Platform 
{
  UNKNOWN = 0,
  ANDROID = 100,
  IOS=200,
  PC=300,
  SERVER=400,
}

struct SimpleResult
{
	1:i32 code,                 // 结果码，0-成功，非零-失败或其他含义
	2:string reason,            // 成功或失败的提示信息
	3:map<string, string> data, // 成功时返回的数据，协商使用
}

struct IssueRequest 
{
    1:string seq,			// 请求序列号
    2:i64 actId,    		// 活动标识，非必须，和业务侧商量使用
    3:i64 taskId,   		// 发放任务标识（中台内部值，方便运营查障）
    4:i64 receiver, 		// 接收者（uid，sid等）
	5:i64 sid,      		// 所在频道 非必须
	6:i64 ssid,     		// 所在子频道 非必须
	7:string itemType,		// 业务侧发放项目类型（非必须，协商使用）	
	8:string itemId,		// 业务侧发放项目标识
	9:i64 itemNum,  		// 发放项目数量
	10:Platform platform;	// 平台编码(用于报表统计，非必须)
    11:string timestamp,	// 时间戳，格式 yyyy-MM-dd HH:mm:ss	
    12:string ip,    		// 用户IP（非必须）
    13:string mac,   		// 用户机器码（非必须）
    14:i64 extLong,			// 扩展长整形，协商使用
    15:string extJson,      // 扩展JSON，用于发放需要填信息的道具
    16:map<string, string> extData,	// 扩展 map，协商使用
    17:string sign,			// 请求参数签名（暂未启用）
}

struct IssueResponse
{
	1:i32 code,                 // 结果码，0-成功，非零-失败或其他含义
	2:string reason,            // 成功或失败的提示信息
	3:string seq,               // 返回请求时的seq，可用于实现异步处理
	4:string receipt,	        // 回执信息，需要返回给用户的卡券 号码、密码 等内容，呈现给用户（非必须）
	5:map<string, string> data, // 成功时返回的数据，协商使用
}

struct PopupMsg {
    1: i32 platform;//1 & platform => Android，2 & platform => iOS
    2: string icon;//图标
    3: string title;//标题
    4: string content;//内容
    5: string link;//链接，见https://doc.yy.com/pages/viewpage.action?pageId=56655898
    6: string background;//背景
    7: map<string, string> extend;
}

struct MallGoods {
    1: i32 id;
    2: string name;
    3: i32 price;
    4: string image;
}

struct BaseRsp {
    1: i32 result;
    2: string message;
}

struct BooleanRsp {
    1: i32 result;
    2: string message;
    3: bool canPass;
}

struct ChannelInfo {
    1: i64 topSid;
    2: i64 subSid;
    3: i32 channelType;
    4: i32 pluginType;
    5: i32 templateId;
}

struct ChannelRsp {
    /* 0-成功，非0失败*/
    1: i32 result;
    2: string msg;
    3: ChannelInfo channelInfo;
}


struct ChanneInfoData {
    1:i64 sid;
    2:i64 asid;
    3:i64 promoteCount;
    4:string downloadUrl;
}

struct ZhuiWanComponentCommonRsp {
    1: i32 result; // 0-成功，其他-失败
    2: string msg;
    3:ChanneInfoData channeInfo;
}

struct ZhuiWanComponentCommonReq {
    1:i64 actId;
    2:i64 loginUid;
    3:i64 sid;
    4:i32 clientType; // 1-android,2-ios,3-pc
    5:string hdid;
    6:map<string, string> extData;
}

struct MallGoodsListRsp {
     /* 0-成功，非0失败*/
     1: i32 result;
     2: string msg;
     3: list<MallGoods> goodslist;
}

struct ChannelPackageInfo {
    1:i64 actId; // 活动ID
    2:string channelId; // 渠道ID
    3:string channelName; // 渠道名称
    4:string downloadUrl; // 包url
    5:i32 status; // 包状态，1-有效，2-失效
    6:i64 loginUid; // 操作人
}

struct ZhuiWanChannelPackageReq {
    1:list<ChannelPackageInfo> packageInfoList;
    2:map<string, string> extData;
}

struct BaseChannel {
    1: i64 topSid;
    2: i64 subSid;
}

struct CheckUserNewReq {
    1:list<i64> uids;
}

struct CheckUserNewRsp {
    1:i32             code;       //状态码 0 成功 500 未知错误
    2:string          msg;
    3:map<i64, bool>  userStatusMap; //key:uid, val:true->新用户 
}


service ZhuiWanPrizeIssueService
{
	/*
	* 连接测试
	*/
	void ping(),

	/*
	* 版本查询
	*/
	string version(),

   /**
    *  活动用来判断用户是否是新用户接口
    **/
    CheckUserNewRsp getUserNewReq(1:CheckUserNewReq req);
	
    /**
     * 预留通用接口
     * busiId - 业务方标识
     * type - 调用类型，自定义自解释
     * data - 请求参数
     * sign - 请求参数签名（用 busiId 的 key）
     */
    SimpleResult invoke(1:i64 busiId, 2:i64 type, 3:map<string, string> data, 4:string sign);

    /**
    * 发放请求接口
    * request - 请求消息
    **/
	IssueResponse issue(1:IssueRequest request);

	/**
    * 发送应用内push
    * @param uid
    * @param msg 弹窗信息
    **/
    BaseRsp sendPopupMessage(1: string seqId, 2: i64 uid, 3: PopupMsg msg);

    /**
    * 发送单播
    * @param uid
    * @param content 内容（JSON）
    * @param minType 小类
    **/
    BaseRsp sendUnicast(1: string seqId, 2: i64 uid, 3: string content, 4: i32 minType);

    /**
    * 发送多播
    * @param uids
    * @param content 内容（JSON）
    * @param minType 小类
    **/
    BaseRsp sendMultiUsers(1: string seqId, 2: list<i64> uids, 3: string content, 4: i32 minType);

    /**
    * 发送推送
    * @param uids
    * @param title 标题
    * @param msg 内容
    * @param image 背景图，可不传
    * @param link 跳转链接，见https://doc.yy.com/pages/viewpage.action?pageId=56655898
    **/
    BaseRsp sendPush(1: string seqId, 2: list<i64> uids, 3: string title, 4: string msg, 5: string image, 6: string link);

    /**
    * 发送短信
    * @param uids
    * @param content
    **/
    BaseRsp sendSms(1: string seqId, 2: list<i64> uids, 3: string content);

    /**
    * 发送短信验证码
    * @param uid
    * @param smsInfo 下发文案，content中，使用{code}来标识生成的验证码,使用{passport}来标识通行证掩码
    * @param expireTime 有效时间（单位：秒，不能超过24小时，建议不超过10分钟）
    **/
    BaseRsp sendSmsVerifyCode(1: string seqId, 2: i64 uid, 3: string smsInfo, 4: i32 expireTime);

    /**
    * 校验短信验证码
    * @param uid
    * @param code 验证码
    **/
    BooleanRsp verifySmsCode(1: i64 uid, 2: string code);

    /**
    * 检测是否有风险
    * @param uid
    * @param hdid
    * @param ip 可不传
    * @param scene 使用场景，暂不使用
    **/
    BooleanRsp checkRiskValue(1: i64 uid, 2: string hdid, 3: string ip, 4: string scene);

    /**
    * 查询频道类型和模板信息
    * @param topSid
    * @param subSid
    **/
    ChannelRsp getChannelInfo(1:i64 topSid, 2:i64 subSid);

    /* 公会报名 */
    ZhuiWanComponentCommonRsp channelRegister(1:ZhuiWanComponentCommonReq request);

    /*获取公会报名信息*/
    ZhuiWanComponentCommonRsp getOwRegisterInfo(1:ZhuiWanComponentCommonReq request);

    /*增加玩豆*/
    BaseRsp addBeans(1: i64 uid,2: string seqId,3: i32 beans);

    /*根据活动id获取玩豆商城列表*/
    MallGoodsListRsp getMallGoodsList(1: i64 actId);

    /*批量上传游戏渠道包*/
    ZhuiWanComponentCommonRsp uploadGameChannelPackage(1:ZhuiWanChannelPackageReq request);

    /*获取未使用渠道包的数量*/
    SimpleResult getValidChannelPackageCount(1:ZhuiWanChannelPackageReq request);

    /**
    *  批量查询陪玩约会插件的PC频道列表
    *  @param context 请求上下文，方便定位日志
    **/
    list<BaseChannel> batchGetPeiwanPluginChannel(1: string context);
}