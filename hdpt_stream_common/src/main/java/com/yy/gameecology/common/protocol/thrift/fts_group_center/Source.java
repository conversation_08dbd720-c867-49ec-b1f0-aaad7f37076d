/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_group_center;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum Source implements TEnum {
  Unknown(0),
  SourceJY(500),
  SourcePK(600),
  SourceBaby(400),
  SourceZW(800),
  SourcePW(900);

  private final int value;

  private Source(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static Source findByValue(int value) { 
    switch (value) {
      case 0:
        return Unknown;
      case 500:
        return SourceJY;
      case 600:
        return SourcePK;
      case 400:
        return SourceBaby;
      case 800:
        return SourceZW;
      case 900:
        return SourcePW;
      default:
        return null;
    }
  }
}
