package com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream;

import org.apache.dubbo.common.annotation.Yrpc;
import com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Ping;
import com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.Pong;
import com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferRequest;
import com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream.SetBufferResponse;

/**
 * 活动平台数据流规则引擎 yyp 服务
 * <AUTHOR>
 * @date 2022/4/24 19:23
 */
public interface HDZKStreamService{

	/**
	 * ping，探活用
	 */
	@Yrpc(functionName="setBuffer",reqUri=256386,resUri=256642) // 1001<<8|130, 1002<<8|130
	public Pong ping (Ping req);

	/**
	 * 设置活动积分加成
	 */
	@Yrpc(functionName="setBuffer",reqUri=256898,resUri=257154) // 1003<<8|130, 1004<<8|130
	public SetBufferResponse setBuffer (SetBufferRequest req);

}