/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.turnover_family;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-09-09")
public class TDatingIdentify implements org.apache.thrift.TBase<TDatingIdentify, TDatingIdentify._Fields>, java.io.Serializable, Cloneable, Comparable<TDatingIdentify> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TDatingIdentify");

  private static final org.apache.thrift.protocol.TField ANCHOR_FIELD_DESC = new org.apache.thrift.protocol.TField("anchor", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField ROOM_MGR_FIELD_DESC = new org.apache.thrift.protocol.TField("roomMgr", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField TING_MGR_FIELD_DESC = new org.apache.thrift.protocol.TField("tingMgr", org.apache.thrift.protocol.TType.I32, (short)3);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TDatingIdentifyStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TDatingIdentifyTupleSchemeFactory());
  }

  /**
   * 是否主持，1：是，0：否
   */
  public int anchor; // required
  /**
   * 是否房管，1：是，0：否
   */
  public int roomMgr; // required
  /**
   * 是否厅管，1：是，0：否
   */
  public int tingMgr; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 是否主持，1：是，0：否
     */
    ANCHOR((short)1, "anchor"),
    /**
     * 是否房管，1：是，0：否
     */
    ROOM_MGR((short)2, "roomMgr"),
    /**
     * 是否厅管，1：是，0：否
     */
    TING_MGR((short)3, "tingMgr");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ANCHOR
          return ANCHOR;
        case 2: // ROOM_MGR
          return ROOM_MGR;
        case 3: // TING_MGR
          return TING_MGR;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ANCHOR_ISSET_ID = 0;
  private static final int __ROOMMGR_ISSET_ID = 1;
  private static final int __TINGMGR_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ANCHOR, new org.apache.thrift.meta_data.FieldMetaData("anchor", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ROOM_MGR, new org.apache.thrift.meta_data.FieldMetaData("roomMgr", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TING_MGR, new org.apache.thrift.meta_data.FieldMetaData("tingMgr", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TDatingIdentify.class, metaDataMap);
  }

  public TDatingIdentify() {
  }

  public TDatingIdentify(
    int anchor,
    int roomMgr,
    int tingMgr)
  {
    this();
    this.anchor = anchor;
    setAnchorIsSet(true);
    this.roomMgr = roomMgr;
    setRoomMgrIsSet(true);
    this.tingMgr = tingMgr;
    setTingMgrIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TDatingIdentify(TDatingIdentify other) {
    __isset_bitfield = other.__isset_bitfield;
    this.anchor = other.anchor;
    this.roomMgr = other.roomMgr;
    this.tingMgr = other.tingMgr;
  }

  public TDatingIdentify deepCopy() {
    return new TDatingIdentify(this);
  }

  @Override
  public void clear() {
    setAnchorIsSet(false);
    this.anchor = 0;
    setRoomMgrIsSet(false);
    this.roomMgr = 0;
    setTingMgrIsSet(false);
    this.tingMgr = 0;
  }

  /**
   * 是否主持，1：是，0：否
   */
  public int getAnchor() {
    return this.anchor;
  }

  /**
   * 是否主持，1：是，0：否
   */
  public TDatingIdentify setAnchor(int anchor) {
    this.anchor = anchor;
    setAnchorIsSet(true);
    return this;
  }

  public void unsetAnchor() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ANCHOR_ISSET_ID);
  }

  /** Returns true if field anchor is set (has been assigned a value) and false otherwise */
  public boolean isSetAnchor() {
    return EncodingUtils.testBit(__isset_bitfield, __ANCHOR_ISSET_ID);
  }

  public void setAnchorIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ANCHOR_ISSET_ID, value);
  }

  /**
   * 是否房管，1：是，0：否
   */
  public int getRoomMgr() {
    return this.roomMgr;
  }

  /**
   * 是否房管，1：是，0：否
   */
  public TDatingIdentify setRoomMgr(int roomMgr) {
    this.roomMgr = roomMgr;
    setRoomMgrIsSet(true);
    return this;
  }

  public void unsetRoomMgr() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROOMMGR_ISSET_ID);
  }

  /** Returns true if field roomMgr is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomMgr() {
    return EncodingUtils.testBit(__isset_bitfield, __ROOMMGR_ISSET_ID);
  }

  public void setRoomMgrIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROOMMGR_ISSET_ID, value);
  }

  /**
   * 是否厅管，1：是，0：否
   */
  public int getTingMgr() {
    return this.tingMgr;
  }

  /**
   * 是否厅管，1：是，0：否
   */
  public TDatingIdentify setTingMgr(int tingMgr) {
    this.tingMgr = tingMgr;
    setTingMgrIsSet(true);
    return this;
  }

  public void unsetTingMgr() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TINGMGR_ISSET_ID);
  }

  /** Returns true if field tingMgr is set (has been assigned a value) and false otherwise */
  public boolean isSetTingMgr() {
    return EncodingUtils.testBit(__isset_bitfield, __TINGMGR_ISSET_ID);
  }

  public void setTingMgrIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TINGMGR_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ANCHOR:
      if (value == null) {
        unsetAnchor();
      } else {
        setAnchor((Integer)value);
      }
      break;

    case ROOM_MGR:
      if (value == null) {
        unsetRoomMgr();
      } else {
        setRoomMgr((Integer)value);
      }
      break;

    case TING_MGR:
      if (value == null) {
        unsetTingMgr();
      } else {
        setTingMgr((Integer)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ANCHOR:
      return getAnchor();

    case ROOM_MGR:
      return getRoomMgr();

    case TING_MGR:
      return getTingMgr();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ANCHOR:
      return isSetAnchor();
    case ROOM_MGR:
      return isSetRoomMgr();
    case TING_MGR:
      return isSetTingMgr();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TDatingIdentify)
      return this.equals((TDatingIdentify)that);
    return false;
  }

  public boolean equals(TDatingIdentify that) {
    if (that == null)
      return false;

    boolean this_present_anchor = true;
    boolean that_present_anchor = true;
    if (this_present_anchor || that_present_anchor) {
      if (!(this_present_anchor && that_present_anchor))
        return false;
      if (this.anchor != that.anchor)
        return false;
    }

    boolean this_present_roomMgr = true;
    boolean that_present_roomMgr = true;
    if (this_present_roomMgr || that_present_roomMgr) {
      if (!(this_present_roomMgr && that_present_roomMgr))
        return false;
      if (this.roomMgr != that.roomMgr)
        return false;
    }

    boolean this_present_tingMgr = true;
    boolean that_present_tingMgr = true;
    if (this_present_tingMgr || that_present_tingMgr) {
      if (!(this_present_tingMgr && that_present_tingMgr))
        return false;
      if (this.tingMgr != that.tingMgr)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_anchor = true;
    list.add(present_anchor);
    if (present_anchor)
      list.add(anchor);

    boolean present_roomMgr = true;
    list.add(present_roomMgr);
    if (present_roomMgr)
      list.add(roomMgr);

    boolean present_tingMgr = true;
    list.add(present_tingMgr);
    if (present_tingMgr)
      list.add(tingMgr);

    return list.hashCode();
  }

  @Override
  public int compareTo(TDatingIdentify other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAnchor()).compareTo(other.isSetAnchor());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAnchor()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.anchor, other.anchor);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoomMgr()).compareTo(other.isSetRoomMgr());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomMgr()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomMgr, other.roomMgr);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTingMgr()).compareTo(other.isSetTingMgr());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTingMgr()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tingMgr, other.tingMgr);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TDatingIdentify(");
    boolean first = true;

    sb.append("anchor:");
    sb.append(this.anchor);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roomMgr:");
    sb.append(this.roomMgr);
    first = false;
    if (!first) sb.append(", ");
    sb.append("tingMgr:");
    sb.append(this.tingMgr);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TDatingIdentifyStandardSchemeFactory implements SchemeFactory {
    public TDatingIdentifyStandardScheme getScheme() {
      return new TDatingIdentifyStandardScheme();
    }
  }

  private static class TDatingIdentifyStandardScheme extends StandardScheme<TDatingIdentify> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TDatingIdentify struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ANCHOR
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.anchor = iprot.readI32();
              struct.setAnchorIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ROOM_MGR
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.roomMgr = iprot.readI32();
              struct.setRoomMgrIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // TING_MGR
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.tingMgr = iprot.readI32();
              struct.setTingMgrIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TDatingIdentify struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ANCHOR_FIELD_DESC);
      oprot.writeI32(struct.anchor);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ROOM_MGR_FIELD_DESC);
      oprot.writeI32(struct.roomMgr);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TING_MGR_FIELD_DESC);
      oprot.writeI32(struct.tingMgr);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TDatingIdentifyTupleSchemeFactory implements SchemeFactory {
    public TDatingIdentifyTupleScheme getScheme() {
      return new TDatingIdentifyTupleScheme();
    }
  }

  private static class TDatingIdentifyTupleScheme extends TupleScheme<TDatingIdentify> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TDatingIdentify struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAnchor()) {
        optionals.set(0);
      }
      if (struct.isSetRoomMgr()) {
        optionals.set(1);
      }
      if (struct.isSetTingMgr()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetAnchor()) {
        oprot.writeI32(struct.anchor);
      }
      if (struct.isSetRoomMgr()) {
        oprot.writeI32(struct.roomMgr);
      }
      if (struct.isSetTingMgr()) {
        oprot.writeI32(struct.tingMgr);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TDatingIdentify struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.anchor = iprot.readI32();
        struct.setAnchorIsSet(true);
      }
      if (incoming.get(1)) {
        struct.roomMgr = iprot.readI32();
        struct.setRoomMgrIsSet(true);
      }
      if (incoming.get(2)) {
        struct.tingMgr = iprot.readI32();
        struct.setTingMgrIsSet(true);
      }
    }
  }

}

