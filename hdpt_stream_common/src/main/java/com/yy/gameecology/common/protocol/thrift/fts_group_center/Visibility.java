/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_group_center;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum Visibility implements TEnum {
  Normal(0),
  Gray(1);

  private final int value;

  private Visibility(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static Visibility findByValue(int value) { 
    switch (value) {
      case 0:
        return Normal;
      case 1:
        return Gray;
      default:
        return null;
    }
  }
}
