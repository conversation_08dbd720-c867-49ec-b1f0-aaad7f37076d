syntax = "proto3";

import "uri.proto";

package com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream;

option java_outer_classname = "HDZKStream";
option java_multiple_files = false;

message Ping {
  option (yyp.uri) = 256386; // 1001<<8 | 130
}

message Pong {
  uint32 result = 1;
  option (yyp.uri) = 256642; // 1002<<8 | 130
}

/* 频道信息 */
message Channel {
  int64 sid = 1; // 顶级频道
  repeated int64 ssids = 2; // 顶级频道下的子频道
}

/*
  成员过滤器，用来生成具体的过滤实例 和 过滤模板
  1）过滤实例：笛卡尔积：${recvUid}:${sendUid}:${giftId}:${sid}:${ssid}:结构的过滤模板，对应元素不存在时，用 _ 占位，会覆盖式存储到redis中
  2）过滤模板：当有送礼流水时，用该模板生产一个流水的过滤实例，然后去之前存储的实例中查找，若找到则命中，使用关联的加成配置
  3) 送礼流水匹配时，越具体越优先
 */
message MemberFilter {
  repeated Channel channels = 1; // 指定的频道集合
  repeated int64 sendUids   = 3; // 指定的送礼人UID集合
  repeated int64 recvUids   = 4; // 指定的收礼人UID集合
  repeated string giftIds   = 5; // 指定的礼物ID集合
  map<string, string> extMap  = 99; // 扩展数据，双方约定使用
}

message SetBufferRequest{
  int64 actId  = 1; // 活动id
  string seq   = 2; // 请求seq（服务端 7 x 24小时内去重），在actId下唯一即可

  // 加成设置
  int32 ratio      = 10; // 倍率百分数，大于等于0的值， 假设ratio=n，就是n%，若小于100可以认为是惩罚了
  bool roundDown   = 11; // 取整指示， true：向下，false：向上
  string beginTime = 12; // 加成开始时刻，格式为 yyyy-MM-dd HH:mm:ss
  string endTime   = 13; // 加成结束时刻，格式为 yyyy-MM-dd HH:mm:ss
  int32 policy     = 15; // 当相同成员过滤key存在时的处理策略， 0：覆盖（默认值），1：不覆盖，其它值待后续实现。。。

  // 加成范围
  bool global               = 21; // 是否活动全局加成
  repeated string roles     = 22; // 为角色做的加成（不管在什么榜单），优先级高于 global 的指定
  repeated int32 rankings   = 23; // 为指定榜单做的加成，优先级高于 roles 中的指定

  // 加成过滤条件
  MemberFilter memberFilter = 31; // 成员过滤器

  string timestamp          = 32; // 请求时间戳，为空时取系统自身的时间，格式为 yyyy-MM-dd HH:mm:ss
  map<string, string> extMap  = 99; // 扩展数据，双方约定使用

  option (yyp.uri) = 256898; // 1003<<8 | 130
}

message SetBufferResponse{
  int64 actId    = 1; // 活动id
  string seq     = 2; // 请求seq（服务端24小时内去重），在actId下唯一
  int32 code     = 3; // 结果码，0-成功，非零-失败或其他含义
  string reason  = 4; // 成功或失败的提示信息
  map<string, string> extMap  = 99; // 扩展数据，双方约定使用
  option (yyp.uri) = 257154; // 1004<<8 | 130
}

// URI MAX TYPE = 130
service HDZKStreamService {

  // 探活用
  rpc ping(Ping) returns (Pong);

  // 设置积分加成
  rpc setBuffer(SetBufferRequest) returns (SetBufferResponse);

}

