/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class RoleDetail implements org.apache.thrift.TBase<RoleDetail, RoleDetail._Fields>, java.io.Serializable, Cloneable, Comparable<RoleDetail> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RoleDetail");

  private static final org.apache.thrift.protocol.TField ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roleId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField ROLE_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("roleName", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField ROLE_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("roleType", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField ROLE_BUSI_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roleBusiId", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField ROLE_EXTJSON_FIELD_DESC = new org.apache.thrift.protocol.TField("roleExtjson", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField ROLE_HINT_FIELD_DESC = new org.apache.thrift.protocol.TField("roleHint", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField ROLE_HINT_EXTJSON_FIELD_DESC = new org.apache.thrift.protocol.TField("roleHintExtjson", org.apache.thrift.protocol.TType.STRING, (short)7);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RoleDetailStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RoleDetailTupleSchemeFactory());
  }

  public long roleId; // required
  public String roleName; // required
  public long roleType; // required
  public long roleBusiId; // required
  public String roleExtjson; // required
  public long roleHint; // required
  public String roleHintExtjson; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ROLE_ID((short)1, "roleId"),
    ROLE_NAME((short)2, "roleName"),
    ROLE_TYPE((short)3, "roleType"),
    ROLE_BUSI_ID((short)4, "roleBusiId"),
    ROLE_EXTJSON((short)5, "roleExtjson"),
    ROLE_HINT((short)6, "roleHint"),
    ROLE_HINT_EXTJSON((short)7, "roleHintExtjson");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROLE_ID
          return ROLE_ID;
        case 2: // ROLE_NAME
          return ROLE_NAME;
        case 3: // ROLE_TYPE
          return ROLE_TYPE;
        case 4: // ROLE_BUSI_ID
          return ROLE_BUSI_ID;
        case 5: // ROLE_EXTJSON
          return ROLE_EXTJSON;
        case 6: // ROLE_HINT
          return ROLE_HINT;
        case 7: // ROLE_HINT_EXTJSON
          return ROLE_HINT_EXTJSON;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ROLEID_ISSET_ID = 0;
  private static final int __ROLETYPE_ISSET_ID = 1;
  private static final int __ROLEBUSIID_ISSET_ID = 2;
  private static final int __ROLEHINT_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("roleId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROLE_NAME, new org.apache.thrift.meta_data.FieldMetaData("roleName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ROLE_TYPE, new org.apache.thrift.meta_data.FieldMetaData("roleType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROLE_BUSI_ID, new org.apache.thrift.meta_data.FieldMetaData("roleBusiId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROLE_EXTJSON, new org.apache.thrift.meta_data.FieldMetaData("roleExtjson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ROLE_HINT, new org.apache.thrift.meta_data.FieldMetaData("roleHint", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROLE_HINT_EXTJSON, new org.apache.thrift.meta_data.FieldMetaData("roleHintExtjson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RoleDetail.class, metaDataMap);
  }

  public RoleDetail() {
  }

  public RoleDetail(
    long roleId,
    String roleName,
    long roleType,
    long roleBusiId,
    String roleExtjson,
    long roleHint,
    String roleHintExtjson)
  {
    this();
    this.roleId = roleId;
    setRoleIdIsSet(true);
    this.roleName = roleName;
    this.roleType = roleType;
    setRoleTypeIsSet(true);
    this.roleBusiId = roleBusiId;
    setRoleBusiIdIsSet(true);
    this.roleExtjson = roleExtjson;
    this.roleHint = roleHint;
    setRoleHintIsSet(true);
    this.roleHintExtjson = roleHintExtjson;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RoleDetail(RoleDetail other) {
    __isset_bitfield = other.__isset_bitfield;
    this.roleId = other.roleId;
    if (other.isSetRoleName()) {
      this.roleName = other.roleName;
    }
    this.roleType = other.roleType;
    this.roleBusiId = other.roleBusiId;
    if (other.isSetRoleExtjson()) {
      this.roleExtjson = other.roleExtjson;
    }
    this.roleHint = other.roleHint;
    if (other.isSetRoleHintExtjson()) {
      this.roleHintExtjson = other.roleHintExtjson;
    }
  }

  public RoleDetail deepCopy() {
    return new RoleDetail(this);
  }

  @Override
  public void clear() {
    setRoleIdIsSet(false);
    this.roleId = 0;
    this.roleName = null;
    setRoleTypeIsSet(false);
    this.roleType = 0;
    setRoleBusiIdIsSet(false);
    this.roleBusiId = 0;
    this.roleExtjson = null;
    setRoleHintIsSet(false);
    this.roleHint = 0;
    this.roleHintExtjson = null;
  }

  public long getRoleId() {
    return this.roleId;
  }

  public RoleDetail setRoleId(long roleId) {
    this.roleId = roleId;
    setRoleIdIsSet(true);
    return this;
  }

  public void unsetRoleId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  /** Returns true if field roleId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleId() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLEID_ISSET_ID);
  }

  public void setRoleIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLEID_ISSET_ID, value);
  }

  public String getRoleName() {
    return this.roleName;
  }

  public RoleDetail setRoleName(String roleName) {
    this.roleName = roleName;
    return this;
  }

  public void unsetRoleName() {
    this.roleName = null;
  }

  /** Returns true if field roleName is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleName() {
    return this.roleName != null;
  }

  public void setRoleNameIsSet(boolean value) {
    if (!value) {
      this.roleName = null;
    }
  }

  public long getRoleType() {
    return this.roleType;
  }

  public RoleDetail setRoleType(long roleType) {
    this.roleType = roleType;
    setRoleTypeIsSet(true);
    return this;
  }

  public void unsetRoleType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLETYPE_ISSET_ID);
  }

  /** Returns true if field roleType is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleType() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLETYPE_ISSET_ID);
  }

  public void setRoleTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLETYPE_ISSET_ID, value);
  }

  public long getRoleBusiId() {
    return this.roleBusiId;
  }

  public RoleDetail setRoleBusiId(long roleBusiId) {
    this.roleBusiId = roleBusiId;
    setRoleBusiIdIsSet(true);
    return this;
  }

  public void unsetRoleBusiId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLEBUSIID_ISSET_ID);
  }

  /** Returns true if field roleBusiId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleBusiId() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLEBUSIID_ISSET_ID);
  }

  public void setRoleBusiIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLEBUSIID_ISSET_ID, value);
  }

  public String getRoleExtjson() {
    return this.roleExtjson;
  }

  public RoleDetail setRoleExtjson(String roleExtjson) {
    this.roleExtjson = roleExtjson;
    return this;
  }

  public void unsetRoleExtjson() {
    this.roleExtjson = null;
  }

  /** Returns true if field roleExtjson is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleExtjson() {
    return this.roleExtjson != null;
  }

  public void setRoleExtjsonIsSet(boolean value) {
    if (!value) {
      this.roleExtjson = null;
    }
  }

  public long getRoleHint() {
    return this.roleHint;
  }

  public RoleDetail setRoleHint(long roleHint) {
    this.roleHint = roleHint;
    setRoleHintIsSet(true);
    return this;
  }

  public void unsetRoleHint() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLEHINT_ISSET_ID);
  }

  /** Returns true if field roleHint is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleHint() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLEHINT_ISSET_ID);
  }

  public void setRoleHintIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLEHINT_ISSET_ID, value);
  }

  public String getRoleHintExtjson() {
    return this.roleHintExtjson;
  }

  public RoleDetail setRoleHintExtjson(String roleHintExtjson) {
    this.roleHintExtjson = roleHintExtjson;
    return this;
  }

  public void unsetRoleHintExtjson() {
    this.roleHintExtjson = null;
  }

  /** Returns true if field roleHintExtjson is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleHintExtjson() {
    return this.roleHintExtjson != null;
  }

  public void setRoleHintExtjsonIsSet(boolean value) {
    if (!value) {
      this.roleHintExtjson = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ROLE_ID:
      if (value == null) {
        unsetRoleId();
      } else {
        setRoleId((Long)value);
      }
      break;

    case ROLE_NAME:
      if (value == null) {
        unsetRoleName();
      } else {
        setRoleName((String)value);
      }
      break;

    case ROLE_TYPE:
      if (value == null) {
        unsetRoleType();
      } else {
        setRoleType((Long)value);
      }
      break;

    case ROLE_BUSI_ID:
      if (value == null) {
        unsetRoleBusiId();
      } else {
        setRoleBusiId((Long)value);
      }
      break;

    case ROLE_EXTJSON:
      if (value == null) {
        unsetRoleExtjson();
      } else {
        setRoleExtjson((String)value);
      }
      break;

    case ROLE_HINT:
      if (value == null) {
        unsetRoleHint();
      } else {
        setRoleHint((Long)value);
      }
      break;

    case ROLE_HINT_EXTJSON:
      if (value == null) {
        unsetRoleHintExtjson();
      } else {
        setRoleHintExtjson((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ROLE_ID:
      return getRoleId();

    case ROLE_NAME:
      return getRoleName();

    case ROLE_TYPE:
      return getRoleType();

    case ROLE_BUSI_ID:
      return getRoleBusiId();

    case ROLE_EXTJSON:
      return getRoleExtjson();

    case ROLE_HINT:
      return getRoleHint();

    case ROLE_HINT_EXTJSON:
      return getRoleHintExtjson();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ROLE_ID:
      return isSetRoleId();
    case ROLE_NAME:
      return isSetRoleName();
    case ROLE_TYPE:
      return isSetRoleType();
    case ROLE_BUSI_ID:
      return isSetRoleBusiId();
    case ROLE_EXTJSON:
      return isSetRoleExtjson();
    case ROLE_HINT:
      return isSetRoleHint();
    case ROLE_HINT_EXTJSON:
      return isSetRoleHintExtjson();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RoleDetail)
      return this.equals((RoleDetail)that);
    return false;
  }

  public boolean equals(RoleDetail that) {
    if (that == null)
      return false;

    boolean this_present_roleId = true;
    boolean that_present_roleId = true;
    if (this_present_roleId || that_present_roleId) {
      if (!(this_present_roleId && that_present_roleId))
        return false;
      if (this.roleId != that.roleId)
        return false;
    }

    boolean this_present_roleName = true && this.isSetRoleName();
    boolean that_present_roleName = true && that.isSetRoleName();
    if (this_present_roleName || that_present_roleName) {
      if (!(this_present_roleName && that_present_roleName))
        return false;
      if (!this.roleName.equals(that.roleName))
        return false;
    }

    boolean this_present_roleType = true;
    boolean that_present_roleType = true;
    if (this_present_roleType || that_present_roleType) {
      if (!(this_present_roleType && that_present_roleType))
        return false;
      if (this.roleType != that.roleType)
        return false;
    }

    boolean this_present_roleBusiId = true;
    boolean that_present_roleBusiId = true;
    if (this_present_roleBusiId || that_present_roleBusiId) {
      if (!(this_present_roleBusiId && that_present_roleBusiId))
        return false;
      if (this.roleBusiId != that.roleBusiId)
        return false;
    }

    boolean this_present_roleExtjson = true && this.isSetRoleExtjson();
    boolean that_present_roleExtjson = true && that.isSetRoleExtjson();
    if (this_present_roleExtjson || that_present_roleExtjson) {
      if (!(this_present_roleExtjson && that_present_roleExtjson))
        return false;
      if (!this.roleExtjson.equals(that.roleExtjson))
        return false;
    }

    boolean this_present_roleHint = true;
    boolean that_present_roleHint = true;
    if (this_present_roleHint || that_present_roleHint) {
      if (!(this_present_roleHint && that_present_roleHint))
        return false;
      if (this.roleHint != that.roleHint)
        return false;
    }

    boolean this_present_roleHintExtjson = true && this.isSetRoleHintExtjson();
    boolean that_present_roleHintExtjson = true && that.isSetRoleHintExtjson();
    if (this_present_roleHintExtjson || that_present_roleHintExtjson) {
      if (!(this_present_roleHintExtjson && that_present_roleHintExtjson))
        return false;
      if (!this.roleHintExtjson.equals(that.roleHintExtjson))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_roleId = true;
    list.add(present_roleId);
    if (present_roleId)
      list.add(roleId);

    boolean present_roleName = true && (isSetRoleName());
    list.add(present_roleName);
    if (present_roleName)
      list.add(roleName);

    boolean present_roleType = true;
    list.add(present_roleType);
    if (present_roleType)
      list.add(roleType);

    boolean present_roleBusiId = true;
    list.add(present_roleBusiId);
    if (present_roleBusiId)
      list.add(roleBusiId);

    boolean present_roleExtjson = true && (isSetRoleExtjson());
    list.add(present_roleExtjson);
    if (present_roleExtjson)
      list.add(roleExtjson);

    boolean present_roleHint = true;
    list.add(present_roleHint);
    if (present_roleHint)
      list.add(roleHint);

    boolean present_roleHintExtjson = true && (isSetRoleHintExtjson());
    list.add(present_roleHintExtjson);
    if (present_roleHintExtjson)
      list.add(roleHintExtjson);

    return list.hashCode();
  }

  @Override
  public int compareTo(RoleDetail other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRoleId()).compareTo(other.isSetRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleId, other.roleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleName()).compareTo(other.isSetRoleName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleName, other.roleName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleType()).compareTo(other.isSetRoleType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleType, other.roleType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleBusiId()).compareTo(other.isSetRoleBusiId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleBusiId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleBusiId, other.roleBusiId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleExtjson()).compareTo(other.isSetRoleExtjson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleExtjson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleExtjson, other.roleExtjson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleHint()).compareTo(other.isSetRoleHint());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleHint()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleHint, other.roleHint);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleHintExtjson()).compareTo(other.isSetRoleHintExtjson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleHintExtjson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleHintExtjson, other.roleHintExtjson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RoleDetail(");
    boolean first = true;

    sb.append("roleId:");
    sb.append(this.roleId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleName:");
    if (this.roleName == null) {
      sb.append("null");
    } else {
      sb.append(this.roleName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleType:");
    sb.append(this.roleType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleBusiId:");
    sb.append(this.roleBusiId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleExtjson:");
    if (this.roleExtjson == null) {
      sb.append("null");
    } else {
      sb.append(this.roleExtjson);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleHint:");
    sb.append(this.roleHint);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleHintExtjson:");
    if (this.roleHintExtjson == null) {
      sb.append("null");
    } else {
      sb.append(this.roleHintExtjson);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RoleDetailStandardSchemeFactory implements SchemeFactory {
    public RoleDetailStandardScheme getScheme() {
      return new RoleDetailStandardScheme();
    }
  }

  private static class RoleDetailStandardScheme extends StandardScheme<RoleDetail> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RoleDetail struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleId = iprot.readI64();
              struct.setRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // ROLE_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.roleName = iprot.readString();
              struct.setRoleNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ROLE_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleType = iprot.readI64();
              struct.setRoleTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ROLE_BUSI_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleBusiId = iprot.readI64();
              struct.setRoleBusiIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ROLE_EXTJSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.roleExtjson = iprot.readString();
              struct.setRoleExtjsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // ROLE_HINT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleHint = iprot.readI64();
              struct.setRoleHintIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // ROLE_HINT_EXTJSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.roleHintExtjson = iprot.readString();
              struct.setRoleHintExtjsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RoleDetail struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ROLE_ID_FIELD_DESC);
      oprot.writeI64(struct.roleId);
      oprot.writeFieldEnd();
      if (struct.roleName != null) {
        oprot.writeFieldBegin(ROLE_NAME_FIELD_DESC);
        oprot.writeString(struct.roleName);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(ROLE_TYPE_FIELD_DESC);
      oprot.writeI64(struct.roleType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ROLE_BUSI_ID_FIELD_DESC);
      oprot.writeI64(struct.roleBusiId);
      oprot.writeFieldEnd();
      if (struct.roleExtjson != null) {
        oprot.writeFieldBegin(ROLE_EXTJSON_FIELD_DESC);
        oprot.writeString(struct.roleExtjson);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(ROLE_HINT_FIELD_DESC);
      oprot.writeI64(struct.roleHint);
      oprot.writeFieldEnd();
      if (struct.roleHintExtjson != null) {
        oprot.writeFieldBegin(ROLE_HINT_EXTJSON_FIELD_DESC);
        oprot.writeString(struct.roleHintExtjson);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RoleDetailTupleSchemeFactory implements SchemeFactory {
    public RoleDetailTupleScheme getScheme() {
      return new RoleDetailTupleScheme();
    }
  }

  private static class RoleDetailTupleScheme extends TupleScheme<RoleDetail> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RoleDetail struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRoleId()) {
        optionals.set(0);
      }
      if (struct.isSetRoleName()) {
        optionals.set(1);
      }
      if (struct.isSetRoleType()) {
        optionals.set(2);
      }
      if (struct.isSetRoleBusiId()) {
        optionals.set(3);
      }
      if (struct.isSetRoleExtjson()) {
        optionals.set(4);
      }
      if (struct.isSetRoleHint()) {
        optionals.set(5);
      }
      if (struct.isSetRoleHintExtjson()) {
        optionals.set(6);
      }
      oprot.writeBitSet(optionals, 7);
      if (struct.isSetRoleId()) {
        oprot.writeI64(struct.roleId);
      }
      if (struct.isSetRoleName()) {
        oprot.writeString(struct.roleName);
      }
      if (struct.isSetRoleType()) {
        oprot.writeI64(struct.roleType);
      }
      if (struct.isSetRoleBusiId()) {
        oprot.writeI64(struct.roleBusiId);
      }
      if (struct.isSetRoleExtjson()) {
        oprot.writeString(struct.roleExtjson);
      }
      if (struct.isSetRoleHint()) {
        oprot.writeI64(struct.roleHint);
      }
      if (struct.isSetRoleHintExtjson()) {
        oprot.writeString(struct.roleHintExtjson);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RoleDetail struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(7);
      if (incoming.get(0)) {
        struct.roleId = iprot.readI64();
        struct.setRoleIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.roleName = iprot.readString();
        struct.setRoleNameIsSet(true);
      }
      if (incoming.get(2)) {
        struct.roleType = iprot.readI64();
        struct.setRoleTypeIsSet(true);
      }
      if (incoming.get(3)) {
        struct.roleBusiId = iprot.readI64();
        struct.setRoleBusiIdIsSet(true);
      }
      if (incoming.get(4)) {
        struct.roleExtjson = iprot.readString();
        struct.setRoleExtjsonIsSet(true);
      }
      if (incoming.get(5)) {
        struct.roleHint = iprot.readI64();
        struct.setRoleHintIsSet(true);
      }
      if (incoming.get(6)) {
        struct.roleHintExtjson = iprot.readString();
        struct.setRoleHintExtjsonIsSet(true);
      }
    }
  }

}

