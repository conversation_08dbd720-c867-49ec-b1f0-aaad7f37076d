/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class EnrollmentInfo implements org.apache.thrift.TBase<EnrollmentInfo, EnrollmentInfo._Fields>, java.io.Serializable, Cloneable, Comparable<EnrollmentInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("EnrollmentInfo");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField MEMBER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("memberId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField SRC_ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("srcRoleId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField ROLE_BUSI_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roleBusiId", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField ROLE_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("roleType", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField DEST_ROLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("destRoleId", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField SIGN_SID_FIELD_DESC = new org.apache.thrift.protocol.TField("signSid", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField SIGN_ASID_FIELD_DESC = new org.apache.thrift.protocol.TField("signAsid", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField START_RANK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("startRankId", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField START_PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("startPhaseId", org.apache.thrift.protocol.TType.I64, (short)11);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new EnrollmentInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new EnrollmentInfoTupleSchemeFactory());
  }

  public long actId; // required
  public String memberId; // required
  public long srcRoleId; // required
  public long roleBusiId; // required
  public long roleType; // required
  public long destRoleId; // required
  public long status; // required
  public long signSid; // required
  public long signAsid; // required
  public long startRankId; // required
  public long startPhaseId; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    MEMBER_ID((short)2, "memberId"),
    SRC_ROLE_ID((short)3, "srcRoleId"),
    ROLE_BUSI_ID((short)4, "roleBusiId"),
    ROLE_TYPE((short)5, "roleType"),
    DEST_ROLE_ID((short)6, "destRoleId"),
    STATUS((short)7, "status"),
    SIGN_SID((short)8, "signSid"),
    SIGN_ASID((short)9, "signAsid"),
    START_RANK_ID((short)10, "startRankId"),
    START_PHASE_ID((short)11, "startPhaseId"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // MEMBER_ID
          return MEMBER_ID;
        case 3: // SRC_ROLE_ID
          return SRC_ROLE_ID;
        case 4: // ROLE_BUSI_ID
          return ROLE_BUSI_ID;
        case 5: // ROLE_TYPE
          return ROLE_TYPE;
        case 6: // DEST_ROLE_ID
          return DEST_ROLE_ID;
        case 7: // STATUS
          return STATUS;
        case 8: // SIGN_SID
          return SIGN_SID;
        case 9: // SIGN_ASID
          return SIGN_ASID;
        case 10: // START_RANK_ID
          return START_RANK_ID;
        case 11: // START_PHASE_ID
          return START_PHASE_ID;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __SRCROLEID_ISSET_ID = 1;
  private static final int __ROLEBUSIID_ISSET_ID = 2;
  private static final int __ROLETYPE_ISSET_ID = 3;
  private static final int __DESTROLEID_ISSET_ID = 4;
  private static final int __STATUS_ISSET_ID = 5;
  private static final int __SIGNSID_ISSET_ID = 6;
  private static final int __SIGNASID_ISSET_ID = 7;
  private static final int __STARTRANKID_ISSET_ID = 8;
  private static final int __STARTPHASEID_ISSET_ID = 9;
  private short __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.MEMBER_ID, new org.apache.thrift.meta_data.FieldMetaData("memberId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SRC_ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("srcRoleId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROLE_BUSI_ID, new org.apache.thrift.meta_data.FieldMetaData("roleBusiId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ROLE_TYPE, new org.apache.thrift.meta_data.FieldMetaData("roleType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DEST_ROLE_ID, new org.apache.thrift.meta_data.FieldMetaData("destRoleId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SIGN_SID, new org.apache.thrift.meta_data.FieldMetaData("signSid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SIGN_ASID, new org.apache.thrift.meta_data.FieldMetaData("signAsid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.START_RANK_ID, new org.apache.thrift.meta_data.FieldMetaData("startRankId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.START_PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("startPhaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(EnrollmentInfo.class, metaDataMap);
  }

  public EnrollmentInfo() {
  }

  public EnrollmentInfo(
    long actId,
    String memberId,
    long srcRoleId,
    long roleBusiId,
    long roleType,
    long destRoleId,
    long status,
    long signSid,
    long signAsid,
    long startRankId,
    long startPhaseId,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.memberId = memberId;
    this.srcRoleId = srcRoleId;
    setSrcRoleIdIsSet(true);
    this.roleBusiId = roleBusiId;
    setRoleBusiIdIsSet(true);
    this.roleType = roleType;
    setRoleTypeIsSet(true);
    this.destRoleId = destRoleId;
    setDestRoleIdIsSet(true);
    this.status = status;
    setStatusIsSet(true);
    this.signSid = signSid;
    setSignSidIsSet(true);
    this.signAsid = signAsid;
    setSignAsidIsSet(true);
    this.startRankId = startRankId;
    setStartRankIdIsSet(true);
    this.startPhaseId = startPhaseId;
    setStartPhaseIdIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public EnrollmentInfo(EnrollmentInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    if (other.isSetMemberId()) {
      this.memberId = other.memberId;
    }
    this.srcRoleId = other.srcRoleId;
    this.roleBusiId = other.roleBusiId;
    this.roleType = other.roleType;
    this.destRoleId = other.destRoleId;
    this.status = other.status;
    this.signSid = other.signSid;
    this.signAsid = other.signAsid;
    this.startRankId = other.startRankId;
    this.startPhaseId = other.startPhaseId;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public EnrollmentInfo deepCopy() {
    return new EnrollmentInfo(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    this.memberId = null;
    setSrcRoleIdIsSet(false);
    this.srcRoleId = 0;
    setRoleBusiIdIsSet(false);
    this.roleBusiId = 0;
    setRoleTypeIsSet(false);
    this.roleType = 0;
    setDestRoleIdIsSet(false);
    this.destRoleId = 0;
    setStatusIsSet(false);
    this.status = 0;
    setSignSidIsSet(false);
    this.signSid = 0;
    setSignAsidIsSet(false);
    this.signAsid = 0;
    setStartRankIdIsSet(false);
    this.startRankId = 0;
    setStartPhaseIdIsSet(false);
    this.startPhaseId = 0;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public EnrollmentInfo setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public String getMemberId() {
    return this.memberId;
  }

  public EnrollmentInfo setMemberId(String memberId) {
    this.memberId = memberId;
    return this;
  }

  public void unsetMemberId() {
    this.memberId = null;
  }

  /** Returns true if field memberId is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberId() {
    return this.memberId != null;
  }

  public void setMemberIdIsSet(boolean value) {
    if (!value) {
      this.memberId = null;
    }
  }

  public long getSrcRoleId() {
    return this.srcRoleId;
  }

  public EnrollmentInfo setSrcRoleId(long srcRoleId) {
    this.srcRoleId = srcRoleId;
    setSrcRoleIdIsSet(true);
    return this;
  }

  public void unsetSrcRoleId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SRCROLEID_ISSET_ID);
  }

  /** Returns true if field srcRoleId is set (has been assigned a value) and false otherwise */
  public boolean isSetSrcRoleId() {
    return EncodingUtils.testBit(__isset_bitfield, __SRCROLEID_ISSET_ID);
  }

  public void setSrcRoleIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SRCROLEID_ISSET_ID, value);
  }

  public long getRoleBusiId() {
    return this.roleBusiId;
  }

  public EnrollmentInfo setRoleBusiId(long roleBusiId) {
    this.roleBusiId = roleBusiId;
    setRoleBusiIdIsSet(true);
    return this;
  }

  public void unsetRoleBusiId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLEBUSIID_ISSET_ID);
  }

  /** Returns true if field roleBusiId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleBusiId() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLEBUSIID_ISSET_ID);
  }

  public void setRoleBusiIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLEBUSIID_ISSET_ID, value);
  }

  public long getRoleType() {
    return this.roleType;
  }

  public EnrollmentInfo setRoleType(long roleType) {
    this.roleType = roleType;
    setRoleTypeIsSet(true);
    return this;
  }

  public void unsetRoleType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROLETYPE_ISSET_ID);
  }

  /** Returns true if field roleType is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleType() {
    return EncodingUtils.testBit(__isset_bitfield, __ROLETYPE_ISSET_ID);
  }

  public void setRoleTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROLETYPE_ISSET_ID, value);
  }

  public long getDestRoleId() {
    return this.destRoleId;
  }

  public EnrollmentInfo setDestRoleId(long destRoleId) {
    this.destRoleId = destRoleId;
    setDestRoleIdIsSet(true);
    return this;
  }

  public void unsetDestRoleId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __DESTROLEID_ISSET_ID);
  }

  /** Returns true if field destRoleId is set (has been assigned a value) and false otherwise */
  public boolean isSetDestRoleId() {
    return EncodingUtils.testBit(__isset_bitfield, __DESTROLEID_ISSET_ID);
  }

  public void setDestRoleIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __DESTROLEID_ISSET_ID, value);
  }

  public long getStatus() {
    return this.status;
  }

  public EnrollmentInfo setStatus(long status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  public long getSignSid() {
    return this.signSid;
  }

  public EnrollmentInfo setSignSid(long signSid) {
    this.signSid = signSid;
    setSignSidIsSet(true);
    return this;
  }

  public void unsetSignSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SIGNSID_ISSET_ID);
  }

  /** Returns true if field signSid is set (has been assigned a value) and false otherwise */
  public boolean isSetSignSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SIGNSID_ISSET_ID);
  }

  public void setSignSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SIGNSID_ISSET_ID, value);
  }

  public long getSignAsid() {
    return this.signAsid;
  }

  public EnrollmentInfo setSignAsid(long signAsid) {
    this.signAsid = signAsid;
    setSignAsidIsSet(true);
    return this;
  }

  public void unsetSignAsid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SIGNASID_ISSET_ID);
  }

  /** Returns true if field signAsid is set (has been assigned a value) and false otherwise */
  public boolean isSetSignAsid() {
    return EncodingUtils.testBit(__isset_bitfield, __SIGNASID_ISSET_ID);
  }

  public void setSignAsidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SIGNASID_ISSET_ID, value);
  }

  public long getStartRankId() {
    return this.startRankId;
  }

  public EnrollmentInfo setStartRankId(long startRankId) {
    this.startRankId = startRankId;
    setStartRankIdIsSet(true);
    return this;
  }

  public void unsetStartRankId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STARTRANKID_ISSET_ID);
  }

  /** Returns true if field startRankId is set (has been assigned a value) and false otherwise */
  public boolean isSetStartRankId() {
    return EncodingUtils.testBit(__isset_bitfield, __STARTRANKID_ISSET_ID);
  }

  public void setStartRankIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STARTRANKID_ISSET_ID, value);
  }

  public long getStartPhaseId() {
    return this.startPhaseId;
  }

  public EnrollmentInfo setStartPhaseId(long startPhaseId) {
    this.startPhaseId = startPhaseId;
    setStartPhaseIdIsSet(true);
    return this;
  }

  public void unsetStartPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STARTPHASEID_ISSET_ID);
  }

  /** Returns true if field startPhaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetStartPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __STARTPHASEID_ISSET_ID);
  }

  public void setStartPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STARTPHASEID_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public EnrollmentInfo setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case MEMBER_ID:
      if (value == null) {
        unsetMemberId();
      } else {
        setMemberId((String)value);
      }
      break;

    case SRC_ROLE_ID:
      if (value == null) {
        unsetSrcRoleId();
      } else {
        setSrcRoleId((Long)value);
      }
      break;

    case ROLE_BUSI_ID:
      if (value == null) {
        unsetRoleBusiId();
      } else {
        setRoleBusiId((Long)value);
      }
      break;

    case ROLE_TYPE:
      if (value == null) {
        unsetRoleType();
      } else {
        setRoleType((Long)value);
      }
      break;

    case DEST_ROLE_ID:
      if (value == null) {
        unsetDestRoleId();
      } else {
        setDestRoleId((Long)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Long)value);
      }
      break;

    case SIGN_SID:
      if (value == null) {
        unsetSignSid();
      } else {
        setSignSid((Long)value);
      }
      break;

    case SIGN_ASID:
      if (value == null) {
        unsetSignAsid();
      } else {
        setSignAsid((Long)value);
      }
      break;

    case START_RANK_ID:
      if (value == null) {
        unsetStartRankId();
      } else {
        setStartRankId((Long)value);
      }
      break;

    case START_PHASE_ID:
      if (value == null) {
        unsetStartPhaseId();
      } else {
        setStartPhaseId((Long)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case MEMBER_ID:
      return getMemberId();

    case SRC_ROLE_ID:
      return getSrcRoleId();

    case ROLE_BUSI_ID:
      return getRoleBusiId();

    case ROLE_TYPE:
      return getRoleType();

    case DEST_ROLE_ID:
      return getDestRoleId();

    case STATUS:
      return getStatus();

    case SIGN_SID:
      return getSignSid();

    case SIGN_ASID:
      return getSignAsid();

    case START_RANK_ID:
      return getStartRankId();

    case START_PHASE_ID:
      return getStartPhaseId();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case MEMBER_ID:
      return isSetMemberId();
    case SRC_ROLE_ID:
      return isSetSrcRoleId();
    case ROLE_BUSI_ID:
      return isSetRoleBusiId();
    case ROLE_TYPE:
      return isSetRoleType();
    case DEST_ROLE_ID:
      return isSetDestRoleId();
    case STATUS:
      return isSetStatus();
    case SIGN_SID:
      return isSetSignSid();
    case SIGN_ASID:
      return isSetSignAsid();
    case START_RANK_ID:
      return isSetStartRankId();
    case START_PHASE_ID:
      return isSetStartPhaseId();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof EnrollmentInfo)
      return this.equals((EnrollmentInfo)that);
    return false;
  }

  public boolean equals(EnrollmentInfo that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_memberId = true && this.isSetMemberId();
    boolean that_present_memberId = true && that.isSetMemberId();
    if (this_present_memberId || that_present_memberId) {
      if (!(this_present_memberId && that_present_memberId))
        return false;
      if (!this.memberId.equals(that.memberId))
        return false;
    }

    boolean this_present_srcRoleId = true;
    boolean that_present_srcRoleId = true;
    if (this_present_srcRoleId || that_present_srcRoleId) {
      if (!(this_present_srcRoleId && that_present_srcRoleId))
        return false;
      if (this.srcRoleId != that.srcRoleId)
        return false;
    }

    boolean this_present_roleBusiId = true;
    boolean that_present_roleBusiId = true;
    if (this_present_roleBusiId || that_present_roleBusiId) {
      if (!(this_present_roleBusiId && that_present_roleBusiId))
        return false;
      if (this.roleBusiId != that.roleBusiId)
        return false;
    }

    boolean this_present_roleType = true;
    boolean that_present_roleType = true;
    if (this_present_roleType || that_present_roleType) {
      if (!(this_present_roleType && that_present_roleType))
        return false;
      if (this.roleType != that.roleType)
        return false;
    }

    boolean this_present_destRoleId = true;
    boolean that_present_destRoleId = true;
    if (this_present_destRoleId || that_present_destRoleId) {
      if (!(this_present_destRoleId && that_present_destRoleId))
        return false;
      if (this.destRoleId != that.destRoleId)
        return false;
    }

    boolean this_present_status = true;
    boolean that_present_status = true;
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_signSid = true;
    boolean that_present_signSid = true;
    if (this_present_signSid || that_present_signSid) {
      if (!(this_present_signSid && that_present_signSid))
        return false;
      if (this.signSid != that.signSid)
        return false;
    }

    boolean this_present_signAsid = true;
    boolean that_present_signAsid = true;
    if (this_present_signAsid || that_present_signAsid) {
      if (!(this_present_signAsid && that_present_signAsid))
        return false;
      if (this.signAsid != that.signAsid)
        return false;
    }

    boolean this_present_startRankId = true;
    boolean that_present_startRankId = true;
    if (this_present_startRankId || that_present_startRankId) {
      if (!(this_present_startRankId && that_present_startRankId))
        return false;
      if (this.startRankId != that.startRankId)
        return false;
    }

    boolean this_present_startPhaseId = true;
    boolean that_present_startPhaseId = true;
    if (this_present_startPhaseId || that_present_startPhaseId) {
      if (!(this_present_startPhaseId && that_present_startPhaseId))
        return false;
      if (this.startPhaseId != that.startPhaseId)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_memberId = true && (isSetMemberId());
    list.add(present_memberId);
    if (present_memberId)
      list.add(memberId);

    boolean present_srcRoleId = true;
    list.add(present_srcRoleId);
    if (present_srcRoleId)
      list.add(srcRoleId);

    boolean present_roleBusiId = true;
    list.add(present_roleBusiId);
    if (present_roleBusiId)
      list.add(roleBusiId);

    boolean present_roleType = true;
    list.add(present_roleType);
    if (present_roleType)
      list.add(roleType);

    boolean present_destRoleId = true;
    list.add(present_destRoleId);
    if (present_destRoleId)
      list.add(destRoleId);

    boolean present_status = true;
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_signSid = true;
    list.add(present_signSid);
    if (present_signSid)
      list.add(signSid);

    boolean present_signAsid = true;
    list.add(present_signAsid);
    if (present_signAsid)
      list.add(signAsid);

    boolean present_startRankId = true;
    list.add(present_startRankId);
    if (present_startRankId)
      list.add(startRankId);

    boolean present_startPhaseId = true;
    list.add(present_startPhaseId);
    if (present_startPhaseId)
      list.add(startPhaseId);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(EnrollmentInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMemberId()).compareTo(other.isSetMemberId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberId, other.memberId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSrcRoleId()).compareTo(other.isSetSrcRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSrcRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.srcRoleId, other.srcRoleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleBusiId()).compareTo(other.isSetRoleBusiId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleBusiId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleBusiId, other.roleBusiId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleType()).compareTo(other.isSetRoleType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleType, other.roleType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDestRoleId()).compareTo(other.isSetDestRoleId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDestRoleId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.destRoleId, other.destRoleId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSignSid()).compareTo(other.isSetSignSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSignSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.signSid, other.signSid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSignAsid()).compareTo(other.isSetSignAsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSignAsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.signAsid, other.signAsid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStartRankId()).compareTo(other.isSetStartRankId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartRankId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startRankId, other.startRankId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStartPhaseId()).compareTo(other.isSetStartPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStartPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.startPhaseId, other.startPhaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("EnrollmentInfo(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("memberId:");
    if (this.memberId == null) {
      sb.append("null");
    } else {
      sb.append(this.memberId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("srcRoleId:");
    sb.append(this.srcRoleId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleBusiId:");
    sb.append(this.roleBusiId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleType:");
    sb.append(this.roleType);
    first = false;
    if (!first) sb.append(", ");
    sb.append("destRoleId:");
    sb.append(this.destRoleId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("status:");
    sb.append(this.status);
    first = false;
    if (!first) sb.append(", ");
    sb.append("signSid:");
    sb.append(this.signSid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("signAsid:");
    sb.append(this.signAsid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("startRankId:");
    sb.append(this.startRankId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("startPhaseId:");
    sb.append(this.startPhaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class EnrollmentInfoStandardSchemeFactory implements SchemeFactory {
    public EnrollmentInfoStandardScheme getScheme() {
      return new EnrollmentInfoStandardScheme();
    }
  }

  private static class EnrollmentInfoStandardScheme extends StandardScheme<EnrollmentInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, EnrollmentInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MEMBER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.memberId = iprot.readString();
              struct.setMemberIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SRC_ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.srcRoleId = iprot.readI64();
              struct.setSrcRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // ROLE_BUSI_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleBusiId = iprot.readI64();
              struct.setRoleBusiIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ROLE_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.roleType = iprot.readI64();
              struct.setRoleTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // DEST_ROLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.destRoleId = iprot.readI64();
              struct.setDestRoleIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.status = iprot.readI64();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // SIGN_SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.signSid = iprot.readI64();
              struct.setSignSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // SIGN_ASID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.signAsid = iprot.readI64();
              struct.setSignAsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // START_RANK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.startRankId = iprot.readI64();
              struct.setStartRankIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // START_PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.startPhaseId = iprot.readI64();
              struct.setStartPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map416 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map416.size);
                String _key417;
                String _val418;
                for (int _i419 = 0; _i419 < _map416.size; ++_i419)
                {
                  _key417 = iprot.readString();
                  _val418 = iprot.readString();
                  struct.extData.put(_key417, _val418);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, EnrollmentInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      if (struct.memberId != null) {
        oprot.writeFieldBegin(MEMBER_ID_FIELD_DESC);
        oprot.writeString(struct.memberId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SRC_ROLE_ID_FIELD_DESC);
      oprot.writeI64(struct.srcRoleId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ROLE_BUSI_ID_FIELD_DESC);
      oprot.writeI64(struct.roleBusiId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ROLE_TYPE_FIELD_DESC);
      oprot.writeI64(struct.roleType);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(DEST_ROLE_ID_FIELD_DESC);
      oprot.writeI64(struct.destRoleId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(STATUS_FIELD_DESC);
      oprot.writeI64(struct.status);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SIGN_SID_FIELD_DESC);
      oprot.writeI64(struct.signSid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SIGN_ASID_FIELD_DESC);
      oprot.writeI64(struct.signAsid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(START_RANK_ID_FIELD_DESC);
      oprot.writeI64(struct.startRankId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(START_PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.startPhaseId);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter420 : struct.extData.entrySet())
          {
            oprot.writeString(_iter420.getKey());
            oprot.writeString(_iter420.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class EnrollmentInfoTupleSchemeFactory implements SchemeFactory {
    public EnrollmentInfoTupleScheme getScheme() {
      return new EnrollmentInfoTupleScheme();
    }
  }

  private static class EnrollmentInfoTupleScheme extends TupleScheme<EnrollmentInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, EnrollmentInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetMemberId()) {
        optionals.set(1);
      }
      if (struct.isSetSrcRoleId()) {
        optionals.set(2);
      }
      if (struct.isSetRoleBusiId()) {
        optionals.set(3);
      }
      if (struct.isSetRoleType()) {
        optionals.set(4);
      }
      if (struct.isSetDestRoleId()) {
        optionals.set(5);
      }
      if (struct.isSetStatus()) {
        optionals.set(6);
      }
      if (struct.isSetSignSid()) {
        optionals.set(7);
      }
      if (struct.isSetSignAsid()) {
        optionals.set(8);
      }
      if (struct.isSetStartRankId()) {
        optionals.set(9);
      }
      if (struct.isSetStartPhaseId()) {
        optionals.set(10);
      }
      if (struct.isSetExtData()) {
        optionals.set(11);
      }
      oprot.writeBitSet(optionals, 12);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetMemberId()) {
        oprot.writeString(struct.memberId);
      }
      if (struct.isSetSrcRoleId()) {
        oprot.writeI64(struct.srcRoleId);
      }
      if (struct.isSetRoleBusiId()) {
        oprot.writeI64(struct.roleBusiId);
      }
      if (struct.isSetRoleType()) {
        oprot.writeI64(struct.roleType);
      }
      if (struct.isSetDestRoleId()) {
        oprot.writeI64(struct.destRoleId);
      }
      if (struct.isSetStatus()) {
        oprot.writeI64(struct.status);
      }
      if (struct.isSetSignSid()) {
        oprot.writeI64(struct.signSid);
      }
      if (struct.isSetSignAsid()) {
        oprot.writeI64(struct.signAsid);
      }
      if (struct.isSetStartRankId()) {
        oprot.writeI64(struct.startRankId);
      }
      if (struct.isSetStartPhaseId()) {
        oprot.writeI64(struct.startPhaseId);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter421 : struct.extData.entrySet())
          {
            oprot.writeString(_iter421.getKey());
            oprot.writeString(_iter421.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, EnrollmentInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(12);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.memberId = iprot.readString();
        struct.setMemberIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.srcRoleId = iprot.readI64();
        struct.setSrcRoleIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.roleBusiId = iprot.readI64();
        struct.setRoleBusiIdIsSet(true);
      }
      if (incoming.get(4)) {
        struct.roleType = iprot.readI64();
        struct.setRoleTypeIsSet(true);
      }
      if (incoming.get(5)) {
        struct.destRoleId = iprot.readI64();
        struct.setDestRoleIdIsSet(true);
      }
      if (incoming.get(6)) {
        struct.status = iprot.readI64();
        struct.setStatusIsSet(true);
      }
      if (incoming.get(7)) {
        struct.signSid = iprot.readI64();
        struct.setSignSidIsSet(true);
      }
      if (incoming.get(8)) {
        struct.signAsid = iprot.readI64();
        struct.setSignAsidIsSet(true);
      }
      if (incoming.get(9)) {
        struct.startRankId = iprot.readI64();
        struct.setStartRankIdIsSet(true);
      }
      if (incoming.get(10)) {
        struct.startPhaseId = iprot.readI64();
        struct.setStartPhaseIdIsSet(true);
      }
      if (incoming.get(11)) {
        {
          org.apache.thrift.protocol.TMap _map422 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map422.size);
          String _key423;
          String _val424;
          for (int _i425 = 0; _i425 < _map422.size; ++_i425)
          {
            _key423 = iprot.readString();
            _val424 = iprot.readString();
            struct.extData.put(_key423, _val424);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

