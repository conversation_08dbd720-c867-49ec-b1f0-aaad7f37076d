/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_room;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-06-25")
public class RoomInfo implements org.apache.thrift.TBase<RoomInfo, RoomInfo._Fields>, java.io.Serializable, Cloneable, Comparable<RoomInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RoomInfo");

  private static final org.apache.thrift.protocol.TField ROOM_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("roomId", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField TITLE_FIELD_DESC = new org.apache.thrift.protocol.TField("title", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField COVER_FIELD_DESC = new org.apache.thrift.protocol.TField("cover", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField OW_FIELD_DESC = new org.apache.thrift.protocol.TField("ow", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField SSID_FIELD_DESC = new org.apache.thrift.protocol.TField("ssid", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField FAMILY_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("familyId", org.apache.thrift.protocol.TType.I64, (short)7);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RoomInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RoomInfoTupleSchemeFactory());
  }

  public int roomId; // required
  public String title; // required
  public String cover; // required
  public long ow; // required
  public long sid; // required
  public long ssid; // required
  public long familyId; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ROOM_ID((short)1, "roomId"),
    TITLE((short)2, "title"),
    COVER((short)3, "cover"),
    OW((short)4, "ow"),
    SID((short)5, "sid"),
    SSID((short)6, "ssid"),
    FAMILY_ID((short)7, "familyId");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ROOM_ID
          return ROOM_ID;
        case 2: // TITLE
          return TITLE;
        case 3: // COVER
          return COVER;
        case 4: // OW
          return OW;
        case 5: // SID
          return SID;
        case 6: // SSID
          return SSID;
        case 7: // FAMILY_ID
          return FAMILY_ID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ROOMID_ISSET_ID = 0;
  private static final int __OW_ISSET_ID = 1;
  private static final int __SID_ISSET_ID = 2;
  private static final int __SSID_ISSET_ID = 3;
  private static final int __FAMILYID_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ROOM_ID, new org.apache.thrift.meta_data.FieldMetaData("roomId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.TITLE, new org.apache.thrift.meta_data.FieldMetaData("title", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.COVER, new org.apache.thrift.meta_data.FieldMetaData("cover", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.OW, new org.apache.thrift.meta_data.FieldMetaData("ow", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SSID, new org.apache.thrift.meta_data.FieldMetaData("ssid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.FAMILY_ID, new org.apache.thrift.meta_data.FieldMetaData("familyId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RoomInfo.class, metaDataMap);
  }

  public RoomInfo() {
  }

  public RoomInfo(
    int roomId,
    String title,
    String cover,
    long ow,
    long sid,
    long ssid,
    long familyId)
  {
    this();
    this.roomId = roomId;
    setRoomIdIsSet(true);
    this.title = title;
    this.cover = cover;
    this.ow = ow;
    setOwIsSet(true);
    this.sid = sid;
    setSidIsSet(true);
    this.ssid = ssid;
    setSsidIsSet(true);
    this.familyId = familyId;
    setFamilyIdIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RoomInfo(RoomInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.roomId = other.roomId;
    if (other.isSetTitle()) {
      this.title = other.title;
    }
    if (other.isSetCover()) {
      this.cover = other.cover;
    }
    this.ow = other.ow;
    this.sid = other.sid;
    this.ssid = other.ssid;
    this.familyId = other.familyId;
  }

  public RoomInfo deepCopy() {
    return new RoomInfo(this);
  }

  @Override
  public void clear() {
    setRoomIdIsSet(false);
    this.roomId = 0;
    this.title = null;
    this.cover = null;
    setOwIsSet(false);
    this.ow = 0;
    setSidIsSet(false);
    this.sid = 0;
    setSsidIsSet(false);
    this.ssid = 0;
    setFamilyIdIsSet(false);
    this.familyId = 0;
  }

  public int getRoomId() {
    return this.roomId;
  }

  public RoomInfo setRoomId(int roomId) {
    this.roomId = roomId;
    setRoomIdIsSet(true);
    return this;
  }

  public void unsetRoomId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ROOMID_ISSET_ID);
  }

  /** Returns true if field roomId is set (has been assigned a value) and false otherwise */
  public boolean isSetRoomId() {
    return EncodingUtils.testBit(__isset_bitfield, __ROOMID_ISSET_ID);
  }

  public void setRoomIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ROOMID_ISSET_ID, value);
  }

  public String getTitle() {
    return this.title;
  }

  public RoomInfo setTitle(String title) {
    this.title = title;
    return this;
  }

  public void unsetTitle() {
    this.title = null;
  }

  /** Returns true if field title is set (has been assigned a value) and false otherwise */
  public boolean isSetTitle() {
    return this.title != null;
  }

  public void setTitleIsSet(boolean value) {
    if (!value) {
      this.title = null;
    }
  }

  public String getCover() {
    return this.cover;
  }

  public RoomInfo setCover(String cover) {
    this.cover = cover;
    return this;
  }

  public void unsetCover() {
    this.cover = null;
  }

  /** Returns true if field cover is set (has been assigned a value) and false otherwise */
  public boolean isSetCover() {
    return this.cover != null;
  }

  public void setCoverIsSet(boolean value) {
    if (!value) {
      this.cover = null;
    }
  }

  public long getOw() {
    return this.ow;
  }

  public RoomInfo setOw(long ow) {
    this.ow = ow;
    setOwIsSet(true);
    return this;
  }

  public void unsetOw() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __OW_ISSET_ID);
  }

  /** Returns true if field ow is set (has been assigned a value) and false otherwise */
  public boolean isSetOw() {
    return EncodingUtils.testBit(__isset_bitfield, __OW_ISSET_ID);
  }

  public void setOwIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __OW_ISSET_ID, value);
  }

  public long getSid() {
    return this.sid;
  }

  public RoomInfo setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getSsid() {
    return this.ssid;
  }

  public RoomInfo setSsid(long ssid) {
    this.ssid = ssid;
    setSsidIsSet(true);
    return this;
  }

  public void unsetSsid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  /** Returns true if field ssid is set (has been assigned a value) and false otherwise */
  public boolean isSetSsid() {
    return EncodingUtils.testBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  public void setSsidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SSID_ISSET_ID, value);
  }

  public long getFamilyId() {
    return this.familyId;
  }

  public RoomInfo setFamilyId(long familyId) {
    this.familyId = familyId;
    setFamilyIdIsSet(true);
    return this;
  }

  public void unsetFamilyId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FAMILYID_ISSET_ID);
  }

  /** Returns true if field familyId is set (has been assigned a value) and false otherwise */
  public boolean isSetFamilyId() {
    return EncodingUtils.testBit(__isset_bitfield, __FAMILYID_ISSET_ID);
  }

  public void setFamilyIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FAMILYID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ROOM_ID:
      if (value == null) {
        unsetRoomId();
      } else {
        setRoomId((Integer)value);
      }
      break;

    case TITLE:
      if (value == null) {
        unsetTitle();
      } else {
        setTitle((String)value);
      }
      break;

    case COVER:
      if (value == null) {
        unsetCover();
      } else {
        setCover((String)value);
      }
      break;

    case OW:
      if (value == null) {
        unsetOw();
      } else {
        setOw((Long)value);
      }
      break;

    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case SSID:
      if (value == null) {
        unsetSsid();
      } else {
        setSsid((Long)value);
      }
      break;

    case FAMILY_ID:
      if (value == null) {
        unsetFamilyId();
      } else {
        setFamilyId((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ROOM_ID:
      return getRoomId();

    case TITLE:
      return getTitle();

    case COVER:
      return getCover();

    case OW:
      return getOw();

    case SID:
      return getSid();

    case SSID:
      return getSsid();

    case FAMILY_ID:
      return getFamilyId();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ROOM_ID:
      return isSetRoomId();
    case TITLE:
      return isSetTitle();
    case COVER:
      return isSetCover();
    case OW:
      return isSetOw();
    case SID:
      return isSetSid();
    case SSID:
      return isSetSsid();
    case FAMILY_ID:
      return isSetFamilyId();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RoomInfo)
      return this.equals((RoomInfo)that);
    return false;
  }

  public boolean equals(RoomInfo that) {
    if (that == null)
      return false;

    boolean this_present_roomId = true;
    boolean that_present_roomId = true;
    if (this_present_roomId || that_present_roomId) {
      if (!(this_present_roomId && that_present_roomId))
        return false;
      if (this.roomId != that.roomId)
        return false;
    }

    boolean this_present_title = true && this.isSetTitle();
    boolean that_present_title = true && that.isSetTitle();
    if (this_present_title || that_present_title) {
      if (!(this_present_title && that_present_title))
        return false;
      if (!this.title.equals(that.title))
        return false;
    }

    boolean this_present_cover = true && this.isSetCover();
    boolean that_present_cover = true && that.isSetCover();
    if (this_present_cover || that_present_cover) {
      if (!(this_present_cover && that_present_cover))
        return false;
      if (!this.cover.equals(that.cover))
        return false;
    }

    boolean this_present_ow = true;
    boolean that_present_ow = true;
    if (this_present_ow || that_present_ow) {
      if (!(this_present_ow && that_present_ow))
        return false;
      if (this.ow != that.ow)
        return false;
    }

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_ssid = true;
    boolean that_present_ssid = true;
    if (this_present_ssid || that_present_ssid) {
      if (!(this_present_ssid && that_present_ssid))
        return false;
      if (this.ssid != that.ssid)
        return false;
    }

    boolean this_present_familyId = true;
    boolean that_present_familyId = true;
    if (this_present_familyId || that_present_familyId) {
      if (!(this_present_familyId && that_present_familyId))
        return false;
      if (this.familyId != that.familyId)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_roomId = true;
    list.add(present_roomId);
    if (present_roomId)
      list.add(roomId);

    boolean present_title = true && (isSetTitle());
    list.add(present_title);
    if (present_title)
      list.add(title);

    boolean present_cover = true && (isSetCover());
    list.add(present_cover);
    if (present_cover)
      list.add(cover);

    boolean present_ow = true;
    list.add(present_ow);
    if (present_ow)
      list.add(ow);

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_ssid = true;
    list.add(present_ssid);
    if (present_ssid)
      list.add(ssid);

    boolean present_familyId = true;
    list.add(present_familyId);
    if (present_familyId)
      list.add(familyId);

    return list.hashCode();
  }

  @Override
  public int compareTo(RoomInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRoomId()).compareTo(other.isSetRoomId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoomId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roomId, other.roomId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTitle()).compareTo(other.isSetTitle());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTitle()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.title, other.title);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCover()).compareTo(other.isSetCover());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCover()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.cover, other.cover);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOw()).compareTo(other.isSetOw());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOw()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ow, other.ow);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSsid()).compareTo(other.isSetSsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssid, other.ssid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFamilyId()).compareTo(other.isSetFamilyId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFamilyId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.familyId, other.familyId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RoomInfo(");
    boolean first = true;

    sb.append("roomId:");
    sb.append(this.roomId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("title:");
    if (this.title == null) {
      sb.append("null");
    } else {
      sb.append(this.title);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("cover:");
    if (this.cover == null) {
      sb.append("null");
    } else {
      sb.append(this.cover);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ow:");
    sb.append(this.ow);
    first = false;
    if (!first) sb.append(", ");
    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ssid:");
    sb.append(this.ssid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("familyId:");
    sb.append(this.familyId);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RoomInfoStandardSchemeFactory implements SchemeFactory {
    public RoomInfoStandardScheme getScheme() {
      return new RoomInfoStandardScheme();
    }
  }

  private static class RoomInfoStandardScheme extends StandardScheme<RoomInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RoomInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ROOM_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.roomId = iprot.readI32();
              struct.setRoomIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // TITLE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.title = iprot.readString();
              struct.setTitleIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // COVER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.cover = iprot.readString();
              struct.setCoverIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // OW
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ow = iprot.readI64();
              struct.setOwIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // SSID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ssid = iprot.readI64();
              struct.setSsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // FAMILY_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.familyId = iprot.readI64();
              struct.setFamilyIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RoomInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ROOM_ID_FIELD_DESC);
      oprot.writeI32(struct.roomId);
      oprot.writeFieldEnd();
      if (struct.title != null) {
        oprot.writeFieldBegin(TITLE_FIELD_DESC);
        oprot.writeString(struct.title);
        oprot.writeFieldEnd();
      }
      if (struct.cover != null) {
        oprot.writeFieldBegin(COVER_FIELD_DESC);
        oprot.writeString(struct.cover);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(OW_FIELD_DESC);
      oprot.writeI64(struct.ow);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SSID_FIELD_DESC);
      oprot.writeI64(struct.ssid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FAMILY_ID_FIELD_DESC);
      oprot.writeI64(struct.familyId);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RoomInfoTupleSchemeFactory implements SchemeFactory {
    public RoomInfoTupleScheme getScheme() {
      return new RoomInfoTupleScheme();
    }
  }

  private static class RoomInfoTupleScheme extends TupleScheme<RoomInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RoomInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRoomId()) {
        optionals.set(0);
      }
      if (struct.isSetTitle()) {
        optionals.set(1);
      }
      if (struct.isSetCover()) {
        optionals.set(2);
      }
      if (struct.isSetOw()) {
        optionals.set(3);
      }
      if (struct.isSetSid()) {
        optionals.set(4);
      }
      if (struct.isSetSsid()) {
        optionals.set(5);
      }
      if (struct.isSetFamilyId()) {
        optionals.set(6);
      }
      oprot.writeBitSet(optionals, 7);
      if (struct.isSetRoomId()) {
        oprot.writeI32(struct.roomId);
      }
      if (struct.isSetTitle()) {
        oprot.writeString(struct.title);
      }
      if (struct.isSetCover()) {
        oprot.writeString(struct.cover);
      }
      if (struct.isSetOw()) {
        oprot.writeI64(struct.ow);
      }
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetSsid()) {
        oprot.writeI64(struct.ssid);
      }
      if (struct.isSetFamilyId()) {
        oprot.writeI64(struct.familyId);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RoomInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(7);
      if (incoming.get(0)) {
        struct.roomId = iprot.readI32();
        struct.setRoomIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.title = iprot.readString();
        struct.setTitleIsSet(true);
      }
      if (incoming.get(2)) {
        struct.cover = iprot.readString();
        struct.setCoverIsSet(true);
      }
      if (incoming.get(3)) {
        struct.ow = iprot.readI64();
        struct.setOwIsSet(true);
      }
      if (incoming.get(4)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(5)) {
        struct.ssid = iprot.readI64();
        struct.setSsidIsSet(true);
      }
      if (incoming.get(6)) {
        struct.familyId = iprot.readI64();
        struct.setFamilyIdIsSet(true);
      }
    }
  }

}

