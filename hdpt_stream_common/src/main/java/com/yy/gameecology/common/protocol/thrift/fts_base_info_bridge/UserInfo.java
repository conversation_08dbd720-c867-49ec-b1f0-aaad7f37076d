/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class UserInfo implements org.apache.thrift.TBase<UserInfo, UserInfo._Fields>, java.io.Serializable, Cloneable, Comparable<UserInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("UserInfo");

  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField AVATAR_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("avatar_url", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField NICK_FIELD_DESC = new org.apache.thrift.protocol.TField("nick", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField NOBLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("noble_id", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField ECOLOGY_NOBLE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("ecology_noble_id", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField IMID_FIELD_DESC = new org.apache.thrift.protocol.TField("imid", org.apache.thrift.protocol.TType.I64, (short)6);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new UserInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new UserInfoTupleSchemeFactory());
  }

  public long uid; // required
  public String avatar_url; // required
  public String nick; // required
  public long noble_id; // required
  public long ecology_noble_id; // required
  public long imid; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    UID((short)1, "uid"),
    AVATAR_URL((short)2, "avatar_url"),
    NICK((short)3, "nick"),
    NOBLE_ID((short)4, "noble_id"),
    ECOLOGY_NOBLE_ID((short)5, "ecology_noble_id"),
    IMID((short)6, "imid");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // UID
          return UID;
        case 2: // AVATAR_URL
          return AVATAR_URL;
        case 3: // NICK
          return NICK;
        case 4: // NOBLE_ID
          return NOBLE_ID;
        case 5: // ECOLOGY_NOBLE_ID
          return ECOLOGY_NOBLE_ID;
        case 6: // IMID
          return IMID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __UID_ISSET_ID = 0;
  private static final int __NOBLE_ID_ISSET_ID = 1;
  private static final int __ECOLOGY_NOBLE_ID_ISSET_ID = 2;
  private static final int __IMID_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.AVATAR_URL, new org.apache.thrift.meta_data.FieldMetaData("avatar_url", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.NICK, new org.apache.thrift.meta_data.FieldMetaData("nick", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.NOBLE_ID, new org.apache.thrift.meta_data.FieldMetaData("noble_id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ECOLOGY_NOBLE_ID, new org.apache.thrift.meta_data.FieldMetaData("ecology_noble_id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.IMID, new org.apache.thrift.meta_data.FieldMetaData("imid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(UserInfo.class, metaDataMap);
  }

  public UserInfo() {
  }

  public UserInfo(
    long uid,
    String avatar_url,
    String nick,
    long noble_id,
    long ecology_noble_id,
    long imid)
  {
    this();
    this.uid = uid;
    setUidIsSet(true);
    this.avatar_url = avatar_url;
    this.nick = nick;
    this.noble_id = noble_id;
    setNoble_idIsSet(true);
    this.ecology_noble_id = ecology_noble_id;
    setEcology_noble_idIsSet(true);
    this.imid = imid;
    setImidIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public UserInfo(UserInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.uid = other.uid;
    if (other.isSetAvatar_url()) {
      this.avatar_url = other.avatar_url;
    }
    if (other.isSetNick()) {
      this.nick = other.nick;
    }
    this.noble_id = other.noble_id;
    this.ecology_noble_id = other.ecology_noble_id;
    this.imid = other.imid;
  }

  public UserInfo deepCopy() {
    return new UserInfo(this);
  }

  @Override
  public void clear() {
    setUidIsSet(false);
    this.uid = 0;
    this.avatar_url = null;
    this.nick = null;
    setNoble_idIsSet(false);
    this.noble_id = 0;
    setEcology_noble_idIsSet(false);
    this.ecology_noble_id = 0;
    setImidIsSet(false);
    this.imid = 0;
  }

  public long getUid() {
    return this.uid;
  }

  public UserInfo setUid(long uid) {
    this.uid = uid;
    setUidIsSet(true);
    return this;
  }

  public void unsetUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __UID_ISSET_ID);
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return EncodingUtils.testBit(__isset_bitfield, __UID_ISSET_ID);
  }

  public void setUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __UID_ISSET_ID, value);
  }

  public String getAvatar_url() {
    return this.avatar_url;
  }

  public UserInfo setAvatar_url(String avatar_url) {
    this.avatar_url = avatar_url;
    return this;
  }

  public void unsetAvatar_url() {
    this.avatar_url = null;
  }

  /** Returns true if field avatar_url is set (has been assigned a value) and false otherwise */
  public boolean isSetAvatar_url() {
    return this.avatar_url != null;
  }

  public void setAvatar_urlIsSet(boolean value) {
    if (!value) {
      this.avatar_url = null;
    }
  }

  public String getNick() {
    return this.nick;
  }

  public UserInfo setNick(String nick) {
    this.nick = nick;
    return this;
  }

  public void unsetNick() {
    this.nick = null;
  }

  /** Returns true if field nick is set (has been assigned a value) and false otherwise */
  public boolean isSetNick() {
    return this.nick != null;
  }

  public void setNickIsSet(boolean value) {
    if (!value) {
      this.nick = null;
    }
  }

  public long getNoble_id() {
    return this.noble_id;
  }

  public UserInfo setNoble_id(long noble_id) {
    this.noble_id = noble_id;
    setNoble_idIsSet(true);
    return this;
  }

  public void unsetNoble_id() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __NOBLE_ID_ISSET_ID);
  }

  /** Returns true if field noble_id is set (has been assigned a value) and false otherwise */
  public boolean isSetNoble_id() {
    return EncodingUtils.testBit(__isset_bitfield, __NOBLE_ID_ISSET_ID);
  }

  public void setNoble_idIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __NOBLE_ID_ISSET_ID, value);
  }

  public long getEcology_noble_id() {
    return this.ecology_noble_id;
  }

  public UserInfo setEcology_noble_id(long ecology_noble_id) {
    this.ecology_noble_id = ecology_noble_id;
    setEcology_noble_idIsSet(true);
    return this;
  }

  public void unsetEcology_noble_id() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ECOLOGY_NOBLE_ID_ISSET_ID);
  }

  /** Returns true if field ecology_noble_id is set (has been assigned a value) and false otherwise */
  public boolean isSetEcology_noble_id() {
    return EncodingUtils.testBit(__isset_bitfield, __ECOLOGY_NOBLE_ID_ISSET_ID);
  }

  public void setEcology_noble_idIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ECOLOGY_NOBLE_ID_ISSET_ID, value);
  }

  public long getImid() {
    return this.imid;
  }

  public UserInfo setImid(long imid) {
    this.imid = imid;
    setImidIsSet(true);
    return this;
  }

  public void unsetImid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __IMID_ISSET_ID);
  }

  /** Returns true if field imid is set (has been assigned a value) and false otherwise */
  public boolean isSetImid() {
    return EncodingUtils.testBit(__isset_bitfield, __IMID_ISSET_ID);
  }

  public void setImidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __IMID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((Long)value);
      }
      break;

    case AVATAR_URL:
      if (value == null) {
        unsetAvatar_url();
      } else {
        setAvatar_url((String)value);
      }
      break;

    case NICK:
      if (value == null) {
        unsetNick();
      } else {
        setNick((String)value);
      }
      break;

    case NOBLE_ID:
      if (value == null) {
        unsetNoble_id();
      } else {
        setNoble_id((Long)value);
      }
      break;

    case ECOLOGY_NOBLE_ID:
      if (value == null) {
        unsetEcology_noble_id();
      } else {
        setEcology_noble_id((Long)value);
      }
      break;

    case IMID:
      if (value == null) {
        unsetImid();
      } else {
        setImid((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case UID:
      return getUid();

    case AVATAR_URL:
      return getAvatar_url();

    case NICK:
      return getNick();

    case NOBLE_ID:
      return getNoble_id();

    case ECOLOGY_NOBLE_ID:
      return getEcology_noble_id();

    case IMID:
      return getImid();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case UID:
      return isSetUid();
    case AVATAR_URL:
      return isSetAvatar_url();
    case NICK:
      return isSetNick();
    case NOBLE_ID:
      return isSetNoble_id();
    case ECOLOGY_NOBLE_ID:
      return isSetEcology_noble_id();
    case IMID:
      return isSetImid();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof UserInfo)
      return this.equals((UserInfo)that);
    return false;
  }

  public boolean equals(UserInfo that) {
    if (that == null)
      return false;

    boolean this_present_uid = true;
    boolean that_present_uid = true;
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (this.uid != that.uid)
        return false;
    }

    boolean this_present_avatar_url = true && this.isSetAvatar_url();
    boolean that_present_avatar_url = true && that.isSetAvatar_url();
    if (this_present_avatar_url || that_present_avatar_url) {
      if (!(this_present_avatar_url && that_present_avatar_url))
        return false;
      if (!this.avatar_url.equals(that.avatar_url))
        return false;
    }

    boolean this_present_nick = true && this.isSetNick();
    boolean that_present_nick = true && that.isSetNick();
    if (this_present_nick || that_present_nick) {
      if (!(this_present_nick && that_present_nick))
        return false;
      if (!this.nick.equals(that.nick))
        return false;
    }

    boolean this_present_noble_id = true;
    boolean that_present_noble_id = true;
    if (this_present_noble_id || that_present_noble_id) {
      if (!(this_present_noble_id && that_present_noble_id))
        return false;
      if (this.noble_id != that.noble_id)
        return false;
    }

    boolean this_present_ecology_noble_id = true;
    boolean that_present_ecology_noble_id = true;
    if (this_present_ecology_noble_id || that_present_ecology_noble_id) {
      if (!(this_present_ecology_noble_id && that_present_ecology_noble_id))
        return false;
      if (this.ecology_noble_id != that.ecology_noble_id)
        return false;
    }

    boolean this_present_imid = true;
    boolean that_present_imid = true;
    if (this_present_imid || that_present_imid) {
      if (!(this_present_imid && that_present_imid))
        return false;
      if (this.imid != that.imid)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_uid = true;
    list.add(present_uid);
    if (present_uid)
      list.add(uid);

    boolean present_avatar_url = true && (isSetAvatar_url());
    list.add(present_avatar_url);
    if (present_avatar_url)
      list.add(avatar_url);

    boolean present_nick = true && (isSetNick());
    list.add(present_nick);
    if (present_nick)
      list.add(nick);

    boolean present_noble_id = true;
    list.add(present_noble_id);
    if (present_noble_id)
      list.add(noble_id);

    boolean present_ecology_noble_id = true;
    list.add(present_ecology_noble_id);
    if (present_ecology_noble_id)
      list.add(ecology_noble_id);

    boolean present_imid = true;
    list.add(present_imid);
    if (present_imid)
      list.add(imid);

    return list.hashCode();
  }

  @Override
  public int compareTo(UserInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAvatar_url()).compareTo(other.isSetAvatar_url());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAvatar_url()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.avatar_url, other.avatar_url);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetNick()).compareTo(other.isSetNick());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNick()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.nick, other.nick);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetNoble_id()).compareTo(other.isSetNoble_id());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetNoble_id()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.noble_id, other.noble_id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetEcology_noble_id()).compareTo(other.isSetEcology_noble_id());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEcology_noble_id()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ecology_noble_id, other.ecology_noble_id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetImid()).compareTo(other.isSetImid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetImid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.imid, other.imid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("UserInfo(");
    boolean first = true;

    sb.append("uid:");
    sb.append(this.uid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("avatar_url:");
    if (this.avatar_url == null) {
      sb.append("null");
    } else {
      sb.append(this.avatar_url);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("nick:");
    if (this.nick == null) {
      sb.append("null");
    } else {
      sb.append(this.nick);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("noble_id:");
    sb.append(this.noble_id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ecology_noble_id:");
    sb.append(this.ecology_noble_id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("imid:");
    sb.append(this.imid);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class UserInfoStandardSchemeFactory implements SchemeFactory {
    public UserInfoStandardScheme getScheme() {
      return new UserInfoStandardScheme();
    }
  }

  private static class UserInfoStandardScheme extends StandardScheme<UserInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, UserInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.uid = iprot.readI64();
              struct.setUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // AVATAR_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.avatar_url = iprot.readString();
              struct.setAvatar_urlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // NICK
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.nick = iprot.readString();
              struct.setNickIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // NOBLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.noble_id = iprot.readI64();
              struct.setNoble_idIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // ECOLOGY_NOBLE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ecology_noble_id = iprot.readI64();
              struct.setEcology_noble_idIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // IMID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.imid = iprot.readI64();
              struct.setImidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, UserInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(UID_FIELD_DESC);
      oprot.writeI64(struct.uid);
      oprot.writeFieldEnd();
      if (struct.avatar_url != null) {
        oprot.writeFieldBegin(AVATAR_URL_FIELD_DESC);
        oprot.writeString(struct.avatar_url);
        oprot.writeFieldEnd();
      }
      if (struct.nick != null) {
        oprot.writeFieldBegin(NICK_FIELD_DESC);
        oprot.writeString(struct.nick);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(NOBLE_ID_FIELD_DESC);
      oprot.writeI64(struct.noble_id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(ECOLOGY_NOBLE_ID_FIELD_DESC);
      oprot.writeI64(struct.ecology_noble_id);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(IMID_FIELD_DESC);
      oprot.writeI64(struct.imid);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class UserInfoTupleSchemeFactory implements SchemeFactory {
    public UserInfoTupleScheme getScheme() {
      return new UserInfoTupleScheme();
    }
  }

  private static class UserInfoTupleScheme extends TupleScheme<UserInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, UserInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetUid()) {
        optionals.set(0);
      }
      if (struct.isSetAvatar_url()) {
        optionals.set(1);
      }
      if (struct.isSetNick()) {
        optionals.set(2);
      }
      if (struct.isSetNoble_id()) {
        optionals.set(3);
      }
      if (struct.isSetEcology_noble_id()) {
        optionals.set(4);
      }
      if (struct.isSetImid()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetUid()) {
        oprot.writeI64(struct.uid);
      }
      if (struct.isSetAvatar_url()) {
        oprot.writeString(struct.avatar_url);
      }
      if (struct.isSetNick()) {
        oprot.writeString(struct.nick);
      }
      if (struct.isSetNoble_id()) {
        oprot.writeI64(struct.noble_id);
      }
      if (struct.isSetEcology_noble_id()) {
        oprot.writeI64(struct.ecology_noble_id);
      }
      if (struct.isSetImid()) {
        oprot.writeI64(struct.imid);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, UserInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.uid = iprot.readI64();
        struct.setUidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.avatar_url = iprot.readString();
        struct.setAvatar_urlIsSet(true);
      }
      if (incoming.get(2)) {
        struct.nick = iprot.readString();
        struct.setNickIsSet(true);
      }
      if (incoming.get(3)) {
        struct.noble_id = iprot.readI64();
        struct.setNoble_idIsSet(true);
      }
      if (incoming.get(4)) {
        struct.ecology_noble_id = iprot.readI64();
        struct.setEcology_noble_idIsSet(true);
      }
      if (incoming.get(5)) {
        struct.imid = iprot.readI64();
        struct.setImidIsSet(true);
      }
    }
  }

}

