/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class SaveEnrollmentInfoRequest implements org.apache.thrift.TBase<SaveEnrollmentInfoRequest, SaveEnrollmentInfoRequest._Fields>, java.io.Serializable, Cloneable, Comparable<SaveEnrollmentInfoRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaveEnrollmentInfoRequest");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField SEQ_FIELD_DESC = new org.apache.thrift.protocol.TField("seq", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField ENROLLMENT_INFO_FIELD_DESC = new org.apache.thrift.protocol.TField("enrollmentInfo", org.apache.thrift.protocol.TType.STRUCT, (short)3);
  private static final org.apache.thrift.protocol.TField UPDATE_FIELD_DESC = new org.apache.thrift.protocol.TField("update", org.apache.thrift.protocol.TType.BOOL, (short)4);
  private static final org.apache.thrift.protocol.TField REMARK_FIELD_DESC = new org.apache.thrift.protocol.TField("remark", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaveEnrollmentInfoRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaveEnrollmentInfoRequestTupleSchemeFactory());
  }

  public long actId; // required
  public String seq; // required
  public EnrollmentInfo enrollmentInfo; // required
  public boolean update; // required
  public String remark; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    SEQ((short)2, "seq"),
    ENROLLMENT_INFO((short)3, "enrollmentInfo"),
    UPDATE((short)4, "update"),
    REMARK((short)5, "remark"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // SEQ
          return SEQ;
        case 3: // ENROLLMENT_INFO
          return ENROLLMENT_INFO;
        case 4: // UPDATE
          return UPDATE;
        case 5: // REMARK
          return REMARK;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __UPDATE_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SEQ, new org.apache.thrift.meta_data.FieldMetaData("seq", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ENROLLMENT_INFO, new org.apache.thrift.meta_data.FieldMetaData("enrollmentInfo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, EnrollmentInfo.class)));
    tmpMap.put(_Fields.UPDATE, new org.apache.thrift.meta_data.FieldMetaData("update", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.REMARK, new org.apache.thrift.meta_data.FieldMetaData("remark", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaveEnrollmentInfoRequest.class, metaDataMap);
  }

  public SaveEnrollmentInfoRequest() {
  }

  public SaveEnrollmentInfoRequest(
    long actId,
    String seq,
    EnrollmentInfo enrollmentInfo,
    boolean update,
    String remark,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.seq = seq;
    this.enrollmentInfo = enrollmentInfo;
    this.update = update;
    setUpdateIsSet(true);
    this.remark = remark;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaveEnrollmentInfoRequest(SaveEnrollmentInfoRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    if (other.isSetSeq()) {
      this.seq = other.seq;
    }
    if (other.isSetEnrollmentInfo()) {
      this.enrollmentInfo = new EnrollmentInfo(other.enrollmentInfo);
    }
    this.update = other.update;
    if (other.isSetRemark()) {
      this.remark = other.remark;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public SaveEnrollmentInfoRequest deepCopy() {
    return new SaveEnrollmentInfoRequest(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    this.seq = null;
    this.enrollmentInfo = null;
    setUpdateIsSet(false);
    this.update = false;
    this.remark = null;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public SaveEnrollmentInfoRequest setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public String getSeq() {
    return this.seq;
  }

  public SaveEnrollmentInfoRequest setSeq(String seq) {
    this.seq = seq;
    return this;
  }

  public void unsetSeq() {
    this.seq = null;
  }

  /** Returns true if field seq is set (has been assigned a value) and false otherwise */
  public boolean isSetSeq() {
    return this.seq != null;
  }

  public void setSeqIsSet(boolean value) {
    if (!value) {
      this.seq = null;
    }
  }

  public EnrollmentInfo getEnrollmentInfo() {
    return this.enrollmentInfo;
  }

  public SaveEnrollmentInfoRequest setEnrollmentInfo(EnrollmentInfo enrollmentInfo) {
    this.enrollmentInfo = enrollmentInfo;
    return this;
  }

  public void unsetEnrollmentInfo() {
    this.enrollmentInfo = null;
  }

  /** Returns true if field enrollmentInfo is set (has been assigned a value) and false otherwise */
  public boolean isSetEnrollmentInfo() {
    return this.enrollmentInfo != null;
  }

  public void setEnrollmentInfoIsSet(boolean value) {
    if (!value) {
      this.enrollmentInfo = null;
    }
  }

  public boolean isUpdate() {
    return this.update;
  }

  public SaveEnrollmentInfoRequest setUpdate(boolean update) {
    this.update = update;
    setUpdateIsSet(true);
    return this;
  }

  public void unsetUpdate() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __UPDATE_ISSET_ID);
  }

  /** Returns true if field update is set (has been assigned a value) and false otherwise */
  public boolean isSetUpdate() {
    return EncodingUtils.testBit(__isset_bitfield, __UPDATE_ISSET_ID);
  }

  public void setUpdateIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __UPDATE_ISSET_ID, value);
  }

  public String getRemark() {
    return this.remark;
  }

  public SaveEnrollmentInfoRequest setRemark(String remark) {
    this.remark = remark;
    return this;
  }

  public void unsetRemark() {
    this.remark = null;
  }

  /** Returns true if field remark is set (has been assigned a value) and false otherwise */
  public boolean isSetRemark() {
    return this.remark != null;
  }

  public void setRemarkIsSet(boolean value) {
    if (!value) {
      this.remark = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public SaveEnrollmentInfoRequest setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case SEQ:
      if (value == null) {
        unsetSeq();
      } else {
        setSeq((String)value);
      }
      break;

    case ENROLLMENT_INFO:
      if (value == null) {
        unsetEnrollmentInfo();
      } else {
        setEnrollmentInfo((EnrollmentInfo)value);
      }
      break;

    case UPDATE:
      if (value == null) {
        unsetUpdate();
      } else {
        setUpdate((Boolean)value);
      }
      break;

    case REMARK:
      if (value == null) {
        unsetRemark();
      } else {
        setRemark((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case SEQ:
      return getSeq();

    case ENROLLMENT_INFO:
      return getEnrollmentInfo();

    case UPDATE:
      return isUpdate();

    case REMARK:
      return getRemark();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case SEQ:
      return isSetSeq();
    case ENROLLMENT_INFO:
      return isSetEnrollmentInfo();
    case UPDATE:
      return isSetUpdate();
    case REMARK:
      return isSetRemark();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaveEnrollmentInfoRequest)
      return this.equals((SaveEnrollmentInfoRequest)that);
    return false;
  }

  public boolean equals(SaveEnrollmentInfoRequest that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_seq = true && this.isSetSeq();
    boolean that_present_seq = true && that.isSetSeq();
    if (this_present_seq || that_present_seq) {
      if (!(this_present_seq && that_present_seq))
        return false;
      if (!this.seq.equals(that.seq))
        return false;
    }

    boolean this_present_enrollmentInfo = true && this.isSetEnrollmentInfo();
    boolean that_present_enrollmentInfo = true && that.isSetEnrollmentInfo();
    if (this_present_enrollmentInfo || that_present_enrollmentInfo) {
      if (!(this_present_enrollmentInfo && that_present_enrollmentInfo))
        return false;
      if (!this.enrollmentInfo.equals(that.enrollmentInfo))
        return false;
    }

    boolean this_present_update = true;
    boolean that_present_update = true;
    if (this_present_update || that_present_update) {
      if (!(this_present_update && that_present_update))
        return false;
      if (this.update != that.update)
        return false;
    }

    boolean this_present_remark = true && this.isSetRemark();
    boolean that_present_remark = true && that.isSetRemark();
    if (this_present_remark || that_present_remark) {
      if (!(this_present_remark && that_present_remark))
        return false;
      if (!this.remark.equals(that.remark))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_seq = true && (isSetSeq());
    list.add(present_seq);
    if (present_seq)
      list.add(seq);

    boolean present_enrollmentInfo = true && (isSetEnrollmentInfo());
    list.add(present_enrollmentInfo);
    if (present_enrollmentInfo)
      list.add(enrollmentInfo);

    boolean present_update = true;
    list.add(present_update);
    if (present_update)
      list.add(update);

    boolean present_remark = true && (isSetRemark());
    list.add(present_remark);
    if (present_remark)
      list.add(remark);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaveEnrollmentInfoRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSeq()).compareTo(other.isSetSeq());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSeq()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.seq, other.seq);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetEnrollmentInfo()).compareTo(other.isSetEnrollmentInfo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetEnrollmentInfo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.enrollmentInfo, other.enrollmentInfo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUpdate()).compareTo(other.isSetUpdate());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUpdate()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.update, other.update);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRemark()).compareTo(other.isSetRemark());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRemark()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.remark, other.remark);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaveEnrollmentInfoRequest(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("seq:");
    if (this.seq == null) {
      sb.append("null");
    } else {
      sb.append(this.seq);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("enrollmentInfo:");
    if (this.enrollmentInfo == null) {
      sb.append("null");
    } else {
      sb.append(this.enrollmentInfo);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("update:");
    sb.append(this.update);
    first = false;
    if (!first) sb.append(", ");
    sb.append("remark:");
    if (this.remark == null) {
      sb.append("null");
    } else {
      sb.append(this.remark);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (enrollmentInfo != null) {
      enrollmentInfo.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaveEnrollmentInfoRequestStandardSchemeFactory implements SchemeFactory {
    public SaveEnrollmentInfoRequestStandardScheme getScheme() {
      return new SaveEnrollmentInfoRequestStandardScheme();
    }
  }

  private static class SaveEnrollmentInfoRequestStandardScheme extends StandardScheme<SaveEnrollmentInfoRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaveEnrollmentInfoRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SEQ
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.seq = iprot.readString();
              struct.setSeqIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ENROLLMENT_INFO
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.enrollmentInfo = new EnrollmentInfo();
              struct.enrollmentInfo.read(iprot);
              struct.setEnrollmentInfoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // UPDATE
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.update = iprot.readBool();
              struct.setUpdateIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // REMARK
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.remark = iprot.readString();
              struct.setRemarkIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map746 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map746.size);
                String _key747;
                String _val748;
                for (int _i749 = 0; _i749 < _map746.size; ++_i749)
                {
                  _key747 = iprot.readString();
                  _val748 = iprot.readString();
                  struct.extData.put(_key747, _val748);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaveEnrollmentInfoRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      if (struct.seq != null) {
        oprot.writeFieldBegin(SEQ_FIELD_DESC);
        oprot.writeString(struct.seq);
        oprot.writeFieldEnd();
      }
      if (struct.enrollmentInfo != null) {
        oprot.writeFieldBegin(ENROLLMENT_INFO_FIELD_DESC);
        struct.enrollmentInfo.write(oprot);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(UPDATE_FIELD_DESC);
      oprot.writeBool(struct.update);
      oprot.writeFieldEnd();
      if (struct.remark != null) {
        oprot.writeFieldBegin(REMARK_FIELD_DESC);
        oprot.writeString(struct.remark);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter750 : struct.extData.entrySet())
          {
            oprot.writeString(_iter750.getKey());
            oprot.writeString(_iter750.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaveEnrollmentInfoRequestTupleSchemeFactory implements SchemeFactory {
    public SaveEnrollmentInfoRequestTupleScheme getScheme() {
      return new SaveEnrollmentInfoRequestTupleScheme();
    }
  }

  private static class SaveEnrollmentInfoRequestTupleScheme extends TupleScheme<SaveEnrollmentInfoRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaveEnrollmentInfoRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetSeq()) {
        optionals.set(1);
      }
      if (struct.isSetEnrollmentInfo()) {
        optionals.set(2);
      }
      if (struct.isSetUpdate()) {
        optionals.set(3);
      }
      if (struct.isSetRemark()) {
        optionals.set(4);
      }
      if (struct.isSetExtData()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetSeq()) {
        oprot.writeString(struct.seq);
      }
      if (struct.isSetEnrollmentInfo()) {
        struct.enrollmentInfo.write(oprot);
      }
      if (struct.isSetUpdate()) {
        oprot.writeBool(struct.update);
      }
      if (struct.isSetRemark()) {
        oprot.writeString(struct.remark);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter751 : struct.extData.entrySet())
          {
            oprot.writeString(_iter751.getKey());
            oprot.writeString(_iter751.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaveEnrollmentInfoRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.seq = iprot.readString();
        struct.setSeqIsSet(true);
      }
      if (incoming.get(2)) {
        struct.enrollmentInfo = new EnrollmentInfo();
        struct.enrollmentInfo.read(iprot);
        struct.setEnrollmentInfoIsSet(true);
      }
      if (incoming.get(3)) {
        struct.update = iprot.readBool();
        struct.setUpdateIsSet(true);
      }
      if (incoming.get(4)) {
        struct.remark = iprot.readString();
        struct.setRemarkIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TMap _map752 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map752.size);
          String _key753;
          String _val754;
          for (int _i755 = 0; _i755 < _map752.size; ++_i755)
          {
            _key753 = iprot.readString();
            _val754 = iprot.readString();
            struct.extData.put(_key753, _val754);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

