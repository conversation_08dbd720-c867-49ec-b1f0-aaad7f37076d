/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class ZsetQueryCondition implements org.apache.thrift.TBase<ZsetQueryCondition, ZsetQueryCondition._Fields>, java.io.Serializable, Cloneable, Comparable<ZsetQueryCondition> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ZsetQueryCondition");

  private static final org.apache.thrift.protocol.TField KEY_FIELD_DESC = new org.apache.thrift.protocol.TField("key", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField WAY_FIELD_DESC = new org.apache.thrift.protocol.TField("way", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField FROM_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("fromName", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField TO_FIELD_DESC = new org.apache.thrift.protocol.TField("to", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField OFFSET_FIELD_DESC = new org.apache.thrift.protocol.TField("offset", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("count", org.apache.thrift.protocol.TType.I64, (short)6);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ZsetQueryConditionStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ZsetQueryConditionTupleSchemeFactory());
  }

  public String key; // required
  public int way; // required
  /**
   * thrift0.9 不允许from
   * 
   */
  public long fromName; // required
  public long to; // required
  public long offset; // required
  public long count; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    KEY((short)1, "key"),
    WAY((short)2, "way"),
    /**
     * thrift0.9 不允许from
     * 
     */
    FROM_NAME((short)3, "fromName"),
    TO((short)4, "to"),
    OFFSET((short)5, "offset"),
    COUNT((short)6, "count");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // KEY
          return KEY;
        case 2: // WAY
          return WAY;
        case 3: // FROM_NAME
          return FROM_NAME;
        case 4: // TO
          return TO;
        case 5: // OFFSET
          return OFFSET;
        case 6: // COUNT
          return COUNT;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __WAY_ISSET_ID = 0;
  private static final int __FROMNAME_ISSET_ID = 1;
  private static final int __TO_ISSET_ID = 2;
  private static final int __OFFSET_ISSET_ID = 3;
  private static final int __COUNT_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.KEY, new org.apache.thrift.meta_data.FieldMetaData("key", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.WAY, new org.apache.thrift.meta_data.FieldMetaData("way", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.FROM_NAME, new org.apache.thrift.meta_data.FieldMetaData("fromName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TO, new org.apache.thrift.meta_data.FieldMetaData("to", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.OFFSET, new org.apache.thrift.meta_data.FieldMetaData("offset", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.COUNT, new org.apache.thrift.meta_data.FieldMetaData("count", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ZsetQueryCondition.class, metaDataMap);
  }

  public ZsetQueryCondition() {
  }

  public ZsetQueryCondition(
    String key,
    int way,
    long fromName,
    long to,
    long offset,
    long count)
  {
    this();
    this.key = key;
    this.way = way;
    setWayIsSet(true);
    this.fromName = fromName;
    setFromNameIsSet(true);
    this.to = to;
    setToIsSet(true);
    this.offset = offset;
    setOffsetIsSet(true);
    this.count = count;
    setCountIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ZsetQueryCondition(ZsetQueryCondition other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetKey()) {
      this.key = other.key;
    }
    this.way = other.way;
    this.fromName = other.fromName;
    this.to = other.to;
    this.offset = other.offset;
    this.count = other.count;
  }

  public ZsetQueryCondition deepCopy() {
    return new ZsetQueryCondition(this);
  }

  @Override
  public void clear() {
    this.key = null;
    setWayIsSet(false);
    this.way = 0;
    setFromNameIsSet(false);
    this.fromName = 0;
    setToIsSet(false);
    this.to = 0;
    setOffsetIsSet(false);
    this.offset = 0;
    setCountIsSet(false);
    this.count = 0;
  }

  public String getKey() {
    return this.key;
  }

  public ZsetQueryCondition setKey(String key) {
    this.key = key;
    return this;
  }

  public void unsetKey() {
    this.key = null;
  }

  /** Returns true if field key is set (has been assigned a value) and false otherwise */
  public boolean isSetKey() {
    return this.key != null;
  }

  public void setKeyIsSet(boolean value) {
    if (!value) {
      this.key = null;
    }
  }

  public int getWay() {
    return this.way;
  }

  public ZsetQueryCondition setWay(int way) {
    this.way = way;
    setWayIsSet(true);
    return this;
  }

  public void unsetWay() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __WAY_ISSET_ID);
  }

  /** Returns true if field way is set (has been assigned a value) and false otherwise */
  public boolean isSetWay() {
    return EncodingUtils.testBit(__isset_bitfield, __WAY_ISSET_ID);
  }

  public void setWayIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __WAY_ISSET_ID, value);
  }

  /**
   * thrift0.9 不允许from
   * 
   */
  public long getFromName() {
    return this.fromName;
  }

  /**
   * thrift0.9 不允许from
   * 
   */
  public ZsetQueryCondition setFromName(long fromName) {
    this.fromName = fromName;
    setFromNameIsSet(true);
    return this;
  }

  public void unsetFromName() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FROMNAME_ISSET_ID);
  }

  /** Returns true if field fromName is set (has been assigned a value) and false otherwise */
  public boolean isSetFromName() {
    return EncodingUtils.testBit(__isset_bitfield, __FROMNAME_ISSET_ID);
  }

  public void setFromNameIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FROMNAME_ISSET_ID, value);
  }

  public long getTo() {
    return this.to;
  }

  public ZsetQueryCondition setTo(long to) {
    this.to = to;
    setToIsSet(true);
    return this;
  }

  public void unsetTo() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TO_ISSET_ID);
  }

  /** Returns true if field to is set (has been assigned a value) and false otherwise */
  public boolean isSetTo() {
    return EncodingUtils.testBit(__isset_bitfield, __TO_ISSET_ID);
  }

  public void setToIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TO_ISSET_ID, value);
  }

  public long getOffset() {
    return this.offset;
  }

  public ZsetQueryCondition setOffset(long offset) {
    this.offset = offset;
    setOffsetIsSet(true);
    return this;
  }

  public void unsetOffset() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __OFFSET_ISSET_ID);
  }

  /** Returns true if field offset is set (has been assigned a value) and false otherwise */
  public boolean isSetOffset() {
    return EncodingUtils.testBit(__isset_bitfield, __OFFSET_ISSET_ID);
  }

  public void setOffsetIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __OFFSET_ISSET_ID, value);
  }

  public long getCount() {
    return this.count;
  }

  public ZsetQueryCondition setCount(long count) {
    this.count = count;
    setCountIsSet(true);
    return this;
  }

  public void unsetCount() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COUNT_ISSET_ID);
  }

  /** Returns true if field count is set (has been assigned a value) and false otherwise */
  public boolean isSetCount() {
    return EncodingUtils.testBit(__isset_bitfield, __COUNT_ISSET_ID);
  }

  public void setCountIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COUNT_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case KEY:
      if (value == null) {
        unsetKey();
      } else {
        setKey((String)value);
      }
      break;

    case WAY:
      if (value == null) {
        unsetWay();
      } else {
        setWay((Integer)value);
      }
      break;

    case FROM_NAME:
      if (value == null) {
        unsetFromName();
      } else {
        setFromName((Long)value);
      }
      break;

    case TO:
      if (value == null) {
        unsetTo();
      } else {
        setTo((Long)value);
      }
      break;

    case OFFSET:
      if (value == null) {
        unsetOffset();
      } else {
        setOffset((Long)value);
      }
      break;

    case COUNT:
      if (value == null) {
        unsetCount();
      } else {
        setCount((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case KEY:
      return getKey();

    case WAY:
      return getWay();

    case FROM_NAME:
      return getFromName();

    case TO:
      return getTo();

    case OFFSET:
      return getOffset();

    case COUNT:
      return getCount();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case KEY:
      return isSetKey();
    case WAY:
      return isSetWay();
    case FROM_NAME:
      return isSetFromName();
    case TO:
      return isSetTo();
    case OFFSET:
      return isSetOffset();
    case COUNT:
      return isSetCount();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ZsetQueryCondition)
      return this.equals((ZsetQueryCondition)that);
    return false;
  }

  public boolean equals(ZsetQueryCondition that) {
    if (that == null)
      return false;

    boolean this_present_key = true && this.isSetKey();
    boolean that_present_key = true && that.isSetKey();
    if (this_present_key || that_present_key) {
      if (!(this_present_key && that_present_key))
        return false;
      if (!this.key.equals(that.key))
        return false;
    }

    boolean this_present_way = true;
    boolean that_present_way = true;
    if (this_present_way || that_present_way) {
      if (!(this_present_way && that_present_way))
        return false;
      if (this.way != that.way)
        return false;
    }

    boolean this_present_fromName = true;
    boolean that_present_fromName = true;
    if (this_present_fromName || that_present_fromName) {
      if (!(this_present_fromName && that_present_fromName))
        return false;
      if (this.fromName != that.fromName)
        return false;
    }

    boolean this_present_to = true;
    boolean that_present_to = true;
    if (this_present_to || that_present_to) {
      if (!(this_present_to && that_present_to))
        return false;
      if (this.to != that.to)
        return false;
    }

    boolean this_present_offset = true;
    boolean that_present_offset = true;
    if (this_present_offset || that_present_offset) {
      if (!(this_present_offset && that_present_offset))
        return false;
      if (this.offset != that.offset)
        return false;
    }

    boolean this_present_count = true;
    boolean that_present_count = true;
    if (this_present_count || that_present_count) {
      if (!(this_present_count && that_present_count))
        return false;
      if (this.count != that.count)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_key = true && (isSetKey());
    list.add(present_key);
    if (present_key)
      list.add(key);

    boolean present_way = true;
    list.add(present_way);
    if (present_way)
      list.add(way);

    boolean present_fromName = true;
    list.add(present_fromName);
    if (present_fromName)
      list.add(fromName);

    boolean present_to = true;
    list.add(present_to);
    if (present_to)
      list.add(to);

    boolean present_offset = true;
    list.add(present_offset);
    if (present_offset)
      list.add(offset);

    boolean present_count = true;
    list.add(present_count);
    if (present_count)
      list.add(count);

    return list.hashCode();
  }

  @Override
  public int compareTo(ZsetQueryCondition other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetKey()).compareTo(other.isSetKey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.key, other.key);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetWay()).compareTo(other.isSetWay());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetWay()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.way, other.way);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFromName()).compareTo(other.isSetFromName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFromName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.fromName, other.fromName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTo()).compareTo(other.isSetTo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.to, other.to);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetOffset()).compareTo(other.isSetOffset());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetOffset()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.offset, other.offset);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCount()).compareTo(other.isSetCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.count, other.count);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ZsetQueryCondition(");
    boolean first = true;

    sb.append("key:");
    if (this.key == null) {
      sb.append("null");
    } else {
      sb.append(this.key);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("way:");
    sb.append(this.way);
    first = false;
    if (!first) sb.append(", ");
    sb.append("fromName:");
    sb.append(this.fromName);
    first = false;
    if (!first) sb.append(", ");
    sb.append("to:");
    sb.append(this.to);
    first = false;
    if (!first) sb.append(", ");
    sb.append("offset:");
    sb.append(this.offset);
    first = false;
    if (!first) sb.append(", ");
    sb.append("count:");
    sb.append(this.count);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ZsetQueryConditionStandardSchemeFactory implements SchemeFactory {
    public ZsetQueryConditionStandardScheme getScheme() {
      return new ZsetQueryConditionStandardScheme();
    }
  }

  private static class ZsetQueryConditionStandardScheme extends StandardScheme<ZsetQueryCondition> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ZsetQueryCondition struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // KEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.key = iprot.readString();
              struct.setKeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // WAY
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.way = iprot.readI32();
              struct.setWayIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // FROM_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.fromName = iprot.readI64();
              struct.setFromNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TO
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.to = iprot.readI64();
              struct.setToIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // OFFSET
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.offset = iprot.readI64();
              struct.setOffsetIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.count = iprot.readI64();
              struct.setCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ZsetQueryCondition struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.key != null) {
        oprot.writeFieldBegin(KEY_FIELD_DESC);
        oprot.writeString(struct.key);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(WAY_FIELD_DESC);
      oprot.writeI32(struct.way);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FROM_NAME_FIELD_DESC);
      oprot.writeI64(struct.fromName);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TO_FIELD_DESC);
      oprot.writeI64(struct.to);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(OFFSET_FIELD_DESC);
      oprot.writeI64(struct.offset);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(COUNT_FIELD_DESC);
      oprot.writeI64(struct.count);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ZsetQueryConditionTupleSchemeFactory implements SchemeFactory {
    public ZsetQueryConditionTupleScheme getScheme() {
      return new ZsetQueryConditionTupleScheme();
    }
  }

  private static class ZsetQueryConditionTupleScheme extends TupleScheme<ZsetQueryCondition> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ZsetQueryCondition struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetKey()) {
        optionals.set(0);
      }
      if (struct.isSetWay()) {
        optionals.set(1);
      }
      if (struct.isSetFromName()) {
        optionals.set(2);
      }
      if (struct.isSetTo()) {
        optionals.set(3);
      }
      if (struct.isSetOffset()) {
        optionals.set(4);
      }
      if (struct.isSetCount()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetKey()) {
        oprot.writeString(struct.key);
      }
      if (struct.isSetWay()) {
        oprot.writeI32(struct.way);
      }
      if (struct.isSetFromName()) {
        oprot.writeI64(struct.fromName);
      }
      if (struct.isSetTo()) {
        oprot.writeI64(struct.to);
      }
      if (struct.isSetOffset()) {
        oprot.writeI64(struct.offset);
      }
      if (struct.isSetCount()) {
        oprot.writeI64(struct.count);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ZsetQueryCondition struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.key = iprot.readString();
        struct.setKeyIsSet(true);
      }
      if (incoming.get(1)) {
        struct.way = iprot.readI32();
        struct.setWayIsSet(true);
      }
      if (incoming.get(2)) {
        struct.fromName = iprot.readI64();
        struct.setFromNameIsSet(true);
      }
      if (incoming.get(3)) {
        struct.to = iprot.readI64();
        struct.setToIsSet(true);
      }
      if (incoming.get(4)) {
        struct.offset = iprot.readI64();
        struct.setOffsetIsSet(true);
      }
      if (incoming.get(5)) {
        struct.count = iprot.readI64();
        struct.setCountIsSet(true);
      }
    }
  }

}

