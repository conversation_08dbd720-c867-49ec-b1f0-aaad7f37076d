 # 交友 刘志斌 提供/2021-03-23

namespace java com.yy.gameecology.common.protocol.thrift.fts_compere_group

// s2s name
// 测试：fts_compere_group_test
// 生产：fts_compere_group

struct CommonRet
{
    1:i64 code; // 0-success
    2:string message;
}

enum OutGroup {
    NONE = 0 // 无分组
    HAT  = 1 // 帽子主持
    TEAM = 2 // 天团主持
    S_MALE = 3 // 超级男主持
    S_FEMALE = 4  // 超级女主持
    SUPER = 5 // 超级主持
    V_MALE = 6 // 视频男主持
    V_FEMALE = 7 // 视频女主持
    A_MALE = 8 // 音频男主持
    A_FEMALE = 9 // 音频女主持
    MULTI = 10 // 多人超主
    NORMAL = 11 // 普通分组
    New = 12 // 新分组
    YZ = 13  // 约战主持
    BB_VIDEO = 14 // 宝贝视频
    BB_AUDIO = 15 // 宝贝音频
}

enum Source {
    Unknown = 0;
    SourceJY = 500;
    SourcePK = 600;
    SourceBaby = 400;
    SourceZW  = 800;
    SourcePW  = 900
}

enum QueryType {
    TypeCompere = 0; // 主持，默认
    TypeGuild = 1;   // 公会
    TypeChannel = 2; // 厅
}

enum Visibility {
    Normal = 0; // 正式分组
    Gray = 1; // 灰度分组
}

struct CompereGroup
{
    1:OutGroup group; // 分组结果
    2:i64 version; // 版本号
    3:i64 cover; // 扩展字段
}

struct GroupResult
{
    1:OutGroup group; // 分组结果
    2:i64 version; // 版本号
    3:i64 cover; // 扩展字段
    4:string config_group; // 分组映射
}

struct QueryCompereGroupReq
{
    1:i64 r_id; // 规则ID
    2:list<i64> u_id_list; // 请求的主持
    15:map<string, string> expand;
}

struct QueryCompereGroupResp
{
    1:CommonRet header;
    2:map<i64, CompereGroup> group_map;
    15:map<string, string> expand;
}

struct QueryGroupReq
{
    1:i64 r_id; // 规则ID
    2:list<string> members; // 请求的主持 厅-sid:ssid
    3:QueryType query_type; // 请求类型
    4:Source source; // 业务源
    5:Visibility visible; // 0-正式数据 1-灰度数据
    15:map<string, string> expand;
}

struct BatchQueryGroupReq
{
    1:list<QueryGroupReq> request_list;
}

struct QueryGroupResp
{
    1:CommonRet header;
    2:map<string, GroupResult> group_map;
    15:map<string, string> expand;
}

struct BatchQueryGroupResp
{
    1:CommonRet header;
    2:list<QueryGroupResp> response_list;
}

service FtsCompereGroupService {
    // 测试链接
    void ping();

    // 查询主持分组
    QueryCompereGroupResp QueryCompereGroup(1:QueryCompereGroupReq req);

    // 查询分组
    QueryGroupResp QueryGroup(1:QueryGroupReq req);

    // 批量查询分组
    BatchQueryGroupResp BatchQueryGroup(1:BatchQueryGroupReq req);
}
