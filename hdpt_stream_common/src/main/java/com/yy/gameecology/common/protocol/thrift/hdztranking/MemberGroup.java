/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class MemberGroup implements org.apache.thrift.TBase<MemberGroup, MemberGroup._Fields>, java.io.Serializable, Cloneable, Comparable<MemberGroup> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("MemberGroup");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RANK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rankId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField PASS_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("passCount", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField GROUP_ITEMS_FIELD_DESC = new org.apache.thrift.protocol.TField("groupItems", org.apache.thrift.protocol.TType.LIST, (short)5);
  private static final org.apache.thrift.protocol.TField REMARK_FIELD_DESC = new org.apache.thrift.protocol.TField("remark", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new MemberGroupStandardSchemeFactory());
    schemes.put(TupleScheme.class, new MemberGroupTupleSchemeFactory());
  }

  public long actId; // required
  public long rankId; // required
  public long phaseId; // required
  public int passCount; // required
  public List<GroupItem> groupItems; // required
  public String remark; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    RANK_ID((short)2, "rankId"),
    PHASE_ID((short)3, "phaseId"),
    PASS_COUNT((short)4, "passCount"),
    GROUP_ITEMS((short)5, "groupItems"),
    REMARK((short)6, "remark"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // RANK_ID
          return RANK_ID;
        case 3: // PHASE_ID
          return PHASE_ID;
        case 4: // PASS_COUNT
          return PASS_COUNT;
        case 5: // GROUP_ITEMS
          return GROUP_ITEMS;
        case 6: // REMARK
          return REMARK;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __RANKID_ISSET_ID = 1;
  private static final int __PHASEID_ISSET_ID = 2;
  private static final int __PASSCOUNT_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANK_ID, new org.apache.thrift.meta_data.FieldMetaData("rankId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PASS_COUNT, new org.apache.thrift.meta_data.FieldMetaData("passCount", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.GROUP_ITEMS, new org.apache.thrift.meta_data.FieldMetaData("groupItems", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GroupItem.class))));
    tmpMap.put(_Fields.REMARK, new org.apache.thrift.meta_data.FieldMetaData("remark", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(MemberGroup.class, metaDataMap);
  }

  public MemberGroup() {
  }

  public MemberGroup(
    long actId,
    long rankId,
    long phaseId,
    int passCount,
    List<GroupItem> groupItems,
    String remark,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.rankId = rankId;
    setRankIdIsSet(true);
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    this.passCount = passCount;
    setPassCountIsSet(true);
    this.groupItems = groupItems;
    this.remark = remark;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public MemberGroup(MemberGroup other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    this.rankId = other.rankId;
    this.phaseId = other.phaseId;
    this.passCount = other.passCount;
    if (other.isSetGroupItems()) {
      List<GroupItem> __this__groupItems = new ArrayList<GroupItem>(other.groupItems.size());
      for (GroupItem other_element : other.groupItems) {
        __this__groupItems.add(new GroupItem(other_element));
      }
      this.groupItems = __this__groupItems;
    }
    if (other.isSetRemark()) {
      this.remark = other.remark;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public MemberGroup deepCopy() {
    return new MemberGroup(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    setRankIdIsSet(false);
    this.rankId = 0;
    setPhaseIdIsSet(false);
    this.phaseId = 0;
    setPassCountIsSet(false);
    this.passCount = 0;
    this.groupItems = null;
    this.remark = null;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public MemberGroup setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getRankId() {
    return this.rankId;
  }

  public MemberGroup setRankId(long rankId) {
    this.rankId = rankId;
    setRankIdIsSet(true);
    return this;
  }

  public void unsetRankId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  /** Returns true if field rankId is set (has been assigned a value) and false otherwise */
  public boolean isSetRankId() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  public void setRankIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKID_ISSET_ID, value);
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public MemberGroup setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public int getPassCount() {
    return this.passCount;
  }

  public MemberGroup setPassCount(int passCount) {
    this.passCount = passCount;
    setPassCountIsSet(true);
    return this;
  }

  public void unsetPassCount() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PASSCOUNT_ISSET_ID);
  }

  /** Returns true if field passCount is set (has been assigned a value) and false otherwise */
  public boolean isSetPassCount() {
    return EncodingUtils.testBit(__isset_bitfield, __PASSCOUNT_ISSET_ID);
  }

  public void setPassCountIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PASSCOUNT_ISSET_ID, value);
  }

  public int getGroupItemsSize() {
    return (this.groupItems == null) ? 0 : this.groupItems.size();
  }

  public java.util.Iterator<GroupItem> getGroupItemsIterator() {
    return (this.groupItems == null) ? null : this.groupItems.iterator();
  }

  public void addToGroupItems(GroupItem elem) {
    if (this.groupItems == null) {
      this.groupItems = new ArrayList<GroupItem>();
    }
    this.groupItems.add(elem);
  }

  public List<GroupItem> getGroupItems() {
    return this.groupItems;
  }

  public MemberGroup setGroupItems(List<GroupItem> groupItems) {
    this.groupItems = groupItems;
    return this;
  }

  public void unsetGroupItems() {
    this.groupItems = null;
  }

  /** Returns true if field groupItems is set (has been assigned a value) and false otherwise */
  public boolean isSetGroupItems() {
    return this.groupItems != null;
  }

  public void setGroupItemsIsSet(boolean value) {
    if (!value) {
      this.groupItems = null;
    }
  }

  public String getRemark() {
    return this.remark;
  }

  public MemberGroup setRemark(String remark) {
    this.remark = remark;
    return this;
  }

  public void unsetRemark() {
    this.remark = null;
  }

  /** Returns true if field remark is set (has been assigned a value) and false otherwise */
  public boolean isSetRemark() {
    return this.remark != null;
  }

  public void setRemarkIsSet(boolean value) {
    if (!value) {
      this.remark = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public MemberGroup setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case RANK_ID:
      if (value == null) {
        unsetRankId();
      } else {
        setRankId((Long)value);
      }
      break;

    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    case PASS_COUNT:
      if (value == null) {
        unsetPassCount();
      } else {
        setPassCount((Integer)value);
      }
      break;

    case GROUP_ITEMS:
      if (value == null) {
        unsetGroupItems();
      } else {
        setGroupItems((List<GroupItem>)value);
      }
      break;

    case REMARK:
      if (value == null) {
        unsetRemark();
      } else {
        setRemark((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case RANK_ID:
      return getRankId();

    case PHASE_ID:
      return getPhaseId();

    case PASS_COUNT:
      return getPassCount();

    case GROUP_ITEMS:
      return getGroupItems();

    case REMARK:
      return getRemark();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case RANK_ID:
      return isSetRankId();
    case PHASE_ID:
      return isSetPhaseId();
    case PASS_COUNT:
      return isSetPassCount();
    case GROUP_ITEMS:
      return isSetGroupItems();
    case REMARK:
      return isSetRemark();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof MemberGroup)
      return this.equals((MemberGroup)that);
    return false;
  }

  public boolean equals(MemberGroup that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_rankId = true;
    boolean that_present_rankId = true;
    if (this_present_rankId || that_present_rankId) {
      if (!(this_present_rankId && that_present_rankId))
        return false;
      if (this.rankId != that.rankId)
        return false;
    }

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    boolean this_present_passCount = true;
    boolean that_present_passCount = true;
    if (this_present_passCount || that_present_passCount) {
      if (!(this_present_passCount && that_present_passCount))
        return false;
      if (this.passCount != that.passCount)
        return false;
    }

    boolean this_present_groupItems = true && this.isSetGroupItems();
    boolean that_present_groupItems = true && that.isSetGroupItems();
    if (this_present_groupItems || that_present_groupItems) {
      if (!(this_present_groupItems && that_present_groupItems))
        return false;
      if (!this.groupItems.equals(that.groupItems))
        return false;
    }

    boolean this_present_remark = true && this.isSetRemark();
    boolean that_present_remark = true && that.isSetRemark();
    if (this_present_remark || that_present_remark) {
      if (!(this_present_remark && that_present_remark))
        return false;
      if (!this.remark.equals(that.remark))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_rankId = true;
    list.add(present_rankId);
    if (present_rankId)
      list.add(rankId);

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    boolean present_passCount = true;
    list.add(present_passCount);
    if (present_passCount)
      list.add(passCount);

    boolean present_groupItems = true && (isSetGroupItems());
    list.add(present_groupItems);
    if (present_groupItems)
      list.add(groupItems);

    boolean present_remark = true && (isSetRemark());
    list.add(present_remark);
    if (present_remark)
      list.add(remark);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(MemberGroup other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankId()).compareTo(other.isSetRankId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankId, other.rankId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPassCount()).compareTo(other.isSetPassCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPassCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.passCount, other.passCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetGroupItems()).compareTo(other.isSetGroupItems());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetGroupItems()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.groupItems, other.groupItems);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRemark()).compareTo(other.isSetRemark());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRemark()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.remark, other.remark);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("MemberGroup(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankId:");
    sb.append(this.rankId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("passCount:");
    sb.append(this.passCount);
    first = false;
    if (!first) sb.append(", ");
    sb.append("groupItems:");
    if (this.groupItems == null) {
      sb.append("null");
    } else {
      sb.append(this.groupItems);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("remark:");
    if (this.remark == null) {
      sb.append("null");
    } else {
      sb.append(this.remark);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class MemberGroupStandardSchemeFactory implements SchemeFactory {
    public MemberGroupStandardScheme getScheme() {
      return new MemberGroupStandardScheme();
    }
  }

  private static class MemberGroupStandardScheme extends StandardScheme<MemberGroup> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, MemberGroup struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RANK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankId = iprot.readI64();
              struct.setRankIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PASS_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.passCount = iprot.readI32();
              struct.setPassCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // GROUP_ITEMS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list678 = iprot.readListBegin();
                struct.groupItems = new ArrayList<GroupItem>(_list678.size);
                GroupItem _elem679;
                for (int _i680 = 0; _i680 < _list678.size; ++_i680)
                {
                  _elem679 = new GroupItem();
                  _elem679.read(iprot);
                  struct.groupItems.add(_elem679);
                }
                iprot.readListEnd();
              }
              struct.setGroupItemsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // REMARK
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.remark = iprot.readString();
              struct.setRemarkIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map681 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map681.size);
                String _key682;
                String _val683;
                for (int _i684 = 0; _i684 < _map681.size; ++_i684)
                {
                  _key682 = iprot.readString();
                  _val683 = iprot.readString();
                  struct.extData.put(_key682, _val683);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, MemberGroup struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANK_ID_FIELD_DESC);
      oprot.writeI64(struct.rankId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PASS_COUNT_FIELD_DESC);
      oprot.writeI32(struct.passCount);
      oprot.writeFieldEnd();
      if (struct.groupItems != null) {
        oprot.writeFieldBegin(GROUP_ITEMS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.groupItems.size()));
          for (GroupItem _iter685 : struct.groupItems)
          {
            _iter685.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.remark != null) {
        oprot.writeFieldBegin(REMARK_FIELD_DESC);
        oprot.writeString(struct.remark);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter686 : struct.extData.entrySet())
          {
            oprot.writeString(_iter686.getKey());
            oprot.writeString(_iter686.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class MemberGroupTupleSchemeFactory implements SchemeFactory {
    public MemberGroupTupleScheme getScheme() {
      return new MemberGroupTupleScheme();
    }
  }

  private static class MemberGroupTupleScheme extends TupleScheme<MemberGroup> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, MemberGroup struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetRankId()) {
        optionals.set(1);
      }
      if (struct.isSetPhaseId()) {
        optionals.set(2);
      }
      if (struct.isSetPassCount()) {
        optionals.set(3);
      }
      if (struct.isSetGroupItems()) {
        optionals.set(4);
      }
      if (struct.isSetRemark()) {
        optionals.set(5);
      }
      if (struct.isSetExtData()) {
        optionals.set(6);
      }
      oprot.writeBitSet(optionals, 7);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetRankId()) {
        oprot.writeI64(struct.rankId);
      }
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
      if (struct.isSetPassCount()) {
        oprot.writeI32(struct.passCount);
      }
      if (struct.isSetGroupItems()) {
        {
          oprot.writeI32(struct.groupItems.size());
          for (GroupItem _iter687 : struct.groupItems)
          {
            _iter687.write(oprot);
          }
        }
      }
      if (struct.isSetRemark()) {
        oprot.writeString(struct.remark);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter688 : struct.extData.entrySet())
          {
            oprot.writeString(_iter688.getKey());
            oprot.writeString(_iter688.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, MemberGroup struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(7);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.rankId = iprot.readI64();
        struct.setRankIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.passCount = iprot.readI32();
        struct.setPassCountIsSet(true);
      }
      if (incoming.get(4)) {
        {
          org.apache.thrift.protocol.TList _list689 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.groupItems = new ArrayList<GroupItem>(_list689.size);
          GroupItem _elem690;
          for (int _i691 = 0; _i691 < _list689.size; ++_i691)
          {
            _elem690 = new GroupItem();
            _elem690.read(iprot);
            struct.groupItems.add(_elem690);
          }
        }
        struct.setGroupItemsIsSet(true);
      }
      if (incoming.get(5)) {
        struct.remark = iprot.readString();
        struct.setRemarkIsSet(true);
      }
      if (incoming.get(6)) {
        {
          org.apache.thrift.protocol.TMap _map692 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map692.size);
          String _key693;
          String _val694;
          for (int _i695 = 0; _i695 < _map692.size; ++_i695)
          {
            _key693 = iprot.readString();
            _val694 = iprot.readString();
            struct.extData.put(_key693, _val694);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

