/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class QueryMemberResponse implements org.apache.thrift.TBase<QueryMemberResponse, QueryMemberResponse._Fields>, java.io.Serializable, Cloneable, Comparable<QueryMemberResponse> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryMemberResponse");

  private static final org.apache.thrift.protocol.TField MEMBER_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("memberMap", org.apache.thrift.protocol.TType.MAP, (short)1);
  private static final org.apache.thrift.protocol.TField EXT_FIELD_DESC = new org.apache.thrift.protocol.TField("ext", org.apache.thrift.protocol.TType.MAP, (short)2);
  private static final org.apache.thrift.protocol.TField CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("code", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField REASON_FIELD_DESC = new org.apache.thrift.protocol.TField("reason", org.apache.thrift.protocol.TType.STRING, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryMemberResponseStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryMemberResponseTupleSchemeFactory());
  }

  public Map<String,Map<String,MemberItemInfo>> memberMap; // required
  public Map<String,String> ext; // required
  public int code; // required
  public String reason; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    MEMBER_MAP((short)1, "memberMap"),
    EXT((short)2, "ext"),
    CODE((short)3, "code"),
    REASON((short)4, "reason");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // MEMBER_MAP
          return MEMBER_MAP;
        case 2: // EXT
          return EXT;
        case 3: // CODE
          return CODE;
        case 4: // REASON
          return REASON;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __CODE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.MEMBER_MAP, new org.apache.thrift.meta_data.FieldMetaData("memberMap", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
                new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
                new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, MemberItemInfo.class)))));
    tmpMap.put(_Fields.EXT, new org.apache.thrift.meta_data.FieldMetaData("ext", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.CODE, new org.apache.thrift.meta_data.FieldMetaData("code", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.REASON, new org.apache.thrift.meta_data.FieldMetaData("reason", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryMemberResponse.class, metaDataMap);
  }

  public QueryMemberResponse() {
  }

  public QueryMemberResponse(
    Map<String,Map<String,MemberItemInfo>> memberMap,
    Map<String,String> ext,
    int code,
    String reason)
  {
    this();
    this.memberMap = memberMap;
    this.ext = ext;
    this.code = code;
    setCodeIsSet(true);
    this.reason = reason;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryMemberResponse(QueryMemberResponse other) {
    __isset_bitfield = other.__isset_bitfield;
    if (other.isSetMemberMap()) {
      Map<String,Map<String,MemberItemInfo>> __this__memberMap = new HashMap<String,Map<String,MemberItemInfo>>(other.memberMap.size());
      for (Map.Entry<String, Map<String,MemberItemInfo>> other_element : other.memberMap.entrySet()) {

        String other_element_key = other_element.getKey();
        Map<String,MemberItemInfo> other_element_value = other_element.getValue();

        String __this__memberMap_copy_key = other_element_key;

        Map<String,MemberItemInfo> __this__memberMap_copy_value = new HashMap<String,MemberItemInfo>(other_element_value.size());
        for (Map.Entry<String, MemberItemInfo> other_element_value_element : other_element_value.entrySet()) {

          String other_element_value_element_key = other_element_value_element.getKey();
          MemberItemInfo other_element_value_element_value = other_element_value_element.getValue();

          String __this__memberMap_copy_value_copy_key = other_element_value_element_key;

          MemberItemInfo __this__memberMap_copy_value_copy_value = new MemberItemInfo(other_element_value_element_value);

          __this__memberMap_copy_value.put(__this__memberMap_copy_value_copy_key, __this__memberMap_copy_value_copy_value);
        }

        __this__memberMap.put(__this__memberMap_copy_key, __this__memberMap_copy_value);
      }
      this.memberMap = __this__memberMap;
    }
    if (other.isSetExt()) {
      Map<String,String> __this__ext = new HashMap<String,String>(other.ext);
      this.ext = __this__ext;
    }
    this.code = other.code;
    if (other.isSetReason()) {
      this.reason = other.reason;
    }
  }

  public QueryMemberResponse deepCopy() {
    return new QueryMemberResponse(this);
  }

  @Override
  public void clear() {
    this.memberMap = null;
    this.ext = null;
    setCodeIsSet(false);
    this.code = 0;
    this.reason = null;
  }

  public int getMemberMapSize() {
    return (this.memberMap == null) ? 0 : this.memberMap.size();
  }

  public void putToMemberMap(String key, Map<String,MemberItemInfo> val) {
    if (this.memberMap == null) {
      this.memberMap = new HashMap<String,Map<String,MemberItemInfo>>();
    }
    this.memberMap.put(key, val);
  }

  public Map<String,Map<String,MemberItemInfo>> getMemberMap() {
    return this.memberMap;
  }

  public QueryMemberResponse setMemberMap(Map<String,Map<String,MemberItemInfo>> memberMap) {
    this.memberMap = memberMap;
    return this;
  }

  public void unsetMemberMap() {
    this.memberMap = null;
  }

  /** Returns true if field memberMap is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberMap() {
    return this.memberMap != null;
  }

  public void setMemberMapIsSet(boolean value) {
    if (!value) {
      this.memberMap = null;
    }
  }

  public int getExtSize() {
    return (this.ext == null) ? 0 : this.ext.size();
  }

  public void putToExt(String key, String val) {
    if (this.ext == null) {
      this.ext = new HashMap<String,String>();
    }
    this.ext.put(key, val);
  }

  public Map<String,String> getExt() {
    return this.ext;
  }

  public QueryMemberResponse setExt(Map<String,String> ext) {
    this.ext = ext;
    return this;
  }

  public void unsetExt() {
    this.ext = null;
  }

  /** Returns true if field ext is set (has been assigned a value) and false otherwise */
  public boolean isSetExt() {
    return this.ext != null;
  }

  public void setExtIsSet(boolean value) {
    if (!value) {
      this.ext = null;
    }
  }

  public int getCode() {
    return this.code;
  }

  public QueryMemberResponse setCode(int code) {
    this.code = code;
    setCodeIsSet(true);
    return this;
  }

  public void unsetCode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CODE_ISSET_ID);
  }

  /** Returns true if field code is set (has been assigned a value) and false otherwise */
  public boolean isSetCode() {
    return EncodingUtils.testBit(__isset_bitfield, __CODE_ISSET_ID);
  }

  public void setCodeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CODE_ISSET_ID, value);
  }

  public String getReason() {
    return this.reason;
  }

  public QueryMemberResponse setReason(String reason) {
    this.reason = reason;
    return this;
  }

  public void unsetReason() {
    this.reason = null;
  }

  /** Returns true if field reason is set (has been assigned a value) and false otherwise */
  public boolean isSetReason() {
    return this.reason != null;
  }

  public void setReasonIsSet(boolean value) {
    if (!value) {
      this.reason = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case MEMBER_MAP:
      if (value == null) {
        unsetMemberMap();
      } else {
        setMemberMap((Map<String,Map<String,MemberItemInfo>>)value);
      }
      break;

    case EXT:
      if (value == null) {
        unsetExt();
      } else {
        setExt((Map<String,String>)value);
      }
      break;

    case CODE:
      if (value == null) {
        unsetCode();
      } else {
        setCode((Integer)value);
      }
      break;

    case REASON:
      if (value == null) {
        unsetReason();
      } else {
        setReason((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case MEMBER_MAP:
      return getMemberMap();

    case EXT:
      return getExt();

    case CODE:
      return getCode();

    case REASON:
      return getReason();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case MEMBER_MAP:
      return isSetMemberMap();
    case EXT:
      return isSetExt();
    case CODE:
      return isSetCode();
    case REASON:
      return isSetReason();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryMemberResponse)
      return this.equals((QueryMemberResponse)that);
    return false;
  }

  public boolean equals(QueryMemberResponse that) {
    if (that == null)
      return false;

    boolean this_present_memberMap = true && this.isSetMemberMap();
    boolean that_present_memberMap = true && that.isSetMemberMap();
    if (this_present_memberMap || that_present_memberMap) {
      if (!(this_present_memberMap && that_present_memberMap))
        return false;
      if (!this.memberMap.equals(that.memberMap))
        return false;
    }

    boolean this_present_ext = true && this.isSetExt();
    boolean that_present_ext = true && that.isSetExt();
    if (this_present_ext || that_present_ext) {
      if (!(this_present_ext && that_present_ext))
        return false;
      if (!this.ext.equals(that.ext))
        return false;
    }

    boolean this_present_code = true;
    boolean that_present_code = true;
    if (this_present_code || that_present_code) {
      if (!(this_present_code && that_present_code))
        return false;
      if (this.code != that.code)
        return false;
    }

    boolean this_present_reason = true && this.isSetReason();
    boolean that_present_reason = true && that.isSetReason();
    if (this_present_reason || that_present_reason) {
      if (!(this_present_reason && that_present_reason))
        return false;
      if (!this.reason.equals(that.reason))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_memberMap = true && (isSetMemberMap());
    list.add(present_memberMap);
    if (present_memberMap)
      list.add(memberMap);

    boolean present_ext = true && (isSetExt());
    list.add(present_ext);
    if (present_ext)
      list.add(ext);

    boolean present_code = true;
    list.add(present_code);
    if (present_code)
      list.add(code);

    boolean present_reason = true && (isSetReason());
    list.add(present_reason);
    if (present_reason)
      list.add(reason);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryMemberResponse other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetMemberMap()).compareTo(other.isSetMemberMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberMap, other.memberMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExt()).compareTo(other.isSetExt());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExt()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ext, other.ext);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCode()).compareTo(other.isSetCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.code, other.code);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReason()).compareTo(other.isSetReason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reason, other.reason);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryMemberResponse(");
    boolean first = true;

    sb.append("memberMap:");
    if (this.memberMap == null) {
      sb.append("null");
    } else {
      sb.append(this.memberMap);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("ext:");
    if (this.ext == null) {
      sb.append("null");
    } else {
      sb.append(this.ext);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("code:");
    sb.append(this.code);
    first = false;
    if (!first) sb.append(", ");
    sb.append("reason:");
    if (this.reason == null) {
      sb.append("null");
    } else {
      sb.append(this.reason);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryMemberResponseStandardSchemeFactory implements SchemeFactory {
    public QueryMemberResponseStandardScheme getScheme() {
      return new QueryMemberResponseStandardScheme();
    }
  }

  private static class QueryMemberResponseStandardScheme extends StandardScheme<QueryMemberResponse> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryMemberResponse struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // MEMBER_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map134 = iprot.readMapBegin();
                struct.memberMap = new HashMap<String,Map<String,MemberItemInfo>>(2*_map134.size);
                String _key135;
                Map<String,MemberItemInfo> _val136;
                for (int _i137 = 0; _i137 < _map134.size; ++_i137)
                {
                  _key135 = iprot.readString();
                  {
                    org.apache.thrift.protocol.TMap _map138 = iprot.readMapBegin();
                    _val136 = new HashMap<String,MemberItemInfo>(2*_map138.size);
                    String _key139;
                    MemberItemInfo _val140;
                    for (int _i141 = 0; _i141 < _map138.size; ++_i141)
                    {
                      _key139 = iprot.readString();
                      _val140 = new MemberItemInfo();
                      _val140.read(iprot);
                      _val136.put(_key139, _val140);
                    }
                    iprot.readMapEnd();
                  }
                  struct.memberMap.put(_key135, _val136);
                }
                iprot.readMapEnd();
              }
              struct.setMemberMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // EXT
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map142 = iprot.readMapBegin();
                struct.ext = new HashMap<String,String>(2*_map142.size);
                String _key143;
                String _val144;
                for (int _i145 = 0; _i145 < _map142.size; ++_i145)
                {
                  _key143 = iprot.readString();
                  _val144 = iprot.readString();
                  struct.ext.put(_key143, _val144);
                }
                iprot.readMapEnd();
              }
              struct.setExtIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.code = iprot.readI32();
              struct.setCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // REASON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.reason = iprot.readString();
              struct.setReasonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryMemberResponse struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.memberMap != null) {
        oprot.writeFieldBegin(MEMBER_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.MAP, struct.memberMap.size()));
          for (Map.Entry<String, Map<String,MemberItemInfo>> _iter146 : struct.memberMap.entrySet())
          {
            oprot.writeString(_iter146.getKey());
            {
              oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRUCT, _iter146.getValue().size()));
              for (Map.Entry<String, MemberItemInfo> _iter147 : _iter146.getValue().entrySet())
              {
                oprot.writeString(_iter147.getKey());
                _iter147.getValue().write(oprot);
              }
              oprot.writeMapEnd();
            }
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.ext != null) {
        oprot.writeFieldBegin(EXT_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.ext.size()));
          for (Map.Entry<String, String> _iter148 : struct.ext.entrySet())
          {
            oprot.writeString(_iter148.getKey());
            oprot.writeString(_iter148.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(CODE_FIELD_DESC);
      oprot.writeI32(struct.code);
      oprot.writeFieldEnd();
      if (struct.reason != null) {
        oprot.writeFieldBegin(REASON_FIELD_DESC);
        oprot.writeString(struct.reason);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryMemberResponseTupleSchemeFactory implements SchemeFactory {
    public QueryMemberResponseTupleScheme getScheme() {
      return new QueryMemberResponseTupleScheme();
    }
  }

  private static class QueryMemberResponseTupleScheme extends TupleScheme<QueryMemberResponse> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryMemberResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetMemberMap()) {
        optionals.set(0);
      }
      if (struct.isSetExt()) {
        optionals.set(1);
      }
      if (struct.isSetCode()) {
        optionals.set(2);
      }
      if (struct.isSetReason()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetMemberMap()) {
        {
          oprot.writeI32(struct.memberMap.size());
          for (Map.Entry<String, Map<String,MemberItemInfo>> _iter149 : struct.memberMap.entrySet())
          {
            oprot.writeString(_iter149.getKey());
            {
              oprot.writeI32(_iter149.getValue().size());
              for (Map.Entry<String, MemberItemInfo> _iter150 : _iter149.getValue().entrySet())
              {
                oprot.writeString(_iter150.getKey());
                _iter150.getValue().write(oprot);
              }
            }
          }
        }
      }
      if (struct.isSetExt()) {
        {
          oprot.writeI32(struct.ext.size());
          for (Map.Entry<String, String> _iter151 : struct.ext.entrySet())
          {
            oprot.writeString(_iter151.getKey());
            oprot.writeString(_iter151.getValue());
          }
        }
      }
      if (struct.isSetCode()) {
        oprot.writeI32(struct.code);
      }
      if (struct.isSetReason()) {
        oprot.writeString(struct.reason);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryMemberResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TMap _map152 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.MAP, iprot.readI32());
          struct.memberMap = new HashMap<String,Map<String,MemberItemInfo>>(2*_map152.size);
          String _key153;
          Map<String,MemberItemInfo> _val154;
          for (int _i155 = 0; _i155 < _map152.size; ++_i155)
          {
            _key153 = iprot.readString();
            {
              org.apache.thrift.protocol.TMap _map156 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
              _val154 = new HashMap<String,MemberItemInfo>(2*_map156.size);
              String _key157;
              MemberItemInfo _val158;
              for (int _i159 = 0; _i159 < _map156.size; ++_i159)
              {
                _key157 = iprot.readString();
                _val158 = new MemberItemInfo();
                _val158.read(iprot);
                _val154.put(_key157, _val158);
              }
            }
            struct.memberMap.put(_key153, _val154);
          }
        }
        struct.setMemberMapIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map160 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.ext = new HashMap<String,String>(2*_map160.size);
          String _key161;
          String _val162;
          for (int _i163 = 0; _i163 < _map160.size; ++_i163)
          {
            _key161 = iprot.readString();
            _val162 = iprot.readString();
            struct.ext.put(_key161, _val162);
          }
        }
        struct.setExtIsSet(true);
      }
      if (incoming.get(2)) {
        struct.code = iprot.readI32();
        struct.setCodeIsSet(true);
      }
      if (incoming.get(3)) {
        struct.reason = iprot.readString();
        struct.setReasonIsSet(true);
      }
    }
  }

}

