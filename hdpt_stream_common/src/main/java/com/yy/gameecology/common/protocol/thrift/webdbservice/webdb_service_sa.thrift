# webdb_service.thrift -- Thrift interfaces definition for webdb_sinfo

namespace cpp server.webdb
namespace java com.yy.gameecology.common.protocol.thrift.webdbservice
namespace php server.webdb
namespace perl server.webdb
namespace py server.webdb

//================================== sa(service agent) params struct start ====================================//
struct AuthorizeMsg {
	1:string AuthUser="",
	2:string AuthKey="",
	3:map<string, string> keyValue,
}

struct StringList {
	1:list<string> strList,
}

/**
 * @param     rescode            返回码
 * @param     keyValue           保留字段
 */
struct SaResponse {
	1:i32 rescode,
	2:map<string, string> keyValue,
}

/**
 * @param     rescode            返回码
 * @param     keyValue           保留字段
 * @param     keyIndex           columns中各字段在dataSet中对应的索引
 * @param     dataset            内层StringList表示一行查询结果,外层list表示多行查询结果
 */
struct SaResponseSet {
	1:i32 rescode,
	2:map<string, string> keyValue,
	3:map<string, i32> keyIndex,
	4:list<StringList> dataSet
}

//================================ sa(service agent) sinfo params start ================================//
/**
 * 通过顶级频道id查询频道信息
 * @param     appkey          客户端的标识
 * @param     sids            顶级频道id列表(列表最大长度为500)
 * @param     type             0 - 顶级频道id列表为频道长号
 *                             1 - 顶级频道id列表为频道短号
 *                             2 - 顶级频道id列表包含频道长号与频道短号
 * @param     columns         需要查询的字段集合，可以查询如下字段
*                               - "sid", 频道长号
*                               - "name", 频道名称
*                               - "isp", 频道isp
*                               - "area", 区域
*                               - "province", 省份
*                               - "city", 城市
*                               - "blimit", 限制只有会员能够进入
*                               - "passwd", 频道密码
*                               - "bpub", 是否开放搜索
*                               - "create_time", 创建时间
*                               - "style", 麦序
*                               - "microtime", 麦序时间
*                               - "typestr", 类型描述
*                               - "type", 类型
*                               - "is_limit_txt", 是否限制文字聊天速度
*                               - "txt_limittime", 限制文字聊天速度每句间隔秒数
*                               - "ownerid", 所有者用户uid
*                               - "logo_index", 频道logo类型
*                               - "apply_jifen", 入会积分
*                               - "apply_announce", 入会声明
*                               - "lang"：语言
*                               - "template_id", 频道模板
*                               - "credit", 频道信用
*                               - "apptype", 频道类型
*                               - "anony_limit", 频道匿名用户最大人数限制
*                               - "logo_url", 频道logo url
*                               - "sinfo_jifen", 频道积分
*                               - "asid", 频道短号
*                               - "user_limit", 频道人数限制   
*                               - "bulletin", 公告内容 (对于公告内容,不允许批量查询)
*                               - "bulletstamp", 公告时间戳
*                               - "jiedai_sid", 接待频道号
* @SaResponseSet             顶级频道信息结果集，如果没有查到结果则dataSet为空
*/
struct SaRequestSession {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:list<string> sids,
	4:i32 type,
	5:list<string> columns,
}

/**
 * 通过顶级频道id和子频道id查询子频道信息
 * @param     appkey          客户端的标识
 * @param     tidSids         顶级频道id和子频道id的列表(列表最大长度为500)，列表中的每行记录包含两个元素：
 *            第一个元素      顶级频道ID (频道长号)
 *            第二个元素      子频道ID
 * @param     columns         需要查询的字段集合，可以查询如下字段
 *                              - "tid": 顶级频道id
 *                              - "sid": 频道id
 *                              - "pid": 父频道id
 *                              - "name": 频道名字
 *                              - "maxman" 子频道人数限制
 *                              - "passwd": 频道密码
 *                              - "create_time": 频道创建时间
 *                              - "style", 麦序模式
 *                              - "microtime", 麦序时间
 *                              - "is_limit_txt", 是否限制文字聊天速度
 *                              - "txt_limittime", 限制文字聊天速度每句间隔秒数
 *                              - "sort", 排序序号
 *                              - "charge", 收费频道类型
 *                              - "template_id", 频道模板
 *                              - "is_passwd_set", 子频道是否有设置密码
 *                              - "is_guest_access_limit", 子频道是否有限制游客进入
 *                              - "bulletin", 公告内容 (对于公告内容,不允许批量查询)
 *                              - "bulletstamp", 公告时间戳      
 * @SaResponseSet               子频道信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestSubsess {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:list<StringList> tidSids,
	4:list<string> columns,
}

/**
 * 通过用户uid查询用户拥有的频道信息
 * @param     appkey          客户端的标识
 * @param     ownerid         用户uid
 * @param     apptype         频道apptype(默认apptype为空或0,表示不指定apptype)
 * @param     optype          操作类型(默认optype为空或0, 若optype为1表示仅查询用户拥有的频道数量)
 * @columns                   需要查询的字段集合，可以查询如下字段
 *                               - "sid": 频道长位id，
 *                               - "apptype": 频道apptype
 *                               - "name": 频道名字，
 *                               - "create_time", 创建时间
 *                               - "asid": 频道短位id
 * @SaResponseSet              用户拥有的频道信息结果集，如果没有查到结果则dataSet为空
 *                             若optype=1，keyValue中存放<ownerid,频道数量>的结果
 */
struct SaRequestOwnersess  {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:string ownerid,
	4:string apptype,
	5:string optype,
	6:list<string> columns,
}

/**
 * 通过用户uid查询用户收藏的频道信息
 * @param     appkey          客户端的标识
 * @param     uid             用户uid
 * @columns                   需要查询的字段集合，可以查询如下字段
 *                               - "sid": 频道长位id，
 *                               - "name": 频道名字，
 *                               - "type": 频道类型，例如 0为游戏/1为娱乐/3为教育 等
 *                               - "typestr": 频道类型二级描述，例如 "K歌地带"/"九龙朝" 等
 *                               - "create_time": 频道创建时间
 *                               - "apptype": 创建频道的APP类型，0为YY/1为100教育/2为语聊等；默认为0
 *                               - "template_id", 频道开播模板，例如：16777217为娱乐模板/33554521游戏直播模板 等
 *                               - "asid": 频道短位id
 * @SaResponseSet              用户收藏的频道信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestFavorsess  {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:string uid,
    4:list<string> columns,
}

/**
 * 通过顶级频道id和父频道id查询所有子频道信息
 * @param     appkey          客户端的标识
 * @param     tid             顶级频道id
 * @param     pid             父频道id
 * @param     recursive       是否需要递归查询
 * @param     columns         需要查询的字段集合，可以查询如下字段
 *                              - "sid": 频道id
 *                              - "pid": 父频道id
 *                              - "tid": 顶级频道id
 *                              - "name": 频道名字
 *                              - "passwd": 频道密码
 *                              - "create_time": 频道创建时间
 *                              - "style", 麦序模式
 *                              - "microtime", 麦序时间
 *                              - "is_limit_txt", is_limit_txt
 *                              - "txt_limittime", txt_limittime
 *                              - "sort", 排序序号
 *                              - "charge", 收费频道类型
 *                              - "template_id", 频道模板
 * @SaResponseSet               子频道信息结果集，如果没有查到结果则dataSet为空
 */
 struct SaRequestRecursess {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:string tid,
    4:string pid,
    5:bool recursive,
    6:list<string> columns,
}

/**
 * 创建顶级频道
 * @param     appkey          客户端的标识
 * @param     type            创建频道方式
 *                               -0 根据svctype创建频道，webdb根据svctype分配频道号(uid, svctype字段必选项)
 * @param     keyValue        可以设置如下字段的值
 *                               -svctype     业务类型（webdb内部使用），请与webdb负责人确认
 *                               -name        频道名称
 *                               -isp         网络类型,如果不确定填"3"
 *                               -blimit      是否限制级频道，如果不确定填"0"
 *                               -bpub        是否公开频道，如果不确定填"1"
 *                               -typestr     主玩游戏描述,例如"英雄联盟"/"K歌地带"
 *                               -type        所属类别，游戏填"0",娱乐填"1",请根据频道类别确定具体值
 *                               -uid         频道创建者的用户id
 *                               -ip          频道创建者的ip(用于logdb记录日志)，如果不确定填"0"
 *                               -area        频道area属性，如果不确定填"0"
 *                               -lang        频道language属性，如果不确定填"0"
 *                               -template_id 频道模版id，请业务自己确定
 *                               -user_limit  频道在线人数最大限制，取值范围(0,100000]
 *                               -isvip       标识是否YY会员创建频道；1表示是，最大允许创建5个频道；0表示否，最大允许创建3个频道。
 *                                            请在确定的情况下填true，否则填false
 * @SaResponse		       返回值rescode，创建成功返回频道id，若创建失败返回
 *                               -1, 读取数据库失败
 *                               -2, 参数错误
 *                               -3, 频道数已经达到上线
 *                               -4, 获取频道号失败
 */
struct SaRequestCreatesess {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:i32 type,
    4:map<string, string> keyValue,
}
//================================ sa(service agent) sinfo params end ==================================//

//================================ sa(service agent) uinfo params start ================================//
/**
 * 通过uid查询用户信息(如果webdb查询不到,会从udb同步用户信息)
 * @param      appkey          客户端的标识
 * @param      uid             用户uid
 * @param      columns         需要查询的字段集合，可以查询如下字段
 *                               - "id", 用户uid
 *                               - "yyno", YY号
 *                               - "nick", 昵称
 *                               - "sex", 性别
 *                               - "birthday", 生日
 *                               - "area", 地区
 *                               - "province", 省份
 *                               - "city", 城市
 *                               - "sign", 签名
 *                               - "intro", 个人说明
 *                               - "jifen", 个人积分,按分钟计算
 *                               - "register_time", 注册时间
 *                               - "passport", 通行证
 *                               - "account", 邮箱
 *                               - "custom_logo", 用户个人普通自定义头像url(60*60)
 *                               - "hdlogo"，个人高清头像图片url(640*640)
 *                               - "session_card"，频道基础名片图片url
 *                               - "logo_index"，个人系统头像类型。0为自定义头像，即为hdlogo；其他值为系统定义头像
 *                               - "hd_logo_60", 个人高清自定义头像url(60*60)
 *                               - "hd_logo_100", 个人高清自定义头像url(100*100)
 *                               - "hd_logo_144", 个人高清自定义头像url(144*144)
 *                               - "hd_logo_640", 个人高清自定义头像url(640*640)
 * @SaResponseSet                用户信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestUser {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:string uid,
	4:list<string> columns,	
}

/**
 * 通过uid查询用户信息(批量)
 * @param     appkey           客户端的标识
 * @param     uids             用户uid列表(列表最大长度为500)
 * @param     columns          需要查询的字段集合，批量接口只支持以下字段
 *                               - "id", 用户uid
 *                               - "yyno", YY号
 *                               - "nick", 用户昵称
 *                               - "sex", 性别
 *                               - "birthday", 生日
 *                               - "area", 地区
 *                               - "province", 省份
 *                               - "city", 城市
 *                               - "sign", 签名
 *                               - "intro", 个人说明
 *                               - "jifen", 个人积分,按分钟计算
 *                               - "register_time", 注册时间
 *                               - "passport", 通行证
 *                               - "account", 邮箱
 *                               - "custom_logo", 用户个人普通自定义头像url(60*60)
 *                               - "hdlogo"，个人高清头像图片url(640*640)
 *                               - "session_card"，频道基础名片图片url
 *                               - "logo_index"，个人系统头像类型。0为自定义头像，即为hdlogo；其他值为系统定义头像
 *                               - "hd_logo_60", 个人高清自定义头像url(60*60)
 *                               - "hd_logo_100", 个人高清自定义头像url(100*100)
 *                               - "hd_logo_144", 个人高清自定义头像url(144*144)
 *                               - "hd_logo_640", 个人高清自定义头像url(640*640)
 * @SaResponseSet              用户信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestBatchUser {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:list<string> uids,
	4:list<string> columns,	
}

/**
 * 通过yy号查询用户的uid
 * @param     appkey           客户端的标识
 * @param     imids            用户yy号(又称imid)列表(列表最大长度为500)
 * @SaResponse                 keyValue格式为<yy号，uid>的映射，映射中只包含能查询到对应uid的元素,
 *                             如果参数给出的全部yy号都不是有效yy，则keyValue为空
 */
struct SaRequestUid {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:list<string> imids,
}

/**
 * 通过uid设置用户信息
 * @param	appkey	    客户端标识
 * @param 	uid			用户uid
 * @param	props		用户信息<key, value>，支持的key如下
 *							- "nick" 用户昵称
 *							- "sex" 性别
 *							- "birthday" 生日
 *							- "area" 国家（地区）
 *							- "province" 省
 *							- "city" 市
 *							- "sign" 签名
 *							- "intro" 个人说明
 *                          - "stage_name" 艺名
 * @SaResponse			 返回值rescode，设置成功返回0
 */
struct SaRequestSetUser {
    1:AuthorizeMsg authMsg,
    2:string appkey,
	3:string uid,
	4:map<string,string> props,
}

/**
 * 禁止用户修改个人资料(昵称/签名/个性签名)
 * @param   appkey        客户端标识
 * @param   uid           被封禁uid
 * @param   beginTime     封禁开始时间，格式为"2018-09-13 09:08:20"
 * @param   endTime       封禁结束时间，格式为"2018-09-14 21:10:00"
 * @param   reasonCode    封禁原因: 0 - 违规; 1 - 公会uid
 *
 * @SaResponse			  返回值rescode，设置成功返回0
 */
struct SaRequestLmtUptUser {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:string uid, 
	4:string beginTime, 
	5:string endTime, 
	6:i16 reasonCode,
}

/**
 * 解禁用户修改个人资料(昵称/签名/个性签名)的限制
 * @param   appkey        客户端标识
 * @param   uids          解禁的用户uid列表(列表最大长度为50)
 * @param   reasonCode    封禁原因: 0 - 违规; 1 - 公会uid      
 *                    
 *
 * @SaResponse			  返回值rescode，设置成功返回0
 */
struct SaRequestUnlmtUptUser {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:list<string> uids, 
	4:i16 reasonCode,
}
//================================ sa(service agent) uinfo params end ==================================//

//================================ sa(service agent) smember params start ==============================//
/**
 * 查询某个用户在一些频道的角色信息(批量)
 * @param     appkey           客户端的标识
 * @param     uid              用户id
 * @param     tids             频道id列表(列表最大长度为500)
 * @param     columns          需要查询的字段集合，批量接口只支持以下字段
 *                               - "tid",  频道id
 *                               - "type", 角色类型
 *                                   可能取值如下：
 *                                   取值   意义
 *                                   88     嘉宾（VIP），绿色马甲
 *                                   100    会员，蓝色马甲
 *                                   150    子频道管理员，红色马甲
 *                                   200    全频道（包括子频道）管理员，黄色马甲
 *                                   230    副会长，橙色马甲
 *                                   255    频道拥有者，紫色马甲
 *                                   300    客服，黑色马甲
 *                                   1000   超级管理员，黑色马甲
 *                               - "jifen", 原始的频道积分(或叫贡献),按分钟计算
 *                               - "add_time", 加入频道时间
 * @SaResponseSet              频道成员角色信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestSessSmember {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:string uid,
    4:list<string> tids,
    5:list<string> columns,	
}

/**
 * 查询某个频道内一些用户的角色信息(批量)
 * @param     appkey           客户端的标识
 * @param     tid              频道id
 * @param     uids             用户id列表(列表最大长度为500)
 * @param     columns          需要查询的字段集合，批量接口只支持以下字段
 *                               - "uid",  用户ID
 *                               - "type", 角色类型
 *                                   可能取值如下：
 *                                   取值   意义
 *                                   88     嘉宾（VIP），绿色马甲
 *                                   100    会员，蓝色马甲
 *                                   150    子频道管理员，红色马甲
 *                                   200    全频道（包括子频道）管理员，黄色马甲
 *                                   230    副会长，橙色马甲
 *                                   255    频道拥有者，紫色马甲
 *                                   300    客服，黑色马甲
 *                                   1000   超级管理员，黑色马甲
 *                               - "jifen", 原始的频道积分(或叫贡献),按分钟计算
 *                               - "add_time", 加入频道时间
 * @SaResponseSet              频道成员角色信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestUserSmember {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:string tid,
    4:list<string> uids,
    5:list<string> columns,	
}

/**
 * 按角色查询用户参与的频道列表(我的频道)
 * @param     appkey           客户端的标识
 * @param     uid              用户id
 * @param     rolers           频道角色值列表，例如：88/100/150/200/230/255
 *                             如果roles为 ALL 表示查询所有角色值
 * @param     columns          需要查询的字段集合，批量接口只支持以下字段
 *                               - "tid",  频道id
 *                               - "roler", 角色类型
 *                                   可能取值如下：
 *                                   取值   意义
 *                                   88     嘉宾（VIP），绿色马甲
 *                                   100    会员，蓝色马甲
 *                                   150    子频道管理员，红色马甲
 *                                   200    全频道（包括子频道）管理员，黄色马甲
 *                                   230    副会长，橙色马甲
 *                                   255    频道拥有者，紫色马甲
 *                                   300    客服，黑色马甲
 *                                   1000   超级管理员，黑色马甲
 *                               - "jifen", 原始的频道积分(或叫贡献),按分钟计算
 *                               - "add_time", 加入频道时间
 *                               - "type": 频道类型，例如 0为游戏/1为娱乐/3为教育 等
 *                               - "typestr": 频道类型二级描述，例如 "K歌地带"/"九龙朝" 等
 *                               - "asid": 频道短位id
 *                               - "name": 频道名字
 * @SaResponseSet              按角色分类的频道列表信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestJoinSmember {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:string uid,
    4:list<string> rolers,
    5:list<string> columns,	
}
	
//================================ sa(service agent) smember params end ================================//

//================================ sa(service agent) ban params start ==============================//
/**
 * 查询某个频道内被封禁的PC信息(分页)
 * @param     appkey          客户端的标识
 * @param     sid             频道id
 * @param     date_from       被封起始时间，格式为"2018-09-13 09:08:20",如果为空则忽略此参数
 * @param     date_to         被封最晚时间，格式为"2018-09-14 21:10:00",如果为空则忽略此参数
 * @param     reason          被封原因，可选值为：
 *                              0 - 所有原因
 *                              1 - 恶意刷屏
 *                              2 - 谩骂
 *                              3 - 刷广告
 *                              4 - 其他(除上面1,2,3三个原因外所有其他原因)
 * @param     uid             被封用户id，如果为空则忽略此参数
 * @param     columns         要查询的字段，可选值为
 *                              - bandate, 被封时间
 *                              - admin_uid, 操作者(封人者)id
 *                              - user_uid, 被封用户id
 *                              - sid, 频道id
 *                              - pcinfo, 被封ip信息
 *                              - user_nick, 被封用户昵称
 *                              - ip, 被封ip
 *                              - unbandate, 解封时间
 *                              - reason, 被封原因
 * @param     page            要查询的页码，以0为起始页码，不能为负数
 * @param     per_page        每页返回的记录数，不能小于1，不能大于500，
 *                            相关的多次查询里每次分页查询此值应该保持不变
 * @param     order_by        指定排序字段，可选值为
 *                               - bandate
 *                               - admin_uid 
 *                               - user_uid
 * @param     descending      指定排序字段是否按降序排序
 *                              1 - 表示指定降序排序
 * @SaResponseSet             封禁的PC信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestPCBan {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:string sid,
    4:string date_from, 
    5:string date_to, 
    6:i32 reason,
    7:string uid, 
    8:list<string> columns, 
    9:i32 page, 
    10:i32 per_page,
    11:string order_by, 
    12:bool descending
}

/**
 * 查询某个频道内被封禁的用户信息(分页)
 * @param     appkey          客户端的标识
 * @param     sid             频道id
 * @param     date_from       被封起始时间，格式为"2018-09-13 09:08:20",如果为空则忽略此参数
 * @param     date_to         被封最晚时间，格式为"2018-09-14 21:10:00",如果为空则忽略此参数
 * @param     reason          被封原因，可选值为：
 *                              0 - 所有原因
 *                              1 - 恶意刷屏
 *                              2 - 谩骂
 *                              3 - 刷广告
 *                              4 - 其他(除上面1,2,3三个原因外所有其他原因)
 * @param     uid             被封用户id，如果为VALUE_NULL则忽略此参数
 * @param     columns         要查询的字段，可选值为
 *                              - op_date, 被封时间
 *                              - uid_, 被封用户id
 *                              - admin_uid, 操作者(封人者)id
 *                              - sid, 顶级频道id
 *                              - reason, 被封原因
 * @param     page            要查询的页码，以0为起始页码，不能为负数
 * @param     per_page        每页返回的记录数，不能小于1，不能大于500，
 *                            相关的多次查询里每次分页查询此值应该保持不变
 * @param     order_by        指定排序字段，可选值为
 *                              - op_date, 
 *                              - uid, 
 *                              - admin_uid
 * @param     descending      指定排序字段是否按降序排序
 *                              1 - 按降序排序
 * @SaResponseSet             封禁的用户信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestUserBan {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:string sid,
    4:string date_from, 
    5:string date_to, 
    6:i32 reason,
    7:string uid, 
    8:list<string> columns, 
    9:i32 page, 
    10:i32 per_page,
    11:string order_by, 
    12:bool descending
}

//================================ sa(service agent) ban params end ================================//

//================================ sa(service agent) roam params start ==============================//
/**
 * 查询漫游信息(最近访问频道)
 * @param     appkey           客户端的标识
 * @param     uids             用户id列表(列表最大长度为100)
 * @param     columns          需要查询的字段集合，可以查询如下字段
 *                               - "uid",
 *                               - "version",
 *                               - "value",
 * @SaResponseSet              漫游信息结果集，如果没有查到结果则dataSet为空
 */
struct SaRequestRoam {
    1:AuthorizeMsg authMsg,
    2:string appkey,
    3:list<string> uids,
    4:list<string> columns, 
}
//================================ sa(service agent) roam params end ================================//

//================================== sa(service agent) params struct end ====================================//

// 频道信息服务接口
service webdb_sinfo_service 
{
	//=========================================== sinfo sa(service agent) thrift apis =============================================//
	SaResponseSet  sa_get_session_info(1:SaRequestSession request),
	SaResponseSet  sa_get_subsess_info(1:SaRequestSubsess request),
	SaResponseSet  sa_get_ownersess_info(1:SaRequestOwnersess request),
	SaResponseSet  sa_get_favorsess_info(1:SaRequestFavorsess request),
	SaResponseSet  sa_get_recursess_info(1:SaRequestRecursess request),
	SaResponse     sa_create_session_info(1:SaRequestCreatesess request),
	//=========================================== sinfo sa(service agent) thrift apis =============================================//

}

// 用户信息服务接口
service webdb_uinfo_service
{
	//=========================================== uinfo sa(service agent) thrift apis =============================================//
	SaResponseSet sa_get_user_info(1:SaRequestUser request),
	SaResponseSet sa_batch_get_user_info(1:SaRequestBatchUser request),
	SaResponse    sa_get_uid_by_imid(1:SaRequestUid request),
	SaResponse    sa_set_user_info(1:SaRequestSetUser request),
	SaResponse    sa_limit_update_uinfo(1:SaRequestLmtUptUser request),
	SaResponse    sa_unlimit_update_uinfo(1:SaRequestUnlmtUptUser request),
	//=========================================== uinfo sa(service agent) thrift apis =============================================//
}

// 马甲信息服务接口
service webdb_smember_service
{
    //=========================================== smember sa(service agent) thrift apis =============================================//
    SaResponseSet  sa_get_sess_smember_info(1:SaRequestSessSmember request),
    SaResponseSet  sa_get_user_smember_info(1:SaRequestUserSmember request),
	SaResponseSet  sa_get_join_smember_info(1:SaRequestJoinSmember request),
    //=========================================== smember sa(service agent) thrift apis =============================================//
 }
 
 
// PC,用户封禁信息服务接口
service webdb_ban_service
{	
    //=========================================== ban sa(service agent) thrift apis =============================================//
    SaResponseSet  sa_get_pc_ban_info(1:SaRequestPCBan request),
    SaResponseSet  sa_get_user_ban_info(1:SaRequestUserBan request),
    //=========================================== ban sa(service agent) thrift apis =============================================//

}

// 漫游信息服务接口
service webdb_roam_service
{
    //=========================================== roam sa(service agent) thrift apis =============================================//
    SaResponseSet  sa_get_roam_info(1:SaRequestRoam request),
    //=========================================== roam sa(service agent) thrift apis =============================================//
}