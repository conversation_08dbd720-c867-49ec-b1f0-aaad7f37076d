/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_price;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-02-22")
public class BaseChannel implements org.apache.thrift.TBase<BaseChannel, BaseChannel._Fields>, java.io.Serializable, Cloneable, Comparable<BaseChannel> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("BaseChannel");

  private static final org.apache.thrift.protocol.TField TOP_SID_FIELD_DESC = new org.apache.thrift.protocol.TField("topSid", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField SUB_SID_FIELD_DESC = new org.apache.thrift.protocol.TField("subSid", org.apache.thrift.protocol.TType.I64, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new BaseChannelStandardSchemeFactory());
    schemes.put(TupleScheme.class, new BaseChannelTupleSchemeFactory());
  }

  public long topSid; // required
  public long subSid; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    TOP_SID((short)1, "topSid"),
    SUB_SID((short)2, "subSid");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // TOP_SID
          return TOP_SID;
        case 2: // SUB_SID
          return SUB_SID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __TOPSID_ISSET_ID = 0;
  private static final int __SUBSID_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.TOP_SID, new org.apache.thrift.meta_data.FieldMetaData("topSid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SUB_SID, new org.apache.thrift.meta_data.FieldMetaData("subSid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(BaseChannel.class, metaDataMap);
  }

  public BaseChannel() {
  }

  public BaseChannel(
    long topSid,
    long subSid)
  {
    this();
    this.topSid = topSid;
    setTopSidIsSet(true);
    this.subSid = subSid;
    setSubSidIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public BaseChannel(BaseChannel other) {
    __isset_bitfield = other.__isset_bitfield;
    this.topSid = other.topSid;
    this.subSid = other.subSid;
  }

  public BaseChannel deepCopy() {
    return new BaseChannel(this);
  }

  @Override
  public void clear() {
    setTopSidIsSet(false);
    this.topSid = 0;
    setSubSidIsSet(false);
    this.subSid = 0;
  }

  public long getTopSid() {
    return this.topSid;
  }

  public BaseChannel setTopSid(long topSid) {
    this.topSid = topSid;
    setTopSidIsSet(true);
    return this;
  }

  public void unsetTopSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TOPSID_ISSET_ID);
  }

  /** Returns true if field topSid is set (has been assigned a value) and false otherwise */
  public boolean isSetTopSid() {
    return EncodingUtils.testBit(__isset_bitfield, __TOPSID_ISSET_ID);
  }

  public void setTopSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TOPSID_ISSET_ID, value);
  }

  public long getSubSid() {
    return this.subSid;
  }

  public BaseChannel setSubSid(long subSid) {
    this.subSid = subSid;
    setSubSidIsSet(true);
    return this;
  }

  public void unsetSubSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SUBSID_ISSET_ID);
  }

  /** Returns true if field subSid is set (has been assigned a value) and false otherwise */
  public boolean isSetSubSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SUBSID_ISSET_ID);
  }

  public void setSubSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SUBSID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case TOP_SID:
      if (value == null) {
        unsetTopSid();
      } else {
        setTopSid((Long)value);
      }
      break;

    case SUB_SID:
      if (value == null) {
        unsetSubSid();
      } else {
        setSubSid((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case TOP_SID:
      return getTopSid();

    case SUB_SID:
      return getSubSid();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case TOP_SID:
      return isSetTopSid();
    case SUB_SID:
      return isSetSubSid();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof BaseChannel)
      return this.equals((BaseChannel)that);
    return false;
  }

  public boolean equals(BaseChannel that) {
    if (that == null)
      return false;

    boolean this_present_topSid = true;
    boolean that_present_topSid = true;
    if (this_present_topSid || that_present_topSid) {
      if (!(this_present_topSid && that_present_topSid))
        return false;
      if (this.topSid != that.topSid)
        return false;
    }

    boolean this_present_subSid = true;
    boolean that_present_subSid = true;
    if (this_present_subSid || that_present_subSid) {
      if (!(this_present_subSid && that_present_subSid))
        return false;
      if (this.subSid != that.subSid)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_topSid = true;
    list.add(present_topSid);
    if (present_topSid)
      list.add(topSid);

    boolean present_subSid = true;
    list.add(present_subSid);
    if (present_subSid)
      list.add(subSid);

    return list.hashCode();
  }

  @Override
  public int compareTo(BaseChannel other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetTopSid()).compareTo(other.isSetTopSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTopSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.topSid, other.topSid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSubSid()).compareTo(other.isSetSubSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSubSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.subSid, other.subSid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("BaseChannel(");
    boolean first = true;

    sb.append("topSid:");
    sb.append(this.topSid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("subSid:");
    sb.append(this.subSid);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class BaseChannelStandardSchemeFactory implements SchemeFactory {
    public BaseChannelStandardScheme getScheme() {
      return new BaseChannelStandardScheme();
    }
  }

  private static class BaseChannelStandardScheme extends StandardScheme<BaseChannel> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, BaseChannel struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // TOP_SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.topSid = iprot.readI64();
              struct.setTopSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SUB_SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.subSid = iprot.readI64();
              struct.setSubSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, BaseChannel struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(TOP_SID_FIELD_DESC);
      oprot.writeI64(struct.topSid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SUB_SID_FIELD_DESC);
      oprot.writeI64(struct.subSid);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class BaseChannelTupleSchemeFactory implements SchemeFactory {
    public BaseChannelTupleScheme getScheme() {
      return new BaseChannelTupleScheme();
    }
  }

  private static class BaseChannelTupleScheme extends TupleScheme<BaseChannel> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, BaseChannel struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetTopSid()) {
        optionals.set(0);
      }
      if (struct.isSetSubSid()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetTopSid()) {
        oprot.writeI64(struct.topSid);
      }
      if (struct.isSetSubSid()) {
        oprot.writeI64(struct.subSid);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, BaseChannel struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.topSid = iprot.readI64();
        struct.setTopSidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.subSid = iprot.readI64();
        struct.setSubSidIsSet(true);
      }
    }
  }

}

