/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class QueryQualificationGroupRequest implements org.apache.thrift.TBase<QueryQualificationGroupRequest, QueryQualificationGroupRequest._Fields>, java.io.Serializable, Cloneable, Comparable<QueryQualificationGroupRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryQualificationGroupRequest");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RANK_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rankId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField RETURN_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("returnData", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField QUERY_DATE_STR_FIELD_DESC = new org.apache.thrift.protocol.TField("queryDateStr", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryQualificationGroupRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryQualificationGroupRequestTupleSchemeFactory());
  }

  public long actId; // required
  public long rankId; // required
  public long phaseId; // required
  public int returnData; // required
  public String queryDateStr; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    RANK_ID((short)2, "rankId"),
    PHASE_ID((short)3, "phaseId"),
    RETURN_DATA((short)4, "returnData"),
    QUERY_DATE_STR((short)5, "queryDateStr"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // RANK_ID
          return RANK_ID;
        case 3: // PHASE_ID
          return PHASE_ID;
        case 4: // RETURN_DATA
          return RETURN_DATA;
        case 5: // QUERY_DATE_STR
          return QUERY_DATE_STR;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __RANKID_ISSET_ID = 1;
  private static final int __PHASEID_ISSET_ID = 2;
  private static final int __RETURNDATA_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANK_ID, new org.apache.thrift.meta_data.FieldMetaData("rankId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RETURN_DATA, new org.apache.thrift.meta_data.FieldMetaData("returnData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.QUERY_DATE_STR, new org.apache.thrift.meta_data.FieldMetaData("queryDateStr", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryQualificationGroupRequest.class, metaDataMap);
  }

  public QueryQualificationGroupRequest() {
  }

  public QueryQualificationGroupRequest(
    long actId,
    long rankId,
    long phaseId,
    int returnData,
    String queryDateStr,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.rankId = rankId;
    setRankIdIsSet(true);
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    this.returnData = returnData;
    setReturnDataIsSet(true);
    this.queryDateStr = queryDateStr;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryQualificationGroupRequest(QueryQualificationGroupRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    this.rankId = other.rankId;
    this.phaseId = other.phaseId;
    this.returnData = other.returnData;
    if (other.isSetQueryDateStr()) {
      this.queryDateStr = other.queryDateStr;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public QueryQualificationGroupRequest deepCopy() {
    return new QueryQualificationGroupRequest(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    setRankIdIsSet(false);
    this.rankId = 0;
    setPhaseIdIsSet(false);
    this.phaseId = 0;
    setReturnDataIsSet(false);
    this.returnData = 0;
    this.queryDateStr = null;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public QueryQualificationGroupRequest setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getRankId() {
    return this.rankId;
  }

  public QueryQualificationGroupRequest setRankId(long rankId) {
    this.rankId = rankId;
    setRankIdIsSet(true);
    return this;
  }

  public void unsetRankId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  /** Returns true if field rankId is set (has been assigned a value) and false otherwise */
  public boolean isSetRankId() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKID_ISSET_ID);
  }

  public void setRankIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKID_ISSET_ID, value);
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public QueryQualificationGroupRequest setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public int getReturnData() {
    return this.returnData;
  }

  public QueryQualificationGroupRequest setReturnData(int returnData) {
    this.returnData = returnData;
    setReturnDataIsSet(true);
    return this;
  }

  public void unsetReturnData() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RETURNDATA_ISSET_ID);
  }

  /** Returns true if field returnData is set (has been assigned a value) and false otherwise */
  public boolean isSetReturnData() {
    return EncodingUtils.testBit(__isset_bitfield, __RETURNDATA_ISSET_ID);
  }

  public void setReturnDataIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RETURNDATA_ISSET_ID, value);
  }

  public String getQueryDateStr() {
    return this.queryDateStr;
  }

  public QueryQualificationGroupRequest setQueryDateStr(String queryDateStr) {
    this.queryDateStr = queryDateStr;
    return this;
  }

  public void unsetQueryDateStr() {
    this.queryDateStr = null;
  }

  /** Returns true if field queryDateStr is set (has been assigned a value) and false otherwise */
  public boolean isSetQueryDateStr() {
    return this.queryDateStr != null;
  }

  public void setQueryDateStrIsSet(boolean value) {
    if (!value) {
      this.queryDateStr = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public QueryQualificationGroupRequest setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case RANK_ID:
      if (value == null) {
        unsetRankId();
      } else {
        setRankId((Long)value);
      }
      break;

    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    case RETURN_DATA:
      if (value == null) {
        unsetReturnData();
      } else {
        setReturnData((Integer)value);
      }
      break;

    case QUERY_DATE_STR:
      if (value == null) {
        unsetQueryDateStr();
      } else {
        setQueryDateStr((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case RANK_ID:
      return getRankId();

    case PHASE_ID:
      return getPhaseId();

    case RETURN_DATA:
      return getReturnData();

    case QUERY_DATE_STR:
      return getQueryDateStr();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case RANK_ID:
      return isSetRankId();
    case PHASE_ID:
      return isSetPhaseId();
    case RETURN_DATA:
      return isSetReturnData();
    case QUERY_DATE_STR:
      return isSetQueryDateStr();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryQualificationGroupRequest)
      return this.equals((QueryQualificationGroupRequest)that);
    return false;
  }

  public boolean equals(QueryQualificationGroupRequest that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_rankId = true;
    boolean that_present_rankId = true;
    if (this_present_rankId || that_present_rankId) {
      if (!(this_present_rankId && that_present_rankId))
        return false;
      if (this.rankId != that.rankId)
        return false;
    }

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    boolean this_present_returnData = true;
    boolean that_present_returnData = true;
    if (this_present_returnData || that_present_returnData) {
      if (!(this_present_returnData && that_present_returnData))
        return false;
      if (this.returnData != that.returnData)
        return false;
    }

    boolean this_present_queryDateStr = true && this.isSetQueryDateStr();
    boolean that_present_queryDateStr = true && that.isSetQueryDateStr();
    if (this_present_queryDateStr || that_present_queryDateStr) {
      if (!(this_present_queryDateStr && that_present_queryDateStr))
        return false;
      if (!this.queryDateStr.equals(that.queryDateStr))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_rankId = true;
    list.add(present_rankId);
    if (present_rankId)
      list.add(rankId);

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    boolean present_returnData = true;
    list.add(present_returnData);
    if (present_returnData)
      list.add(returnData);

    boolean present_queryDateStr = true && (isSetQueryDateStr());
    list.add(present_queryDateStr);
    if (present_queryDateStr)
      list.add(queryDateStr);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryQualificationGroupRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankId()).compareTo(other.isSetRankId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankId, other.rankId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReturnData()).compareTo(other.isSetReturnData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReturnData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.returnData, other.returnData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetQueryDateStr()).compareTo(other.isSetQueryDateStr());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetQueryDateStr()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.queryDateStr, other.queryDateStr);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryQualificationGroupRequest(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankId:");
    sb.append(this.rankId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("returnData:");
    sb.append(this.returnData);
    first = false;
    if (!first) sb.append(", ");
    sb.append("queryDateStr:");
    if (this.queryDateStr == null) {
      sb.append("null");
    } else {
      sb.append(this.queryDateStr);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryQualificationGroupRequestStandardSchemeFactory implements SchemeFactory {
    public QueryQualificationGroupRequestStandardScheme getScheme() {
      return new QueryQualificationGroupRequestStandardScheme();
    }
  }

  private static class QueryQualificationGroupRequestStandardScheme extends StandardScheme<QueryQualificationGroupRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryQualificationGroupRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RANK_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankId = iprot.readI64();
              struct.setRankIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // RETURN_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.returnData = iprot.readI32();
              struct.setReturnDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // QUERY_DATE_STR
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.queryDateStr = iprot.readString();
              struct.setQueryDateStrIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map650 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map650.size);
                String _key651;
                String _val652;
                for (int _i653 = 0; _i653 < _map650.size; ++_i653)
                {
                  _key651 = iprot.readString();
                  _val652 = iprot.readString();
                  struct.extData.put(_key651, _val652);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryQualificationGroupRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANK_ID_FIELD_DESC);
      oprot.writeI64(struct.rankId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RETURN_DATA_FIELD_DESC);
      oprot.writeI32(struct.returnData);
      oprot.writeFieldEnd();
      if (struct.queryDateStr != null) {
        oprot.writeFieldBegin(QUERY_DATE_STR_FIELD_DESC);
        oprot.writeString(struct.queryDateStr);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter654 : struct.extData.entrySet())
          {
            oprot.writeString(_iter654.getKey());
            oprot.writeString(_iter654.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryQualificationGroupRequestTupleSchemeFactory implements SchemeFactory {
    public QueryQualificationGroupRequestTupleScheme getScheme() {
      return new QueryQualificationGroupRequestTupleScheme();
    }
  }

  private static class QueryQualificationGroupRequestTupleScheme extends TupleScheme<QueryQualificationGroupRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryQualificationGroupRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetRankId()) {
        optionals.set(1);
      }
      if (struct.isSetPhaseId()) {
        optionals.set(2);
      }
      if (struct.isSetReturnData()) {
        optionals.set(3);
      }
      if (struct.isSetQueryDateStr()) {
        optionals.set(4);
      }
      if (struct.isSetExtData()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetRankId()) {
        oprot.writeI64(struct.rankId);
      }
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
      if (struct.isSetReturnData()) {
        oprot.writeI32(struct.returnData);
      }
      if (struct.isSetQueryDateStr()) {
        oprot.writeString(struct.queryDateStr);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter655 : struct.extData.entrySet())
          {
            oprot.writeString(_iter655.getKey());
            oprot.writeString(_iter655.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryQualificationGroupRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.rankId = iprot.readI64();
        struct.setRankIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.returnData = iprot.readI32();
        struct.setReturnDataIsSet(true);
      }
      if (incoming.get(4)) {
        struct.queryDateStr = iprot.readString();
        struct.setQueryDateStrIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TMap _map656 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map656.size);
          String _key657;
          String _val658;
          for (int _i659 = 0; _i659 < _map656.size; ++_i659)
          {
            _key657 = iprot.readString();
            _val658 = iprot.readString();
            struct.extData.put(_key657, _val658);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

