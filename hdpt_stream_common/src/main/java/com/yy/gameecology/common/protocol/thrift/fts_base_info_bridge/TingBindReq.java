/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class TingBindReq implements org.apache.thrift.TBase<TingBindReq, TingBindReq._Fields>, java.io.Serializable, Cloneable, Comparable<TingBindReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TingBindReq");

  private static final org.apache.thrift.protocol.TField APPID_FIELD_DESC = new org.apache.thrift.protocol.TField("appid", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField UID_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("uid_list", org.apache.thrift.protocol.TType.LIST, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TingBindReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TingBindReqTupleSchemeFactory());
  }

  /**
   * 
   * @see TAppId
   */
  public TAppId appid; // required
  public List<Long> uid_list; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 
     * @see TAppId
     */
    APPID((short)1, "appid"),
    UID_LIST((short)2, "uid_list");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // APPID
          return APPID;
        case 2: // UID_LIST
          return UID_LIST;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.APPID, new org.apache.thrift.meta_data.FieldMetaData("appid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, TAppId.class)));
    tmpMap.put(_Fields.UID_LIST, new org.apache.thrift.meta_data.FieldMetaData("uid_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TingBindReq.class, metaDataMap);
  }

  public TingBindReq() {
  }

  public TingBindReq(
    TAppId appid,
    List<Long> uid_list)
  {
    this();
    this.appid = appid;
    this.uid_list = uid_list;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TingBindReq(TingBindReq other) {
    if (other.isSetAppid()) {
      this.appid = other.appid;
    }
    if (other.isSetUid_list()) {
      List<Long> __this__uid_list = new ArrayList<Long>(other.uid_list);
      this.uid_list = __this__uid_list;
    }
  }

  public TingBindReq deepCopy() {
    return new TingBindReq(this);
  }

  @Override
  public void clear() {
    this.appid = null;
    this.uid_list = null;
  }

  /**
   * 
   * @see TAppId
   */
  public TAppId getAppid() {
    return this.appid;
  }

  /**
   * 
   * @see TAppId
   */
  public TingBindReq setAppid(TAppId appid) {
    this.appid = appid;
    return this;
  }

  public void unsetAppid() {
    this.appid = null;
  }

  /** Returns true if field appid is set (has been assigned a value) and false otherwise */
  public boolean isSetAppid() {
    return this.appid != null;
  }

  public void setAppidIsSet(boolean value) {
    if (!value) {
      this.appid = null;
    }
  }

  public int getUid_listSize() {
    return (this.uid_list == null) ? 0 : this.uid_list.size();
  }

  public java.util.Iterator<Long> getUid_listIterator() {
    return (this.uid_list == null) ? null : this.uid_list.iterator();
  }

  public void addToUid_list(long elem) {
    if (this.uid_list == null) {
      this.uid_list = new ArrayList<Long>();
    }
    this.uid_list.add(elem);
  }

  public List<Long> getUid_list() {
    return this.uid_list;
  }

  public TingBindReq setUid_list(List<Long> uid_list) {
    this.uid_list = uid_list;
    return this;
  }

  public void unsetUid_list() {
    this.uid_list = null;
  }

  /** Returns true if field uid_list is set (has been assigned a value) and false otherwise */
  public boolean isSetUid_list() {
    return this.uid_list != null;
  }

  public void setUid_listIsSet(boolean value) {
    if (!value) {
      this.uid_list = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case APPID:
      if (value == null) {
        unsetAppid();
      } else {
        setAppid((TAppId)value);
      }
      break;

    case UID_LIST:
      if (value == null) {
        unsetUid_list();
      } else {
        setUid_list((List<Long>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case APPID:
      return getAppid();

    case UID_LIST:
      return getUid_list();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case APPID:
      return isSetAppid();
    case UID_LIST:
      return isSetUid_list();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TingBindReq)
      return this.equals((TingBindReq)that);
    return false;
  }

  public boolean equals(TingBindReq that) {
    if (that == null)
      return false;

    boolean this_present_appid = true && this.isSetAppid();
    boolean that_present_appid = true && that.isSetAppid();
    if (this_present_appid || that_present_appid) {
      if (!(this_present_appid && that_present_appid))
        return false;
      if (!this.appid.equals(that.appid))
        return false;
    }

    boolean this_present_uid_list = true && this.isSetUid_list();
    boolean that_present_uid_list = true && that.isSetUid_list();
    if (this_present_uid_list || that_present_uid_list) {
      if (!(this_present_uid_list && that_present_uid_list))
        return false;
      if (!this.uid_list.equals(that.uid_list))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_appid = true && (isSetAppid());
    list.add(present_appid);
    if (present_appid)
      list.add(appid.getValue());

    boolean present_uid_list = true && (isSetUid_list());
    list.add(present_uid_list);
    if (present_uid_list)
      list.add(uid_list);

    return list.hashCode();
  }

  @Override
  public int compareTo(TingBindReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAppid()).compareTo(other.isSetAppid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appid, other.appid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUid_list()).compareTo(other.isSetUid_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid_list, other.uid_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TingBindReq(");
    boolean first = true;

    sb.append("appid:");
    if (this.appid == null) {
      sb.append("null");
    } else {
      sb.append(this.appid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("uid_list:");
    if (this.uid_list == null) {
      sb.append("null");
    } else {
      sb.append(this.uid_list);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TingBindReqStandardSchemeFactory implements SchemeFactory {
    public TingBindReqStandardScheme getScheme() {
      return new TingBindReqStandardScheme();
    }
  }

  private static class TingBindReqStandardScheme extends StandardScheme<TingBindReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TingBindReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // APPID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.appid = com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge.TAppId.findByValue(iprot.readI32());
              struct.setAppidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // UID_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list218 = iprot.readListBegin();
                struct.uid_list = new ArrayList<Long>(_list218.size);
                long _elem219;
                for (int _i220 = 0; _i220 < _list218.size; ++_i220)
                {
                  _elem219 = iprot.readI64();
                  struct.uid_list.add(_elem219);
                }
                iprot.readListEnd();
              }
              struct.setUid_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TingBindReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.appid != null) {
        oprot.writeFieldBegin(APPID_FIELD_DESC);
        oprot.writeI32(struct.appid.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.uid_list != null) {
        oprot.writeFieldBegin(UID_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.uid_list.size()));
          for (long _iter221 : struct.uid_list)
          {
            oprot.writeI64(_iter221);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TingBindReqTupleSchemeFactory implements SchemeFactory {
    public TingBindReqTupleScheme getScheme() {
      return new TingBindReqTupleScheme();
    }
  }

  private static class TingBindReqTupleScheme extends TupleScheme<TingBindReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TingBindReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAppid()) {
        optionals.set(0);
      }
      if (struct.isSetUid_list()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetAppid()) {
        oprot.writeI32(struct.appid.getValue());
      }
      if (struct.isSetUid_list()) {
        {
          oprot.writeI32(struct.uid_list.size());
          for (long _iter222 : struct.uid_list)
          {
            oprot.writeI64(_iter222);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TingBindReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.appid = com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge.TAppId.findByValue(iprot.readI32());
        struct.setAppidIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list223 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.uid_list = new ArrayList<Long>(_list223.size);
          long _elem224;
          for (int _i225 = 0; _i225 < _list223.size; ++_i225)
          {
            _elem224 = iprot.readI64();
            struct.uid_list.add(_elem224);
          }
        }
        struct.setUid_listIsSet(true);
      }
    }
  }

}

