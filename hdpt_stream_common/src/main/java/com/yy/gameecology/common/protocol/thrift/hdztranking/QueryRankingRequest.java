/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 榜单查询业务参数
 * 
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class QueryRankingRequest implements org.apache.thrift.TBase<QueryRankingRequest, QueryRankingRequest._Fields>, java.io.Serializable, Cloneable, Comparable<QueryRankingRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryRankingRequest");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RANKING_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rankingId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField RANKING_COUNT_FIELD_DESC = new org.apache.thrift.protocol.TField("rankingCount", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField DATE_STR_FIELD_DESC = new org.apache.thrift.protocol.TField("dateStr", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField FIND_SRC_MEMBER_FIELD_DESC = new org.apache.thrift.protocol.TField("findSrcMember", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField RANK_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("rankType", org.apache.thrift.protocol.TType.STRING, (short)7);
  private static final org.apache.thrift.protocol.TField POINTED_MEMBER_FIELD_DESC = new org.apache.thrift.protocol.TField("pointedMember", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryRankingRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryRankingRequestTupleSchemeFactory());
  }

  public long actId; // required
  public long rankingId; // required
  public long rankingCount; // required
  public long phaseId; // required
  public String dateStr; // required
  public String findSrcMember; // required
  public String rankType; // required
  public String pointedMember; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    RANKING_ID((short)2, "rankingId"),
    RANKING_COUNT((short)3, "rankingCount"),
    PHASE_ID((short)4, "phaseId"),
    DATE_STR((short)5, "dateStr"),
    FIND_SRC_MEMBER((short)6, "findSrcMember"),
    RANK_TYPE((short)7, "rankType"),
    POINTED_MEMBER((short)8, "pointedMember"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // RANKING_ID
          return RANKING_ID;
        case 3: // RANKING_COUNT
          return RANKING_COUNT;
        case 4: // PHASE_ID
          return PHASE_ID;
        case 5: // DATE_STR
          return DATE_STR;
        case 6: // FIND_SRC_MEMBER
          return FIND_SRC_MEMBER;
        case 7: // RANK_TYPE
          return RANK_TYPE;
        case 8: // POINTED_MEMBER
          return POINTED_MEMBER;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __RANKINGID_ISSET_ID = 1;
  private static final int __RANKINGCOUNT_ISSET_ID = 2;
  private static final int __PHASEID_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANKING_ID, new org.apache.thrift.meta_data.FieldMetaData("rankingId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANKING_COUNT, new org.apache.thrift.meta_data.FieldMetaData("rankingCount", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DATE_STR, new org.apache.thrift.meta_data.FieldMetaData("dateStr", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.FIND_SRC_MEMBER, new org.apache.thrift.meta_data.FieldMetaData("findSrcMember", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RANK_TYPE, new org.apache.thrift.meta_data.FieldMetaData("rankType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.POINTED_MEMBER, new org.apache.thrift.meta_data.FieldMetaData("pointedMember", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryRankingRequest.class, metaDataMap);
  }

  public QueryRankingRequest() {
  }

  public QueryRankingRequest(
    long actId,
    long rankingId,
    long rankingCount,
    long phaseId,
    String dateStr,
    String findSrcMember,
    String rankType,
    String pointedMember,
    Map<String,String> extData)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.rankingId = rankingId;
    setRankingIdIsSet(true);
    this.rankingCount = rankingCount;
    setRankingCountIsSet(true);
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    this.dateStr = dateStr;
    this.findSrcMember = findSrcMember;
    this.rankType = rankType;
    this.pointedMember = pointedMember;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryRankingRequest(QueryRankingRequest other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    this.rankingId = other.rankingId;
    this.rankingCount = other.rankingCount;
    this.phaseId = other.phaseId;
    if (other.isSetDateStr()) {
      this.dateStr = other.dateStr;
    }
    if (other.isSetFindSrcMember()) {
      this.findSrcMember = other.findSrcMember;
    }
    if (other.isSetRankType()) {
      this.rankType = other.rankType;
    }
    if (other.isSetPointedMember()) {
      this.pointedMember = other.pointedMember;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public QueryRankingRequest deepCopy() {
    return new QueryRankingRequest(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    setRankingIdIsSet(false);
    this.rankingId = 0;
    setRankingCountIsSet(false);
    this.rankingCount = 0;
    setPhaseIdIsSet(false);
    this.phaseId = 0;
    this.dateStr = null;
    this.findSrcMember = null;
    this.rankType = null;
    this.pointedMember = null;
    this.extData = null;
  }

  public long getActId() {
    return this.actId;
  }

  public QueryRankingRequest setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getRankingId() {
    return this.rankingId;
  }

  public QueryRankingRequest setRankingId(long rankingId) {
    this.rankingId = rankingId;
    setRankingIdIsSet(true);
    return this;
  }

  public void unsetRankingId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKINGID_ISSET_ID);
  }

  /** Returns true if field rankingId is set (has been assigned a value) and false otherwise */
  public boolean isSetRankingId() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKINGID_ISSET_ID);
  }

  public void setRankingIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKINGID_ISSET_ID, value);
  }

  public long getRankingCount() {
    return this.rankingCount;
  }

  public QueryRankingRequest setRankingCount(long rankingCount) {
    this.rankingCount = rankingCount;
    setRankingCountIsSet(true);
    return this;
  }

  public void unsetRankingCount() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKINGCOUNT_ISSET_ID);
  }

  /** Returns true if field rankingCount is set (has been assigned a value) and false otherwise */
  public boolean isSetRankingCount() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKINGCOUNT_ISSET_ID);
  }

  public void setRankingCountIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKINGCOUNT_ISSET_ID, value);
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public QueryRankingRequest setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public String getDateStr() {
    return this.dateStr;
  }

  public QueryRankingRequest setDateStr(String dateStr) {
    this.dateStr = dateStr;
    return this;
  }

  public void unsetDateStr() {
    this.dateStr = null;
  }

  /** Returns true if field dateStr is set (has been assigned a value) and false otherwise */
  public boolean isSetDateStr() {
    return this.dateStr != null;
  }

  public void setDateStrIsSet(boolean value) {
    if (!value) {
      this.dateStr = null;
    }
  }

  public String getFindSrcMember() {
    return this.findSrcMember;
  }

  public QueryRankingRequest setFindSrcMember(String findSrcMember) {
    this.findSrcMember = findSrcMember;
    return this;
  }

  public void unsetFindSrcMember() {
    this.findSrcMember = null;
  }

  /** Returns true if field findSrcMember is set (has been assigned a value) and false otherwise */
  public boolean isSetFindSrcMember() {
    return this.findSrcMember != null;
  }

  public void setFindSrcMemberIsSet(boolean value) {
    if (!value) {
      this.findSrcMember = null;
    }
  }

  public String getRankType() {
    return this.rankType;
  }

  public QueryRankingRequest setRankType(String rankType) {
    this.rankType = rankType;
    return this;
  }

  public void unsetRankType() {
    this.rankType = null;
  }

  /** Returns true if field rankType is set (has been assigned a value) and false otherwise */
  public boolean isSetRankType() {
    return this.rankType != null;
  }

  public void setRankTypeIsSet(boolean value) {
    if (!value) {
      this.rankType = null;
    }
  }

  public String getPointedMember() {
    return this.pointedMember;
  }

  public QueryRankingRequest setPointedMember(String pointedMember) {
    this.pointedMember = pointedMember;
    return this;
  }

  public void unsetPointedMember() {
    this.pointedMember = null;
  }

  /** Returns true if field pointedMember is set (has been assigned a value) and false otherwise */
  public boolean isSetPointedMember() {
    return this.pointedMember != null;
  }

  public void setPointedMemberIsSet(boolean value) {
    if (!value) {
      this.pointedMember = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public QueryRankingRequest setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case RANKING_ID:
      if (value == null) {
        unsetRankingId();
      } else {
        setRankingId((Long)value);
      }
      break;

    case RANKING_COUNT:
      if (value == null) {
        unsetRankingCount();
      } else {
        setRankingCount((Long)value);
      }
      break;

    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    case DATE_STR:
      if (value == null) {
        unsetDateStr();
      } else {
        setDateStr((String)value);
      }
      break;

    case FIND_SRC_MEMBER:
      if (value == null) {
        unsetFindSrcMember();
      } else {
        setFindSrcMember((String)value);
      }
      break;

    case RANK_TYPE:
      if (value == null) {
        unsetRankType();
      } else {
        setRankType((String)value);
      }
      break;

    case POINTED_MEMBER:
      if (value == null) {
        unsetPointedMember();
      } else {
        setPointedMember((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case RANKING_ID:
      return getRankingId();

    case RANKING_COUNT:
      return getRankingCount();

    case PHASE_ID:
      return getPhaseId();

    case DATE_STR:
      return getDateStr();

    case FIND_SRC_MEMBER:
      return getFindSrcMember();

    case RANK_TYPE:
      return getRankType();

    case POINTED_MEMBER:
      return getPointedMember();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case RANKING_ID:
      return isSetRankingId();
    case RANKING_COUNT:
      return isSetRankingCount();
    case PHASE_ID:
      return isSetPhaseId();
    case DATE_STR:
      return isSetDateStr();
    case FIND_SRC_MEMBER:
      return isSetFindSrcMember();
    case RANK_TYPE:
      return isSetRankType();
    case POINTED_MEMBER:
      return isSetPointedMember();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryRankingRequest)
      return this.equals((QueryRankingRequest)that);
    return false;
  }

  public boolean equals(QueryRankingRequest that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_rankingId = true;
    boolean that_present_rankingId = true;
    if (this_present_rankingId || that_present_rankingId) {
      if (!(this_present_rankingId && that_present_rankingId))
        return false;
      if (this.rankingId != that.rankingId)
        return false;
    }

    boolean this_present_rankingCount = true;
    boolean that_present_rankingCount = true;
    if (this_present_rankingCount || that_present_rankingCount) {
      if (!(this_present_rankingCount && that_present_rankingCount))
        return false;
      if (this.rankingCount != that.rankingCount)
        return false;
    }

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    boolean this_present_dateStr = true && this.isSetDateStr();
    boolean that_present_dateStr = true && that.isSetDateStr();
    if (this_present_dateStr || that_present_dateStr) {
      if (!(this_present_dateStr && that_present_dateStr))
        return false;
      if (!this.dateStr.equals(that.dateStr))
        return false;
    }

    boolean this_present_findSrcMember = true && this.isSetFindSrcMember();
    boolean that_present_findSrcMember = true && that.isSetFindSrcMember();
    if (this_present_findSrcMember || that_present_findSrcMember) {
      if (!(this_present_findSrcMember && that_present_findSrcMember))
        return false;
      if (!this.findSrcMember.equals(that.findSrcMember))
        return false;
    }

    boolean this_present_rankType = true && this.isSetRankType();
    boolean that_present_rankType = true && that.isSetRankType();
    if (this_present_rankType || that_present_rankType) {
      if (!(this_present_rankType && that_present_rankType))
        return false;
      if (!this.rankType.equals(that.rankType))
        return false;
    }

    boolean this_present_pointedMember = true && this.isSetPointedMember();
    boolean that_present_pointedMember = true && that.isSetPointedMember();
    if (this_present_pointedMember || that_present_pointedMember) {
      if (!(this_present_pointedMember && that_present_pointedMember))
        return false;
      if (!this.pointedMember.equals(that.pointedMember))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_rankingId = true;
    list.add(present_rankingId);
    if (present_rankingId)
      list.add(rankingId);

    boolean present_rankingCount = true;
    list.add(present_rankingCount);
    if (present_rankingCount)
      list.add(rankingCount);

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    boolean present_dateStr = true && (isSetDateStr());
    list.add(present_dateStr);
    if (present_dateStr)
      list.add(dateStr);

    boolean present_findSrcMember = true && (isSetFindSrcMember());
    list.add(present_findSrcMember);
    if (present_findSrcMember)
      list.add(findSrcMember);

    boolean present_rankType = true && (isSetRankType());
    list.add(present_rankType);
    if (present_rankType)
      list.add(rankType);

    boolean present_pointedMember = true && (isSetPointedMember());
    list.add(present_pointedMember);
    if (present_pointedMember)
      list.add(pointedMember);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryRankingRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankingId()).compareTo(other.isSetRankingId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankingId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankingId, other.rankingId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankingCount()).compareTo(other.isSetRankingCount());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankingCount()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankingCount, other.rankingCount);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDateStr()).compareTo(other.isSetDateStr());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDateStr()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dateStr, other.dateStr);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFindSrcMember()).compareTo(other.isSetFindSrcMember());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFindSrcMember()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.findSrcMember, other.findSrcMember);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankType()).compareTo(other.isSetRankType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankType, other.rankType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPointedMember()).compareTo(other.isSetPointedMember());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPointedMember()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pointedMember, other.pointedMember);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryRankingRequest(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankingId:");
    sb.append(this.rankingId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankingCount:");
    sb.append(this.rankingCount);
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("dateStr:");
    if (this.dateStr == null) {
      sb.append("null");
    } else {
      sb.append(this.dateStr);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("findSrcMember:");
    if (this.findSrcMember == null) {
      sb.append("null");
    } else {
      sb.append(this.findSrcMember);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankType:");
    if (this.rankType == null) {
      sb.append("null");
    } else {
      sb.append(this.rankType);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("pointedMember:");
    if (this.pointedMember == null) {
      sb.append("null");
    } else {
      sb.append(this.pointedMember);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryRankingRequestStandardSchemeFactory implements SchemeFactory {
    public QueryRankingRequestStandardScheme getScheme() {
      return new QueryRankingRequestStandardScheme();
    }
  }

  private static class QueryRankingRequestStandardScheme extends StandardScheme<QueryRankingRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryRankingRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RANKING_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankingId = iprot.readI64();
              struct.setRankingIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // RANKING_COUNT
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankingCount = iprot.readI64();
              struct.setRankingCountIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // DATE_STR
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.dateStr = iprot.readString();
              struct.setDateStrIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // FIND_SRC_MEMBER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.findSrcMember = iprot.readString();
              struct.setFindSrcMemberIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // RANK_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.rankType = iprot.readString();
              struct.setRankTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // POINTED_MEMBER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.pointedMember = iprot.readString();
              struct.setPointedMemberIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map18 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map18.size);
                String _key19;
                String _val20;
                for (int _i21 = 0; _i21 < _map18.size; ++_i21)
                {
                  _key19 = iprot.readString();
                  _val20 = iprot.readString();
                  struct.extData.put(_key19, _val20);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryRankingRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANKING_ID_FIELD_DESC);
      oprot.writeI64(struct.rankingId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANKING_COUNT_FIELD_DESC);
      oprot.writeI64(struct.rankingCount);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      if (struct.dateStr != null) {
        oprot.writeFieldBegin(DATE_STR_FIELD_DESC);
        oprot.writeString(struct.dateStr);
        oprot.writeFieldEnd();
      }
      if (struct.findSrcMember != null) {
        oprot.writeFieldBegin(FIND_SRC_MEMBER_FIELD_DESC);
        oprot.writeString(struct.findSrcMember);
        oprot.writeFieldEnd();
      }
      if (struct.rankType != null) {
        oprot.writeFieldBegin(RANK_TYPE_FIELD_DESC);
        oprot.writeString(struct.rankType);
        oprot.writeFieldEnd();
      }
      if (struct.pointedMember != null) {
        oprot.writeFieldBegin(POINTED_MEMBER_FIELD_DESC);
        oprot.writeString(struct.pointedMember);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter22 : struct.extData.entrySet())
          {
            oprot.writeString(_iter22.getKey());
            oprot.writeString(_iter22.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryRankingRequestTupleSchemeFactory implements SchemeFactory {
    public QueryRankingRequestTupleScheme getScheme() {
      return new QueryRankingRequestTupleScheme();
    }
  }

  private static class QueryRankingRequestTupleScheme extends TupleScheme<QueryRankingRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryRankingRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetRankingId()) {
        optionals.set(1);
      }
      if (struct.isSetRankingCount()) {
        optionals.set(2);
      }
      if (struct.isSetPhaseId()) {
        optionals.set(3);
      }
      if (struct.isSetDateStr()) {
        optionals.set(4);
      }
      if (struct.isSetFindSrcMember()) {
        optionals.set(5);
      }
      if (struct.isSetRankType()) {
        optionals.set(6);
      }
      if (struct.isSetPointedMember()) {
        optionals.set(7);
      }
      if (struct.isSetExtData()) {
        optionals.set(8);
      }
      oprot.writeBitSet(optionals, 9);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetRankingId()) {
        oprot.writeI64(struct.rankingId);
      }
      if (struct.isSetRankingCount()) {
        oprot.writeI64(struct.rankingCount);
      }
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
      if (struct.isSetDateStr()) {
        oprot.writeString(struct.dateStr);
      }
      if (struct.isSetFindSrcMember()) {
        oprot.writeString(struct.findSrcMember);
      }
      if (struct.isSetRankType()) {
        oprot.writeString(struct.rankType);
      }
      if (struct.isSetPointedMember()) {
        oprot.writeString(struct.pointedMember);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter23 : struct.extData.entrySet())
          {
            oprot.writeString(_iter23.getKey());
            oprot.writeString(_iter23.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryRankingRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(9);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.rankingId = iprot.readI64();
        struct.setRankingIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.rankingCount = iprot.readI64();
        struct.setRankingCountIsSet(true);
      }
      if (incoming.get(3)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
      if (incoming.get(4)) {
        struct.dateStr = iprot.readString();
        struct.setDateStrIsSet(true);
      }
      if (incoming.get(5)) {
        struct.findSrcMember = iprot.readString();
        struct.setFindSrcMemberIsSet(true);
      }
      if (incoming.get(6)) {
        struct.rankType = iprot.readString();
        struct.setRankTypeIsSet(true);
      }
      if (incoming.get(7)) {
        struct.pointedMember = iprot.readString();
        struct.setPointedMemberIsSet(true);
      }
      if (incoming.get(8)) {
        {
          org.apache.thrift.protocol.TMap _map24 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map24.size);
          String _key25;
          String _val26;
          for (int _i27 = 0; _i27 < _map24.size; ++_i27)
          {
            _key25 = iprot.readString();
            _val26 = iprot.readString();
            struct.extData.put(_key25, _val26);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

