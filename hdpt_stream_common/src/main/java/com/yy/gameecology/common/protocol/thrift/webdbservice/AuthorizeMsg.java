/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class AuthorizeMsg implements org.apache.thrift.TBase<AuthorizeMsg, AuthorizeMsg._Fields>, java.io.Serializable, Cloneable, Comparable<AuthorizeMsg> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("AuthorizeMsg");

  private static final org.apache.thrift.protocol.TField AUTH_USER_FIELD_DESC = new org.apache.thrift.protocol.TField("AuthUser", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField AUTH_KEY_FIELD_DESC = new org.apache.thrift.protocol.TField("AuthKey", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField KEY_VALUE_FIELD_DESC = new org.apache.thrift.protocol.TField("keyValue", org.apache.thrift.protocol.TType.MAP, (short)3);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new AuthorizeMsgStandardSchemeFactory());
    schemes.put(TupleScheme.class, new AuthorizeMsgTupleSchemeFactory());
  }

  public String AuthUser; // required
  public String AuthKey; // required
  public Map<String,String> keyValue; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    AUTH_USER((short)1, "AuthUser"),
    AUTH_KEY((short)2, "AuthKey"),
    KEY_VALUE((short)3, "keyValue");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // AUTH_USER
          return AUTH_USER;
        case 2: // AUTH_KEY
          return AUTH_KEY;
        case 3: // KEY_VALUE
          return KEY_VALUE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.AUTH_USER, new org.apache.thrift.meta_data.FieldMetaData("AuthUser", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.AUTH_KEY, new org.apache.thrift.meta_data.FieldMetaData("AuthKey", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.KEY_VALUE, new org.apache.thrift.meta_data.FieldMetaData("keyValue", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(AuthorizeMsg.class, metaDataMap);
  }

  public AuthorizeMsg() {
    this.AuthUser = "";

    this.AuthKey = "";

  }

  public AuthorizeMsg(
    String AuthUser,
    String AuthKey,
    Map<String,String> keyValue)
  {
    this();
    this.AuthUser = AuthUser;
    this.AuthKey = AuthKey;
    this.keyValue = keyValue;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public AuthorizeMsg(AuthorizeMsg other) {
    if (other.isSetAuthUser()) {
      this.AuthUser = other.AuthUser;
    }
    if (other.isSetAuthKey()) {
      this.AuthKey = other.AuthKey;
    }
    if (other.isSetKeyValue()) {
      Map<String,String> __this__keyValue = new HashMap<String,String>(other.keyValue);
      this.keyValue = __this__keyValue;
    }
  }

  public AuthorizeMsg deepCopy() {
    return new AuthorizeMsg(this);
  }

  @Override
  public void clear() {
    this.AuthUser = "";

    this.AuthKey = "";

    this.keyValue = null;
  }

  public String getAuthUser() {
    return this.AuthUser;
  }

  public AuthorizeMsg setAuthUser(String AuthUser) {
    this.AuthUser = AuthUser;
    return this;
  }

  public void unsetAuthUser() {
    this.AuthUser = null;
  }

  /** Returns true if field AuthUser is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthUser() {
    return this.AuthUser != null;
  }

  public void setAuthUserIsSet(boolean value) {
    if (!value) {
      this.AuthUser = null;
    }
  }

  public String getAuthKey() {
    return this.AuthKey;
  }

  public AuthorizeMsg setAuthKey(String AuthKey) {
    this.AuthKey = AuthKey;
    return this;
  }

  public void unsetAuthKey() {
    this.AuthKey = null;
  }

  /** Returns true if field AuthKey is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthKey() {
    return this.AuthKey != null;
  }

  public void setAuthKeyIsSet(boolean value) {
    if (!value) {
      this.AuthKey = null;
    }
  }

  public int getKeyValueSize() {
    return (this.keyValue == null) ? 0 : this.keyValue.size();
  }

  public void putToKeyValue(String key, String val) {
    if (this.keyValue == null) {
      this.keyValue = new HashMap<String,String>();
    }
    this.keyValue.put(key, val);
  }

  public Map<String,String> getKeyValue() {
    return this.keyValue;
  }

  public AuthorizeMsg setKeyValue(Map<String,String> keyValue) {
    this.keyValue = keyValue;
    return this;
  }

  public void unsetKeyValue() {
    this.keyValue = null;
  }

  /** Returns true if field keyValue is set (has been assigned a value) and false otherwise */
  public boolean isSetKeyValue() {
    return this.keyValue != null;
  }

  public void setKeyValueIsSet(boolean value) {
    if (!value) {
      this.keyValue = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case AUTH_USER:
      if (value == null) {
        unsetAuthUser();
      } else {
        setAuthUser((String)value);
      }
      break;

    case AUTH_KEY:
      if (value == null) {
        unsetAuthKey();
      } else {
        setAuthKey((String)value);
      }
      break;

    case KEY_VALUE:
      if (value == null) {
        unsetKeyValue();
      } else {
        setKeyValue((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case AUTH_USER:
      return getAuthUser();

    case AUTH_KEY:
      return getAuthKey();

    case KEY_VALUE:
      return getKeyValue();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case AUTH_USER:
      return isSetAuthUser();
    case AUTH_KEY:
      return isSetAuthKey();
    case KEY_VALUE:
      return isSetKeyValue();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof AuthorizeMsg)
      return this.equals((AuthorizeMsg)that);
    return false;
  }

  public boolean equals(AuthorizeMsg that) {
    if (that == null)
      return false;

    boolean this_present_AuthUser = true && this.isSetAuthUser();
    boolean that_present_AuthUser = true && that.isSetAuthUser();
    if (this_present_AuthUser || that_present_AuthUser) {
      if (!(this_present_AuthUser && that_present_AuthUser))
        return false;
      if (!this.AuthUser.equals(that.AuthUser))
        return false;
    }

    boolean this_present_AuthKey = true && this.isSetAuthKey();
    boolean that_present_AuthKey = true && that.isSetAuthKey();
    if (this_present_AuthKey || that_present_AuthKey) {
      if (!(this_present_AuthKey && that_present_AuthKey))
        return false;
      if (!this.AuthKey.equals(that.AuthKey))
        return false;
    }

    boolean this_present_keyValue = true && this.isSetKeyValue();
    boolean that_present_keyValue = true && that.isSetKeyValue();
    if (this_present_keyValue || that_present_keyValue) {
      if (!(this_present_keyValue && that_present_keyValue))
        return false;
      if (!this.keyValue.equals(that.keyValue))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_AuthUser = true && (isSetAuthUser());
    list.add(present_AuthUser);
    if (present_AuthUser)
      list.add(AuthUser);

    boolean present_AuthKey = true && (isSetAuthKey());
    list.add(present_AuthKey);
    if (present_AuthKey)
      list.add(AuthKey);

    boolean present_keyValue = true && (isSetKeyValue());
    list.add(present_keyValue);
    if (present_keyValue)
      list.add(keyValue);

    return list.hashCode();
  }

  @Override
  public int compareTo(AuthorizeMsg other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAuthUser()).compareTo(other.isSetAuthUser());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthUser()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.AuthUser, other.AuthUser);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAuthKey()).compareTo(other.isSetAuthKey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthKey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.AuthKey, other.AuthKey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetKeyValue()).compareTo(other.isSetKeyValue());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetKeyValue()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.keyValue, other.keyValue);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("AuthorizeMsg(");
    boolean first = true;

    sb.append("AuthUser:");
    if (this.AuthUser == null) {
      sb.append("null");
    } else {
      sb.append(this.AuthUser);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("AuthKey:");
    if (this.AuthKey == null) {
      sb.append("null");
    } else {
      sb.append(this.AuthKey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("keyValue:");
    if (this.keyValue == null) {
      sb.append("null");
    } else {
      sb.append(this.keyValue);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class AuthorizeMsgStandardSchemeFactory implements SchemeFactory {
    public AuthorizeMsgStandardScheme getScheme() {
      return new AuthorizeMsgStandardScheme();
    }
  }

  private static class AuthorizeMsgStandardScheme extends StandardScheme<AuthorizeMsg> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, AuthorizeMsg struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // AUTH_USER
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.AuthUser = iprot.readString();
              struct.setAuthUserIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // AUTH_KEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.AuthKey = iprot.readString();
              struct.setAuthKeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // KEY_VALUE
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map0 = iprot.readMapBegin();
                struct.keyValue = new HashMap<String,String>(2*_map0.size);
                String _key1;
                String _val2;
                for (int _i3 = 0; _i3 < _map0.size; ++_i3)
                {
                  _key1 = iprot.readString();
                  _val2 = iprot.readString();
                  struct.keyValue.put(_key1, _val2);
                }
                iprot.readMapEnd();
              }
              struct.setKeyValueIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, AuthorizeMsg struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.AuthUser != null) {
        oprot.writeFieldBegin(AUTH_USER_FIELD_DESC);
        oprot.writeString(struct.AuthUser);
        oprot.writeFieldEnd();
      }
      if (struct.AuthKey != null) {
        oprot.writeFieldBegin(AUTH_KEY_FIELD_DESC);
        oprot.writeString(struct.AuthKey);
        oprot.writeFieldEnd();
      }
      if (struct.keyValue != null) {
        oprot.writeFieldBegin(KEY_VALUE_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.keyValue.size()));
          for (Map.Entry<String, String> _iter4 : struct.keyValue.entrySet())
          {
            oprot.writeString(_iter4.getKey());
            oprot.writeString(_iter4.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class AuthorizeMsgTupleSchemeFactory implements SchemeFactory {
    public AuthorizeMsgTupleScheme getScheme() {
      return new AuthorizeMsgTupleScheme();
    }
  }

  private static class AuthorizeMsgTupleScheme extends TupleScheme<AuthorizeMsg> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, AuthorizeMsg struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAuthUser()) {
        optionals.set(0);
      }
      if (struct.isSetAuthKey()) {
        optionals.set(1);
      }
      if (struct.isSetKeyValue()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetAuthUser()) {
        oprot.writeString(struct.AuthUser);
      }
      if (struct.isSetAuthKey()) {
        oprot.writeString(struct.AuthKey);
      }
      if (struct.isSetKeyValue()) {
        {
          oprot.writeI32(struct.keyValue.size());
          for (Map.Entry<String, String> _iter5 : struct.keyValue.entrySet())
          {
            oprot.writeString(_iter5.getKey());
            oprot.writeString(_iter5.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, AuthorizeMsg struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.AuthUser = iprot.readString();
        struct.setAuthUserIsSet(true);
      }
      if (incoming.get(1)) {
        struct.AuthKey = iprot.readString();
        struct.setAuthKeyIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map6 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.keyValue = new HashMap<String,String>(2*_map6.size);
          String _key7;
          String _val8;
          for (int _i9 = 0; _i9 < _map6.size; ++_i9)
          {
            _key7 = iprot.readString();
            _val8 = iprot.readString();
            struct.keyValue.put(_key7, _val8);
          }
        }
        struct.setKeyValueIsSet(true);
      }
    }
  }

}

