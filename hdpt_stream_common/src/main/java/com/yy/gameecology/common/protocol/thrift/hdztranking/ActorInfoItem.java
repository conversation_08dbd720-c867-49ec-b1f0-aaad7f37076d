/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class ActorInfoItem implements org.apache.thrift.TBase<ActorInfoItem, ActorInfoItem._Fields>, java.io.Serializable, Cloneable, Comparable<ActorInfoItem> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ActorInfoItem");

  private static final org.apache.thrift.protocol.TField RANKING_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rankingId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField PHASE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("phaseId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField ACTOR_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actorId", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("score", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField RANK_FIELD_DESC = new org.apache.thrift.protocol.TField("rank", org.apache.thrift.protocol.TType.I64, (short)6);
  private static final org.apache.thrift.protocol.TField PRE_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("preScore", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField DATE_STR_FIELD_DESC = new org.apache.thrift.protocol.TField("dateStr", org.apache.thrift.protocol.TType.STRING, (short)8);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ActorInfoItemStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ActorInfoItemTupleSchemeFactory());
  }

  public long rankingId; // required
  public long phaseId; // required
  public String actorId; // required
  public int status; // required
  public long score; // required
  public long rank; // required
  public long preScore; // required
  public String dateStr; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RANKING_ID((short)1, "rankingId"),
    PHASE_ID((short)2, "phaseId"),
    ACTOR_ID((short)3, "actorId"),
    STATUS((short)4, "status"),
    SCORE((short)5, "score"),
    RANK((short)6, "rank"),
    PRE_SCORE((short)7, "preScore"),
    DATE_STR((short)8, "dateStr"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RANKING_ID
          return RANKING_ID;
        case 2: // PHASE_ID
          return PHASE_ID;
        case 3: // ACTOR_ID
          return ACTOR_ID;
        case 4: // STATUS
          return STATUS;
        case 5: // SCORE
          return SCORE;
        case 6: // RANK
          return RANK;
        case 7: // PRE_SCORE
          return PRE_SCORE;
        case 8: // DATE_STR
          return DATE_STR;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RANKINGID_ISSET_ID = 0;
  private static final int __PHASEID_ISSET_ID = 1;
  private static final int __STATUS_ISSET_ID = 2;
  private static final int __SCORE_ISSET_ID = 3;
  private static final int __RANK_ISSET_ID = 4;
  private static final int __PRESCORE_ISSET_ID = 5;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RANKING_ID, new org.apache.thrift.meta_data.FieldMetaData("rankingId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PHASE_ID, new org.apache.thrift.meta_data.FieldMetaData("phaseId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.ACTOR_ID, new org.apache.thrift.meta_data.FieldMetaData("actorId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SCORE, new org.apache.thrift.meta_data.FieldMetaData("score", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANK, new org.apache.thrift.meta_data.FieldMetaData("rank", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.PRE_SCORE, new org.apache.thrift.meta_data.FieldMetaData("preScore", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.DATE_STR, new org.apache.thrift.meta_data.FieldMetaData("dateStr", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ActorInfoItem.class, metaDataMap);
  }

  public ActorInfoItem() {
  }

  public ActorInfoItem(
    long rankingId,
    long phaseId,
    String actorId,
    int status,
    long score,
    long rank,
    long preScore,
    String dateStr,
    Map<String,String> extData)
  {
    this();
    this.rankingId = rankingId;
    setRankingIdIsSet(true);
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    this.actorId = actorId;
    this.status = status;
    setStatusIsSet(true);
    this.score = score;
    setScoreIsSet(true);
    this.rank = rank;
    setRankIsSet(true);
    this.preScore = preScore;
    setPreScoreIsSet(true);
    this.dateStr = dateStr;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ActorInfoItem(ActorInfoItem other) {
    __isset_bitfield = other.__isset_bitfield;
    this.rankingId = other.rankingId;
    this.phaseId = other.phaseId;
    if (other.isSetActorId()) {
      this.actorId = other.actorId;
    }
    this.status = other.status;
    this.score = other.score;
    this.rank = other.rank;
    this.preScore = other.preScore;
    if (other.isSetDateStr()) {
      this.dateStr = other.dateStr;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public ActorInfoItem deepCopy() {
    return new ActorInfoItem(this);
  }

  @Override
  public void clear() {
    setRankingIdIsSet(false);
    this.rankingId = 0;
    setPhaseIdIsSet(false);
    this.phaseId = 0;
    this.actorId = null;
    setStatusIsSet(false);
    this.status = 0;
    setScoreIsSet(false);
    this.score = 0;
    setRankIsSet(false);
    this.rank = 0;
    setPreScoreIsSet(false);
    this.preScore = 0;
    this.dateStr = null;
    this.extData = null;
  }

  public long getRankingId() {
    return this.rankingId;
  }

  public ActorInfoItem setRankingId(long rankingId) {
    this.rankingId = rankingId;
    setRankingIdIsSet(true);
    return this;
  }

  public void unsetRankingId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKINGID_ISSET_ID);
  }

  /** Returns true if field rankingId is set (has been assigned a value) and false otherwise */
  public boolean isSetRankingId() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKINGID_ISSET_ID);
  }

  public void setRankingIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKINGID_ISSET_ID, value);
  }

  public long getPhaseId() {
    return this.phaseId;
  }

  public ActorInfoItem setPhaseId(long phaseId) {
    this.phaseId = phaseId;
    setPhaseIdIsSet(true);
    return this;
  }

  public void unsetPhaseId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  /** Returns true if field phaseId is set (has been assigned a value) and false otherwise */
  public boolean isSetPhaseId() {
    return EncodingUtils.testBit(__isset_bitfield, __PHASEID_ISSET_ID);
  }

  public void setPhaseIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PHASEID_ISSET_ID, value);
  }

  public String getActorId() {
    return this.actorId;
  }

  public ActorInfoItem setActorId(String actorId) {
    this.actorId = actorId;
    return this;
  }

  public void unsetActorId() {
    this.actorId = null;
  }

  /** Returns true if field actorId is set (has been assigned a value) and false otherwise */
  public boolean isSetActorId() {
    return this.actorId != null;
  }

  public void setActorIdIsSet(boolean value) {
    if (!value) {
      this.actorId = null;
    }
  }

  public int getStatus() {
    return this.status;
  }

  public ActorInfoItem setStatus(int status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  public long getScore() {
    return this.score;
  }

  public ActorInfoItem setScore(long score) {
    this.score = score;
    setScoreIsSet(true);
    return this;
  }

  public void unsetScore() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  /** Returns true if field score is set (has been assigned a value) and false otherwise */
  public boolean isSetScore() {
    return EncodingUtils.testBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  public void setScoreIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SCORE_ISSET_ID, value);
  }

  public long getRank() {
    return this.rank;
  }

  public ActorInfoItem setRank(long rank) {
    this.rank = rank;
    setRankIsSet(true);
    return this;
  }

  public void unsetRank() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANK_ISSET_ID);
  }

  /** Returns true if field rank is set (has been assigned a value) and false otherwise */
  public boolean isSetRank() {
    return EncodingUtils.testBit(__isset_bitfield, __RANK_ISSET_ID);
  }

  public void setRankIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANK_ISSET_ID, value);
  }

  public long getPreScore() {
    return this.preScore;
  }

  public ActorInfoItem setPreScore(long preScore) {
    this.preScore = preScore;
    setPreScoreIsSet(true);
    return this;
  }

  public void unsetPreScore() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __PRESCORE_ISSET_ID);
  }

  /** Returns true if field preScore is set (has been assigned a value) and false otherwise */
  public boolean isSetPreScore() {
    return EncodingUtils.testBit(__isset_bitfield, __PRESCORE_ISSET_ID);
  }

  public void setPreScoreIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __PRESCORE_ISSET_ID, value);
  }

  public String getDateStr() {
    return this.dateStr;
  }

  public ActorInfoItem setDateStr(String dateStr) {
    this.dateStr = dateStr;
    return this;
  }

  public void unsetDateStr() {
    this.dateStr = null;
  }

  /** Returns true if field dateStr is set (has been assigned a value) and false otherwise */
  public boolean isSetDateStr() {
    return this.dateStr != null;
  }

  public void setDateStrIsSet(boolean value) {
    if (!value) {
      this.dateStr = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public ActorInfoItem setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RANKING_ID:
      if (value == null) {
        unsetRankingId();
      } else {
        setRankingId((Long)value);
      }
      break;

    case PHASE_ID:
      if (value == null) {
        unsetPhaseId();
      } else {
        setPhaseId((Long)value);
      }
      break;

    case ACTOR_ID:
      if (value == null) {
        unsetActorId();
      } else {
        setActorId((String)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Integer)value);
      }
      break;

    case SCORE:
      if (value == null) {
        unsetScore();
      } else {
        setScore((Long)value);
      }
      break;

    case RANK:
      if (value == null) {
        unsetRank();
      } else {
        setRank((Long)value);
      }
      break;

    case PRE_SCORE:
      if (value == null) {
        unsetPreScore();
      } else {
        setPreScore((Long)value);
      }
      break;

    case DATE_STR:
      if (value == null) {
        unsetDateStr();
      } else {
        setDateStr((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RANKING_ID:
      return getRankingId();

    case PHASE_ID:
      return getPhaseId();

    case ACTOR_ID:
      return getActorId();

    case STATUS:
      return getStatus();

    case SCORE:
      return getScore();

    case RANK:
      return getRank();

    case PRE_SCORE:
      return getPreScore();

    case DATE_STR:
      return getDateStr();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RANKING_ID:
      return isSetRankingId();
    case PHASE_ID:
      return isSetPhaseId();
    case ACTOR_ID:
      return isSetActorId();
    case STATUS:
      return isSetStatus();
    case SCORE:
      return isSetScore();
    case RANK:
      return isSetRank();
    case PRE_SCORE:
      return isSetPreScore();
    case DATE_STR:
      return isSetDateStr();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ActorInfoItem)
      return this.equals((ActorInfoItem)that);
    return false;
  }

  public boolean equals(ActorInfoItem that) {
    if (that == null)
      return false;

    boolean this_present_rankingId = true;
    boolean that_present_rankingId = true;
    if (this_present_rankingId || that_present_rankingId) {
      if (!(this_present_rankingId && that_present_rankingId))
        return false;
      if (this.rankingId != that.rankingId)
        return false;
    }

    boolean this_present_phaseId = true;
    boolean that_present_phaseId = true;
    if (this_present_phaseId || that_present_phaseId) {
      if (!(this_present_phaseId && that_present_phaseId))
        return false;
      if (this.phaseId != that.phaseId)
        return false;
    }

    boolean this_present_actorId = true && this.isSetActorId();
    boolean that_present_actorId = true && that.isSetActorId();
    if (this_present_actorId || that_present_actorId) {
      if (!(this_present_actorId && that_present_actorId))
        return false;
      if (!this.actorId.equals(that.actorId))
        return false;
    }

    boolean this_present_status = true;
    boolean that_present_status = true;
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_score = true;
    boolean that_present_score = true;
    if (this_present_score || that_present_score) {
      if (!(this_present_score && that_present_score))
        return false;
      if (this.score != that.score)
        return false;
    }

    boolean this_present_rank = true;
    boolean that_present_rank = true;
    if (this_present_rank || that_present_rank) {
      if (!(this_present_rank && that_present_rank))
        return false;
      if (this.rank != that.rank)
        return false;
    }

    boolean this_present_preScore = true;
    boolean that_present_preScore = true;
    if (this_present_preScore || that_present_preScore) {
      if (!(this_present_preScore && that_present_preScore))
        return false;
      if (this.preScore != that.preScore)
        return false;
    }

    boolean this_present_dateStr = true && this.isSetDateStr();
    boolean that_present_dateStr = true && that.isSetDateStr();
    if (this_present_dateStr || that_present_dateStr) {
      if (!(this_present_dateStr && that_present_dateStr))
        return false;
      if (!this.dateStr.equals(that.dateStr))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_rankingId = true;
    list.add(present_rankingId);
    if (present_rankingId)
      list.add(rankingId);

    boolean present_phaseId = true;
    list.add(present_phaseId);
    if (present_phaseId)
      list.add(phaseId);

    boolean present_actorId = true && (isSetActorId());
    list.add(present_actorId);
    if (present_actorId)
      list.add(actorId);

    boolean present_status = true;
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_score = true;
    list.add(present_score);
    if (present_score)
      list.add(score);

    boolean present_rank = true;
    list.add(present_rank);
    if (present_rank)
      list.add(rank);

    boolean present_preScore = true;
    list.add(present_preScore);
    if (present_preScore)
      list.add(preScore);

    boolean present_dateStr = true && (isSetDateStr());
    list.add(present_dateStr);
    if (present_dateStr)
      list.add(dateStr);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(ActorInfoItem other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRankingId()).compareTo(other.isSetRankingId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankingId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankingId, other.rankingId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhaseId()).compareTo(other.isSetPhaseId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhaseId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phaseId, other.phaseId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetActorId()).compareTo(other.isSetActorId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActorId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actorId, other.actorId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetScore()).compareTo(other.isSetScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.score, other.score);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRank()).compareTo(other.isSetRank());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRank()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rank, other.rank);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPreScore()).compareTo(other.isSetPreScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPreScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.preScore, other.preScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDateStr()).compareTo(other.isSetDateStr());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDateStr()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.dateStr, other.dateStr);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ActorInfoItem(");
    boolean first = true;

    sb.append("rankingId:");
    sb.append(this.rankingId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("phaseId:");
    sb.append(this.phaseId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("actorId:");
    if (this.actorId == null) {
      sb.append("null");
    } else {
      sb.append(this.actorId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("status:");
    sb.append(this.status);
    first = false;
    if (!first) sb.append(", ");
    sb.append("score:");
    sb.append(this.score);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rank:");
    sb.append(this.rank);
    first = false;
    if (!first) sb.append(", ");
    sb.append("preScore:");
    sb.append(this.preScore);
    first = false;
    if (!first) sb.append(", ");
    sb.append("dateStr:");
    if (this.dateStr == null) {
      sb.append("null");
    } else {
      sb.append(this.dateStr);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ActorInfoItemStandardSchemeFactory implements SchemeFactory {
    public ActorInfoItemStandardScheme getScheme() {
      return new ActorInfoItemStandardScheme();
    }
  }

  private static class ActorInfoItemStandardScheme extends StandardScheme<ActorInfoItem> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ActorInfoItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RANKING_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankingId = iprot.readI64();
              struct.setRankingIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // PHASE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.phaseId = iprot.readI64();
              struct.setPhaseIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ACTOR_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.actorId = iprot.readString();
              struct.setActorIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.status = iprot.readI32();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.score = iprot.readI64();
              struct.setScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // RANK
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rank = iprot.readI64();
              struct.setRankIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // PRE_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.preScore = iprot.readI64();
              struct.setPreScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // DATE_STR
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.dateStr = iprot.readString();
              struct.setDateStrIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map194 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map194.size);
                String _key195;
                String _val196;
                for (int _i197 = 0; _i197 < _map194.size; ++_i197)
                {
                  _key195 = iprot.readString();
                  _val196 = iprot.readString();
                  struct.extData.put(_key195, _val196);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ActorInfoItem struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RANKING_ID_FIELD_DESC);
      oprot.writeI64(struct.rankingId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PHASE_ID_FIELD_DESC);
      oprot.writeI64(struct.phaseId);
      oprot.writeFieldEnd();
      if (struct.actorId != null) {
        oprot.writeFieldBegin(ACTOR_ID_FIELD_DESC);
        oprot.writeString(struct.actorId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(STATUS_FIELD_DESC);
      oprot.writeI32(struct.status);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SCORE_FIELD_DESC);
      oprot.writeI64(struct.score);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANK_FIELD_DESC);
      oprot.writeI64(struct.rank);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(PRE_SCORE_FIELD_DESC);
      oprot.writeI64(struct.preScore);
      oprot.writeFieldEnd();
      if (struct.dateStr != null) {
        oprot.writeFieldBegin(DATE_STR_FIELD_DESC);
        oprot.writeString(struct.dateStr);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter198 : struct.extData.entrySet())
          {
            oprot.writeString(_iter198.getKey());
            oprot.writeString(_iter198.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ActorInfoItemTupleSchemeFactory implements SchemeFactory {
    public ActorInfoItemTupleScheme getScheme() {
      return new ActorInfoItemTupleScheme();
    }
  }

  private static class ActorInfoItemTupleScheme extends TupleScheme<ActorInfoItem> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ActorInfoItem struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRankingId()) {
        optionals.set(0);
      }
      if (struct.isSetPhaseId()) {
        optionals.set(1);
      }
      if (struct.isSetActorId()) {
        optionals.set(2);
      }
      if (struct.isSetStatus()) {
        optionals.set(3);
      }
      if (struct.isSetScore()) {
        optionals.set(4);
      }
      if (struct.isSetRank()) {
        optionals.set(5);
      }
      if (struct.isSetPreScore()) {
        optionals.set(6);
      }
      if (struct.isSetDateStr()) {
        optionals.set(7);
      }
      if (struct.isSetExtData()) {
        optionals.set(8);
      }
      oprot.writeBitSet(optionals, 9);
      if (struct.isSetRankingId()) {
        oprot.writeI64(struct.rankingId);
      }
      if (struct.isSetPhaseId()) {
        oprot.writeI64(struct.phaseId);
      }
      if (struct.isSetActorId()) {
        oprot.writeString(struct.actorId);
      }
      if (struct.isSetStatus()) {
        oprot.writeI32(struct.status);
      }
      if (struct.isSetScore()) {
        oprot.writeI64(struct.score);
      }
      if (struct.isSetRank()) {
        oprot.writeI64(struct.rank);
      }
      if (struct.isSetPreScore()) {
        oprot.writeI64(struct.preScore);
      }
      if (struct.isSetDateStr()) {
        oprot.writeString(struct.dateStr);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter199 : struct.extData.entrySet())
          {
            oprot.writeString(_iter199.getKey());
            oprot.writeString(_iter199.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ActorInfoItem struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(9);
      if (incoming.get(0)) {
        struct.rankingId = iprot.readI64();
        struct.setRankingIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.phaseId = iprot.readI64();
        struct.setPhaseIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.actorId = iprot.readString();
        struct.setActorIdIsSet(true);
      }
      if (incoming.get(3)) {
        struct.status = iprot.readI32();
        struct.setStatusIsSet(true);
      }
      if (incoming.get(4)) {
        struct.score = iprot.readI64();
        struct.setScoreIsSet(true);
      }
      if (incoming.get(5)) {
        struct.rank = iprot.readI64();
        struct.setRankIsSet(true);
      }
      if (incoming.get(6)) {
        struct.preScore = iprot.readI64();
        struct.setPreScoreIsSet(true);
      }
      if (incoming.get(7)) {
        struct.dateStr = iprot.readString();
        struct.setDateStrIsSet(true);
      }
      if (incoming.get(8)) {
        {
          org.apache.thrift.protocol.TMap _map200 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map200.size);
          String _key201;
          String _val202;
          for (int _i203 = 0; _i203 < _map200.size; ++_i203)
          {
            _key201 = iprot.readString();
            _val202 = iprot.readString();
            struct.extData.put(_key201, _val202);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

