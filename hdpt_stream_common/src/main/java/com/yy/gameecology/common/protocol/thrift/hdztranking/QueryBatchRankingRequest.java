/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class QueryBatchRankingRequest implements org.apache.thrift.TBase<QueryBatchRankingRequest, QueryBatchRankingRequest._Fields>, java.io.Serializable, Cloneable, Comparable<QueryBatchRankingRequest> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryBatchRankingRequest");

  private static final org.apache.thrift.protocol.TField BATCH_QUEST_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("batchQuestData", org.apache.thrift.protocol.TType.MAP, (short)1);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryBatchRankingRequestStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryBatchRankingRequestTupleSchemeFactory());
  }

  public Map<String,QueryRankingRequest> batchQuestData; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    BATCH_QUEST_DATA((short)1, "batchQuestData"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BATCH_QUEST_DATA
          return BATCH_QUEST_DATA;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BATCH_QUEST_DATA, new org.apache.thrift.meta_data.FieldMetaData("batchQuestData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, QueryRankingRequest.class))));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryBatchRankingRequest.class, metaDataMap);
  }

  public QueryBatchRankingRequest() {
  }

  public QueryBatchRankingRequest(
    Map<String,QueryRankingRequest> batchQuestData,
    Map<String,String> extData)
  {
    this();
    this.batchQuestData = batchQuestData;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryBatchRankingRequest(QueryBatchRankingRequest other) {
    if (other.isSetBatchQuestData()) {
      Map<String,QueryRankingRequest> __this__batchQuestData = new HashMap<String,QueryRankingRequest>(other.batchQuestData.size());
      for (Map.Entry<String, QueryRankingRequest> other_element : other.batchQuestData.entrySet()) {

        String other_element_key = other_element.getKey();
        QueryRankingRequest other_element_value = other_element.getValue();

        String __this__batchQuestData_copy_key = other_element_key;

        QueryRankingRequest __this__batchQuestData_copy_value = new QueryRankingRequest(other_element_value);

        __this__batchQuestData.put(__this__batchQuestData_copy_key, __this__batchQuestData_copy_value);
      }
      this.batchQuestData = __this__batchQuestData;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public QueryBatchRankingRequest deepCopy() {
    return new QueryBatchRankingRequest(this);
  }

  @Override
  public void clear() {
    this.batchQuestData = null;
    this.extData = null;
  }

  public int getBatchQuestDataSize() {
    return (this.batchQuestData == null) ? 0 : this.batchQuestData.size();
  }

  public void putToBatchQuestData(String key, QueryRankingRequest val) {
    if (this.batchQuestData == null) {
      this.batchQuestData = new HashMap<String,QueryRankingRequest>();
    }
    this.batchQuestData.put(key, val);
  }

  public Map<String,QueryRankingRequest> getBatchQuestData() {
    return this.batchQuestData;
  }

  public QueryBatchRankingRequest setBatchQuestData(Map<String,QueryRankingRequest> batchQuestData) {
    this.batchQuestData = batchQuestData;
    return this;
  }

  public void unsetBatchQuestData() {
    this.batchQuestData = null;
  }

  /** Returns true if field batchQuestData is set (has been assigned a value) and false otherwise */
  public boolean isSetBatchQuestData() {
    return this.batchQuestData != null;
  }

  public void setBatchQuestDataIsSet(boolean value) {
    if (!value) {
      this.batchQuestData = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public QueryBatchRankingRequest setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case BATCH_QUEST_DATA:
      if (value == null) {
        unsetBatchQuestData();
      } else {
        setBatchQuestData((Map<String,QueryRankingRequest>)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case BATCH_QUEST_DATA:
      return getBatchQuestData();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case BATCH_QUEST_DATA:
      return isSetBatchQuestData();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryBatchRankingRequest)
      return this.equals((QueryBatchRankingRequest)that);
    return false;
  }

  public boolean equals(QueryBatchRankingRequest that) {
    if (that == null)
      return false;

    boolean this_present_batchQuestData = true && this.isSetBatchQuestData();
    boolean that_present_batchQuestData = true && that.isSetBatchQuestData();
    if (this_present_batchQuestData || that_present_batchQuestData) {
      if (!(this_present_batchQuestData && that_present_batchQuestData))
        return false;
      if (!this.batchQuestData.equals(that.batchQuestData))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_batchQuestData = true && (isSetBatchQuestData());
    list.add(present_batchQuestData);
    if (present_batchQuestData)
      list.add(batchQuestData);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryBatchRankingRequest other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetBatchQuestData()).compareTo(other.isSetBatchQuestData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBatchQuestData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.batchQuestData, other.batchQuestData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryBatchRankingRequest(");
    boolean first = true;

    sb.append("batchQuestData:");
    if (this.batchQuestData == null) {
      sb.append("null");
    } else {
      sb.append(this.batchQuestData);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryBatchRankingRequestStandardSchemeFactory implements SchemeFactory {
    public QueryBatchRankingRequestStandardScheme getScheme() {
      return new QueryBatchRankingRequestStandardScheme();
    }
  }

  private static class QueryBatchRankingRequestStandardScheme extends StandardScheme<QueryBatchRankingRequest> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryBatchRankingRequest struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BATCH_QUEST_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map502 = iprot.readMapBegin();
                struct.batchQuestData = new HashMap<String,QueryRankingRequest>(2*_map502.size);
                String _key503;
                QueryRankingRequest _val504;
                for (int _i505 = 0; _i505 < _map502.size; ++_i505)
                {
                  _key503 = iprot.readString();
                  _val504 = new QueryRankingRequest();
                  _val504.read(iprot);
                  struct.batchQuestData.put(_key503, _val504);
                }
                iprot.readMapEnd();
              }
              struct.setBatchQuestDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map506 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map506.size);
                String _key507;
                String _val508;
                for (int _i509 = 0; _i509 < _map506.size; ++_i509)
                {
                  _key507 = iprot.readString();
                  _val508 = iprot.readString();
                  struct.extData.put(_key507, _val508);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryBatchRankingRequest struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.batchQuestData != null) {
        oprot.writeFieldBegin(BATCH_QUEST_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRUCT, struct.batchQuestData.size()));
          for (Map.Entry<String, QueryRankingRequest> _iter510 : struct.batchQuestData.entrySet())
          {
            oprot.writeString(_iter510.getKey());
            _iter510.getValue().write(oprot);
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter511 : struct.extData.entrySet())
          {
            oprot.writeString(_iter511.getKey());
            oprot.writeString(_iter511.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryBatchRankingRequestTupleSchemeFactory implements SchemeFactory {
    public QueryBatchRankingRequestTupleScheme getScheme() {
      return new QueryBatchRankingRequestTupleScheme();
    }
  }

  private static class QueryBatchRankingRequestTupleScheme extends TupleScheme<QueryBatchRankingRequest> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryBatchRankingRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetBatchQuestData()) {
        optionals.set(0);
      }
      if (struct.isSetExtData()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetBatchQuestData()) {
        {
          oprot.writeI32(struct.batchQuestData.size());
          for (Map.Entry<String, QueryRankingRequest> _iter512 : struct.batchQuestData.entrySet())
          {
            oprot.writeString(_iter512.getKey());
            _iter512.getValue().write(oprot);
          }
        }
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter513 : struct.extData.entrySet())
          {
            oprot.writeString(_iter513.getKey());
            oprot.writeString(_iter513.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryBatchRankingRequest struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TMap _map514 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.batchQuestData = new HashMap<String,QueryRankingRequest>(2*_map514.size);
          String _key515;
          QueryRankingRequest _val516;
          for (int _i517 = 0; _i517 < _map514.size; ++_i517)
          {
            _key515 = iprot.readString();
            _val516 = new QueryRankingRequest();
            _val516.read(iprot);
            struct.batchQuestData.put(_key515, _val516);
          }
        }
        struct.setBatchQuestDataIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TMap _map518 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map518.size);
          String _key519;
          String _val520;
          for (int _i521 = 0; _i521 < _map518.size; ++_i521)
          {
            _key519 = iprot.readString();
            _val520 = iprot.readString();
            struct.extData.put(_key519, _val520);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

