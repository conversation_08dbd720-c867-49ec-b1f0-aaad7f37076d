/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class GroupMemberItem implements org.apache.thrift.TBase<GroupMemberItem, GroupMemberItem._Fields>, java.io.Serializable, Cloneable, Comparable<GroupMemberItem> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GroupMemberItem");

  private static final org.apache.thrift.protocol.TField LAST_RANK_FIELD_DESC = new org.apache.thrift.protocol.TField("lastRank", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField MEMBER_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("memberId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField LAST_SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("lastScore", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField RANK_FIELD_DESC = new org.apache.thrift.protocol.TField("rank", org.apache.thrift.protocol.TType.I32, (short)4);
  private static final org.apache.thrift.protocol.TField SCORE_FIELD_DESC = new org.apache.thrift.protocol.TField("score", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new GroupMemberItemStandardSchemeFactory());
    schemes.put(TupleScheme.class, new GroupMemberItemTupleSchemeFactory());
  }

  public int lastRank; // required
  public String memberId; // required
  public long lastScore; // required
  public int rank; // required
  public long score; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    LAST_RANK((short)1, "lastRank"),
    MEMBER_ID((short)2, "memberId"),
    LAST_SCORE((short)3, "lastScore"),
    RANK((short)4, "rank"),
    SCORE((short)5, "score"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // LAST_RANK
          return LAST_RANK;
        case 2: // MEMBER_ID
          return MEMBER_ID;
        case 3: // LAST_SCORE
          return LAST_SCORE;
        case 4: // RANK
          return RANK;
        case 5: // SCORE
          return SCORE;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __LASTRANK_ISSET_ID = 0;
  private static final int __LASTSCORE_ISSET_ID = 1;
  private static final int __RANK_ISSET_ID = 2;
  private static final int __SCORE_ISSET_ID = 3;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.LAST_RANK, new org.apache.thrift.meta_data.FieldMetaData("lastRank", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.MEMBER_ID, new org.apache.thrift.meta_data.FieldMetaData("memberId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.LAST_SCORE, new org.apache.thrift.meta_data.FieldMetaData("lastScore", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANK, new org.apache.thrift.meta_data.FieldMetaData("rank", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.SCORE, new org.apache.thrift.meta_data.FieldMetaData("score", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GroupMemberItem.class, metaDataMap);
  }

  public GroupMemberItem() {
  }

  public GroupMemberItem(
    int lastRank,
    String memberId,
    long lastScore,
    int rank,
    long score,
    Map<String,String> extData)
  {
    this();
    this.lastRank = lastRank;
    setLastRankIsSet(true);
    this.memberId = memberId;
    this.lastScore = lastScore;
    setLastScoreIsSet(true);
    this.rank = rank;
    setRankIsSet(true);
    this.score = score;
    setScoreIsSet(true);
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GroupMemberItem(GroupMemberItem other) {
    __isset_bitfield = other.__isset_bitfield;
    this.lastRank = other.lastRank;
    if (other.isSetMemberId()) {
      this.memberId = other.memberId;
    }
    this.lastScore = other.lastScore;
    this.rank = other.rank;
    this.score = other.score;
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public GroupMemberItem deepCopy() {
    return new GroupMemberItem(this);
  }

  @Override
  public void clear() {
    setLastRankIsSet(false);
    this.lastRank = 0;
    this.memberId = null;
    setLastScoreIsSet(false);
    this.lastScore = 0;
    setRankIsSet(false);
    this.rank = 0;
    setScoreIsSet(false);
    this.score = 0;
    this.extData = null;
  }

  public int getLastRank() {
    return this.lastRank;
  }

  public GroupMemberItem setLastRank(int lastRank) {
    this.lastRank = lastRank;
    setLastRankIsSet(true);
    return this;
  }

  public void unsetLastRank() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LASTRANK_ISSET_ID);
  }

  /** Returns true if field lastRank is set (has been assigned a value) and false otherwise */
  public boolean isSetLastRank() {
    return EncodingUtils.testBit(__isset_bitfield, __LASTRANK_ISSET_ID);
  }

  public void setLastRankIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LASTRANK_ISSET_ID, value);
  }

  public String getMemberId() {
    return this.memberId;
  }

  public GroupMemberItem setMemberId(String memberId) {
    this.memberId = memberId;
    return this;
  }

  public void unsetMemberId() {
    this.memberId = null;
  }

  /** Returns true if field memberId is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberId() {
    return this.memberId != null;
  }

  public void setMemberIdIsSet(boolean value) {
    if (!value) {
      this.memberId = null;
    }
  }

  public long getLastScore() {
    return this.lastScore;
  }

  public GroupMemberItem setLastScore(long lastScore) {
    this.lastScore = lastScore;
    setLastScoreIsSet(true);
    return this;
  }

  public void unsetLastScore() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LASTSCORE_ISSET_ID);
  }

  /** Returns true if field lastScore is set (has been assigned a value) and false otherwise */
  public boolean isSetLastScore() {
    return EncodingUtils.testBit(__isset_bitfield, __LASTSCORE_ISSET_ID);
  }

  public void setLastScoreIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LASTSCORE_ISSET_ID, value);
  }

  public int getRank() {
    return this.rank;
  }

  public GroupMemberItem setRank(int rank) {
    this.rank = rank;
    setRankIsSet(true);
    return this;
  }

  public void unsetRank() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANK_ISSET_ID);
  }

  /** Returns true if field rank is set (has been assigned a value) and false otherwise */
  public boolean isSetRank() {
    return EncodingUtils.testBit(__isset_bitfield, __RANK_ISSET_ID);
  }

  public void setRankIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANK_ISSET_ID, value);
  }

  public long getScore() {
    return this.score;
  }

  public GroupMemberItem setScore(long score) {
    this.score = score;
    setScoreIsSet(true);
    return this;
  }

  public void unsetScore() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  /** Returns true if field score is set (has been assigned a value) and false otherwise */
  public boolean isSetScore() {
    return EncodingUtils.testBit(__isset_bitfield, __SCORE_ISSET_ID);
  }

  public void setScoreIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SCORE_ISSET_ID, value);
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public GroupMemberItem setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case LAST_RANK:
      if (value == null) {
        unsetLastRank();
      } else {
        setLastRank((Integer)value);
      }
      break;

    case MEMBER_ID:
      if (value == null) {
        unsetMemberId();
      } else {
        setMemberId((String)value);
      }
      break;

    case LAST_SCORE:
      if (value == null) {
        unsetLastScore();
      } else {
        setLastScore((Long)value);
      }
      break;

    case RANK:
      if (value == null) {
        unsetRank();
      } else {
        setRank((Integer)value);
      }
      break;

    case SCORE:
      if (value == null) {
        unsetScore();
      } else {
        setScore((Long)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case LAST_RANK:
      return getLastRank();

    case MEMBER_ID:
      return getMemberId();

    case LAST_SCORE:
      return getLastScore();

    case RANK:
      return getRank();

    case SCORE:
      return getScore();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case LAST_RANK:
      return isSetLastRank();
    case MEMBER_ID:
      return isSetMemberId();
    case LAST_SCORE:
      return isSetLastScore();
    case RANK:
      return isSetRank();
    case SCORE:
      return isSetScore();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof GroupMemberItem)
      return this.equals((GroupMemberItem)that);
    return false;
  }

  public boolean equals(GroupMemberItem that) {
    if (that == null)
      return false;

    boolean this_present_lastRank = true;
    boolean that_present_lastRank = true;
    if (this_present_lastRank || that_present_lastRank) {
      if (!(this_present_lastRank && that_present_lastRank))
        return false;
      if (this.lastRank != that.lastRank)
        return false;
    }

    boolean this_present_memberId = true && this.isSetMemberId();
    boolean that_present_memberId = true && that.isSetMemberId();
    if (this_present_memberId || that_present_memberId) {
      if (!(this_present_memberId && that_present_memberId))
        return false;
      if (!this.memberId.equals(that.memberId))
        return false;
    }

    boolean this_present_lastScore = true;
    boolean that_present_lastScore = true;
    if (this_present_lastScore || that_present_lastScore) {
      if (!(this_present_lastScore && that_present_lastScore))
        return false;
      if (this.lastScore != that.lastScore)
        return false;
    }

    boolean this_present_rank = true;
    boolean that_present_rank = true;
    if (this_present_rank || that_present_rank) {
      if (!(this_present_rank && that_present_rank))
        return false;
      if (this.rank != that.rank)
        return false;
    }

    boolean this_present_score = true;
    boolean that_present_score = true;
    if (this_present_score || that_present_score) {
      if (!(this_present_score && that_present_score))
        return false;
      if (this.score != that.score)
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_lastRank = true;
    list.add(present_lastRank);
    if (present_lastRank)
      list.add(lastRank);

    boolean present_memberId = true && (isSetMemberId());
    list.add(present_memberId);
    if (present_memberId)
      list.add(memberId);

    boolean present_lastScore = true;
    list.add(present_lastScore);
    if (present_lastScore)
      list.add(lastScore);

    boolean present_rank = true;
    list.add(present_rank);
    if (present_rank)
      list.add(rank);

    boolean present_score = true;
    list.add(present_score);
    if (present_score)
      list.add(score);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(GroupMemberItem other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetLastRank()).compareTo(other.isSetLastRank());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLastRank()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lastRank, other.lastRank);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMemberId()).compareTo(other.isSetMemberId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberId, other.memberId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLastScore()).compareTo(other.isSetLastScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLastScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.lastScore, other.lastScore);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRank()).compareTo(other.isSetRank());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRank()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rank, other.rank);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetScore()).compareTo(other.isSetScore());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetScore()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.score, other.score);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("GroupMemberItem(");
    boolean first = true;

    sb.append("lastRank:");
    sb.append(this.lastRank);
    first = false;
    if (!first) sb.append(", ");
    sb.append("memberId:");
    if (this.memberId == null) {
      sb.append("null");
    } else {
      sb.append(this.memberId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("lastScore:");
    sb.append(this.lastScore);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rank:");
    sb.append(this.rank);
    first = false;
    if (!first) sb.append(", ");
    sb.append("score:");
    sb.append(this.score);
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GroupMemberItemStandardSchemeFactory implements SchemeFactory {
    public GroupMemberItemStandardScheme getScheme() {
      return new GroupMemberItemStandardScheme();
    }
  }

  private static class GroupMemberItemStandardScheme extends StandardScheme<GroupMemberItem> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GroupMemberItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // LAST_RANK
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.lastRank = iprot.readI32();
              struct.setLastRankIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // MEMBER_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.memberId = iprot.readString();
              struct.setMemberIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // LAST_SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.lastScore = iprot.readI64();
              struct.setLastScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // RANK
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.rank = iprot.readI32();
              struct.setRankIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SCORE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.score = iprot.readI64();
              struct.setScoreIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map570 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map570.size);
                String _key571;
                String _val572;
                for (int _i573 = 0; _i573 < _map570.size; ++_i573)
                {
                  _key571 = iprot.readString();
                  _val572 = iprot.readString();
                  struct.extData.put(_key571, _val572);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GroupMemberItem struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(LAST_RANK_FIELD_DESC);
      oprot.writeI32(struct.lastRank);
      oprot.writeFieldEnd();
      if (struct.memberId != null) {
        oprot.writeFieldBegin(MEMBER_ID_FIELD_DESC);
        oprot.writeString(struct.memberId);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(LAST_SCORE_FIELD_DESC);
      oprot.writeI64(struct.lastScore);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANK_FIELD_DESC);
      oprot.writeI32(struct.rank);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SCORE_FIELD_DESC);
      oprot.writeI64(struct.score);
      oprot.writeFieldEnd();
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter574 : struct.extData.entrySet())
          {
            oprot.writeString(_iter574.getKey());
            oprot.writeString(_iter574.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GroupMemberItemTupleSchemeFactory implements SchemeFactory {
    public GroupMemberItemTupleScheme getScheme() {
      return new GroupMemberItemTupleScheme();
    }
  }

  private static class GroupMemberItemTupleScheme extends TupleScheme<GroupMemberItem> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GroupMemberItem struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetLastRank()) {
        optionals.set(0);
      }
      if (struct.isSetMemberId()) {
        optionals.set(1);
      }
      if (struct.isSetLastScore()) {
        optionals.set(2);
      }
      if (struct.isSetRank()) {
        optionals.set(3);
      }
      if (struct.isSetScore()) {
        optionals.set(4);
      }
      if (struct.isSetExtData()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetLastRank()) {
        oprot.writeI32(struct.lastRank);
      }
      if (struct.isSetMemberId()) {
        oprot.writeString(struct.memberId);
      }
      if (struct.isSetLastScore()) {
        oprot.writeI64(struct.lastScore);
      }
      if (struct.isSetRank()) {
        oprot.writeI32(struct.rank);
      }
      if (struct.isSetScore()) {
        oprot.writeI64(struct.score);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter575 : struct.extData.entrySet())
          {
            oprot.writeString(_iter575.getKey());
            oprot.writeString(_iter575.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GroupMemberItem struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.lastRank = iprot.readI32();
        struct.setLastRankIsSet(true);
      }
      if (incoming.get(1)) {
        struct.memberId = iprot.readString();
        struct.setMemberIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.lastScore = iprot.readI64();
        struct.setLastScoreIsSet(true);
      }
      if (incoming.get(3)) {
        struct.rank = iprot.readI32();
        struct.setRankIsSet(true);
      }
      if (incoming.get(4)) {
        struct.score = iprot.readI64();
        struct.setScoreIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TMap _map576 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map576.size);
          String _key577;
          String _val578;
          for (int _i579 = 0; _i579 < _map576.size; ++_i579)
          {
            _key577 = iprot.readString();
            _val578 = iprot.readString();
            struct.extData.put(_key577, _val578);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

