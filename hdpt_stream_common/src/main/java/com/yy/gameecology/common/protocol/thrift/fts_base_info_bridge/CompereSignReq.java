/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class CompereSignReq implements org.apache.thrift.TBase<CompereSignReq, CompereSignReq._Fields>, java.io.Serializable, Cloneable, Comparable<CompereSignReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CompereSignReq");

  private static final org.apache.thrift.protocol.TField UID_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("uid_list", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.STRING, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new CompereSignReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new CompereSignReqTupleSchemeFactory());
  }

  public List<Long> uid_list; // required
  public String expand; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    UID_LIST((short)1, "uid_list"),
    EXPAND((short)2, "expand");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // UID_LIST
          return UID_LIST;
        case 2: // EXPAND
          return EXPAND;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.UID_LIST, new org.apache.thrift.meta_data.FieldMetaData("uid_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CompereSignReq.class, metaDataMap);
  }

  public CompereSignReq() {
  }

  public CompereSignReq(
    List<Long> uid_list,
    String expand)
  {
    this();
    this.uid_list = uid_list;
    this.expand = expand;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CompereSignReq(CompereSignReq other) {
    if (other.isSetUid_list()) {
      List<Long> __this__uid_list = new ArrayList<Long>(other.uid_list);
      this.uid_list = __this__uid_list;
    }
    if (other.isSetExpand()) {
      this.expand = other.expand;
    }
  }

  public CompereSignReq deepCopy() {
    return new CompereSignReq(this);
  }

  @Override
  public void clear() {
    this.uid_list = null;
    this.expand = null;
  }

  public int getUid_listSize() {
    return (this.uid_list == null) ? 0 : this.uid_list.size();
  }

  public java.util.Iterator<Long> getUid_listIterator() {
    return (this.uid_list == null) ? null : this.uid_list.iterator();
  }

  public void addToUid_list(long elem) {
    if (this.uid_list == null) {
      this.uid_list = new ArrayList<Long>();
    }
    this.uid_list.add(elem);
  }

  public List<Long> getUid_list() {
    return this.uid_list;
  }

  public CompereSignReq setUid_list(List<Long> uid_list) {
    this.uid_list = uid_list;
    return this;
  }

  public void unsetUid_list() {
    this.uid_list = null;
  }

  /** Returns true if field uid_list is set (has been assigned a value) and false otherwise */
  public boolean isSetUid_list() {
    return this.uid_list != null;
  }

  public void setUid_listIsSet(boolean value) {
    if (!value) {
      this.uid_list = null;
    }
  }

  public String getExpand() {
    return this.expand;
  }

  public CompereSignReq setExpand(String expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case UID_LIST:
      if (value == null) {
        unsetUid_list();
      } else {
        setUid_list((List<Long>)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case UID_LIST:
      return getUid_list();

    case EXPAND:
      return getExpand();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case UID_LIST:
      return isSetUid_list();
    case EXPAND:
      return isSetExpand();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof CompereSignReq)
      return this.equals((CompereSignReq)that);
    return false;
  }

  public boolean equals(CompereSignReq that) {
    if (that == null)
      return false;

    boolean this_present_uid_list = true && this.isSetUid_list();
    boolean that_present_uid_list = true && that.isSetUid_list();
    if (this_present_uid_list || that_present_uid_list) {
      if (!(this_present_uid_list && that_present_uid_list))
        return false;
      if (!this.uid_list.equals(that.uid_list))
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_uid_list = true && (isSetUid_list());
    list.add(present_uid_list);
    if (present_uid_list)
      list.add(uid_list);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    return list.hashCode();
  }

  @Override
  public int compareTo(CompereSignReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetUid_list()).compareTo(other.isSetUid_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid_list, other.uid_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("CompereSignReq(");
    boolean first = true;

    sb.append("uid_list:");
    if (this.uid_list == null) {
      sb.append("null");
    } else {
      sb.append(this.uid_list);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CompereSignReqStandardSchemeFactory implements SchemeFactory {
    public CompereSignReqStandardScheme getScheme() {
      return new CompereSignReqStandardScheme();
    }
  }

  private static class CompereSignReqStandardScheme extends StandardScheme<CompereSignReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CompereSignReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // UID_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list18 = iprot.readListBegin();
                struct.uid_list = new ArrayList<Long>(_list18.size);
                long _elem19;
                for (int _i20 = 0; _i20 < _list18.size; ++_i20)
                {
                  _elem19 = iprot.readI64();
                  struct.uid_list.add(_elem19);
                }
                iprot.readListEnd();
              }
              struct.setUid_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.expand = iprot.readString();
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CompereSignReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.uid_list != null) {
        oprot.writeFieldBegin(UID_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.uid_list.size()));
          for (long _iter21 : struct.uid_list)
          {
            oprot.writeI64(_iter21);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        oprot.writeString(struct.expand);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CompereSignReqTupleSchemeFactory implements SchemeFactory {
    public CompereSignReqTupleScheme getScheme() {
      return new CompereSignReqTupleScheme();
    }
  }

  private static class CompereSignReqTupleScheme extends TupleScheme<CompereSignReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CompereSignReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetUid_list()) {
        optionals.set(0);
      }
      if (struct.isSetExpand()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetUid_list()) {
        {
          oprot.writeI32(struct.uid_list.size());
          for (long _iter22 : struct.uid_list)
          {
            oprot.writeI64(_iter22);
          }
        }
      }
      if (struct.isSetExpand()) {
        oprot.writeString(struct.expand);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CompereSignReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list23 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.uid_list = new ArrayList<Long>(_list23.size);
          long _elem24;
          for (int _i25 = 0; _i25 < _list23.size; ++_i25)
          {
            _elem24 = iprot.readI64();
            struct.uid_list.add(_elem24);
          }
        }
        struct.setUid_listIsSet(true);
      }
      if (incoming.get(1)) {
        struct.expand = iprot.readString();
        struct.setExpandIsSet(true);
      }
    }
  }

}

