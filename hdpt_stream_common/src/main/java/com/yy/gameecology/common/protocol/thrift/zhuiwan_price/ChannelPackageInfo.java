/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.zhuiwan_price;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2023-02-22")
public class ChannelPackageInfo implements org.apache.thrift.TBase<ChannelPackageInfo, ChannelPackageInfo._Fields>, java.io.Serializable, Cloneable, Comparable<ChannelPackageInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ChannelPackageInfo");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField CHANNEL_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("channelId", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField CHANNEL_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("channelName", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField DOWNLOAD_URL_FIELD_DESC = new org.apache.thrift.protocol.TField("downloadUrl", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField STATUS_FIELD_DESC = new org.apache.thrift.protocol.TField("status", org.apache.thrift.protocol.TType.I32, (short)5);
  private static final org.apache.thrift.protocol.TField LOGIN_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("loginUid", org.apache.thrift.protocol.TType.I64, (short)6);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ChannelPackageInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ChannelPackageInfoTupleSchemeFactory());
  }

  public long actId; // required
  public String channelId; // required
  public String channelName; // required
  public String downloadUrl; // required
  public int status; // required
  public long loginUid; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    CHANNEL_ID((short)2, "channelId"),
    CHANNEL_NAME((short)3, "channelName"),
    DOWNLOAD_URL((short)4, "downloadUrl"),
    STATUS((short)5, "status"),
    LOGIN_UID((short)6, "loginUid");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // CHANNEL_ID
          return CHANNEL_ID;
        case 3: // CHANNEL_NAME
          return CHANNEL_NAME;
        case 4: // DOWNLOAD_URL
          return DOWNLOAD_URL;
        case 5: // STATUS
          return STATUS;
        case 6: // LOGIN_UID
          return LOGIN_UID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __STATUS_ISSET_ID = 1;
  private static final int __LOGINUID_ISSET_ID = 2;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CHANNEL_ID, new org.apache.thrift.meta_data.FieldMetaData("channelId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CHANNEL_NAME, new org.apache.thrift.meta_data.FieldMetaData("channelName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.DOWNLOAD_URL, new org.apache.thrift.meta_data.FieldMetaData("downloadUrl", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.STATUS, new org.apache.thrift.meta_data.FieldMetaData("status", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.LOGIN_UID, new org.apache.thrift.meta_data.FieldMetaData("loginUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ChannelPackageInfo.class, metaDataMap);
  }

  public ChannelPackageInfo() {
  }

  public ChannelPackageInfo(
    long actId,
    String channelId,
    String channelName,
    String downloadUrl,
    int status,
    long loginUid)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.channelId = channelId;
    this.channelName = channelName;
    this.downloadUrl = downloadUrl;
    this.status = status;
    setStatusIsSet(true);
    this.loginUid = loginUid;
    setLoginUidIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ChannelPackageInfo(ChannelPackageInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    if (other.isSetChannelId()) {
      this.channelId = other.channelId;
    }
    if (other.isSetChannelName()) {
      this.channelName = other.channelName;
    }
    if (other.isSetDownloadUrl()) {
      this.downloadUrl = other.downloadUrl;
    }
    this.status = other.status;
    this.loginUid = other.loginUid;
  }

  public ChannelPackageInfo deepCopy() {
    return new ChannelPackageInfo(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    this.channelId = null;
    this.channelName = null;
    this.downloadUrl = null;
    setStatusIsSet(false);
    this.status = 0;
    setLoginUidIsSet(false);
    this.loginUid = 0;
  }

  public long getActId() {
    return this.actId;
  }

  public ChannelPackageInfo setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public String getChannelId() {
    return this.channelId;
  }

  public ChannelPackageInfo setChannelId(String channelId) {
    this.channelId = channelId;
    return this;
  }

  public void unsetChannelId() {
    this.channelId = null;
  }

  /** Returns true if field channelId is set (has been assigned a value) and false otherwise */
  public boolean isSetChannelId() {
    return this.channelId != null;
  }

  public void setChannelIdIsSet(boolean value) {
    if (!value) {
      this.channelId = null;
    }
  }

  public String getChannelName() {
    return this.channelName;
  }

  public ChannelPackageInfo setChannelName(String channelName) {
    this.channelName = channelName;
    return this;
  }

  public void unsetChannelName() {
    this.channelName = null;
  }

  /** Returns true if field channelName is set (has been assigned a value) and false otherwise */
  public boolean isSetChannelName() {
    return this.channelName != null;
  }

  public void setChannelNameIsSet(boolean value) {
    if (!value) {
      this.channelName = null;
    }
  }

  public String getDownloadUrl() {
    return this.downloadUrl;
  }

  public ChannelPackageInfo setDownloadUrl(String downloadUrl) {
    this.downloadUrl = downloadUrl;
    return this;
  }

  public void unsetDownloadUrl() {
    this.downloadUrl = null;
  }

  /** Returns true if field downloadUrl is set (has been assigned a value) and false otherwise */
  public boolean isSetDownloadUrl() {
    return this.downloadUrl != null;
  }

  public void setDownloadUrlIsSet(boolean value) {
    if (!value) {
      this.downloadUrl = null;
    }
  }

  public int getStatus() {
    return this.status;
  }

  public ChannelPackageInfo setStatus(int status) {
    this.status = status;
    setStatusIsSet(true);
    return this;
  }

  public void unsetStatus() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  /** Returns true if field status is set (has been assigned a value) and false otherwise */
  public boolean isSetStatus() {
    return EncodingUtils.testBit(__isset_bitfield, __STATUS_ISSET_ID);
  }

  public void setStatusIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __STATUS_ISSET_ID, value);
  }

  public long getLoginUid() {
    return this.loginUid;
  }

  public ChannelPackageInfo setLoginUid(long loginUid) {
    this.loginUid = loginUid;
    setLoginUidIsSet(true);
    return this;
  }

  public void unsetLoginUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __LOGINUID_ISSET_ID);
  }

  /** Returns true if field loginUid is set (has been assigned a value) and false otherwise */
  public boolean isSetLoginUid() {
    return EncodingUtils.testBit(__isset_bitfield, __LOGINUID_ISSET_ID);
  }

  public void setLoginUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __LOGINUID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case CHANNEL_ID:
      if (value == null) {
        unsetChannelId();
      } else {
        setChannelId((String)value);
      }
      break;

    case CHANNEL_NAME:
      if (value == null) {
        unsetChannelName();
      } else {
        setChannelName((String)value);
      }
      break;

    case DOWNLOAD_URL:
      if (value == null) {
        unsetDownloadUrl();
      } else {
        setDownloadUrl((String)value);
      }
      break;

    case STATUS:
      if (value == null) {
        unsetStatus();
      } else {
        setStatus((Integer)value);
      }
      break;

    case LOGIN_UID:
      if (value == null) {
        unsetLoginUid();
      } else {
        setLoginUid((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case CHANNEL_ID:
      return getChannelId();

    case CHANNEL_NAME:
      return getChannelName();

    case DOWNLOAD_URL:
      return getDownloadUrl();

    case STATUS:
      return getStatus();

    case LOGIN_UID:
      return getLoginUid();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case CHANNEL_ID:
      return isSetChannelId();
    case CHANNEL_NAME:
      return isSetChannelName();
    case DOWNLOAD_URL:
      return isSetDownloadUrl();
    case STATUS:
      return isSetStatus();
    case LOGIN_UID:
      return isSetLoginUid();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ChannelPackageInfo)
      return this.equals((ChannelPackageInfo)that);
    return false;
  }

  public boolean equals(ChannelPackageInfo that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_channelId = true && this.isSetChannelId();
    boolean that_present_channelId = true && that.isSetChannelId();
    if (this_present_channelId || that_present_channelId) {
      if (!(this_present_channelId && that_present_channelId))
        return false;
      if (!this.channelId.equals(that.channelId))
        return false;
    }

    boolean this_present_channelName = true && this.isSetChannelName();
    boolean that_present_channelName = true && that.isSetChannelName();
    if (this_present_channelName || that_present_channelName) {
      if (!(this_present_channelName && that_present_channelName))
        return false;
      if (!this.channelName.equals(that.channelName))
        return false;
    }

    boolean this_present_downloadUrl = true && this.isSetDownloadUrl();
    boolean that_present_downloadUrl = true && that.isSetDownloadUrl();
    if (this_present_downloadUrl || that_present_downloadUrl) {
      if (!(this_present_downloadUrl && that_present_downloadUrl))
        return false;
      if (!this.downloadUrl.equals(that.downloadUrl))
        return false;
    }

    boolean this_present_status = true;
    boolean that_present_status = true;
    if (this_present_status || that_present_status) {
      if (!(this_present_status && that_present_status))
        return false;
      if (this.status != that.status)
        return false;
    }

    boolean this_present_loginUid = true;
    boolean that_present_loginUid = true;
    if (this_present_loginUid || that_present_loginUid) {
      if (!(this_present_loginUid && that_present_loginUid))
        return false;
      if (this.loginUid != that.loginUid)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_channelId = true && (isSetChannelId());
    list.add(present_channelId);
    if (present_channelId)
      list.add(channelId);

    boolean present_channelName = true && (isSetChannelName());
    list.add(present_channelName);
    if (present_channelName)
      list.add(channelName);

    boolean present_downloadUrl = true && (isSetDownloadUrl());
    list.add(present_downloadUrl);
    if (present_downloadUrl)
      list.add(downloadUrl);

    boolean present_status = true;
    list.add(present_status);
    if (present_status)
      list.add(status);

    boolean present_loginUid = true;
    list.add(present_loginUid);
    if (present_loginUid)
      list.add(loginUid);

    return list.hashCode();
  }

  @Override
  public int compareTo(ChannelPackageInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetChannelId()).compareTo(other.isSetChannelId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetChannelId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.channelId, other.channelId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetChannelName()).compareTo(other.isSetChannelName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetChannelName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.channelName, other.channelName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDownloadUrl()).compareTo(other.isSetDownloadUrl());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDownloadUrl()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.downloadUrl, other.downloadUrl);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetStatus()).compareTo(other.isSetStatus());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetStatus()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.status, other.status);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetLoginUid()).compareTo(other.isSetLoginUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetLoginUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.loginUid, other.loginUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ChannelPackageInfo(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("channelId:");
    if (this.channelId == null) {
      sb.append("null");
    } else {
      sb.append(this.channelId);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("channelName:");
    if (this.channelName == null) {
      sb.append("null");
    } else {
      sb.append(this.channelName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("downloadUrl:");
    if (this.downloadUrl == null) {
      sb.append("null");
    } else {
      sb.append(this.downloadUrl);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("status:");
    sb.append(this.status);
    first = false;
    if (!first) sb.append(", ");
    sb.append("loginUid:");
    sb.append(this.loginUid);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ChannelPackageInfoStandardSchemeFactory implements SchemeFactory {
    public ChannelPackageInfoStandardScheme getScheme() {
      return new ChannelPackageInfoStandardScheme();
    }
  }

  private static class ChannelPackageInfoStandardScheme extends StandardScheme<ChannelPackageInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ChannelPackageInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // CHANNEL_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.channelId = iprot.readString();
              struct.setChannelIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // CHANNEL_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.channelName = iprot.readString();
              struct.setChannelNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // DOWNLOAD_URL
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.downloadUrl = iprot.readString();
              struct.setDownloadUrlIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // STATUS
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.status = iprot.readI32();
              struct.setStatusIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // LOGIN_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.loginUid = iprot.readI64();
              struct.setLoginUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ChannelPackageInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      if (struct.channelId != null) {
        oprot.writeFieldBegin(CHANNEL_ID_FIELD_DESC);
        oprot.writeString(struct.channelId);
        oprot.writeFieldEnd();
      }
      if (struct.channelName != null) {
        oprot.writeFieldBegin(CHANNEL_NAME_FIELD_DESC);
        oprot.writeString(struct.channelName);
        oprot.writeFieldEnd();
      }
      if (struct.downloadUrl != null) {
        oprot.writeFieldBegin(DOWNLOAD_URL_FIELD_DESC);
        oprot.writeString(struct.downloadUrl);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(STATUS_FIELD_DESC);
      oprot.writeI32(struct.status);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(LOGIN_UID_FIELD_DESC);
      oprot.writeI64(struct.loginUid);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ChannelPackageInfoTupleSchemeFactory implements SchemeFactory {
    public ChannelPackageInfoTupleScheme getScheme() {
      return new ChannelPackageInfoTupleScheme();
    }
  }

  private static class ChannelPackageInfoTupleScheme extends TupleScheme<ChannelPackageInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ChannelPackageInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetChannelId()) {
        optionals.set(1);
      }
      if (struct.isSetChannelName()) {
        optionals.set(2);
      }
      if (struct.isSetDownloadUrl()) {
        optionals.set(3);
      }
      if (struct.isSetStatus()) {
        optionals.set(4);
      }
      if (struct.isSetLoginUid()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetChannelId()) {
        oprot.writeString(struct.channelId);
      }
      if (struct.isSetChannelName()) {
        oprot.writeString(struct.channelName);
      }
      if (struct.isSetDownloadUrl()) {
        oprot.writeString(struct.downloadUrl);
      }
      if (struct.isSetStatus()) {
        oprot.writeI32(struct.status);
      }
      if (struct.isSetLoginUid()) {
        oprot.writeI64(struct.loginUid);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ChannelPackageInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.channelId = iprot.readString();
        struct.setChannelIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.channelName = iprot.readString();
        struct.setChannelNameIsSet(true);
      }
      if (incoming.get(3)) {
        struct.downloadUrl = iprot.readString();
        struct.setDownloadUrlIsSet(true);
      }
      if (incoming.get(4)) {
        struct.status = iprot.readI32();
        struct.setStatusIsSet(true);
      }
      if (incoming.get(5)) {
        struct.loginUid = iprot.readI64();
        struct.setLoginUidIsSet(true);
      }
    }
  }

}

