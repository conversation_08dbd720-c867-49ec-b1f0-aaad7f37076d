/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class RankingInfo implements org.apache.thrift.TBase<RankingInfo, RankingInfo._Fields>, java.io.Serializable, Cloneable, Comparable<RankingInfo> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("RankingInfo");

  private static final org.apache.thrift.protocol.TField ACT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("actId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField RANKING_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("rankingId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField RANKING_NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("rankingName", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField CURRENT_PHASE_FIELD_DESC = new org.apache.thrift.protocol.TField("currentPhase", org.apache.thrift.protocol.TType.STRUCT, (short)4);
  private static final org.apache.thrift.protocol.TField PHASES_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("phasesMap", org.apache.thrift.protocol.TType.MAP, (short)5);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)6);
  private static final org.apache.thrift.protocol.TField SHOW_BEGIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("showBeginTime", org.apache.thrift.protocol.TType.I64, (short)7);
  private static final org.apache.thrift.protocol.TField SHOW_END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("showEndTime", org.apache.thrift.protocol.TType.I64, (short)8);
  private static final org.apache.thrift.protocol.TField CAL_BEGIN_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("calBeginTime", org.apache.thrift.protocol.TType.I64, (short)9);
  private static final org.apache.thrift.protocol.TField CAL_END_TIME_FIELD_DESC = new org.apache.thrift.protocol.TField("calEndTime", org.apache.thrift.protocol.TType.I64, (short)10);
  private static final org.apache.thrift.protocol.TField TIME_KEY_FIELD_DESC = new org.apache.thrift.protocol.TField("timeKey", org.apache.thrift.protocol.TType.I64, (short)11);
  private static final org.apache.thrift.protocol.TField TIME_KEY_BEGIN_FIELD_DESC = new org.apache.thrift.protocol.TField("timeKeyBegin", org.apache.thrift.protocol.TType.STRING, (short)12);
  private static final org.apache.thrift.protocol.TField TIME_KEY_END_FIELD_DESC = new org.apache.thrift.protocol.TField("timeKeyEnd", org.apache.thrift.protocol.TType.STRING, (short)13);
  private static final org.apache.thrift.protocol.TField RANKING_EXTJSON_FIELD_DESC = new org.apache.thrift.protocol.TField("rankingExtjson", org.apache.thrift.protocol.TType.STRING, (short)14);
  private static final org.apache.thrift.protocol.TField ROLE_ITEM_CONFIG_FIELD_DESC = new org.apache.thrift.protocol.TField("roleItemConfig", org.apache.thrift.protocol.TType.STRUCT, (short)15);
  private static final org.apache.thrift.protocol.TField RANKING_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("rankingType", org.apache.thrift.protocol.TType.I64, (short)16);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new RankingInfoStandardSchemeFactory());
    schemes.put(TupleScheme.class, new RankingInfoTupleSchemeFactory());
  }

  public long actId; // required
  public long rankingId; // required
  public String rankingName; // required
  public RankingPhaseInfo currentPhase; // required
  public Map<Long,RankingPhaseInfo> phasesMap; // required
  public Map<String,String> extData; // required
  public long showBeginTime; // required
  public long showEndTime; // required
  public long calBeginTime; // required
  public long calEndTime; // required
  public long timeKey; // required
  public String timeKeyBegin; // required
  public String timeKeyEnd; // required
  public String rankingExtjson; // required
  public RoleItemConfig roleItemConfig; // required
  public long rankingType; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    ACT_ID((short)1, "actId"),
    RANKING_ID((short)2, "rankingId"),
    RANKING_NAME((short)3, "rankingName"),
    CURRENT_PHASE((short)4, "currentPhase"),
    PHASES_MAP((short)5, "phasesMap"),
    EXT_DATA((short)6, "extData"),
    SHOW_BEGIN_TIME((short)7, "showBeginTime"),
    SHOW_END_TIME((short)8, "showEndTime"),
    CAL_BEGIN_TIME((short)9, "calBeginTime"),
    CAL_END_TIME((short)10, "calEndTime"),
    TIME_KEY((short)11, "timeKey"),
    TIME_KEY_BEGIN((short)12, "timeKeyBegin"),
    TIME_KEY_END((short)13, "timeKeyEnd"),
    RANKING_EXTJSON((short)14, "rankingExtjson"),
    ROLE_ITEM_CONFIG((short)15, "roleItemConfig"),
    RANKING_TYPE((short)16, "rankingType");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ACT_ID
          return ACT_ID;
        case 2: // RANKING_ID
          return RANKING_ID;
        case 3: // RANKING_NAME
          return RANKING_NAME;
        case 4: // CURRENT_PHASE
          return CURRENT_PHASE;
        case 5: // PHASES_MAP
          return PHASES_MAP;
        case 6: // EXT_DATA
          return EXT_DATA;
        case 7: // SHOW_BEGIN_TIME
          return SHOW_BEGIN_TIME;
        case 8: // SHOW_END_TIME
          return SHOW_END_TIME;
        case 9: // CAL_BEGIN_TIME
          return CAL_BEGIN_TIME;
        case 10: // CAL_END_TIME
          return CAL_END_TIME;
        case 11: // TIME_KEY
          return TIME_KEY;
        case 12: // TIME_KEY_BEGIN
          return TIME_KEY_BEGIN;
        case 13: // TIME_KEY_END
          return TIME_KEY_END;
        case 14: // RANKING_EXTJSON
          return RANKING_EXTJSON;
        case 15: // ROLE_ITEM_CONFIG
          return ROLE_ITEM_CONFIG;
        case 16: // RANKING_TYPE
          return RANKING_TYPE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ACTID_ISSET_ID = 0;
  private static final int __RANKINGID_ISSET_ID = 1;
  private static final int __SHOWBEGINTIME_ISSET_ID = 2;
  private static final int __SHOWENDTIME_ISSET_ID = 3;
  private static final int __CALBEGINTIME_ISSET_ID = 4;
  private static final int __CALENDTIME_ISSET_ID = 5;
  private static final int __TIMEKEY_ISSET_ID = 6;
  private static final int __RANKINGTYPE_ISSET_ID = 7;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ACT_ID, new org.apache.thrift.meta_data.FieldMetaData("actId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANKING_ID, new org.apache.thrift.meta_data.FieldMetaData("rankingId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.RANKING_NAME, new org.apache.thrift.meta_data.FieldMetaData("rankingName", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.CURRENT_PHASE, new org.apache.thrift.meta_data.FieldMetaData("currentPhase", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RankingPhaseInfo.class)));
    tmpMap.put(_Fields.PHASES_MAP, new org.apache.thrift.meta_data.FieldMetaData("phasesMap", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64), 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RankingPhaseInfo.class))));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.SHOW_BEGIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("showBeginTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SHOW_END_TIME, new org.apache.thrift.meta_data.FieldMetaData("showEndTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CAL_BEGIN_TIME, new org.apache.thrift.meta_data.FieldMetaData("calBeginTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CAL_END_TIME, new org.apache.thrift.meta_data.FieldMetaData("calEndTime", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TIME_KEY, new org.apache.thrift.meta_data.FieldMetaData("timeKey", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.TIME_KEY_BEGIN, new org.apache.thrift.meta_data.FieldMetaData("timeKeyBegin", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TIME_KEY_END, new org.apache.thrift.meta_data.FieldMetaData("timeKeyEnd", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RANKING_EXTJSON, new org.apache.thrift.meta_data.FieldMetaData("rankingExtjson", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.ROLE_ITEM_CONFIG, new org.apache.thrift.meta_data.FieldMetaData("roleItemConfig", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, RoleItemConfig.class)));
    tmpMap.put(_Fields.RANKING_TYPE, new org.apache.thrift.meta_data.FieldMetaData("rankingType", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(RankingInfo.class, metaDataMap);
  }

  public RankingInfo() {
  }

  public RankingInfo(
    long actId,
    long rankingId,
    String rankingName,
    RankingPhaseInfo currentPhase,
    Map<Long,RankingPhaseInfo> phasesMap,
    Map<String,String> extData,
    long showBeginTime,
    long showEndTime,
    long calBeginTime,
    long calEndTime,
    long timeKey,
    String timeKeyBegin,
    String timeKeyEnd,
    String rankingExtjson,
    RoleItemConfig roleItemConfig,
    long rankingType)
  {
    this();
    this.actId = actId;
    setActIdIsSet(true);
    this.rankingId = rankingId;
    setRankingIdIsSet(true);
    this.rankingName = rankingName;
    this.currentPhase = currentPhase;
    this.phasesMap = phasesMap;
    this.extData = extData;
    this.showBeginTime = showBeginTime;
    setShowBeginTimeIsSet(true);
    this.showEndTime = showEndTime;
    setShowEndTimeIsSet(true);
    this.calBeginTime = calBeginTime;
    setCalBeginTimeIsSet(true);
    this.calEndTime = calEndTime;
    setCalEndTimeIsSet(true);
    this.timeKey = timeKey;
    setTimeKeyIsSet(true);
    this.timeKeyBegin = timeKeyBegin;
    this.timeKeyEnd = timeKeyEnd;
    this.rankingExtjson = rankingExtjson;
    this.roleItemConfig = roleItemConfig;
    this.rankingType = rankingType;
    setRankingTypeIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public RankingInfo(RankingInfo other) {
    __isset_bitfield = other.__isset_bitfield;
    this.actId = other.actId;
    this.rankingId = other.rankingId;
    if (other.isSetRankingName()) {
      this.rankingName = other.rankingName;
    }
    if (other.isSetCurrentPhase()) {
      this.currentPhase = new RankingPhaseInfo(other.currentPhase);
    }
    if (other.isSetPhasesMap()) {
      Map<Long,RankingPhaseInfo> __this__phasesMap = new HashMap<Long,RankingPhaseInfo>(other.phasesMap.size());
      for (Map.Entry<Long, RankingPhaseInfo> other_element : other.phasesMap.entrySet()) {

        Long other_element_key = other_element.getKey();
        RankingPhaseInfo other_element_value = other_element.getValue();

        Long __this__phasesMap_copy_key = other_element_key;

        RankingPhaseInfo __this__phasesMap_copy_value = new RankingPhaseInfo(other_element_value);

        __this__phasesMap.put(__this__phasesMap_copy_key, __this__phasesMap_copy_value);
      }
      this.phasesMap = __this__phasesMap;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
    this.showBeginTime = other.showBeginTime;
    this.showEndTime = other.showEndTime;
    this.calBeginTime = other.calBeginTime;
    this.calEndTime = other.calEndTime;
    this.timeKey = other.timeKey;
    if (other.isSetTimeKeyBegin()) {
      this.timeKeyBegin = other.timeKeyBegin;
    }
    if (other.isSetTimeKeyEnd()) {
      this.timeKeyEnd = other.timeKeyEnd;
    }
    if (other.isSetRankingExtjson()) {
      this.rankingExtjson = other.rankingExtjson;
    }
    if (other.isSetRoleItemConfig()) {
      this.roleItemConfig = new RoleItemConfig(other.roleItemConfig);
    }
    this.rankingType = other.rankingType;
  }

  public RankingInfo deepCopy() {
    return new RankingInfo(this);
  }

  @Override
  public void clear() {
    setActIdIsSet(false);
    this.actId = 0;
    setRankingIdIsSet(false);
    this.rankingId = 0;
    this.rankingName = null;
    this.currentPhase = null;
    this.phasesMap = null;
    this.extData = null;
    setShowBeginTimeIsSet(false);
    this.showBeginTime = 0;
    setShowEndTimeIsSet(false);
    this.showEndTime = 0;
    setCalBeginTimeIsSet(false);
    this.calBeginTime = 0;
    setCalEndTimeIsSet(false);
    this.calEndTime = 0;
    setTimeKeyIsSet(false);
    this.timeKey = 0;
    this.timeKeyBegin = null;
    this.timeKeyEnd = null;
    this.rankingExtjson = null;
    this.roleItemConfig = null;
    setRankingTypeIsSet(false);
    this.rankingType = 0;
  }

  public long getActId() {
    return this.actId;
  }

  public RankingInfo setActId(long actId) {
    this.actId = actId;
    setActIdIsSet(true);
    return this;
  }

  public void unsetActId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  /** Returns true if field actId is set (has been assigned a value) and false otherwise */
  public boolean isSetActId() {
    return EncodingUtils.testBit(__isset_bitfield, __ACTID_ISSET_ID);
  }

  public void setActIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __ACTID_ISSET_ID, value);
  }

  public long getRankingId() {
    return this.rankingId;
  }

  public RankingInfo setRankingId(long rankingId) {
    this.rankingId = rankingId;
    setRankingIdIsSet(true);
    return this;
  }

  public void unsetRankingId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKINGID_ISSET_ID);
  }

  /** Returns true if field rankingId is set (has been assigned a value) and false otherwise */
  public boolean isSetRankingId() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKINGID_ISSET_ID);
  }

  public void setRankingIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKINGID_ISSET_ID, value);
  }

  public String getRankingName() {
    return this.rankingName;
  }

  public RankingInfo setRankingName(String rankingName) {
    this.rankingName = rankingName;
    return this;
  }

  public void unsetRankingName() {
    this.rankingName = null;
  }

  /** Returns true if field rankingName is set (has been assigned a value) and false otherwise */
  public boolean isSetRankingName() {
    return this.rankingName != null;
  }

  public void setRankingNameIsSet(boolean value) {
    if (!value) {
      this.rankingName = null;
    }
  }

  public RankingPhaseInfo getCurrentPhase() {
    return this.currentPhase;
  }

  public RankingInfo setCurrentPhase(RankingPhaseInfo currentPhase) {
    this.currentPhase = currentPhase;
    return this;
  }

  public void unsetCurrentPhase() {
    this.currentPhase = null;
  }

  /** Returns true if field currentPhase is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrentPhase() {
    return this.currentPhase != null;
  }

  public void setCurrentPhaseIsSet(boolean value) {
    if (!value) {
      this.currentPhase = null;
    }
  }

  public int getPhasesMapSize() {
    return (this.phasesMap == null) ? 0 : this.phasesMap.size();
  }

  public void putToPhasesMap(long key, RankingPhaseInfo val) {
    if (this.phasesMap == null) {
      this.phasesMap = new HashMap<Long,RankingPhaseInfo>();
    }
    this.phasesMap.put(key, val);
  }

  public Map<Long,RankingPhaseInfo> getPhasesMap() {
    return this.phasesMap;
  }

  public RankingInfo setPhasesMap(Map<Long,RankingPhaseInfo> phasesMap) {
    this.phasesMap = phasesMap;
    return this;
  }

  public void unsetPhasesMap() {
    this.phasesMap = null;
  }

  /** Returns true if field phasesMap is set (has been assigned a value) and false otherwise */
  public boolean isSetPhasesMap() {
    return this.phasesMap != null;
  }

  public void setPhasesMapIsSet(boolean value) {
    if (!value) {
      this.phasesMap = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public RankingInfo setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public long getShowBeginTime() {
    return this.showBeginTime;
  }

  public RankingInfo setShowBeginTime(long showBeginTime) {
    this.showBeginTime = showBeginTime;
    setShowBeginTimeIsSet(true);
    return this;
  }

  public void unsetShowBeginTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SHOWBEGINTIME_ISSET_ID);
  }

  /** Returns true if field showBeginTime is set (has been assigned a value) and false otherwise */
  public boolean isSetShowBeginTime() {
    return EncodingUtils.testBit(__isset_bitfield, __SHOWBEGINTIME_ISSET_ID);
  }

  public void setShowBeginTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SHOWBEGINTIME_ISSET_ID, value);
  }

  public long getShowEndTime() {
    return this.showEndTime;
  }

  public RankingInfo setShowEndTime(long showEndTime) {
    this.showEndTime = showEndTime;
    setShowEndTimeIsSet(true);
    return this;
  }

  public void unsetShowEndTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SHOWENDTIME_ISSET_ID);
  }

  /** Returns true if field showEndTime is set (has been assigned a value) and false otherwise */
  public boolean isSetShowEndTime() {
    return EncodingUtils.testBit(__isset_bitfield, __SHOWENDTIME_ISSET_ID);
  }

  public void setShowEndTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SHOWENDTIME_ISSET_ID, value);
  }

  public long getCalBeginTime() {
    return this.calBeginTime;
  }

  public RankingInfo setCalBeginTime(long calBeginTime) {
    this.calBeginTime = calBeginTime;
    setCalBeginTimeIsSet(true);
    return this;
  }

  public void unsetCalBeginTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CALBEGINTIME_ISSET_ID);
  }

  /** Returns true if field calBeginTime is set (has been assigned a value) and false otherwise */
  public boolean isSetCalBeginTime() {
    return EncodingUtils.testBit(__isset_bitfield, __CALBEGINTIME_ISSET_ID);
  }

  public void setCalBeginTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CALBEGINTIME_ISSET_ID, value);
  }

  public long getCalEndTime() {
    return this.calEndTime;
  }

  public RankingInfo setCalEndTime(long calEndTime) {
    this.calEndTime = calEndTime;
    setCalEndTimeIsSet(true);
    return this;
  }

  public void unsetCalEndTime() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CALENDTIME_ISSET_ID);
  }

  /** Returns true if field calEndTime is set (has been assigned a value) and false otherwise */
  public boolean isSetCalEndTime() {
    return EncodingUtils.testBit(__isset_bitfield, __CALENDTIME_ISSET_ID);
  }

  public void setCalEndTimeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CALENDTIME_ISSET_ID, value);
  }

  public long getTimeKey() {
    return this.timeKey;
  }

  public RankingInfo setTimeKey(long timeKey) {
    this.timeKey = timeKey;
    setTimeKeyIsSet(true);
    return this;
  }

  public void unsetTimeKey() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __TIMEKEY_ISSET_ID);
  }

  /** Returns true if field timeKey is set (has been assigned a value) and false otherwise */
  public boolean isSetTimeKey() {
    return EncodingUtils.testBit(__isset_bitfield, __TIMEKEY_ISSET_ID);
  }

  public void setTimeKeyIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __TIMEKEY_ISSET_ID, value);
  }

  public String getTimeKeyBegin() {
    return this.timeKeyBegin;
  }

  public RankingInfo setTimeKeyBegin(String timeKeyBegin) {
    this.timeKeyBegin = timeKeyBegin;
    return this;
  }

  public void unsetTimeKeyBegin() {
    this.timeKeyBegin = null;
  }

  /** Returns true if field timeKeyBegin is set (has been assigned a value) and false otherwise */
  public boolean isSetTimeKeyBegin() {
    return this.timeKeyBegin != null;
  }

  public void setTimeKeyBeginIsSet(boolean value) {
    if (!value) {
      this.timeKeyBegin = null;
    }
  }

  public String getTimeKeyEnd() {
    return this.timeKeyEnd;
  }

  public RankingInfo setTimeKeyEnd(String timeKeyEnd) {
    this.timeKeyEnd = timeKeyEnd;
    return this;
  }

  public void unsetTimeKeyEnd() {
    this.timeKeyEnd = null;
  }

  /** Returns true if field timeKeyEnd is set (has been assigned a value) and false otherwise */
  public boolean isSetTimeKeyEnd() {
    return this.timeKeyEnd != null;
  }

  public void setTimeKeyEndIsSet(boolean value) {
    if (!value) {
      this.timeKeyEnd = null;
    }
  }

  public String getRankingExtjson() {
    return this.rankingExtjson;
  }

  public RankingInfo setRankingExtjson(String rankingExtjson) {
    this.rankingExtjson = rankingExtjson;
    return this;
  }

  public void unsetRankingExtjson() {
    this.rankingExtjson = null;
  }

  /** Returns true if field rankingExtjson is set (has been assigned a value) and false otherwise */
  public boolean isSetRankingExtjson() {
    return this.rankingExtjson != null;
  }

  public void setRankingExtjsonIsSet(boolean value) {
    if (!value) {
      this.rankingExtjson = null;
    }
  }

  public RoleItemConfig getRoleItemConfig() {
    return this.roleItemConfig;
  }

  public RankingInfo setRoleItemConfig(RoleItemConfig roleItemConfig) {
    this.roleItemConfig = roleItemConfig;
    return this;
  }

  public void unsetRoleItemConfig() {
    this.roleItemConfig = null;
  }

  /** Returns true if field roleItemConfig is set (has been assigned a value) and false otherwise */
  public boolean isSetRoleItemConfig() {
    return this.roleItemConfig != null;
  }

  public void setRoleItemConfigIsSet(boolean value) {
    if (!value) {
      this.roleItemConfig = null;
    }
  }

  public long getRankingType() {
    return this.rankingType;
  }

  public RankingInfo setRankingType(long rankingType) {
    this.rankingType = rankingType;
    setRankingTypeIsSet(true);
    return this;
  }

  public void unsetRankingType() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RANKINGTYPE_ISSET_ID);
  }

  /** Returns true if field rankingType is set (has been assigned a value) and false otherwise */
  public boolean isSetRankingType() {
    return EncodingUtils.testBit(__isset_bitfield, __RANKINGTYPE_ISSET_ID);
  }

  public void setRankingTypeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RANKINGTYPE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case ACT_ID:
      if (value == null) {
        unsetActId();
      } else {
        setActId((Long)value);
      }
      break;

    case RANKING_ID:
      if (value == null) {
        unsetRankingId();
      } else {
        setRankingId((Long)value);
      }
      break;

    case RANKING_NAME:
      if (value == null) {
        unsetRankingName();
      } else {
        setRankingName((String)value);
      }
      break;

    case CURRENT_PHASE:
      if (value == null) {
        unsetCurrentPhase();
      } else {
        setCurrentPhase((RankingPhaseInfo)value);
      }
      break;

    case PHASES_MAP:
      if (value == null) {
        unsetPhasesMap();
      } else {
        setPhasesMap((Map<Long,RankingPhaseInfo>)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    case SHOW_BEGIN_TIME:
      if (value == null) {
        unsetShowBeginTime();
      } else {
        setShowBeginTime((Long)value);
      }
      break;

    case SHOW_END_TIME:
      if (value == null) {
        unsetShowEndTime();
      } else {
        setShowEndTime((Long)value);
      }
      break;

    case CAL_BEGIN_TIME:
      if (value == null) {
        unsetCalBeginTime();
      } else {
        setCalBeginTime((Long)value);
      }
      break;

    case CAL_END_TIME:
      if (value == null) {
        unsetCalEndTime();
      } else {
        setCalEndTime((Long)value);
      }
      break;

    case TIME_KEY:
      if (value == null) {
        unsetTimeKey();
      } else {
        setTimeKey((Long)value);
      }
      break;

    case TIME_KEY_BEGIN:
      if (value == null) {
        unsetTimeKeyBegin();
      } else {
        setTimeKeyBegin((String)value);
      }
      break;

    case TIME_KEY_END:
      if (value == null) {
        unsetTimeKeyEnd();
      } else {
        setTimeKeyEnd((String)value);
      }
      break;

    case RANKING_EXTJSON:
      if (value == null) {
        unsetRankingExtjson();
      } else {
        setRankingExtjson((String)value);
      }
      break;

    case ROLE_ITEM_CONFIG:
      if (value == null) {
        unsetRoleItemConfig();
      } else {
        setRoleItemConfig((RoleItemConfig)value);
      }
      break;

    case RANKING_TYPE:
      if (value == null) {
        unsetRankingType();
      } else {
        setRankingType((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case ACT_ID:
      return getActId();

    case RANKING_ID:
      return getRankingId();

    case RANKING_NAME:
      return getRankingName();

    case CURRENT_PHASE:
      return getCurrentPhase();

    case PHASES_MAP:
      return getPhasesMap();

    case EXT_DATA:
      return getExtData();

    case SHOW_BEGIN_TIME:
      return getShowBeginTime();

    case SHOW_END_TIME:
      return getShowEndTime();

    case CAL_BEGIN_TIME:
      return getCalBeginTime();

    case CAL_END_TIME:
      return getCalEndTime();

    case TIME_KEY:
      return getTimeKey();

    case TIME_KEY_BEGIN:
      return getTimeKeyBegin();

    case TIME_KEY_END:
      return getTimeKeyEnd();

    case RANKING_EXTJSON:
      return getRankingExtjson();

    case ROLE_ITEM_CONFIG:
      return getRoleItemConfig();

    case RANKING_TYPE:
      return getRankingType();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case ACT_ID:
      return isSetActId();
    case RANKING_ID:
      return isSetRankingId();
    case RANKING_NAME:
      return isSetRankingName();
    case CURRENT_PHASE:
      return isSetCurrentPhase();
    case PHASES_MAP:
      return isSetPhasesMap();
    case EXT_DATA:
      return isSetExtData();
    case SHOW_BEGIN_TIME:
      return isSetShowBeginTime();
    case SHOW_END_TIME:
      return isSetShowEndTime();
    case CAL_BEGIN_TIME:
      return isSetCalBeginTime();
    case CAL_END_TIME:
      return isSetCalEndTime();
    case TIME_KEY:
      return isSetTimeKey();
    case TIME_KEY_BEGIN:
      return isSetTimeKeyBegin();
    case TIME_KEY_END:
      return isSetTimeKeyEnd();
    case RANKING_EXTJSON:
      return isSetRankingExtjson();
    case ROLE_ITEM_CONFIG:
      return isSetRoleItemConfig();
    case RANKING_TYPE:
      return isSetRankingType();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof RankingInfo)
      return this.equals((RankingInfo)that);
    return false;
  }

  public boolean equals(RankingInfo that) {
    if (that == null)
      return false;

    boolean this_present_actId = true;
    boolean that_present_actId = true;
    if (this_present_actId || that_present_actId) {
      if (!(this_present_actId && that_present_actId))
        return false;
      if (this.actId != that.actId)
        return false;
    }

    boolean this_present_rankingId = true;
    boolean that_present_rankingId = true;
    if (this_present_rankingId || that_present_rankingId) {
      if (!(this_present_rankingId && that_present_rankingId))
        return false;
      if (this.rankingId != that.rankingId)
        return false;
    }

    boolean this_present_rankingName = true && this.isSetRankingName();
    boolean that_present_rankingName = true && that.isSetRankingName();
    if (this_present_rankingName || that_present_rankingName) {
      if (!(this_present_rankingName && that_present_rankingName))
        return false;
      if (!this.rankingName.equals(that.rankingName))
        return false;
    }

    boolean this_present_currentPhase = true && this.isSetCurrentPhase();
    boolean that_present_currentPhase = true && that.isSetCurrentPhase();
    if (this_present_currentPhase || that_present_currentPhase) {
      if (!(this_present_currentPhase && that_present_currentPhase))
        return false;
      if (!this.currentPhase.equals(that.currentPhase))
        return false;
    }

    boolean this_present_phasesMap = true && this.isSetPhasesMap();
    boolean that_present_phasesMap = true && that.isSetPhasesMap();
    if (this_present_phasesMap || that_present_phasesMap) {
      if (!(this_present_phasesMap && that_present_phasesMap))
        return false;
      if (!this.phasesMap.equals(that.phasesMap))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    boolean this_present_showBeginTime = true;
    boolean that_present_showBeginTime = true;
    if (this_present_showBeginTime || that_present_showBeginTime) {
      if (!(this_present_showBeginTime && that_present_showBeginTime))
        return false;
      if (this.showBeginTime != that.showBeginTime)
        return false;
    }

    boolean this_present_showEndTime = true;
    boolean that_present_showEndTime = true;
    if (this_present_showEndTime || that_present_showEndTime) {
      if (!(this_present_showEndTime && that_present_showEndTime))
        return false;
      if (this.showEndTime != that.showEndTime)
        return false;
    }

    boolean this_present_calBeginTime = true;
    boolean that_present_calBeginTime = true;
    if (this_present_calBeginTime || that_present_calBeginTime) {
      if (!(this_present_calBeginTime && that_present_calBeginTime))
        return false;
      if (this.calBeginTime != that.calBeginTime)
        return false;
    }

    boolean this_present_calEndTime = true;
    boolean that_present_calEndTime = true;
    if (this_present_calEndTime || that_present_calEndTime) {
      if (!(this_present_calEndTime && that_present_calEndTime))
        return false;
      if (this.calEndTime != that.calEndTime)
        return false;
    }

    boolean this_present_timeKey = true;
    boolean that_present_timeKey = true;
    if (this_present_timeKey || that_present_timeKey) {
      if (!(this_present_timeKey && that_present_timeKey))
        return false;
      if (this.timeKey != that.timeKey)
        return false;
    }

    boolean this_present_timeKeyBegin = true && this.isSetTimeKeyBegin();
    boolean that_present_timeKeyBegin = true && that.isSetTimeKeyBegin();
    if (this_present_timeKeyBegin || that_present_timeKeyBegin) {
      if (!(this_present_timeKeyBegin && that_present_timeKeyBegin))
        return false;
      if (!this.timeKeyBegin.equals(that.timeKeyBegin))
        return false;
    }

    boolean this_present_timeKeyEnd = true && this.isSetTimeKeyEnd();
    boolean that_present_timeKeyEnd = true && that.isSetTimeKeyEnd();
    if (this_present_timeKeyEnd || that_present_timeKeyEnd) {
      if (!(this_present_timeKeyEnd && that_present_timeKeyEnd))
        return false;
      if (!this.timeKeyEnd.equals(that.timeKeyEnd))
        return false;
    }

    boolean this_present_rankingExtjson = true && this.isSetRankingExtjson();
    boolean that_present_rankingExtjson = true && that.isSetRankingExtjson();
    if (this_present_rankingExtjson || that_present_rankingExtjson) {
      if (!(this_present_rankingExtjson && that_present_rankingExtjson))
        return false;
      if (!this.rankingExtjson.equals(that.rankingExtjson))
        return false;
    }

    boolean this_present_roleItemConfig = true && this.isSetRoleItemConfig();
    boolean that_present_roleItemConfig = true && that.isSetRoleItemConfig();
    if (this_present_roleItemConfig || that_present_roleItemConfig) {
      if (!(this_present_roleItemConfig && that_present_roleItemConfig))
        return false;
      if (!this.roleItemConfig.equals(that.roleItemConfig))
        return false;
    }

    boolean this_present_rankingType = true;
    boolean that_present_rankingType = true;
    if (this_present_rankingType || that_present_rankingType) {
      if (!(this_present_rankingType && that_present_rankingType))
        return false;
      if (this.rankingType != that.rankingType)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_actId = true;
    list.add(present_actId);
    if (present_actId)
      list.add(actId);

    boolean present_rankingId = true;
    list.add(present_rankingId);
    if (present_rankingId)
      list.add(rankingId);

    boolean present_rankingName = true && (isSetRankingName());
    list.add(present_rankingName);
    if (present_rankingName)
      list.add(rankingName);

    boolean present_currentPhase = true && (isSetCurrentPhase());
    list.add(present_currentPhase);
    if (present_currentPhase)
      list.add(currentPhase);

    boolean present_phasesMap = true && (isSetPhasesMap());
    list.add(present_phasesMap);
    if (present_phasesMap)
      list.add(phasesMap);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    boolean present_showBeginTime = true;
    list.add(present_showBeginTime);
    if (present_showBeginTime)
      list.add(showBeginTime);

    boolean present_showEndTime = true;
    list.add(present_showEndTime);
    if (present_showEndTime)
      list.add(showEndTime);

    boolean present_calBeginTime = true;
    list.add(present_calBeginTime);
    if (present_calBeginTime)
      list.add(calBeginTime);

    boolean present_calEndTime = true;
    list.add(present_calEndTime);
    if (present_calEndTime)
      list.add(calEndTime);

    boolean present_timeKey = true;
    list.add(present_timeKey);
    if (present_timeKey)
      list.add(timeKey);

    boolean present_timeKeyBegin = true && (isSetTimeKeyBegin());
    list.add(present_timeKeyBegin);
    if (present_timeKeyBegin)
      list.add(timeKeyBegin);

    boolean present_timeKeyEnd = true && (isSetTimeKeyEnd());
    list.add(present_timeKeyEnd);
    if (present_timeKeyEnd)
      list.add(timeKeyEnd);

    boolean present_rankingExtjson = true && (isSetRankingExtjson());
    list.add(present_rankingExtjson);
    if (present_rankingExtjson)
      list.add(rankingExtjson);

    boolean present_roleItemConfig = true && (isSetRoleItemConfig());
    list.add(present_roleItemConfig);
    if (present_roleItemConfig)
      list.add(roleItemConfig);

    boolean present_rankingType = true;
    list.add(present_rankingType);
    if (present_rankingType)
      list.add(rankingType);

    return list.hashCode();
  }

  @Override
  public int compareTo(RankingInfo other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetActId()).compareTo(other.isSetActId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetActId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.actId, other.actId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankingId()).compareTo(other.isSetRankingId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankingId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankingId, other.rankingId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankingName()).compareTo(other.isSetRankingName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankingName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankingName, other.rankingName);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCurrentPhase()).compareTo(other.isSetCurrentPhase());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrentPhase()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.currentPhase, other.currentPhase);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetPhasesMap()).compareTo(other.isSetPhasesMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPhasesMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.phasesMap, other.phasesMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetShowBeginTime()).compareTo(other.isSetShowBeginTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetShowBeginTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.showBeginTime, other.showBeginTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetShowEndTime()).compareTo(other.isSetShowEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetShowEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.showEndTime, other.showEndTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCalBeginTime()).compareTo(other.isSetCalBeginTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCalBeginTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.calBeginTime, other.calBeginTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCalEndTime()).compareTo(other.isSetCalEndTime());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCalEndTime()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.calEndTime, other.calEndTime);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimeKey()).compareTo(other.isSetTimeKey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimeKey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timeKey, other.timeKey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimeKeyBegin()).compareTo(other.isSetTimeKeyBegin());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimeKeyBegin()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timeKeyBegin, other.timeKeyBegin);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTimeKeyEnd()).compareTo(other.isSetTimeKeyEnd());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTimeKeyEnd()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.timeKeyEnd, other.timeKeyEnd);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankingExtjson()).compareTo(other.isSetRankingExtjson());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankingExtjson()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankingExtjson, other.rankingExtjson);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRoleItemConfig()).compareTo(other.isSetRoleItemConfig());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRoleItemConfig()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.roleItemConfig, other.roleItemConfig);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankingType()).compareTo(other.isSetRankingType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankingType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankingType, other.rankingType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("RankingInfo(");
    boolean first = true;

    sb.append("actId:");
    sb.append(this.actId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankingId:");
    sb.append(this.rankingId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankingName:");
    if (this.rankingName == null) {
      sb.append("null");
    } else {
      sb.append(this.rankingName);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("currentPhase:");
    if (this.currentPhase == null) {
      sb.append("null");
    } else {
      sb.append(this.currentPhase);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("phasesMap:");
    if (this.phasesMap == null) {
      sb.append("null");
    } else {
      sb.append(this.phasesMap);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("showBeginTime:");
    sb.append(this.showBeginTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("showEndTime:");
    sb.append(this.showEndTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("calBeginTime:");
    sb.append(this.calBeginTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("calEndTime:");
    sb.append(this.calEndTime);
    first = false;
    if (!first) sb.append(", ");
    sb.append("timeKey:");
    sb.append(this.timeKey);
    first = false;
    if (!first) sb.append(", ");
    sb.append("timeKeyBegin:");
    if (this.timeKeyBegin == null) {
      sb.append("null");
    } else {
      sb.append(this.timeKeyBegin);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("timeKeyEnd:");
    if (this.timeKeyEnd == null) {
      sb.append("null");
    } else {
      sb.append(this.timeKeyEnd);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankingExtjson:");
    if (this.rankingExtjson == null) {
      sb.append("null");
    } else {
      sb.append(this.rankingExtjson);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("roleItemConfig:");
    if (this.roleItemConfig == null) {
      sb.append("null");
    } else {
      sb.append(this.roleItemConfig);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankingType:");
    sb.append(this.rankingType);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (currentPhase != null) {
      currentPhase.validate();
    }
    if (roleItemConfig != null) {
      roleItemConfig.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class RankingInfoStandardSchemeFactory implements SchemeFactory {
    public RankingInfoStandardScheme getScheme() {
      return new RankingInfoStandardScheme();
    }
  }

  private static class RankingInfoStandardScheme extends StandardScheme<RankingInfo> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, RankingInfo struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ACT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.actId = iprot.readI64();
              struct.setActIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // RANKING_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankingId = iprot.readI64();
              struct.setRankingIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // RANKING_NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.rankingName = iprot.readString();
              struct.setRankingNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // CURRENT_PHASE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.currentPhase = new RankingPhaseInfo();
              struct.currentPhase.read(iprot);
              struct.setCurrentPhaseIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // PHASES_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map136 = iprot.readMapBegin();
                struct.phasesMap = new HashMap<Long,RankingPhaseInfo>(2*_map136.size);
                long _key137;
                RankingPhaseInfo _val138;
                for (int _i139 = 0; _i139 < _map136.size; ++_i139)
                {
                  _key137 = iprot.readI64();
                  _val138 = new RankingPhaseInfo();
                  _val138.read(iprot);
                  struct.phasesMap.put(_key137, _val138);
                }
                iprot.readMapEnd();
              }
              struct.setPhasesMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map140 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map140.size);
                String _key141;
                String _val142;
                for (int _i143 = 0; _i143 < _map140.size; ++_i143)
                {
                  _key141 = iprot.readString();
                  _val142 = iprot.readString();
                  struct.extData.put(_key141, _val142);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // SHOW_BEGIN_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.showBeginTime = iprot.readI64();
              struct.setShowBeginTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 8: // SHOW_END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.showEndTime = iprot.readI64();
              struct.setShowEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 9: // CAL_BEGIN_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.calBeginTime = iprot.readI64();
              struct.setCalBeginTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 10: // CAL_END_TIME
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.calEndTime = iprot.readI64();
              struct.setCalEndTimeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 11: // TIME_KEY
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.timeKey = iprot.readI64();
              struct.setTimeKeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 12: // TIME_KEY_BEGIN
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.timeKeyBegin = iprot.readString();
              struct.setTimeKeyBeginIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 13: // TIME_KEY_END
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.timeKeyEnd = iprot.readString();
              struct.setTimeKeyEndIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 14: // RANKING_EXTJSON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.rankingExtjson = iprot.readString();
              struct.setRankingExtjsonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // ROLE_ITEM_CONFIG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.roleItemConfig = new RoleItemConfig();
              struct.roleItemConfig.read(iprot);
              struct.setRoleItemConfigIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 16: // RANKING_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.rankingType = iprot.readI64();
              struct.setRankingTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, RankingInfo struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(ACT_ID_FIELD_DESC);
      oprot.writeI64(struct.actId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(RANKING_ID_FIELD_DESC);
      oprot.writeI64(struct.rankingId);
      oprot.writeFieldEnd();
      if (struct.rankingName != null) {
        oprot.writeFieldBegin(RANKING_NAME_FIELD_DESC);
        oprot.writeString(struct.rankingName);
        oprot.writeFieldEnd();
      }
      if (struct.currentPhase != null) {
        oprot.writeFieldBegin(CURRENT_PHASE_FIELD_DESC);
        struct.currentPhase.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.phasesMap != null) {
        oprot.writeFieldBegin(PHASES_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRUCT, struct.phasesMap.size()));
          for (Map.Entry<Long, RankingPhaseInfo> _iter144 : struct.phasesMap.entrySet())
          {
            oprot.writeI64(_iter144.getKey());
            _iter144.getValue().write(oprot);
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter145 : struct.extData.entrySet())
          {
            oprot.writeString(_iter145.getKey());
            oprot.writeString(_iter145.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SHOW_BEGIN_TIME_FIELD_DESC);
      oprot.writeI64(struct.showBeginTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SHOW_END_TIME_FIELD_DESC);
      oprot.writeI64(struct.showEndTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CAL_BEGIN_TIME_FIELD_DESC);
      oprot.writeI64(struct.calBeginTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(CAL_END_TIME_FIELD_DESC);
      oprot.writeI64(struct.calEndTime);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(TIME_KEY_FIELD_DESC);
      oprot.writeI64(struct.timeKey);
      oprot.writeFieldEnd();
      if (struct.timeKeyBegin != null) {
        oprot.writeFieldBegin(TIME_KEY_BEGIN_FIELD_DESC);
        oprot.writeString(struct.timeKeyBegin);
        oprot.writeFieldEnd();
      }
      if (struct.timeKeyEnd != null) {
        oprot.writeFieldBegin(TIME_KEY_END_FIELD_DESC);
        oprot.writeString(struct.timeKeyEnd);
        oprot.writeFieldEnd();
      }
      if (struct.rankingExtjson != null) {
        oprot.writeFieldBegin(RANKING_EXTJSON_FIELD_DESC);
        oprot.writeString(struct.rankingExtjson);
        oprot.writeFieldEnd();
      }
      if (struct.roleItemConfig != null) {
        oprot.writeFieldBegin(ROLE_ITEM_CONFIG_FIELD_DESC);
        struct.roleItemConfig.write(oprot);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(RANKING_TYPE_FIELD_DESC);
      oprot.writeI64(struct.rankingType);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class RankingInfoTupleSchemeFactory implements SchemeFactory {
    public RankingInfoTupleScheme getScheme() {
      return new RankingInfoTupleScheme();
    }
  }

  private static class RankingInfoTupleScheme extends TupleScheme<RankingInfo> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, RankingInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetActId()) {
        optionals.set(0);
      }
      if (struct.isSetRankingId()) {
        optionals.set(1);
      }
      if (struct.isSetRankingName()) {
        optionals.set(2);
      }
      if (struct.isSetCurrentPhase()) {
        optionals.set(3);
      }
      if (struct.isSetPhasesMap()) {
        optionals.set(4);
      }
      if (struct.isSetExtData()) {
        optionals.set(5);
      }
      if (struct.isSetShowBeginTime()) {
        optionals.set(6);
      }
      if (struct.isSetShowEndTime()) {
        optionals.set(7);
      }
      if (struct.isSetCalBeginTime()) {
        optionals.set(8);
      }
      if (struct.isSetCalEndTime()) {
        optionals.set(9);
      }
      if (struct.isSetTimeKey()) {
        optionals.set(10);
      }
      if (struct.isSetTimeKeyBegin()) {
        optionals.set(11);
      }
      if (struct.isSetTimeKeyEnd()) {
        optionals.set(12);
      }
      if (struct.isSetRankingExtjson()) {
        optionals.set(13);
      }
      if (struct.isSetRoleItemConfig()) {
        optionals.set(14);
      }
      if (struct.isSetRankingType()) {
        optionals.set(15);
      }
      oprot.writeBitSet(optionals, 16);
      if (struct.isSetActId()) {
        oprot.writeI64(struct.actId);
      }
      if (struct.isSetRankingId()) {
        oprot.writeI64(struct.rankingId);
      }
      if (struct.isSetRankingName()) {
        oprot.writeString(struct.rankingName);
      }
      if (struct.isSetCurrentPhase()) {
        struct.currentPhase.write(oprot);
      }
      if (struct.isSetPhasesMap()) {
        {
          oprot.writeI32(struct.phasesMap.size());
          for (Map.Entry<Long, RankingPhaseInfo> _iter146 : struct.phasesMap.entrySet())
          {
            oprot.writeI64(_iter146.getKey());
            _iter146.getValue().write(oprot);
          }
        }
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter147 : struct.extData.entrySet())
          {
            oprot.writeString(_iter147.getKey());
            oprot.writeString(_iter147.getValue());
          }
        }
      }
      if (struct.isSetShowBeginTime()) {
        oprot.writeI64(struct.showBeginTime);
      }
      if (struct.isSetShowEndTime()) {
        oprot.writeI64(struct.showEndTime);
      }
      if (struct.isSetCalBeginTime()) {
        oprot.writeI64(struct.calBeginTime);
      }
      if (struct.isSetCalEndTime()) {
        oprot.writeI64(struct.calEndTime);
      }
      if (struct.isSetTimeKey()) {
        oprot.writeI64(struct.timeKey);
      }
      if (struct.isSetTimeKeyBegin()) {
        oprot.writeString(struct.timeKeyBegin);
      }
      if (struct.isSetTimeKeyEnd()) {
        oprot.writeString(struct.timeKeyEnd);
      }
      if (struct.isSetRankingExtjson()) {
        oprot.writeString(struct.rankingExtjson);
      }
      if (struct.isSetRoleItemConfig()) {
        struct.roleItemConfig.write(oprot);
      }
      if (struct.isSetRankingType()) {
        oprot.writeI64(struct.rankingType);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, RankingInfo struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(16);
      if (incoming.get(0)) {
        struct.actId = iprot.readI64();
        struct.setActIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.rankingId = iprot.readI64();
        struct.setRankingIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.rankingName = iprot.readString();
        struct.setRankingNameIsSet(true);
      }
      if (incoming.get(3)) {
        struct.currentPhase = new RankingPhaseInfo();
        struct.currentPhase.read(iprot);
        struct.setCurrentPhaseIsSet(true);
      }
      if (incoming.get(4)) {
        {
          org.apache.thrift.protocol.TMap _map148 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.I64, org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.phasesMap = new HashMap<Long,RankingPhaseInfo>(2*_map148.size);
          long _key149;
          RankingPhaseInfo _val150;
          for (int _i151 = 0; _i151 < _map148.size; ++_i151)
          {
            _key149 = iprot.readI64();
            _val150 = new RankingPhaseInfo();
            _val150.read(iprot);
            struct.phasesMap.put(_key149, _val150);
          }
        }
        struct.setPhasesMapIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TMap _map152 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map152.size);
          String _key153;
          String _val154;
          for (int _i155 = 0; _i155 < _map152.size; ++_i155)
          {
            _key153 = iprot.readString();
            _val154 = iprot.readString();
            struct.extData.put(_key153, _val154);
          }
        }
        struct.setExtDataIsSet(true);
      }
      if (incoming.get(6)) {
        struct.showBeginTime = iprot.readI64();
        struct.setShowBeginTimeIsSet(true);
      }
      if (incoming.get(7)) {
        struct.showEndTime = iprot.readI64();
        struct.setShowEndTimeIsSet(true);
      }
      if (incoming.get(8)) {
        struct.calBeginTime = iprot.readI64();
        struct.setCalBeginTimeIsSet(true);
      }
      if (incoming.get(9)) {
        struct.calEndTime = iprot.readI64();
        struct.setCalEndTimeIsSet(true);
      }
      if (incoming.get(10)) {
        struct.timeKey = iprot.readI64();
        struct.setTimeKeyIsSet(true);
      }
      if (incoming.get(11)) {
        struct.timeKeyBegin = iprot.readString();
        struct.setTimeKeyBeginIsSet(true);
      }
      if (incoming.get(12)) {
        struct.timeKeyEnd = iprot.readString();
        struct.setTimeKeyEndIsSet(true);
      }
      if (incoming.get(13)) {
        struct.rankingExtjson = iprot.readString();
        struct.setRankingExtjsonIsSet(true);
      }
      if (incoming.get(14)) {
        struct.roleItemConfig = new RoleItemConfig();
        struct.roleItemConfig.read(iprot);
        struct.setRoleItemConfigIsSet(true);
      }
      if (incoming.get(15)) {
        struct.rankingType = iprot.readI64();
        struct.setRankingTypeIsSet(true);
      }
    }
  }

}

