/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class ChannelFightRsp implements org.apache.thrift.TBase<ChannelFightRsp, ChannelFightRsp._Fields>, java.io.Serializable, Cloneable, Comparable<ChannelFightRsp> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ChannelFightRsp");

  private static final org.apache.thrift.protocol.TField RET_FIELD_DESC = new org.apache.thrift.protocol.TField("ret", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField SERIAL_NO_FIELD_DESC = new org.apache.thrift.protocol.TField("serialNo", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField SSID_FIELD_DESC = new org.apache.thrift.protocol.TField("ssid", org.apache.thrift.protocol.TType.I64, (short)4);
  private static final org.apache.thrift.protocol.TField COMPERE_UID_FIELD_DESC = new org.apache.thrift.protocol.TField("compereUid", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField REVENUE_FIELD_DESC = new org.apache.thrift.protocol.TField("revenue", org.apache.thrift.protocol.TType.I64, (short)6);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ChannelFightRspStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ChannelFightRspTupleSchemeFactory());
  }

  public long ret; // required
  public long serialNo; // required
  public long sid; // required
  public long ssid; // required
  public long compereUid; // required
  public long revenue; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    RET((short)1, "ret"),
    SERIAL_NO((short)2, "serialNo"),
    SID((short)3, "sid"),
    SSID((short)4, "ssid"),
    COMPERE_UID((short)5, "compereUid"),
    REVENUE((short)6, "revenue");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // RET
          return RET;
        case 2: // SERIAL_NO
          return SERIAL_NO;
        case 3: // SID
          return SID;
        case 4: // SSID
          return SSID;
        case 5: // COMPERE_UID
          return COMPERE_UID;
        case 6: // REVENUE
          return REVENUE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __RET_ISSET_ID = 0;
  private static final int __SERIALNO_ISSET_ID = 1;
  private static final int __SID_ISSET_ID = 2;
  private static final int __SSID_ISSET_ID = 3;
  private static final int __COMPEREUID_ISSET_ID = 4;
  private static final int __REVENUE_ISSET_ID = 5;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.RET, new org.apache.thrift.meta_data.FieldMetaData("ret", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SERIAL_NO, new org.apache.thrift.meta_data.FieldMetaData("serialNo", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SSID, new org.apache.thrift.meta_data.FieldMetaData("ssid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.COMPERE_UID, new org.apache.thrift.meta_data.FieldMetaData("compereUid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.REVENUE, new org.apache.thrift.meta_data.FieldMetaData("revenue", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ChannelFightRsp.class, metaDataMap);
  }

  public ChannelFightRsp() {
  }

  public ChannelFightRsp(
    long ret,
    long serialNo,
    long sid,
    long ssid,
    long compereUid,
    long revenue)
  {
    this();
    this.ret = ret;
    setRetIsSet(true);
    this.serialNo = serialNo;
    setSerialNoIsSet(true);
    this.sid = sid;
    setSidIsSet(true);
    this.ssid = ssid;
    setSsidIsSet(true);
    this.compereUid = compereUid;
    setCompereUidIsSet(true);
    this.revenue = revenue;
    setRevenueIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ChannelFightRsp(ChannelFightRsp other) {
    __isset_bitfield = other.__isset_bitfield;
    this.ret = other.ret;
    this.serialNo = other.serialNo;
    this.sid = other.sid;
    this.ssid = other.ssid;
    this.compereUid = other.compereUid;
    this.revenue = other.revenue;
  }

  public ChannelFightRsp deepCopy() {
    return new ChannelFightRsp(this);
  }

  @Override
  public void clear() {
    setRetIsSet(false);
    this.ret = 0;
    setSerialNoIsSet(false);
    this.serialNo = 0;
    setSidIsSet(false);
    this.sid = 0;
    setSsidIsSet(false);
    this.ssid = 0;
    setCompereUidIsSet(false);
    this.compereUid = 0;
    setRevenueIsSet(false);
    this.revenue = 0;
  }

  public long getRet() {
    return this.ret;
  }

  public ChannelFightRsp setRet(long ret) {
    this.ret = ret;
    setRetIsSet(true);
    return this;
  }

  public void unsetRet() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __RET_ISSET_ID);
  }

  /** Returns true if field ret is set (has been assigned a value) and false otherwise */
  public boolean isSetRet() {
    return EncodingUtils.testBit(__isset_bitfield, __RET_ISSET_ID);
  }

  public void setRetIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __RET_ISSET_ID, value);
  }

  public long getSerialNo() {
    return this.serialNo;
  }

  public ChannelFightRsp setSerialNo(long serialNo) {
    this.serialNo = serialNo;
    setSerialNoIsSet(true);
    return this;
  }

  public void unsetSerialNo() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SERIALNO_ISSET_ID);
  }

  /** Returns true if field serialNo is set (has been assigned a value) and false otherwise */
  public boolean isSetSerialNo() {
    return EncodingUtils.testBit(__isset_bitfield, __SERIALNO_ISSET_ID);
  }

  public void setSerialNoIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SERIALNO_ISSET_ID, value);
  }

  public long getSid() {
    return this.sid;
  }

  public ChannelFightRsp setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  public long getSsid() {
    return this.ssid;
  }

  public ChannelFightRsp setSsid(long ssid) {
    this.ssid = ssid;
    setSsidIsSet(true);
    return this;
  }

  public void unsetSsid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  /** Returns true if field ssid is set (has been assigned a value) and false otherwise */
  public boolean isSetSsid() {
    return EncodingUtils.testBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  public void setSsidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SSID_ISSET_ID, value);
  }

  public long getCompereUid() {
    return this.compereUid;
  }

  public ChannelFightRsp setCompereUid(long compereUid) {
    this.compereUid = compereUid;
    setCompereUidIsSet(true);
    return this;
  }

  public void unsetCompereUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __COMPEREUID_ISSET_ID);
  }

  /** Returns true if field compereUid is set (has been assigned a value) and false otherwise */
  public boolean isSetCompereUid() {
    return EncodingUtils.testBit(__isset_bitfield, __COMPEREUID_ISSET_ID);
  }

  public void setCompereUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __COMPEREUID_ISSET_ID, value);
  }

  public long getRevenue() {
    return this.revenue;
  }

  public ChannelFightRsp setRevenue(long revenue) {
    this.revenue = revenue;
    setRevenueIsSet(true);
    return this;
  }

  public void unsetRevenue() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __REVENUE_ISSET_ID);
  }

  /** Returns true if field revenue is set (has been assigned a value) and false otherwise */
  public boolean isSetRevenue() {
    return EncodingUtils.testBit(__isset_bitfield, __REVENUE_ISSET_ID);
  }

  public void setRevenueIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __REVENUE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case RET:
      if (value == null) {
        unsetRet();
      } else {
        setRet((Long)value);
      }
      break;

    case SERIAL_NO:
      if (value == null) {
        unsetSerialNo();
      } else {
        setSerialNo((Long)value);
      }
      break;

    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case SSID:
      if (value == null) {
        unsetSsid();
      } else {
        setSsid((Long)value);
      }
      break;

    case COMPERE_UID:
      if (value == null) {
        unsetCompereUid();
      } else {
        setCompereUid((Long)value);
      }
      break;

    case REVENUE:
      if (value == null) {
        unsetRevenue();
      } else {
        setRevenue((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case RET:
      return getRet();

    case SERIAL_NO:
      return getSerialNo();

    case SID:
      return getSid();

    case SSID:
      return getSsid();

    case COMPERE_UID:
      return getCompereUid();

    case REVENUE:
      return getRevenue();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case RET:
      return isSetRet();
    case SERIAL_NO:
      return isSetSerialNo();
    case SID:
      return isSetSid();
    case SSID:
      return isSetSsid();
    case COMPERE_UID:
      return isSetCompereUid();
    case REVENUE:
      return isSetRevenue();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ChannelFightRsp)
      return this.equals((ChannelFightRsp)that);
    return false;
  }

  public boolean equals(ChannelFightRsp that) {
    if (that == null)
      return false;

    boolean this_present_ret = true;
    boolean that_present_ret = true;
    if (this_present_ret || that_present_ret) {
      if (!(this_present_ret && that_present_ret))
        return false;
      if (this.ret != that.ret)
        return false;
    }

    boolean this_present_serialNo = true;
    boolean that_present_serialNo = true;
    if (this_present_serialNo || that_present_serialNo) {
      if (!(this_present_serialNo && that_present_serialNo))
        return false;
      if (this.serialNo != that.serialNo)
        return false;
    }

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_ssid = true;
    boolean that_present_ssid = true;
    if (this_present_ssid || that_present_ssid) {
      if (!(this_present_ssid && that_present_ssid))
        return false;
      if (this.ssid != that.ssid)
        return false;
    }

    boolean this_present_compereUid = true;
    boolean that_present_compereUid = true;
    if (this_present_compereUid || that_present_compereUid) {
      if (!(this_present_compereUid && that_present_compereUid))
        return false;
      if (this.compereUid != that.compereUid)
        return false;
    }

    boolean this_present_revenue = true;
    boolean that_present_revenue = true;
    if (this_present_revenue || that_present_revenue) {
      if (!(this_present_revenue && that_present_revenue))
        return false;
      if (this.revenue != that.revenue)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_ret = true;
    list.add(present_ret);
    if (present_ret)
      list.add(ret);

    boolean present_serialNo = true;
    list.add(present_serialNo);
    if (present_serialNo)
      list.add(serialNo);

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_ssid = true;
    list.add(present_ssid);
    if (present_ssid)
      list.add(ssid);

    boolean present_compereUid = true;
    list.add(present_compereUid);
    if (present_compereUid)
      list.add(compereUid);

    boolean present_revenue = true;
    list.add(present_revenue);
    if (present_revenue)
      list.add(revenue);

    return list.hashCode();
  }

  @Override
  public int compareTo(ChannelFightRsp other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetRet()).compareTo(other.isSetRet());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRet()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ret, other.ret);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSerialNo()).compareTo(other.isSetSerialNo());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSerialNo()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.serialNo, other.serialNo);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSsid()).compareTo(other.isSetSsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssid, other.ssid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetCompereUid()).compareTo(other.isSetCompereUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCompereUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.compereUid, other.compereUid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRevenue()).compareTo(other.isSetRevenue());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRevenue()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.revenue, other.revenue);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ChannelFightRsp(");
    boolean first = true;

    sb.append("ret:");
    sb.append(this.ret);
    first = false;
    if (!first) sb.append(", ");
    sb.append("serialNo:");
    sb.append(this.serialNo);
    first = false;
    if (!first) sb.append(", ");
    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ssid:");
    sb.append(this.ssid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("compereUid:");
    sb.append(this.compereUid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("revenue:");
    sb.append(this.revenue);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ChannelFightRspStandardSchemeFactory implements SchemeFactory {
    public ChannelFightRspStandardScheme getScheme() {
      return new ChannelFightRspStandardScheme();
    }
  }

  private static class ChannelFightRspStandardScheme extends StandardScheme<ChannelFightRsp> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ChannelFightRsp struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // RET
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ret = iprot.readI64();
              struct.setRetIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // SERIAL_NO
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.serialNo = iprot.readI64();
              struct.setSerialNoIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // SSID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ssid = iprot.readI64();
              struct.setSsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // COMPERE_UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.compereUid = iprot.readI64();
              struct.setCompereUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // REVENUE
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.revenue = iprot.readI64();
              struct.setRevenueIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ChannelFightRsp struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(RET_FIELD_DESC);
      oprot.writeI64(struct.ret);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SERIAL_NO_FIELD_DESC);
      oprot.writeI64(struct.serialNo);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SSID_FIELD_DESC);
      oprot.writeI64(struct.ssid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(COMPERE_UID_FIELD_DESC);
      oprot.writeI64(struct.compereUid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(REVENUE_FIELD_DESC);
      oprot.writeI64(struct.revenue);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ChannelFightRspTupleSchemeFactory implements SchemeFactory {
    public ChannelFightRspTupleScheme getScheme() {
      return new ChannelFightRspTupleScheme();
    }
  }

  private static class ChannelFightRspTupleScheme extends TupleScheme<ChannelFightRsp> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ChannelFightRsp struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetRet()) {
        optionals.set(0);
      }
      if (struct.isSetSerialNo()) {
        optionals.set(1);
      }
      if (struct.isSetSid()) {
        optionals.set(2);
      }
      if (struct.isSetSsid()) {
        optionals.set(3);
      }
      if (struct.isSetCompereUid()) {
        optionals.set(4);
      }
      if (struct.isSetRevenue()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetRet()) {
        oprot.writeI64(struct.ret);
      }
      if (struct.isSetSerialNo()) {
        oprot.writeI64(struct.serialNo);
      }
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetSsid()) {
        oprot.writeI64(struct.ssid);
      }
      if (struct.isSetCompereUid()) {
        oprot.writeI64(struct.compereUid);
      }
      if (struct.isSetRevenue()) {
        oprot.writeI64(struct.revenue);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ChannelFightRsp struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.ret = iprot.readI64();
        struct.setRetIsSet(true);
      }
      if (incoming.get(1)) {
        struct.serialNo = iprot.readI64();
        struct.setSerialNoIsSet(true);
      }
      if (incoming.get(2)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.ssid = iprot.readI64();
        struct.setSsidIsSet(true);
      }
      if (incoming.get(4)) {
        struct.compereUid = iprot.readI64();
        struct.setCompereUidIsSet(true);
      }
      if (incoming.get(5)) {
        struct.revenue = iprot.readI64();
        struct.setRevenueIsSet(true);
      }
    }
  }

}

