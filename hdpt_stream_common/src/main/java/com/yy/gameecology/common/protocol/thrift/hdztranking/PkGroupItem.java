/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class PkGroupItem implements org.apache.thrift.TBase<PkGroupItem, PkGroupItem._Fields>, java.io.Serializable, Cloneable, Comparable<PkGroupItem> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("PkGroupItem");

  private static final org.apache.thrift.protocol.TField CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("code", org.apache.thrift.protocol.TType.STRING, (short)1);
  private static final org.apache.thrift.protocol.TField NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("name", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField DAY_FIELD_DESC = new org.apache.thrift.protocol.TField("day", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField MEMBER_PK_ITEMS_FIELD_DESC = new org.apache.thrift.protocol.TField("memberPkItems", org.apache.thrift.protocol.TType.LIST, (short)4);
  private static final org.apache.thrift.protocol.TField REMARK_FIELD_DESC = new org.apache.thrift.protocol.TField("remark", org.apache.thrift.protocol.TType.STRING, (short)5);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new PkGroupItemStandardSchemeFactory());
    schemes.put(TupleScheme.class, new PkGroupItemTupleSchemeFactory());
  }

  public String code; // required
  public String name; // required
  public String day; // required
  public List<List<GroupMemberItem>> memberPkItems; // required
  public String remark; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    CODE((short)1, "code"),
    NAME((short)2, "name"),
    DAY((short)3, "day"),
    MEMBER_PK_ITEMS((short)4, "memberPkItems"),
    REMARK((short)5, "remark"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CODE
          return CODE;
        case 2: // NAME
          return NAME;
        case 3: // DAY
          return DAY;
        case 4: // MEMBER_PK_ITEMS
          return MEMBER_PK_ITEMS;
        case 5: // REMARK
          return REMARK;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CODE, new org.apache.thrift.meta_data.FieldMetaData("code", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.NAME, new org.apache.thrift.meta_data.FieldMetaData("name", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.DAY, new org.apache.thrift.meta_data.FieldMetaData("day", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MEMBER_PK_ITEMS, new org.apache.thrift.meta_data.FieldMetaData("memberPkItems", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
                new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, GroupMemberItem.class)))));
    tmpMap.put(_Fields.REMARK, new org.apache.thrift.meta_data.FieldMetaData("remark", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(PkGroupItem.class, metaDataMap);
  }

  public PkGroupItem() {
  }

  public PkGroupItem(
    String code,
    String name,
    String day,
    List<List<GroupMemberItem>> memberPkItems,
    String remark,
    Map<String,String> extData)
  {
    this();
    this.code = code;
    this.name = name;
    this.day = day;
    this.memberPkItems = memberPkItems;
    this.remark = remark;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public PkGroupItem(PkGroupItem other) {
    if (other.isSetCode()) {
      this.code = other.code;
    }
    if (other.isSetName()) {
      this.name = other.name;
    }
    if (other.isSetDay()) {
      this.day = other.day;
    }
    if (other.isSetMemberPkItems()) {
      List<List<GroupMemberItem>> __this__memberPkItems = new ArrayList<List<GroupMemberItem>>(other.memberPkItems.size());
      for (List<GroupMemberItem> other_element : other.memberPkItems) {
        List<GroupMemberItem> __this__memberPkItems_copy = new ArrayList<GroupMemberItem>(other_element.size());
        for (GroupMemberItem other_element_element : other_element) {
          __this__memberPkItems_copy.add(new GroupMemberItem(other_element_element));
        }
        __this__memberPkItems.add(__this__memberPkItems_copy);
      }
      this.memberPkItems = __this__memberPkItems;
    }
    if (other.isSetRemark()) {
      this.remark = other.remark;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public PkGroupItem deepCopy() {
    return new PkGroupItem(this);
  }

  @Override
  public void clear() {
    this.code = null;
    this.name = null;
    this.day = null;
    this.memberPkItems = null;
    this.remark = null;
    this.extData = null;
  }

  public String getCode() {
    return this.code;
  }

  public PkGroupItem setCode(String code) {
    this.code = code;
    return this;
  }

  public void unsetCode() {
    this.code = null;
  }

  /** Returns true if field code is set (has been assigned a value) and false otherwise */
  public boolean isSetCode() {
    return this.code != null;
  }

  public void setCodeIsSet(boolean value) {
    if (!value) {
      this.code = null;
    }
  }

  public String getName() {
    return this.name;
  }

  public PkGroupItem setName(String name) {
    this.name = name;
    return this;
  }

  public void unsetName() {
    this.name = null;
  }

  /** Returns true if field name is set (has been assigned a value) and false otherwise */
  public boolean isSetName() {
    return this.name != null;
  }

  public void setNameIsSet(boolean value) {
    if (!value) {
      this.name = null;
    }
  }

  public String getDay() {
    return this.day;
  }

  public PkGroupItem setDay(String day) {
    this.day = day;
    return this;
  }

  public void unsetDay() {
    this.day = null;
  }

  /** Returns true if field day is set (has been assigned a value) and false otherwise */
  public boolean isSetDay() {
    return this.day != null;
  }

  public void setDayIsSet(boolean value) {
    if (!value) {
      this.day = null;
    }
  }

  public int getMemberPkItemsSize() {
    return (this.memberPkItems == null) ? 0 : this.memberPkItems.size();
  }

  public java.util.Iterator<List<GroupMemberItem>> getMemberPkItemsIterator() {
    return (this.memberPkItems == null) ? null : this.memberPkItems.iterator();
  }

  public void addToMemberPkItems(List<GroupMemberItem> elem) {
    if (this.memberPkItems == null) {
      this.memberPkItems = new ArrayList<List<GroupMemberItem>>();
    }
    this.memberPkItems.add(elem);
  }

  public List<List<GroupMemberItem>> getMemberPkItems() {
    return this.memberPkItems;
  }

  public PkGroupItem setMemberPkItems(List<List<GroupMemberItem>> memberPkItems) {
    this.memberPkItems = memberPkItems;
    return this;
  }

  public void unsetMemberPkItems() {
    this.memberPkItems = null;
  }

  /** Returns true if field memberPkItems is set (has been assigned a value) and false otherwise */
  public boolean isSetMemberPkItems() {
    return this.memberPkItems != null;
  }

  public void setMemberPkItemsIsSet(boolean value) {
    if (!value) {
      this.memberPkItems = null;
    }
  }

  public String getRemark() {
    return this.remark;
  }

  public PkGroupItem setRemark(String remark) {
    this.remark = remark;
    return this;
  }

  public void unsetRemark() {
    this.remark = null;
  }

  /** Returns true if field remark is set (has been assigned a value) and false otherwise */
  public boolean isSetRemark() {
    return this.remark != null;
  }

  public void setRemarkIsSet(boolean value) {
    if (!value) {
      this.remark = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public PkGroupItem setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case CODE:
      if (value == null) {
        unsetCode();
      } else {
        setCode((String)value);
      }
      break;

    case NAME:
      if (value == null) {
        unsetName();
      } else {
        setName((String)value);
      }
      break;

    case DAY:
      if (value == null) {
        unsetDay();
      } else {
        setDay((String)value);
      }
      break;

    case MEMBER_PK_ITEMS:
      if (value == null) {
        unsetMemberPkItems();
      } else {
        setMemberPkItems((List<List<GroupMemberItem>>)value);
      }
      break;

    case REMARK:
      if (value == null) {
        unsetRemark();
      } else {
        setRemark((String)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case CODE:
      return getCode();

    case NAME:
      return getName();

    case DAY:
      return getDay();

    case MEMBER_PK_ITEMS:
      return getMemberPkItems();

    case REMARK:
      return getRemark();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case CODE:
      return isSetCode();
    case NAME:
      return isSetName();
    case DAY:
      return isSetDay();
    case MEMBER_PK_ITEMS:
      return isSetMemberPkItems();
    case REMARK:
      return isSetRemark();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof PkGroupItem)
      return this.equals((PkGroupItem)that);
    return false;
  }

  public boolean equals(PkGroupItem that) {
    if (that == null)
      return false;

    boolean this_present_code = true && this.isSetCode();
    boolean that_present_code = true && that.isSetCode();
    if (this_present_code || that_present_code) {
      if (!(this_present_code && that_present_code))
        return false;
      if (!this.code.equals(that.code))
        return false;
    }

    boolean this_present_name = true && this.isSetName();
    boolean that_present_name = true && that.isSetName();
    if (this_present_name || that_present_name) {
      if (!(this_present_name && that_present_name))
        return false;
      if (!this.name.equals(that.name))
        return false;
    }

    boolean this_present_day = true && this.isSetDay();
    boolean that_present_day = true && that.isSetDay();
    if (this_present_day || that_present_day) {
      if (!(this_present_day && that_present_day))
        return false;
      if (!this.day.equals(that.day))
        return false;
    }

    boolean this_present_memberPkItems = true && this.isSetMemberPkItems();
    boolean that_present_memberPkItems = true && that.isSetMemberPkItems();
    if (this_present_memberPkItems || that_present_memberPkItems) {
      if (!(this_present_memberPkItems && that_present_memberPkItems))
        return false;
      if (!this.memberPkItems.equals(that.memberPkItems))
        return false;
    }

    boolean this_present_remark = true && this.isSetRemark();
    boolean that_present_remark = true && that.isSetRemark();
    if (this_present_remark || that_present_remark) {
      if (!(this_present_remark && that_present_remark))
        return false;
      if (!this.remark.equals(that.remark))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_code = true && (isSetCode());
    list.add(present_code);
    if (present_code)
      list.add(code);

    boolean present_name = true && (isSetName());
    list.add(present_name);
    if (present_name)
      list.add(name);

    boolean present_day = true && (isSetDay());
    list.add(present_day);
    if (present_day)
      list.add(day);

    boolean present_memberPkItems = true && (isSetMemberPkItems());
    list.add(present_memberPkItems);
    if (present_memberPkItems)
      list.add(memberPkItems);

    boolean present_remark = true && (isSetRemark());
    list.add(present_remark);
    if (present_remark)
      list.add(remark);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(PkGroupItem other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetCode()).compareTo(other.isSetCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.code, other.code);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetName()).compareTo(other.isSetName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.name, other.name);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetDay()).compareTo(other.isSetDay());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetDay()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.day, other.day);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMemberPkItems()).compareTo(other.isSetMemberPkItems());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMemberPkItems()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.memberPkItems, other.memberPkItems);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRemark()).compareTo(other.isSetRemark());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRemark()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.remark, other.remark);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("PkGroupItem(");
    boolean first = true;

    sb.append("code:");
    if (this.code == null) {
      sb.append("null");
    } else {
      sb.append(this.code);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("name:");
    if (this.name == null) {
      sb.append("null");
    } else {
      sb.append(this.name);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("day:");
    if (this.day == null) {
      sb.append("null");
    } else {
      sb.append(this.day);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("memberPkItems:");
    if (this.memberPkItems == null) {
      sb.append("null");
    } else {
      sb.append(this.memberPkItems);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("remark:");
    if (this.remark == null) {
      sb.append("null");
    } else {
      sb.append(this.remark);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class PkGroupItemStandardSchemeFactory implements SchemeFactory {
    public PkGroupItemStandardScheme getScheme() {
      return new PkGroupItemStandardScheme();
    }
  }

  private static class PkGroupItemStandardScheme extends StandardScheme<PkGroupItem> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, PkGroupItem struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.code = iprot.readString();
              struct.setCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.name = iprot.readString();
              struct.setNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // DAY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.day = iprot.readString();
              struct.setDayIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // MEMBER_PK_ITEMS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list580 = iprot.readListBegin();
                struct.memberPkItems = new ArrayList<List<GroupMemberItem>>(_list580.size);
                List<GroupMemberItem> _elem581;
                for (int _i582 = 0; _i582 < _list580.size; ++_i582)
                {
                  {
                    org.apache.thrift.protocol.TList _list583 = iprot.readListBegin();
                    _elem581 = new ArrayList<GroupMemberItem>(_list583.size);
                    GroupMemberItem _elem584;
                    for (int _i585 = 0; _i585 < _list583.size; ++_i585)
                    {
                      _elem584 = new GroupMemberItem();
                      _elem584.read(iprot);
                      _elem581.add(_elem584);
                    }
                    iprot.readListEnd();
                  }
                  struct.memberPkItems.add(_elem581);
                }
                iprot.readListEnd();
              }
              struct.setMemberPkItemsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // REMARK
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.remark = iprot.readString();
              struct.setRemarkIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map586 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map586.size);
                String _key587;
                String _val588;
                for (int _i589 = 0; _i589 < _map586.size; ++_i589)
                {
                  _key587 = iprot.readString();
                  _val588 = iprot.readString();
                  struct.extData.put(_key587, _val588);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, PkGroupItem struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.code != null) {
        oprot.writeFieldBegin(CODE_FIELD_DESC);
        oprot.writeString(struct.code);
        oprot.writeFieldEnd();
      }
      if (struct.name != null) {
        oprot.writeFieldBegin(NAME_FIELD_DESC);
        oprot.writeString(struct.name);
        oprot.writeFieldEnd();
      }
      if (struct.day != null) {
        oprot.writeFieldBegin(DAY_FIELD_DESC);
        oprot.writeString(struct.day);
        oprot.writeFieldEnd();
      }
      if (struct.memberPkItems != null) {
        oprot.writeFieldBegin(MEMBER_PK_ITEMS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.LIST, struct.memberPkItems.size()));
          for (List<GroupMemberItem> _iter590 : struct.memberPkItems)
          {
            {
              oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, _iter590.size()));
              for (GroupMemberItem _iter591 : _iter590)
              {
                _iter591.write(oprot);
              }
              oprot.writeListEnd();
            }
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.remark != null) {
        oprot.writeFieldBegin(REMARK_FIELD_DESC);
        oprot.writeString(struct.remark);
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter592 : struct.extData.entrySet())
          {
            oprot.writeString(_iter592.getKey());
            oprot.writeString(_iter592.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class PkGroupItemTupleSchemeFactory implements SchemeFactory {
    public PkGroupItemTupleScheme getScheme() {
      return new PkGroupItemTupleScheme();
    }
  }

  private static class PkGroupItemTupleScheme extends TupleScheme<PkGroupItem> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, PkGroupItem struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetCode()) {
        optionals.set(0);
      }
      if (struct.isSetName()) {
        optionals.set(1);
      }
      if (struct.isSetDay()) {
        optionals.set(2);
      }
      if (struct.isSetMemberPkItems()) {
        optionals.set(3);
      }
      if (struct.isSetRemark()) {
        optionals.set(4);
      }
      if (struct.isSetExtData()) {
        optionals.set(5);
      }
      oprot.writeBitSet(optionals, 6);
      if (struct.isSetCode()) {
        oprot.writeString(struct.code);
      }
      if (struct.isSetName()) {
        oprot.writeString(struct.name);
      }
      if (struct.isSetDay()) {
        oprot.writeString(struct.day);
      }
      if (struct.isSetMemberPkItems()) {
        {
          oprot.writeI32(struct.memberPkItems.size());
          for (List<GroupMemberItem> _iter593 : struct.memberPkItems)
          {
            {
              oprot.writeI32(_iter593.size());
              for (GroupMemberItem _iter594 : _iter593)
              {
                _iter594.write(oprot);
              }
            }
          }
        }
      }
      if (struct.isSetRemark()) {
        oprot.writeString(struct.remark);
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter595 : struct.extData.entrySet())
          {
            oprot.writeString(_iter595.getKey());
            oprot.writeString(_iter595.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, PkGroupItem struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(6);
      if (incoming.get(0)) {
        struct.code = iprot.readString();
        struct.setCodeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.name = iprot.readString();
        struct.setNameIsSet(true);
      }
      if (incoming.get(2)) {
        struct.day = iprot.readString();
        struct.setDayIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TList _list596 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.LIST, iprot.readI32());
          struct.memberPkItems = new ArrayList<List<GroupMemberItem>>(_list596.size);
          List<GroupMemberItem> _elem597;
          for (int _i598 = 0; _i598 < _list596.size; ++_i598)
          {
            {
              org.apache.thrift.protocol.TList _list599 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
              _elem597 = new ArrayList<GroupMemberItem>(_list599.size);
              GroupMemberItem _elem600;
              for (int _i601 = 0; _i601 < _list599.size; ++_i601)
              {
                _elem600 = new GroupMemberItem();
                _elem600.read(iprot);
                _elem597.add(_elem600);
              }
            }
            struct.memberPkItems.add(_elem597);
          }
        }
        struct.setMemberPkItemsIsSet(true);
      }
      if (incoming.get(4)) {
        struct.remark = iprot.readString();
        struct.setRemarkIsSet(true);
      }
      if (incoming.get(5)) {
        {
          org.apache.thrift.protocol.TMap _map602 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map602.size);
          String _key603;
          String _val604;
          for (int _i605 = 0; _i605 < _map602.size; ++_i605)
          {
            _key603 = iprot.readString();
            _val604 = iprot.readString();
            struct.extData.put(_key603, _val604);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

