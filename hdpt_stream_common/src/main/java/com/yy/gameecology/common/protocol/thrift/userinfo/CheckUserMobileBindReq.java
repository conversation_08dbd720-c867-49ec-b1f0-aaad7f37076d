/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.userinfo;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-08-18")
public class CheckUserMobileBindReq implements org.apache.thrift.TBase<CheckUserMobileBindReq, CheckUserMobileBindReq._Fields>, java.io.Serializable, Cloneable, Comparable<CheckUserMobileBindReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CheckUserMobileBindReq");

  private static final org.apache.thrift.protocol.TField AUTHMSG_FIELD_DESC = new org.apache.thrift.protocol.TField("authmsg", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField APPID_FIELD_DESC = new org.apache.thrift.protocol.TField("appid", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField REQUEST_FIELD_DESC = new org.apache.thrift.protocol.TField("request", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField MOBILE_FIELD_DESC = new org.apache.thrift.protocol.TField("mobile", org.apache.thrift.protocol.TType.STRING, (short)4);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new CheckUserMobileBindReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new CheckUserMobileBindReqTupleSchemeFactory());
  }

  public AuthorizeMsg authmsg; // required
  public String appid; // required
  public String request; // required
  public String mobile; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    AUTHMSG((short)1, "authmsg"),
    APPID((short)2, "appid"),
    REQUEST((short)3, "request"),
    MOBILE((short)4, "mobile");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // AUTHMSG
          return AUTHMSG;
        case 2: // APPID
          return APPID;
        case 3: // REQUEST
          return REQUEST;
        case 4: // MOBILE
          return MOBILE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.AUTHMSG, new org.apache.thrift.meta_data.FieldMetaData("authmsg", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AuthorizeMsg.class)));
    tmpMap.put(_Fields.APPID, new org.apache.thrift.meta_data.FieldMetaData("appid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.REQUEST, new org.apache.thrift.meta_data.FieldMetaData("request", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.MOBILE, new org.apache.thrift.meta_data.FieldMetaData("mobile", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CheckUserMobileBindReq.class, metaDataMap);
  }

  public CheckUserMobileBindReq() {
  }

  public CheckUserMobileBindReq(
    AuthorizeMsg authmsg,
    String appid,
    String request,
    String mobile)
  {
    this();
    this.authmsg = authmsg;
    this.appid = appid;
    this.request = request;
    this.mobile = mobile;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CheckUserMobileBindReq(CheckUserMobileBindReq other) {
    if (other.isSetAuthmsg()) {
      this.authmsg = new AuthorizeMsg(other.authmsg);
    }
    if (other.isSetAppid()) {
      this.appid = other.appid;
    }
    if (other.isSetRequest()) {
      this.request = other.request;
    }
    if (other.isSetMobile()) {
      this.mobile = other.mobile;
    }
  }

  public CheckUserMobileBindReq deepCopy() {
    return new CheckUserMobileBindReq(this);
  }

  @Override
  public void clear() {
    this.authmsg = null;
    this.appid = null;
    this.request = null;
    this.mobile = null;
  }

  public AuthorizeMsg getAuthmsg() {
    return this.authmsg;
  }

  public CheckUserMobileBindReq setAuthmsg(AuthorizeMsg authmsg) {
    this.authmsg = authmsg;
    return this;
  }

  public void unsetAuthmsg() {
    this.authmsg = null;
  }

  /** Returns true if field authmsg is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthmsg() {
    return this.authmsg != null;
  }

  public void setAuthmsgIsSet(boolean value) {
    if (!value) {
      this.authmsg = null;
    }
  }

  public String getAppid() {
    return this.appid;
  }

  public CheckUserMobileBindReq setAppid(String appid) {
    this.appid = appid;
    return this;
  }

  public void unsetAppid() {
    this.appid = null;
  }

  /** Returns true if field appid is set (has been assigned a value) and false otherwise */
  public boolean isSetAppid() {
    return this.appid != null;
  }

  public void setAppidIsSet(boolean value) {
    if (!value) {
      this.appid = null;
    }
  }

  public String getRequest() {
    return this.request;
  }

  public CheckUserMobileBindReq setRequest(String request) {
    this.request = request;
    return this;
  }

  public void unsetRequest() {
    this.request = null;
  }

  /** Returns true if field request is set (has been assigned a value) and false otherwise */
  public boolean isSetRequest() {
    return this.request != null;
  }

  public void setRequestIsSet(boolean value) {
    if (!value) {
      this.request = null;
    }
  }

  public String getMobile() {
    return this.mobile;
  }

  public CheckUserMobileBindReq setMobile(String mobile) {
    this.mobile = mobile;
    return this;
  }

  public void unsetMobile() {
    this.mobile = null;
  }

  /** Returns true if field mobile is set (has been assigned a value) and false otherwise */
  public boolean isSetMobile() {
    return this.mobile != null;
  }

  public void setMobileIsSet(boolean value) {
    if (!value) {
      this.mobile = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case AUTHMSG:
      if (value == null) {
        unsetAuthmsg();
      } else {
        setAuthmsg((AuthorizeMsg)value);
      }
      break;

    case APPID:
      if (value == null) {
        unsetAppid();
      } else {
        setAppid((String)value);
      }
      break;

    case REQUEST:
      if (value == null) {
        unsetRequest();
      } else {
        setRequest((String)value);
      }
      break;

    case MOBILE:
      if (value == null) {
        unsetMobile();
      } else {
        setMobile((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case AUTHMSG:
      return getAuthmsg();

    case APPID:
      return getAppid();

    case REQUEST:
      return getRequest();

    case MOBILE:
      return getMobile();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case AUTHMSG:
      return isSetAuthmsg();
    case APPID:
      return isSetAppid();
    case REQUEST:
      return isSetRequest();
    case MOBILE:
      return isSetMobile();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof CheckUserMobileBindReq)
      return this.equals((CheckUserMobileBindReq)that);
    return false;
  }

  public boolean equals(CheckUserMobileBindReq that) {
    if (that == null)
      return false;

    boolean this_present_authmsg = true && this.isSetAuthmsg();
    boolean that_present_authmsg = true && that.isSetAuthmsg();
    if (this_present_authmsg || that_present_authmsg) {
      if (!(this_present_authmsg && that_present_authmsg))
        return false;
      if (!this.authmsg.equals(that.authmsg))
        return false;
    }

    boolean this_present_appid = true && this.isSetAppid();
    boolean that_present_appid = true && that.isSetAppid();
    if (this_present_appid || that_present_appid) {
      if (!(this_present_appid && that_present_appid))
        return false;
      if (!this.appid.equals(that.appid))
        return false;
    }

    boolean this_present_request = true && this.isSetRequest();
    boolean that_present_request = true && that.isSetRequest();
    if (this_present_request || that_present_request) {
      if (!(this_present_request && that_present_request))
        return false;
      if (!this.request.equals(that.request))
        return false;
    }

    boolean this_present_mobile = true && this.isSetMobile();
    boolean that_present_mobile = true && that.isSetMobile();
    if (this_present_mobile || that_present_mobile) {
      if (!(this_present_mobile && that_present_mobile))
        return false;
      if (!this.mobile.equals(that.mobile))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_authmsg = true && (isSetAuthmsg());
    list.add(present_authmsg);
    if (present_authmsg)
      list.add(authmsg);

    boolean present_appid = true && (isSetAppid());
    list.add(present_appid);
    if (present_appid)
      list.add(appid);

    boolean present_request = true && (isSetRequest());
    list.add(present_request);
    if (present_request)
      list.add(request);

    boolean present_mobile = true && (isSetMobile());
    list.add(present_mobile);
    if (present_mobile)
      list.add(mobile);

    return list.hashCode();
  }

  @Override
  public int compareTo(CheckUserMobileBindReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAuthmsg()).compareTo(other.isSetAuthmsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthmsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.authmsg, other.authmsg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppid()).compareTo(other.isSetAppid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appid, other.appid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRequest()).compareTo(other.isSetRequest());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRequest()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.request, other.request);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetMobile()).compareTo(other.isSetMobile());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMobile()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.mobile, other.mobile);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("CheckUserMobileBindReq(");
    boolean first = true;

    sb.append("authmsg:");
    if (this.authmsg == null) {
      sb.append("null");
    } else {
      sb.append(this.authmsg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appid:");
    if (this.appid == null) {
      sb.append("null");
    } else {
      sb.append(this.appid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("request:");
    if (this.request == null) {
      sb.append("null");
    } else {
      sb.append(this.request);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("mobile:");
    if (this.mobile == null) {
      sb.append("null");
    } else {
      sb.append(this.mobile);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (authmsg != null) {
      authmsg.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CheckUserMobileBindReqStandardSchemeFactory implements SchemeFactory {
    public CheckUserMobileBindReqStandardScheme getScheme() {
      return new CheckUserMobileBindReqStandardScheme();
    }
  }

  private static class CheckUserMobileBindReqStandardScheme extends StandardScheme<CheckUserMobileBindReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CheckUserMobileBindReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // AUTHMSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.authmsg = new AuthorizeMsg();
              struct.authmsg.read(iprot);
              struct.setAuthmsgIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appid = iprot.readString();
              struct.setAppidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // REQUEST
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.request = iprot.readString();
              struct.setRequestIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // MOBILE
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.mobile = iprot.readString();
              struct.setMobileIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CheckUserMobileBindReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.authmsg != null) {
        oprot.writeFieldBegin(AUTHMSG_FIELD_DESC);
        struct.authmsg.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.appid != null) {
        oprot.writeFieldBegin(APPID_FIELD_DESC);
        oprot.writeString(struct.appid);
        oprot.writeFieldEnd();
      }
      if (struct.request != null) {
        oprot.writeFieldBegin(REQUEST_FIELD_DESC);
        oprot.writeString(struct.request);
        oprot.writeFieldEnd();
      }
      if (struct.mobile != null) {
        oprot.writeFieldBegin(MOBILE_FIELD_DESC);
        oprot.writeString(struct.mobile);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CheckUserMobileBindReqTupleSchemeFactory implements SchemeFactory {
    public CheckUserMobileBindReqTupleScheme getScheme() {
      return new CheckUserMobileBindReqTupleScheme();
    }
  }

  private static class CheckUserMobileBindReqTupleScheme extends TupleScheme<CheckUserMobileBindReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CheckUserMobileBindReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAuthmsg()) {
        optionals.set(0);
      }
      if (struct.isSetAppid()) {
        optionals.set(1);
      }
      if (struct.isSetRequest()) {
        optionals.set(2);
      }
      if (struct.isSetMobile()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetAuthmsg()) {
        struct.authmsg.write(oprot);
      }
      if (struct.isSetAppid()) {
        oprot.writeString(struct.appid);
      }
      if (struct.isSetRequest()) {
        oprot.writeString(struct.request);
      }
      if (struct.isSetMobile()) {
        oprot.writeString(struct.mobile);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CheckUserMobileBindReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.authmsg = new AuthorizeMsg();
        struct.authmsg.read(iprot);
        struct.setAuthmsgIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appid = iprot.readString();
        struct.setAppidIsSet(true);
      }
      if (incoming.get(2)) {
        struct.request = iprot.readString();
        struct.setRequestIsSet(true);
      }
      if (incoming.get(3)) {
        struct.mobile = iprot.readString();
        struct.setMobileIsSet(true);
      }
    }
  }

}

