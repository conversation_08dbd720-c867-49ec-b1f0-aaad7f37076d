/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.turnover_family;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2024-09-09")
public class TFamilySsidDetail implements org.apache.thrift.TBase<TFamilySsidDetail, TFamilySsidDetail._Fields>, java.io.Serializable, Cloneable, Comparable<TFamilySsidDetail> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("TFamilySsidDetail");

  private static final org.apache.thrift.protocol.TField APPID_FIELD_DESC = new org.apache.thrift.protocol.TField("appid", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField FAMILY_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("familyId", org.apache.thrift.protocol.TType.I64, (short)2);
  private static final org.apache.thrift.protocol.TField SSID_FIELD_DESC = new org.apache.thrift.protocol.TField("ssid", org.apache.thrift.protocol.TType.I64, (short)3);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.STRING, (short)4);
  private static final org.apache.thrift.protocol.TField SID_FIELD_DESC = new org.apache.thrift.protocol.TField("sid", org.apache.thrift.protocol.TType.I64, (short)5);
  private static final org.apache.thrift.protocol.TField NAME_FIELD_DESC = new org.apache.thrift.protocol.TField("name", org.apache.thrift.protocol.TType.STRING, (short)6);
  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.I64, (short)7);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new TFamilySsidDetailStandardSchemeFactory());
    schemes.put(TupleScheme.class, new TFamilySsidDetailTupleSchemeFactory());
  }

  /**
   * 业务id
   */
  public int appid; // required
  /**
   * 家族id
   */
  public long familyId; // required
  /**
   * ssid
   */
  public long ssid; // required
  /**
   * 扩展字段
   */
  public String expand; // required
  /**
   * sid
   */
  public long sid; // required
  /**
   * 家族名称
   */
  public String name; // required
  /**
   * 家族长uid
   */
  public long uid; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 业务id
     */
    APPID((short)1, "appid"),
    /**
     * 家族id
     */
    FAMILY_ID((short)2, "familyId"),
    /**
     * ssid
     */
    SSID((short)3, "ssid"),
    /**
     * 扩展字段
     */
    EXPAND((short)4, "expand"),
    /**
     * sid
     */
    SID((short)5, "sid"),
    /**
     * 家族名称
     */
    NAME((short)6, "name"),
    /**
     * 家族长uid
     */
    UID((short)7, "uid");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // APPID
          return APPID;
        case 2: // FAMILY_ID
          return FAMILY_ID;
        case 3: // SSID
          return SSID;
        case 4: // EXPAND
          return EXPAND;
        case 5: // SID
          return SID;
        case 6: // NAME
          return NAME;
        case 7: // UID
          return UID;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __APPID_ISSET_ID = 0;
  private static final int __FAMILYID_ISSET_ID = 1;
  private static final int __SSID_ISSET_ID = 2;
  private static final int __SID_ISSET_ID = 3;
  private static final int __UID_ISSET_ID = 4;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.APPID, new org.apache.thrift.meta_data.FieldMetaData("appid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.FAMILY_ID, new org.apache.thrift.meta_data.FieldMetaData("familyId", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.SSID, new org.apache.thrift.meta_data.FieldMetaData("ssid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.SID, new org.apache.thrift.meta_data.FieldMetaData("sid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.NAME, new org.apache.thrift.meta_data.FieldMetaData("name", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(TFamilySsidDetail.class, metaDataMap);
  }

  public TFamilySsidDetail() {
  }

  public TFamilySsidDetail(
    int appid,
    long familyId,
    long ssid,
    String expand,
    long sid,
    String name,
    long uid)
  {
    this();
    this.appid = appid;
    setAppidIsSet(true);
    this.familyId = familyId;
    setFamilyIdIsSet(true);
    this.ssid = ssid;
    setSsidIsSet(true);
    this.expand = expand;
    this.sid = sid;
    setSidIsSet(true);
    this.name = name;
    this.uid = uid;
    setUidIsSet(true);
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public TFamilySsidDetail(TFamilySsidDetail other) {
    __isset_bitfield = other.__isset_bitfield;
    this.appid = other.appid;
    this.familyId = other.familyId;
    this.ssid = other.ssid;
    if (other.isSetExpand()) {
      this.expand = other.expand;
    }
    this.sid = other.sid;
    if (other.isSetName()) {
      this.name = other.name;
    }
    this.uid = other.uid;
  }

  public TFamilySsidDetail deepCopy() {
    return new TFamilySsidDetail(this);
  }

  @Override
  public void clear() {
    setAppidIsSet(false);
    this.appid = 0;
    setFamilyIdIsSet(false);
    this.familyId = 0;
    setSsidIsSet(false);
    this.ssid = 0;
    this.expand = null;
    setSidIsSet(false);
    this.sid = 0;
    this.name = null;
    setUidIsSet(false);
    this.uid = 0;
  }

  /**
   * 业务id
   */
  public int getAppid() {
    return this.appid;
  }

  /**
   * 业务id
   */
  public TFamilySsidDetail setAppid(int appid) {
    this.appid = appid;
    setAppidIsSet(true);
    return this;
  }

  public void unsetAppid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  /** Returns true if field appid is set (has been assigned a value) and false otherwise */
  public boolean isSetAppid() {
    return EncodingUtils.testBit(__isset_bitfield, __APPID_ISSET_ID);
  }

  public void setAppidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __APPID_ISSET_ID, value);
  }

  /**
   * 家族id
   */
  public long getFamilyId() {
    return this.familyId;
  }

  /**
   * 家族id
   */
  public TFamilySsidDetail setFamilyId(long familyId) {
    this.familyId = familyId;
    setFamilyIdIsSet(true);
    return this;
  }

  public void unsetFamilyId() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __FAMILYID_ISSET_ID);
  }

  /** Returns true if field familyId is set (has been assigned a value) and false otherwise */
  public boolean isSetFamilyId() {
    return EncodingUtils.testBit(__isset_bitfield, __FAMILYID_ISSET_ID);
  }

  public void setFamilyIdIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __FAMILYID_ISSET_ID, value);
  }

  /**
   * ssid
   */
  public long getSsid() {
    return this.ssid;
  }

  /**
   * ssid
   */
  public TFamilySsidDetail setSsid(long ssid) {
    this.ssid = ssid;
    setSsidIsSet(true);
    return this;
  }

  public void unsetSsid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  /** Returns true if field ssid is set (has been assigned a value) and false otherwise */
  public boolean isSetSsid() {
    return EncodingUtils.testBit(__isset_bitfield, __SSID_ISSET_ID);
  }

  public void setSsidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SSID_ISSET_ID, value);
  }

  /**
   * 扩展字段
   */
  public String getExpand() {
    return this.expand;
  }

  /**
   * 扩展字段
   */
  public TFamilySsidDetail setExpand(String expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  /**
   * sid
   */
  public long getSid() {
    return this.sid;
  }

  /**
   * sid
   */
  public TFamilySsidDetail setSid(long sid) {
    this.sid = sid;
    setSidIsSet(true);
    return this;
  }

  public void unsetSid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __SID_ISSET_ID);
  }

  /** Returns true if field sid is set (has been assigned a value) and false otherwise */
  public boolean isSetSid() {
    return EncodingUtils.testBit(__isset_bitfield, __SID_ISSET_ID);
  }

  public void setSidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __SID_ISSET_ID, value);
  }

  /**
   * 家族名称
   */
  public String getName() {
    return this.name;
  }

  /**
   * 家族名称
   */
  public TFamilySsidDetail setName(String name) {
    this.name = name;
    return this;
  }

  public void unsetName() {
    this.name = null;
  }

  /** Returns true if field name is set (has been assigned a value) and false otherwise */
  public boolean isSetName() {
    return this.name != null;
  }

  public void setNameIsSet(boolean value) {
    if (!value) {
      this.name = null;
    }
  }

  /**
   * 家族长uid
   */
  public long getUid() {
    return this.uid;
  }

  /**
   * 家族长uid
   */
  public TFamilySsidDetail setUid(long uid) {
    this.uid = uid;
    setUidIsSet(true);
    return this;
  }

  public void unsetUid() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __UID_ISSET_ID);
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return EncodingUtils.testBit(__isset_bitfield, __UID_ISSET_ID);
  }

  public void setUidIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __UID_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case APPID:
      if (value == null) {
        unsetAppid();
      } else {
        setAppid((Integer)value);
      }
      break;

    case FAMILY_ID:
      if (value == null) {
        unsetFamilyId();
      } else {
        setFamilyId((Long)value);
      }
      break;

    case SSID:
      if (value == null) {
        unsetSsid();
      } else {
        setSsid((Long)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((String)value);
      }
      break;

    case SID:
      if (value == null) {
        unsetSid();
      } else {
        setSid((Long)value);
      }
      break;

    case NAME:
      if (value == null) {
        unsetName();
      } else {
        setName((String)value);
      }
      break;

    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((Long)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case APPID:
      return getAppid();

    case FAMILY_ID:
      return getFamilyId();

    case SSID:
      return getSsid();

    case EXPAND:
      return getExpand();

    case SID:
      return getSid();

    case NAME:
      return getName();

    case UID:
      return getUid();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case APPID:
      return isSetAppid();
    case FAMILY_ID:
      return isSetFamilyId();
    case SSID:
      return isSetSsid();
    case EXPAND:
      return isSetExpand();
    case SID:
      return isSetSid();
    case NAME:
      return isSetName();
    case UID:
      return isSetUid();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof TFamilySsidDetail)
      return this.equals((TFamilySsidDetail)that);
    return false;
  }

  public boolean equals(TFamilySsidDetail that) {
    if (that == null)
      return false;

    boolean this_present_appid = true;
    boolean that_present_appid = true;
    if (this_present_appid || that_present_appid) {
      if (!(this_present_appid && that_present_appid))
        return false;
      if (this.appid != that.appid)
        return false;
    }

    boolean this_present_familyId = true;
    boolean that_present_familyId = true;
    if (this_present_familyId || that_present_familyId) {
      if (!(this_present_familyId && that_present_familyId))
        return false;
      if (this.familyId != that.familyId)
        return false;
    }

    boolean this_present_ssid = true;
    boolean that_present_ssid = true;
    if (this_present_ssid || that_present_ssid) {
      if (!(this_present_ssid && that_present_ssid))
        return false;
      if (this.ssid != that.ssid)
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    boolean this_present_sid = true;
    boolean that_present_sid = true;
    if (this_present_sid || that_present_sid) {
      if (!(this_present_sid && that_present_sid))
        return false;
      if (this.sid != that.sid)
        return false;
    }

    boolean this_present_name = true && this.isSetName();
    boolean that_present_name = true && that.isSetName();
    if (this_present_name || that_present_name) {
      if (!(this_present_name && that_present_name))
        return false;
      if (!this.name.equals(that.name))
        return false;
    }

    boolean this_present_uid = true;
    boolean that_present_uid = true;
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (this.uid != that.uid)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_appid = true;
    list.add(present_appid);
    if (present_appid)
      list.add(appid);

    boolean present_familyId = true;
    list.add(present_familyId);
    if (present_familyId)
      list.add(familyId);

    boolean present_ssid = true;
    list.add(present_ssid);
    if (present_ssid)
      list.add(ssid);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    boolean present_sid = true;
    list.add(present_sid);
    if (present_sid)
      list.add(sid);

    boolean present_name = true && (isSetName());
    list.add(present_name);
    if (present_name)
      list.add(name);

    boolean present_uid = true;
    list.add(present_uid);
    if (present_uid)
      list.add(uid);

    return list.hashCode();
  }

  @Override
  public int compareTo(TFamilySsidDetail other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAppid()).compareTo(other.isSetAppid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appid, other.appid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetFamilyId()).compareTo(other.isSetFamilyId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetFamilyId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.familyId, other.familyId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSsid()).compareTo(other.isSetSsid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSsid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.ssid, other.ssid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetSid()).compareTo(other.isSetSid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetSid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.sid, other.sid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetName()).compareTo(other.isSetName());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetName()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.name, other.name);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("TFamilySsidDetail(");
    boolean first = true;

    sb.append("appid:");
    sb.append(this.appid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("familyId:");
    sb.append(this.familyId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("ssid:");
    sb.append(this.ssid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("sid:");
    sb.append(this.sid);
    first = false;
    if (!first) sb.append(", ");
    sb.append("name:");
    if (this.name == null) {
      sb.append("null");
    } else {
      sb.append(this.name);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("uid:");
    sb.append(this.uid);
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class TFamilySsidDetailStandardSchemeFactory implements SchemeFactory {
    public TFamilySsidDetailStandardScheme getScheme() {
      return new TFamilySsidDetailStandardScheme();
    }
  }

  private static class TFamilySsidDetailStandardScheme extends StandardScheme<TFamilySsidDetail> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, TFamilySsidDetail struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // APPID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.appid = iprot.readI32();
              struct.setAppidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // FAMILY_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.familyId = iprot.readI64();
              struct.setFamilyIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // SSID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.ssid = iprot.readI64();
              struct.setSsidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.expand = iprot.readString();
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // SID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.sid = iprot.readI64();
              struct.setSidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 6: // NAME
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.name = iprot.readString();
              struct.setNameIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 7: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.uid = iprot.readI64();
              struct.setUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, TFamilySsidDetail struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(APPID_FIELD_DESC);
      oprot.writeI32(struct.appid);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(FAMILY_ID_FIELD_DESC);
      oprot.writeI64(struct.familyId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(SSID_FIELD_DESC);
      oprot.writeI64(struct.ssid);
      oprot.writeFieldEnd();
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        oprot.writeString(struct.expand);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(SID_FIELD_DESC);
      oprot.writeI64(struct.sid);
      oprot.writeFieldEnd();
      if (struct.name != null) {
        oprot.writeFieldBegin(NAME_FIELD_DESC);
        oprot.writeString(struct.name);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldBegin(UID_FIELD_DESC);
      oprot.writeI64(struct.uid);
      oprot.writeFieldEnd();
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class TFamilySsidDetailTupleSchemeFactory implements SchemeFactory {
    public TFamilySsidDetailTupleScheme getScheme() {
      return new TFamilySsidDetailTupleScheme();
    }
  }

  private static class TFamilySsidDetailTupleScheme extends TupleScheme<TFamilySsidDetail> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, TFamilySsidDetail struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAppid()) {
        optionals.set(0);
      }
      if (struct.isSetFamilyId()) {
        optionals.set(1);
      }
      if (struct.isSetSsid()) {
        optionals.set(2);
      }
      if (struct.isSetExpand()) {
        optionals.set(3);
      }
      if (struct.isSetSid()) {
        optionals.set(4);
      }
      if (struct.isSetName()) {
        optionals.set(5);
      }
      if (struct.isSetUid()) {
        optionals.set(6);
      }
      oprot.writeBitSet(optionals, 7);
      if (struct.isSetAppid()) {
        oprot.writeI32(struct.appid);
      }
      if (struct.isSetFamilyId()) {
        oprot.writeI64(struct.familyId);
      }
      if (struct.isSetSsid()) {
        oprot.writeI64(struct.ssid);
      }
      if (struct.isSetExpand()) {
        oprot.writeString(struct.expand);
      }
      if (struct.isSetSid()) {
        oprot.writeI64(struct.sid);
      }
      if (struct.isSetName()) {
        oprot.writeString(struct.name);
      }
      if (struct.isSetUid()) {
        oprot.writeI64(struct.uid);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, TFamilySsidDetail struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(7);
      if (incoming.get(0)) {
        struct.appid = iprot.readI32();
        struct.setAppidIsSet(true);
      }
      if (incoming.get(1)) {
        struct.familyId = iprot.readI64();
        struct.setFamilyIdIsSet(true);
      }
      if (incoming.get(2)) {
        struct.ssid = iprot.readI64();
        struct.setSsidIsSet(true);
      }
      if (incoming.get(3)) {
        struct.expand = iprot.readString();
        struct.setExpandIsSet(true);
      }
      if (incoming.get(4)) {
        struct.sid = iprot.readI64();
        struct.setSidIsSet(true);
      }
      if (incoming.get(5)) {
        struct.name = iprot.readString();
        struct.setNameIsSet(true);
      }
      if (incoming.get(6)) {
        struct.uid = iprot.readI64();
        struct.setUidIsSet(true);
      }
    }
  }

}

