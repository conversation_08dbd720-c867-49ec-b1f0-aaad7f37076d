/*
 * @(#)CommDao.java
 * 
 * Copyright (c) 欢聚时代
 */

package com.yy.gameecology.common;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.naming.Context;
import javax.naming.InitialContext;
import javax.naming.NamingException;
import javax.sql.DataSource;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.sql.*;
import java.util.*;

/**
 * <pre>
 * &#64;date 2008-06-19
 * &#64;author guolp
 * 
 * &#64;功能说明：
 * CommDao 类是个独立类，用户只需为其提供 jdbc 驱动类包和 log4j 类包（通常项目本身就需要包含这些包，所以本类并不会给项目添加额外负担）
 * 本类唯一需要的外部资源是 '/comm-dao-config.xml' 配置文件，并且该配置文件必须有形式如下的内容 下面的内容也不是完整需要，remark
 * 部分仅为注释，可以省略，另外只需提供分支所指定的部分就可，比如 root.comm-dao-dbconn.obtain-mode =
 * "container" 时只要提供 container 元素及其下内容，application 分支可剪除
 * root.comm-dao-dbconn.container.container-product = "resin" 时只要提供 resin
 * 元素及其下内容，tomcat/weblogic 分支可剪除 以此类推，CommDao只解析分支所指部分的配置内容 
 * 
 *    &lt;?xml version="1.0" encoding="UTF-8"?> 
 *    &lt;root>
 * 	&lt;comm-dao-dbconn obtain-mode="application" remark="obtain-mode can only 'application', 'container', any other equals to 'application'">
 * 		&lt;container container-product="" remark="product can only 'tomcat', 'resin', 'weblogic', any other equals to 'weblogic'">
 * 			&lt;tomcat>
 * 				&lt;jndi-name>UP_DATASOURCE&lt;/jndi-name>
 * 				&lt;auto-commit>false&lt;/auto-commit>
 * 			&lt;/tomcat>
 * 			&lt;resin>
 * 				&lt;jndi-name>UP_DATASOURCE&lt;/jndi-name>
 * 				&lt;auto-commit>false&lt;/auto-commit>
 * 			&lt;/resin>
 * 			&lt;weblogic>
 * 				&lt;jndi-name>UP_DATASOURCE&lt;/jndi-name>			
 * 				&lt;factory>weblogic.jndi.WLInitialContextFactory&lt;/factory>
 * 				&lt;AUTHENTICATE_URL>t3://127.0.0.1:7001&lt;/AUTHENTICATE_URL>
 * 				&lt;user>weblogic&lt;/user>
 * 				&lt;password>weblogic&lt;/password>
 * 				&lt;auto-commit>false&lt;/auto-commit>
 * 			&lt;/weblogic>
 * 		&lt;/container>
 * 		&lt;application app-conn-mode="pool" remark="conn-mode can only 'jdbc', 'pool', any other equals to 'jdbc'">
 * 			&lt;jdbc>
 * 				&lt;driver>oracle.jdbc.driver.OracleDriver&lt;/driver>
 * 				&lt;AUTHENTICATE_URL>**************************************************************>		
 * 				&lt;user>commweb&lt;/user>
 * 				&lt;password>commweb&lt;/password>
 * 				&lt;auto-commit>false&lt;/auto-commit>	
 * 			&lt;/jdbc>
 * 			&lt;pool>
 * 				&lt;driver>oracle.jdbc.driver.OracleDriver&lt;/driver>
 * 				&lt;AUTHENTICATE_URL>**************************************************************>
 * 				&lt;user>commweb&lt;/user>
 * 				&lt;password>commweb&lt;/password>
 * 				&lt;auto-commit>false&lt;/auto-commit>
 * 				&lt;transaction-isolation>2&lt;/transaction-isolation>	
 * 				&lt;max-active>10&lt;/max-active>
 * 				&lt;min-idle>0&lt;/min-idle>
 * 				&lt;max-idle>10&lt;/max-idle>
 * 				&lt;pool-prepared-statements>true&lt;/pool-prepared-statements>
 * 				&lt;max-open-prepared-statements>100&lt;/max-open-prepared-statements>
 * 			&lt;/pool>
 * 		&lt;/application>
 * 	&lt;/comm-dao-dbconn>
 *   &lt;/root>
 * 
 * &#64;版本更新列表
 * 修改版本: 1.0.3
 * 修改日期：2009-02-01
 * 修改人 : guolp
 * 修改说明：对来自 comm-dao-config.xml 中的数据库链接参数按需提供解密能力
 * 复审人：
 * 
 * 修改版本: 1.0.2
 * 修改日期：2008-12-30
 * 修改人 : haimin
 * 修改说明：增加addPageRulePreparedStatement分页的PreparedStatement方法，自动构造预编译语句，提高sql执行效率
 * 复审人：
 * 
 * 修改版本: 1.0.1
 * 修改日期：2008-11-13
 * 修改人 : haimin
 * 修改说明：增加getNextval方法，根据序列名得到序列值
 * 复审人：
 * 
 * 修改版本: 1.0.0
 * 修改日期：2008-06-19
 * 修改人 : guolp
 * 修改说明：形成初始版本
 * 复审人：
 * </pre>
 */

public class CommDao {
    private static final Logger log = LoggerFactory.getLogger(CommDao.class);

    private static final String _xmlFilePath = "/comm-dao-config.xml";

    private static boolean init_result = false;

    private static CommDao instance = null; // new CommDao();

    private CommDao() {
        try {
            init();
            if (log.isInfoEnabled()) {
                log.info("init database connection parameters ok!");
                if (isContainerMode) {
                    log.info(String.format("connection mode is: %s Container", containerProduct));
                } else {
                    if (isAppConnPoolMode) {
                        log.info("connection mode is: pool application");
                    } else {
                        log.info("connection mode is: jdbc application");
                    }
                }
            }
            init_result = true;
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("init database connection parameters fail!", e);
            }
            init_result = false;
        }
    }

    public static CommDao getInstance() {
        if (instance == null) {
            synchronized (System.out) {
                if (instance == null) {
                    instance = new CommDao();
                }
            }
        }

        if (init_result) {
            return instance;
        }

        if (log.isErrorEnabled()) {
            log.error("init database connection parameters fail!");
            log.error("please correct database connection parameters and restart application !");
        }
        return null;
    }

    /**
     * 函数功能: 网路中断或管理数据源的程序重启，数据库重启等会导致数据源失效或连接失效， 本函数负责消除这种意外，重新获取有效的数据库连接
     * 
     * @param interval - 尝试间隔，每次尝试失败后会当前线程会睡眠 interval 秒
     * @param times - 尝试次数
     * @return 超过指定次数尝试获取连接失败后返回 null ，否则返回得到的 Connection 连接对象
     */
    public Connection restoreConnection(long interval, long times) {
        while (times-- > 0) {
            try {
                Connection conn = getConnection();
                if (conn != null) {
                    if (log.isInfoEnabled()) {
                        log.info("redo get database connection ok!");
                    }
                    return conn;
                }
                if (isContainerMode || (!isContainerMode && isAppConnPoolMode)) {
                    datasource = null;
                    context = null;
                }
            } catch (Exception e) {
                if (log.isErrorEnabled()) {
                    log.error("get database connection fail!", e);
                }
            }
            Sleep(interval);
        }
        return null;
    }

    /**
     * 函数功能: 获取一个 Connection 对象
     * 
     * @return Connection连接对象
     */
    public Connection getConnection() {
        Connection conn = null;
        try {

            if (isContainerMode) {
                synchronized (instance) {
                    if (datasource == null)
                        datasource = lookupDataSource();
                    conn = datasource.getConnection();
                }
            } else if (isAppConnPoolMode) {
                synchronized (instance) {
                    if (datasource == null)
                        datasource = getApplicationDatasource();
                    conn = datasource.getConnection();
                }
            } else {
                Class.forName(jdbc_driver).newInstance();
                conn = DriverManager.getConnection(jdbc_url, jdbc_user, jdbc_password);
            }
            conn.setAutoCommit(isAutoCommit);
            if (log.isInfoEnabled()) {
                if (isContainerMode) {
                    if (log.isDebugEnabled()) {
                        log.debug("get connection from container ok!");
                    }
                } else if (isAppConnPoolMode) {
                    if (log.isDebugEnabled()) {
                        log.debug("get application connection from pool ok!");
                    }
                } else {
                    if (log.isDebugEnabled()) {
                        log.debug("get application connection ok!");
                    }
                }
            }
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                if (isContainerMode) {
                    if (log.isErrorEnabled()) {
                        log.error("get connection from container fail!", e);
                    }
                } else if (isAppConnPoolMode) {
                    if (log.isErrorEnabled()) {
                        log.error("get application connection from pool fail!", e);
                    }
                } else {
                    if (log.isErrorEnabled()) {
                        log.error("get application connection fail!", e);
                    }
                }
            }
        }
        return conn;
    }

    public static boolean commit(Connection conn) {
        try {
            if (conn != null)
                conn.commit();
            return true;
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("commit database fail!", e);
            }
            return false;
        }
    }

    public static boolean rollback(Connection conn) {
        try {
            if (conn != null)
                conn.rollback();
            return true;
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("rollback database fail!", e);
            }
            return false;
        }
    }

    /**
     * 函数功能: 关闭 Connection 对象
     * 
     * @param conn - 要被关闭的数据库连接对象
     */
    public static boolean closeConnection(Connection conn) {
        try {
            if (conn != null)
                conn.close();
            return true;
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("close database connection fail!", e);
            }
            return false;
        }
    }


    /**
     * 函数功能: 判断一个 Connection 对象是否有效（由网路中断、DBMS关闭或其它原因导致）
     * 
     * @param conn - Connection 对象
     * @return 能完整正确执行常量查询语句返回 true， 否则返回 false
     */
    public static boolean isValidConnection(Connection conn) {
        Statement stmt = null;
        ResultSet rlst = null;
        try {
            stmt = conn.createStatement();
            rlst = stmt.executeQuery("SELECT 1 FROM DUAL");
            return true;
        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error("test valid database connection fail!", e);
            }
            return false;
        } finally {
            closeResultSet(rlst);
            closeStatement(stmt);
        }
    }

    /**
     * 函数功能: 构造分页 select 语句
     * 
     * @param sql - select 查询语句
     * @param pageNo - 页号
     * @param pageSize - 页大小
     * @return 返回能获取指定页号数据的 select 语句
     */
    public static String addPageRule(String sql, int pageNo, int pageSize) {
        StringBuffer buf = new StringBuffer();
        buf.append("select * from (select rownum rownum_, row_.* from (");
        buf.append(sql);
        buf.append(") row_ where rownum <= ");
        buf.append(pageNo * pageSize);
        buf.append(") where rownum_ > ");
        buf.append((pageNo - 1) * pageSize);
        return buf.toString();
    }

    /**
     * 函数功能: 构造分页 select 语句（注意：第一个pstmt设置的参数为结束记录数endNumber，第二个参数为起始记录数startNumber）<br>
     * 分页语句形如：select * from (select rownum rownum_, row_.* from * (sql) row_ where rownum <= ？)
     * where rownum_ > * ？<br>
     * 使用时需要设置起记录数和结束记录数，并使用PreparedStatement进行设置<br>
     * PreparedStatement pstmt;<br>
     * 其中paqeNo为页码，pageSize为每页记录数<br>
     * startNumber=pageNo * pageSize;<br>
     * endNumber=(pageNo - 1) * pageSize;<br>
     * pstmt.setInt(1, endNumber);<br>
     * pstmt.setInt(2, startNumber);<br>
     * 
     * @param sql - select 查询语句
     * @return 返回能获取指定页号数据的 select 的PreparedStatement语句
     */
    public static String addPageRulePreparedStatement(String sql) {
        StringBuffer buf = new StringBuffer();
        buf.append("select * from (select rownum rownum_, row_.* from (");
        buf.append(sql);
        buf.append(") row_ where rownum <= ? ");
        buf.append(") where rownum_ > ? ");
        String pageSql = buf.toString();
        return pageSql;
    }

    /**
     * 获取指定查询语句包含的结果数
     * 
     * @param select
     * @param conn
     * @return
     * @throws Exception
     */
    public static int getRecorderNumForSelect(String select, Connection conn) throws Exception {
        select = String.format(" SELECT count(1) FROM ( %s ) tmp_name_for_count", select);
        return CommDao.queryForInteger(conn, select);
    }

    // /////////////////////////////////////////////////////////////////////////////////
    //
    // 下面的函数 closeConnection、closeStatement、closeResultSet 帮助关闭数据库资源；
    // 这4个函数做了非 null 判断，并且进行错误日志，可以帮助程序员省却很多代码。

    /**
     * 函数功能: 关闭 Statement 对象
     * 
     * @param stmt - 要被关闭的语句对象
     */
    public static void closeStatement(Statement stmt) {
        try {
            if (stmt != null)
                stmt.close();
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("close database statement fail!", e);
            }
        }
    }

    /**
     * 函数功能: 关闭 ResultSet 对象
     * 
     * @param rlst - 要被关闭的游标对象
     */
    public static void closeResultSet(ResultSet rlst) {
        try {
            if (rlst != null)
                rlst.close();
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("close database result set fail!", e);
            }
        }
    }

    /**
     * 函数功能: 关闭 CallableStatement 对象
     * 
     * @param proc - 要被关闭的存储过程对象
     */
    public static void closeCallableStatement(CallableStatement proc) {
        try {
            if (proc != null)
                proc.close();
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("close database result set fail!", e);
            }
        }
    }

    /***************************************************************************
     * 
     * 数据库连接获取方式参数初始化
     * 
     **************************************************************************/
    // 通用设置
    private boolean isAutoCommit = false;

    // 是否容器模式,
    // true - 容器模式
    // false - 应用程序模式
    private boolean isContainerMode = false;

    private String containerProduct = null;

    private String jndi_name = null;

    // 应用程序模式下，是否连接池模式
    // true - 连接池模式
    // false - jdbc模式
    private boolean isAppConnPoolMode = false;

    // 应用程序模式下，jdbc模式所需要的参数
    private String jdbc_driver = null;

    private String jdbc_url = null;

    private String jdbc_user = null;

    private String jdbc_password = null;

    // 应用程序模式下，pool模式所需要的参数
    // private String pool_driver = null;
    // private String pool_url = null;
    // private String pool_user = null;
    // private String pool_password = null;
    // private int pool_transaction_isolation = -1;
    // private int pool_max_active = -1;
    // private int pool_min_idle = -1;
    // private int pool_max_idle = -1;
    // private boolean pool_prepared_statements = false;
    // private int pool_max_open_prepared_statements = -1;

    // 容器模式下，weblogic模式所需要的参数
    private String weblogic_factory = null;

    private String weblogic_url = null;

    private String weblogic_user = null;

    private String weblogic_password = null;

    // 容器模式下，tomcat模式所需要的参数

    // 容器模式下，resin模式所需要的参数

    private void init() throws Exception {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = null;
        InputStream in = null;
        try {
            in = getResourceAsStream(_xmlFilePath);
            document = builder.parse(in);
            Element root = document.getDocumentElement();
            Element comm_dao_dbconn = (Element) root.getElementsByTagName("comm-dao-dbconn").item(0);

            String obtain_mode = comm_dao_dbconn.getAttribute("obtain-mode");
            obtain_mode = obtain_mode == null ? "" : obtain_mode.trim().toLowerCase();
            isContainerMode = "container".equalsIgnoreCase(obtain_mode);
            if (isContainerMode) {
                Element container = (Element) comm_dao_dbconn.getElementsByTagName("container").item(0);
                containerProduct = container.getAttribute("container-product").trim().toLowerCase();
                if ("tomcat".equals(containerProduct)) {
                    initContainerTomcatJndi(container);
                } else if ("resin".equals(containerProduct)) {
                    initContainerResinJndi(container);
                } else {
                    initContainerWeblogicJndi(container);
                }
            } else {
                Element application = (Element) comm_dao_dbconn.getElementsByTagName("application").item(0);
                String app_conn_mode = application.getAttribute("app-conn-mode");
                app_conn_mode = app_conn_mode == null ? "" : app_conn_mode.trim();
                isAppConnPoolMode = "pool".equalsIgnoreCase(app_conn_mode);
                if (isAppConnPoolMode) {
                    initAppPoolParameter(application);
                } else {
                    initAppJdbcParameter(application);
                }
            }
        } finally {
            if (in != null) {
                in.close();
            }
        }
    }


    public void initContainerTomcatJndi(Element container) throws Exception {
        Element tomcat = (Element) container.getElementsByTagName("tomcat").item(0);

        Element elm = (Element) tomcat.getElementsByTagName("jndi-name").item(0);
        String str = elm.getFirstChild().getNodeValue().trim();
        jndi_name = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) tomcat.getElementsByTagName("auto-commit").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        isAutoCommit = "true".equalsIgnoreCase(isElmValEncrypted(elm) ? decrypt(str) : str);
    }

    public void initContainerResinJndi(Element container) throws Exception {
        Element resin = (Element) container.getElementsByTagName("resin").item(0);

        Element elm = (Element) resin.getElementsByTagName("jndi-name").item(0);
        String str = elm.getFirstChild().getNodeValue().trim();
        jndi_name = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) resin.getElementsByTagName("auto-commit").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        isAutoCommit = "true".equalsIgnoreCase(isElmValEncrypted(elm) ? decrypt(str) : str);
    }

    public void initContainerWeblogicJndi(Element container) throws Exception {
        Element weblogic = (Element) container.getElementsByTagName("weblogic").item(0);

        Element elm = (Element) weblogic.getElementsByTagName("jndi-name").item(0);
        String str = elm.getFirstChild().getNodeValue().trim();
        jndi_name = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) weblogic.getElementsByTagName("factory").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        weblogic_factory = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) weblogic.getElementsByTagName("AUTHENTICATE_URL").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        weblogic_url = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) weblogic.getElementsByTagName("user").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        weblogic_user = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) weblogic.getElementsByTagName("password").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        weblogic_password = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) weblogic.getElementsByTagName("auto-commit").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        isAutoCommit = "true".equalsIgnoreCase(isElmValEncrypted(elm) ? decrypt(str) : str);
    }

    public void initAppPoolParameter(Element application) throws Exception {
        // Element pool = (Element) application.getElementsByTagName("pool").item(0);
        //
        // Element elm = (Element) pool.getElementsByTagName("driver").item(0);
        // String str = elm.getFirstChild().getNodeValue().trim();
        // pool_driver = isElmValEncrypted(elm) ? decrypt(str) : str;
        //
        // elm = (Element) pool.getElementsByTagName("AUTHENTICATE_URL").item(0);
        // str = elm.getFirstChild().getNodeValue().trim();
        // pool_url = isElmValEncrypted(elm) ? decrypt(str) : str;
        //
        // elm = (Element) pool.getElementsByTagName("user").item(0);
        // str = elm.getFirstChild().getNodeValue().trim();
        // pool_user = isElmValEncrypted(elm) ? decrypt(str) : str;
        //
        // elm = (Element) pool.getElementsByTagName("password").item(0);
        // str = elm.getFirstChild()==null ? "" : elm.getFirstChild().getNodeValue().trim();
        // pool_password = isElmValEncrypted(elm) ? decrypt(str) : str;
        //
        // elm = (Element) pool.getElementsByTagName("auto-commit").item(0);
        // str = elm.getFirstChild().getNodeValue().trim();
        // isAutoCommit = "true".equalsIgnoreCase(isElmValEncrypted(elm) ? decrypt(str) : str);
        //
        // elm = (Element) pool.getElementsByTagName("transaction-isolation").item(0);
        // str = elm.getFirstChild().getNodeValue().trim();
        // pool_transaction_isolation = Integer.parseInt(isElmValEncrypted(elm) ? decrypt(str) :
        // str);
        //
        // elm = (Element) pool.getElementsByTagName("max-active").item(0);
        // str = elm.getFirstChild().getNodeValue().trim();
        // pool_max_active = Integer.parseInt(isElmValEncrypted(elm) ? decrypt(str) : str);
        //
        // elm = (Element) pool.getElementsByTagName("min-idle").item(0);
        // str = elm.getFirstChild().getNodeValue().trim();
        // pool_min_idle = Integer.parseInt(isElmValEncrypted(elm) ? decrypt(str) : str);
        //
        // elm = (Element) pool.getElementsByTagName("max-idle").item(0);
        // str = elm.getFirstChild().getNodeValue().trim();
        // pool_max_idle = Integer.parseInt(isElmValEncrypted(elm) ? decrypt(str) : str);
        //
        // elm = (Element) pool.getElementsByTagName("pool-prepared-statements").item(0);
        // str = elm.getFirstChild().getNodeValue().trim();
        // pool_prepared_statements = "true".equalsIgnoreCase(isElmValEncrypted(elm) ? decrypt(str)
        // : str);
        //
        // elm = (Element) pool.getElementsByTagName("max-open-prepared-statements").item(0);
        // str = elm.getFirstChild().getNodeValue().trim();
        // pool_max_open_prepared_statements = Integer.parseInt(isElmValEncrypted(elm) ?
        // decrypt(str) : str);
    }

    public void initAppJdbcParameter(Element application) throws Exception {
        Element jdbc = (Element) application.getElementsByTagName("jdbc").item(0);

        Element elm = (Element) jdbc.getElementsByTagName("driver").item(0);
        String str = elm.getFirstChild().getNodeValue().trim();
        jdbc_driver = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) jdbc.getElementsByTagName("AUTHENTICATE_URL").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        jdbc_url = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) jdbc.getElementsByTagName("user").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        jdbc_user = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) jdbc.getElementsByTagName("password").item(0);
        str = elm.getFirstChild() == null ? "" : elm.getFirstChild().getNodeValue().trim();
        jdbc_password = isElmValEncrypted(elm) ? decrypt(str) : str;

        elm = (Element) jdbc.getElementsByTagName("auto-commit").item(0);
        str = elm.getFirstChild().getNodeValue().trim();
        isAutoCommit = "true".equalsIgnoreCase(isElmValEncrypted(elm) ? decrypt(str) : str);
    }

    /***************************************************************************
     * 
     * 上下文对象和数据源
     * 
     **************************************************************************/
    private static Context context = null;

    private static DataSource datasource = null;

    private DataSource lookupDataSource() {
        try {
            if (context == null) {
                if (containerProduct == null) {
                    return null;
                }
                if ("weblogic".equals(containerProduct)) {
                    Properties properties = new Properties();
                    properties.put(Context.INITIAL_CONTEXT_FACTORY, weblogic_factory);
                    properties.put(Context.PROVIDER_URL, weblogic_url);
                    if (weblogic_user != null && !"".equals(weblogic_user)) {
                        properties.put(Context.SECURITY_PRINCIPAL, weblogic_user);
                        properties.put(Context.SECURITY_CREDENTIALS,
                                weblogic_password == null ? "" : weblogic_password);
                    }
                } else {
                    context = new InitialContext();
                }
            }
            return context == null ? null : (DataSource) context.lookup(jndi_name);
        } catch (NamingException e) {
            if (log.isErrorEnabled()) {
                log.error("JNDI[" + jndi_name + "] search fail！", e);
            }
            return null;
        }
    }

    /**
     * 初始化一个DBCP的DataSource
     * 
     * @return
     */
    private DataSource getApplicationDatasource() {
        return null;
        // BasicDataSource dbcp = new BasicDataSource();
        // dbcp.setDriverClassName(pool_driver);
        // dbcp.setUrl(pool_url);
        // dbcp.setUsername(pool_user);
        // dbcp.setPassword(pool_password);
        // dbcp.setDefaultAutoCommit(isAutoCommit);
        // dbcp.setDefaultTransactionIsolation(pool_transaction_isolation);
        // dbcp.setMaxActive(pool_max_active);
        // dbcp.setMinIdle(pool_min_idle);
        // dbcp.setMaxIdle(pool_max_idle);
        // dbcp.setMaxOpenPreparedStatements(pool_max_open_prepared_statements);
        // dbcp.setPoolPreparedStatements(pool_prepared_statements);
        // return dbcp;
    }

    /***************************************************************************
     * 
     * 查询助手函数
     * 
     **************************************************************************/
    /**
     * 根据指定的sql语句和sql参数列表，执行sql语句
     * 
     * @param sql sql语句
     * @param args sql参数列表
     * @return 执行成功，返回true，否则返回false
     */
    public static boolean execute(Connection conn, final String sql, final Object... args) {
        if (log.isDebugEnabled()) {
            log.debug("execute " + formatSql(sql, args));
        }

        PreparedStatement ps = null;
        try {
            ps = conn.prepareStatement(sql);
            prepareStatement(ps, args);
            return ps.execute();
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("execute error, " + formatSql(sql, args), e);
            }
            throw new RuntimeException(e);
        } finally {
            CommDao.closeStatement(ps);
        }
    }

    /**
     * 根据指定的sql语句和参数列表，执行查询后，第一行第一列的整数值
     */
    public static Integer queryForInteger(Connection conn, final String sql, final Object... args) {
        return (Integer) queryScalar(conn, sql, Integer.class, args);
    }

    /**
     * 根据指定的sql语句和参数列表，执行查询后，第一行第一列的长整数值
     */
    public static Long queryForLong(Connection conn, final String sql, final Object... args) {
        return (Long) queryScalar(conn, sql, Long.class, args);
    }

    /**
     * 根据指定的sql语句和参数列表，执行查询后，第一行第一列的字符串
     */
    public static String queryForString(Connection conn, final String sql, final Object... args) {
        return (String) queryScalar(conn, sql, String.class, args);
    }

    /**
     * 根据指定的sql语句和参数列表，执行查询后，获取第一行第一列的标量值
     * 
     * @param sql sql语句
     * @param clazz 返回值的类型
     * @param args 参数列表
     * @return
     */

    @SuppressWarnings("rawtypes")
    public static Object queryScalar(Connection conn, final String sql, final Class clazz,
            final Object... args) {
        if (log.isDebugEnabled()) {
            log.debug("query " + formatSql(sql, args));
        }

        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            prepareStatement(ps, args);
            rs = ps.executeQuery();
            if (rs.next()) {
                if (clazz == int.class || clazz == Integer.class) {
                    return Integer.valueOf(rs.getInt(1));
                }
                if (clazz == String.class || clazz == char.class || clazz == Character.class) {
                    return rs.getString(1);
                }
                if (clazz == Long.class || clazz == long.class) {
                    return Long.valueOf(rs.getLong(1));
                }
                int sqlType = CommDao.javaTypeToSqlParameterType(clazz);
                return CommDao.getResultSetValue(rs, 1, sqlType);
            }
            return null;
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("query error, " + formatSql(sql, args), e);
            }
            throw new RuntimeException(e);
        } finally {
            CommDao.closeResultSet(rs);
            CommDao.closeStatement(ps);
        }
    }

    /**
     * 根据指定的sql语句和sql参数列表，执行查询后，返回Map key为columnName, value为值
     * 
     * @param sql
     * @param args
     * @return
     */
    public static Map<String, Object> queryForMap(Connection conn, final String sql, final Object... args) {
        return queryForMap(conn, sql, false, args);
    }

    /**
     * 根据指定的sql语句和sql参数列表，执行查询后，返回Map key
     * 如果transColumnName为true，key为column名去掉下划线后的首字母小写，大小写混排的字符串,相当于bean的property name; 否则key为列名
     * value为其值
     * 
     * @param sql
     * @param transColumnName 是否转换列名
     * @param args
     * @return 如果查询结果行数>=1，返回第一行的数据；否则返回 null
     * @throws SQLException
     */
    public static Map<String, Object> queryForMap(Connection conn, final String sql,
            final boolean transColumnName, final Object... args) {
        if (log.isDebugEnabled()) {
            log.debug("query  " + formatSql(sql, args));
        }
        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            prepareStatement(ps, args);
            rs = ps.executeQuery();
            if (rs.next()) {
                ResultSetMetaData meta = rs.getMetaData();
                return generateMap(rs, meta, transColumnName);
            }
            return null;
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("query error, " + formatSql(sql, args), e);
            }
            throw new RuntimeException(e);
        } finally {
            CommDao.closeResultSet(rs);
            CommDao.closeStatement(ps);
        }
    }

    /**
     * 根据指定的sql语句和sql参数列表，执行查询后，返回List 返回的list的元素为一个Map, key为columnName, value为值
     * 
     * @param sql
     * @param args
     * @return
     */
    public static List<Map<String, Object>> queryForList(Connection conn, final String sql,
            final Object... args) {
        return queryForList(conn, sql, false, args);
    }

    /**
     * 根据指定的sql语句和sql参数列表，执行查询后，返回List 返回的list的元素为map key
     * 如果transColumnName为true，key为column名去掉下划线后的首字母小写，大小写混排的字符串,相当于bean的property name; 否则key为列名
     * value为其值
     * 
     * @param sql
     * @param transColumnName 是否转换列名
     * @param args
     * @return list
     * @throws SQLException
     */
    public static List<Map<String, Object>> queryForList(Connection conn, final String sql,
            boolean transColumnName, final Object... args) {
        if (log.isDebugEnabled()) {
            log.debug("query " + formatSql(sql, args));
        }

        PreparedStatement ps = null;
        ResultSet rs = null;
        try {
            ps = conn.prepareStatement(sql);
            prepareStatement(ps, args);
            rs = ps.executeQuery();
            ResultSetMetaData meta = rs.getMetaData();
            List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
            while (rs.next()) {
                Map<String, Object> map = generateMap(rs, meta, transColumnName);
                result.add(map);
            }
            return result;
        } catch (SQLException e) {
            if (log.isErrorEnabled()) {
                log.error("query error, " + formatSql(sql, args), e);
            }
            throw new RuntimeException(e);
        } finally {
            CommDao.closeResultSet(rs);
            CommDao.closeStatement(ps);
        }
    }

    /**
     * 将sql及参数列表格式化
     * 
     * @param sql
     * @param args
     * @return
     */
    private static String formatSql(String sql, Object... args) {
        StringBuilder sb = new StringBuilder();
        sb.append("sql : ").append(sql);
        if (args != null) {
            int index = 0;
            for (int len = args.length; index < len; index++) {
                Object value = args[index];
                if (index != 0) {
                    sb.append(", ");
                } else {
                    sb.append("   parameters: [");
                }
                sb.append(value);
            }
            if (index != 0) {
                sb.append("]");
            }
        }
        return sb.toString();
    }

    /**
     * 将参数
     * 
     * @param ps
     * @param args
     * @throws SQLException
     */
    private static void prepareStatement(PreparedStatement ps, final Object... args) throws SQLException {
        if (args == null) {
            return;
        }
        for (int index = 0, len = args.length; index < len; index++) {
            Object value = args[index];
            CommDao.setParameterValue(ps, index + 1, value);
        }
    }

    /**
     * 从ResultSet中取值，将当前行的数据转为一个map 返回的map key为column名去掉下划线后的首字母小写，大小写混排的字符串 value为其值
     */
    private static Map<String, Object> generateMap(ResultSet rs, ResultSetMetaData meta,
            boolean transColumnName) throws SQLException {
        int columnCount = meta.getColumnCount();
        Map<String, Object> map = new LinkedHashMap<String, Object>(columnCount);
        for (int columnIndex = 1; columnIndex <= columnCount; columnIndex++) {
            String columnName = CommDao.lookupColumnName(meta, columnIndex);
            int sqlType = meta.getColumnType(columnIndex);
            String key = transColumnName ? CommDao.convertColumnNameToPropertyName(columnName) : columnName;
            Object value = CommDao.getResultSetValue(rs, columnIndex, sqlType);
            map.put(key, value);
        }
        return map;
    }

    /**
     * 取得序列的序列的nextval值
     * 
     * @param sequenceName 序列名
     * @param conn 数据库连接
     * @return 序列值
     * @throws Exception
     */
    public static long getNextval(String sequenceName, Connection conn) throws SQLException {
        String sql = " select " + sequenceName + ".nextval from dual ";
        Statement stmt = null;
        ResultSet rs = null;
        try {
            stmt = conn.createStatement();
            rs = stmt.executeQuery(sql);
            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        } catch (SQLException ex) {
            if (log.isErrorEnabled()) {
                log.error("数据库存取异常， sql:" + sql);
            }
            throw ex;
        } finally {
            CommDao.closeResultSet(rs);
            CommDao.closeStatement(stmt);
        }
    }

    /***************************************************************************
     * 
     * JDBC数据库工具
     * 
     **************************************************************************/
    public static final int TYPE_UNKNOWN = Integer.MIN_VALUE;


    @SuppressWarnings("rawtypes")
    private static Map<Class, Integer> javaTypeToSqlTypeMap = new HashMap<Class, Integer>();

    static {
        javaTypeToSqlTypeMap.put(boolean.class, Integer.valueOf(Types.BOOLEAN));
        javaTypeToSqlTypeMap.put(Boolean.class, Integer.valueOf(Types.BOOLEAN));
        javaTypeToSqlTypeMap.put(byte.class, Integer.valueOf(Types.INTEGER));
        javaTypeToSqlTypeMap.put(Byte.class, Integer.valueOf(Types.INTEGER));
        javaTypeToSqlTypeMap.put(short.class, Integer.valueOf(Types.INTEGER));
        javaTypeToSqlTypeMap.put(Short.class, Integer.valueOf(Types.INTEGER));
        javaTypeToSqlTypeMap.put(int.class, Integer.valueOf(Types.INTEGER));
        javaTypeToSqlTypeMap.put(Integer.class, Integer.valueOf(Types.INTEGER));
        javaTypeToSqlTypeMap.put(long.class, Integer.valueOf(Types.INTEGER));
        javaTypeToSqlTypeMap.put(Long.class, Integer.valueOf(Types.INTEGER));
        javaTypeToSqlTypeMap.put(BigInteger.class, Integer.valueOf(Types.INTEGER));
        javaTypeToSqlTypeMap.put(float.class, Integer.valueOf(Types.DECIMAL));
        javaTypeToSqlTypeMap.put(Float.class, Integer.valueOf(Types.DECIMAL));
        javaTypeToSqlTypeMap.put(double.class, Integer.valueOf(Types.DECIMAL));
        javaTypeToSqlTypeMap.put(Double.class, Integer.valueOf(Types.DECIMAL));
        javaTypeToSqlTypeMap.put(BigDecimal.class, Integer.valueOf(Types.DECIMAL));
        javaTypeToSqlTypeMap.put(java.sql.Date.class, Integer.valueOf(Types.DATE));
        javaTypeToSqlTypeMap.put(java.sql.Time.class, Integer.valueOf(Types.TIME));
        javaTypeToSqlTypeMap.put(java.sql.Timestamp.class, Integer.valueOf(Types.TIMESTAMP));
        javaTypeToSqlTypeMap.put(Blob.class, Integer.valueOf(Types.BLOB));
        javaTypeToSqlTypeMap.put(Clob.class, Integer.valueOf(Types.CLOB));
    }

    /**
     * 根据参数值和索引，将参数设置到PreparedStatement
     * 
     * @param ps
     * @param paramIndex
     * @param inValue
     * @throws SQLException
     */

    @SuppressWarnings("rawtypes")
    public static void setParameterValue(PreparedStatement ps, int paramIndex, final Object inValue)
            throws SQLException {
        Class javaType = (inValue != null) ? inValue.getClass() : null;
        int sqlType = (inValue != null) ? javaTypeToSqlParameterType(javaType) : Types.NULL;
        if (log.isTraceEnabled()) {
            String message = String.format(
                    "Setting SQL statement parameter value: column index %d, parameter value [%s], value class [%s], sql type [%s]",
                    paramIndex, inValue, (javaType != null) ? javaType.getName() : "null",
                    sqlType == CommDao.TYPE_UNKNOWN ? "unknown" : Integer.toString(sqlType));
            log.trace(message);
        }
        if (inValue == null) {
            boolean useSetObject = false;
            try {
                DatabaseMetaData dbmd = ps.getConnection().getMetaData();
                String databaseProductName = dbmd.getDatabaseProductName();
                String jdbcDriverName = dbmd.getDriverName();
                if (databaseProductName.startsWith("Informix")
                        || jdbcDriverName.startsWith("Apache Derby Embedded")) {
                    useSetObject = true;
                } else if (databaseProductName.startsWith("DB2")) {
                    sqlType = Types.VARCHAR;
                }
            } catch (Throwable ex) {
                if (log.isDebugEnabled()) {
                    log.debug("Could not check database or driver name", ex);
                }
            }
            if (useSetObject) {
                ps.setObject(paramIndex, null);
            } else {
                ps.setNull(paramIndex, sqlType);
            }
        } else { // inValue != null
            if (sqlType == Types.VARCHAR || sqlType == Types.LONGVARCHAR
                    || (sqlType == Types.CLOB && isStringValue(javaType))) {
                if (inValue instanceof String) {
                    ps.setString(paramIndex, (String) inValue);
                } else {
                    ps.setString(paramIndex, inValue.toString());
                }
            } else if (sqlType == Types.INTEGER) {
                ps.setLong(paramIndex, ((Number) inValue).longValue());
            } else if (sqlType == Types.DECIMAL || sqlType == Types.NUMERIC) {
                if (inValue instanceof BigDecimal) {
                    ps.setBigDecimal(paramIndex, (BigDecimal) inValue);
                } else {
                    ps.setObject(paramIndex, inValue, sqlType);
                }
            } else if (sqlType == Types.DATE) {
                if (inValue instanceof java.util.Date) {
                    if (inValue instanceof java.sql.Date) {
                        ps.setDate(paramIndex, (java.sql.Date) inValue);
                    } else {
                        ps.setDate(paramIndex, new java.sql.Date(((java.util.Date) inValue).getTime()));
                    }
                } else if (inValue instanceof Calendar) {
                    Calendar cal = (Calendar) inValue;
                    ps.setDate(paramIndex, new java.sql.Date(cal.getTime().getTime()), cal);
                } else {
                    ps.setObject(paramIndex, inValue, sqlType);
                }
            } else if (sqlType == Types.TIME) {
                if (inValue instanceof java.util.Date) {
                    if (inValue instanceof java.sql.Time) {
                        ps.setTime(paramIndex, (java.sql.Time) inValue);
                    } else {
                        ps.setTime(paramIndex, new java.sql.Time(((java.util.Date) inValue).getTime()));
                    }
                } else if (inValue instanceof Calendar) {
                    Calendar cal = (Calendar) inValue;
                    ps.setTime(paramIndex, new java.sql.Time(cal.getTime().getTime()), cal);
                } else {
                    ps.setObject(paramIndex, inValue, sqlType);
                }
            } else if (sqlType == Types.TIMESTAMP) {
                if (inValue instanceof java.util.Date) {
                    if (inValue instanceof java.sql.Timestamp) {
                        ps.setTimestamp(paramIndex, (java.sql.Timestamp) inValue);
                    } else {
                        ps.setTimestamp(paramIndex,
                                new java.sql.Timestamp(((java.util.Date) inValue).getTime()));
                    }
                } else if (inValue instanceof Calendar) {
                    Calendar cal = (Calendar) inValue;
                    ps.setTimestamp(paramIndex, new java.sql.Timestamp(cal.getTime().getTime()), cal);
                } else {
                    ps.setObject(paramIndex, inValue, Types.TIMESTAMP);
                }
            } else if (sqlType == CommDao.TYPE_UNKNOWN) {
                if (isStringValue(javaType)) {
                    ps.setString(paramIndex, inValue.toString());
                } else if (isDateValue(javaType)) {
                    ps.setTimestamp(paramIndex, new java.sql.Timestamp(((java.util.Date) inValue).getTime()));
                } else if (inValue instanceof Calendar) {
                    Calendar cal = (Calendar) inValue;
                    ps.setTimestamp(paramIndex, new java.sql.Timestamp(cal.getTime().getTime()));
                } else {
                    // Fall back to generic setObject call without SQL type specified.
                    ps.setObject(paramIndex, inValue);
                }
            } else {
                // Fall back to generic setObject call with SQL type specified.
                ps.setObject(paramIndex, inValue, sqlType);
            }
        }
    }

    /**
     * 由Java Type得到sql Type
     * 
     * @param javaType
     * @return
     */

    @SuppressWarnings("rawtypes")
    public static int javaTypeToSqlParameterType(Class javaType) {
        Integer sqlType = javaTypeToSqlTypeMap.get(javaType);
        if (sqlType != null) {
            return sqlType.intValue();
        }
        if (Number.class.isAssignableFrom(javaType)) {
            return Types.NUMERIC;
        }
        if (isStringValue(javaType)) {
            return Types.VARCHAR;
        }
        if (isDateValue(javaType) || Calendar.class.isAssignableFrom(javaType)) {
            return Types.TIMESTAMP;
        }
        return TYPE_UNKNOWN;
    }

    /**
     * Check whether the given value can be treated as a String value.
     */

    @SuppressWarnings("rawtypes")
    private static boolean isStringValue(Class inValueType) {
        // Consider any CharSequence (including JDK 1.5's StringBuilder) as
        // String.
        return (CharSequence.class.isAssignableFrom(inValueType)
                || StringWriter.class.isAssignableFrom(inValueType));
    }

    /**
     * Check whether the given value is a <code>java.util.Date</code> (but not one of the
     * JDBC-specific subclasses).
     */

    @SuppressWarnings("rawtypes")
    private static boolean isDateValue(Class inValueType) {
        return (java.util.Date.class.isAssignableFrom(inValueType)
                && !(java.sql.Date.class.isAssignableFrom(inValueType)
                        || java.sql.Time.class.isAssignableFrom(inValueType)
                        || java.sql.Timestamp.class.isAssignableFrom(inValueType)));
    }

    /**
     * 在ResultSetMetaData中，查找指定列的列名
     * 
     * @param resultSetMetaData
     * @param columnIndex
     * @return
     * @throws SQLException
     */
    public static String lookupColumnName(ResultSetMetaData resultSetMetaData, int columnIndex)
            throws SQLException {
        String name = resultSetMetaData.getColumnLabel(columnIndex);
        if (name == null || name.length() < 1) {
            name = resultSetMetaData.getColumnName(columnIndex);
        }
        return name;
    }

    /**
     * 将含有下划线的列名，使用camel命名法将其转换成相应的Java bean的属性名 例如，logon_name将被转换成logonName
     * 
     * @param name 要转换的列名
     * @return 转换后的属性名
     */
    public static String convertColumnNameToPropertyName(String name) {
        if (name == null || name.length() < 1) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        boolean nextIsUpper = false;
        if (name.length() > 1 && name.substring(1, 2).equals("_")) {
            result.append(name.substring(0, 1).toUpperCase());
        } else {
            result.append(name.substring(0, 1).toLowerCase());
        }
        for (int i = 1, len = name.length(); i < len; i++) {
            char c = name.charAt(i);
            if (c == '_') {
                nextIsUpper = true;
                continue;
            }
            if (nextIsUpper) {
                result.append(Character.toUpperCase(c));
                nextIsUpper = false;
            } else {
                result.append(Character.toLowerCase(c));
            }

        }
        return result.toString();
    }

    /**
     * 根据sqlType和索引，从ResultSet中取得值
     * 
     * @param rs
     * @param columnIndex
     * @param sqlType
     * @return
     * @throws SQLException
     */
    public static Object getResultSetValue(ResultSet rs, int columnIndex, int sqlType) throws SQLException {
        if (sqlType == Types.VARCHAR || sqlType == Types.LONGVARCHAR) {
            return rs.getString(columnIndex);
        }
        Object obj = rs.getObject(columnIndex);
        if (obj == null) {
            return null;
        }
        if (obj instanceof Blob) {
            return rs.getBytes(columnIndex);
        }
        if (obj instanceof Clob) {
            return rs.getString(columnIndex);
        }
        if (obj.getClass().getName().startsWith("oracle.sql.TIMESTAMP")) {
            return rs.getTimestamp(columnIndex);
        }
        if (obj.getClass().getName().startsWith("oracle.sql.DATE")) {
            String metaDataClassName = rs.getMetaData().getColumnClassName(columnIndex);
            if ("java.sql.Timestamp".equals(metaDataClassName)
                    || "oracle.sql.TIMESTAMP".equals(metaDataClassName)) {
                return rs.getTimestamp(columnIndex);
            }
            return rs.getDate(columnIndex);
        }
        if (obj instanceof java.sql.Date) {
            if ("java.sql.Timestamp".equals(rs.getMetaData().getColumnClassName(columnIndex))) {
                return rs.getTimestamp(columnIndex);
            }
        }
        return obj;
    }

    /***************************************************************************
     * 
     * SQL脚本导入器, 构建测试环境时使用
     * 
     * @throws SQLException
     * 
     **************************************************************************/
    public static void execute(Connection conn, String fileEncoding, List<String> scripts) throws Exception {
        if (scripts == null || scripts.size() == 0) {
            return;
        }
        Statement stmt = null;
        try {
            fileEncoding = fileEncoding == null ? "UTF-8" : fileEncoding;
            stmt = conn.createStatement();
            Iterator<String> iter = scripts.iterator();
            while (iter.hasNext()) {
                String scriptFile = iter.next();
                importScript(scriptFile, stmt, fileEncoding);
            }
        } finally {
            CommDao.closeStatement(stmt);
        }
    }

    public static boolean isOracle(Connection conn) throws SQLException {
        String dbpn = conn.getMetaData().getDatabaseProductName().toLowerCase();
        return "oracle".equalsIgnoreCase(dbpn);
    }

    public static boolean isMysql(Connection conn) throws SQLException {
        String dbpn = conn.getMetaData().getDatabaseProductName().toLowerCase();
        return "mysql".equalsIgnoreCase(dbpn);
    }

    private static void importScript(String importFile, Statement statement, String fileEncoding)
            throws IOException {
        if (log.isInfoEnabled()) {
            log.info("Executing import script: " + importFile);
        }

        BufferedReader reader = null;
        try {
            fileEncoding = fileEncoding == null ? "UTF-8" : fileEncoding;
            reader = new BufferedReader(new InputStreamReader(getResourceAsStream(importFile), fileEncoding));
            StringBuilder buff = new StringBuilder();
            String line = null;
            String sql = null;
            int pos = 0;
            int commentStart = 0;
            int commentEnd = 0;
            while ((line = reader.readLine()) != null) {
                try {
                    if (line.length() == 0 || line.startsWith("--") || line.startsWith("//")) {
                        continue;
                    }
                    buff.append(line).append('\n');
                    commentStart = buff.indexOf("/*");
                    commentEnd = buff.indexOf("*/");
                    if (commentStart >= 0) {
                        if (commentEnd >= 0) { // 注释结束, 删除注释
                            buff.delete(commentStart, commentEnd + 2);
                        } else { // 多行注释未结束,继续读取下一行
                            continue;
                        }
                    }
                    pos = buff.indexOf(";");
                    if (pos == -1) {
                        continue;
                    }
                    sql = buff.substring(0, pos).trim();
                    buff.delete(0, pos + 1);
                    if (log.isTraceEnabled()) {
                        log.trace("excute sql : \n" + sql);
                    }
                    statement.execute(sql);
                } catch (SQLException e) {
                    if (log.isErrorEnabled()) {
                        log.error("import script execution error! sql is :\n" + sql, e);
                    }
                }
            }
        } finally {
            if (reader != null) {
                reader.close();
            }

        }
    }

    /***************************************************************************
     * 
     * 助手函数区
     * 
     **************************************************************************/

    /**
     * 函数功能: 让运行本函数的线程睡眠指定时间
     * 
     * @param seconds - 秒数
     */
    @SuppressWarnings("static-access")
    private void Sleep(long seconds) {
        try {
            log.info("current thread start sleep " + seconds + " seconds...");
            Thread.currentThread().sleep(seconds * 1000);
            log.info("current thread end sleep.");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 获取指定资源的输入流对象
     * 
     * @param resource
     * @return
     */
    private static InputStream getResourceAsStream(String resource) {
        String stripped = resource.startsWith("/") ? resource.substring(1) : resource;
        InputStream stream = null;
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        if (classLoader != null) {
            stream = classLoader.getResourceAsStream(stripped);
        }
        if (stream == null) {
            stream = CommDao.class.getResourceAsStream(resource);
        }
        if (stream == null) {
            stream = CommDao.class.getClassLoader().getResourceAsStream(stripped);
        }
        if (stream == null) {
            throw new RuntimeException(resource + " not found");
        }
        return stream;
    }

    /**
     * 元素节点的值是否被加密
     * 
     * @param node - 元素节点对象
     * @return - true - 其属性 encrypt 的值等于 "true" 或 "yes"， false - 其它值或encrypt属性不存在
     */
    private static boolean isElmValEncrypted(Node node) {
        try {
            NamedNodeMap nodeMap = node.getAttributes();
            if (nodeMap == null)
                return false;
            Node attrNode = nodeMap.getNamedItem("encrypt");
            if (attrNode == null)
                return false;
            String encrypt = attrNode.getNodeValue();
            encrypt = encrypt == null ? "" : encrypt.trim().toLowerCase();
            return "true".equals(encrypt) || "yes".equals(encrypt);
        } catch (Exception e) {
            return false;
        }
    }

    /*************************************************************
     * 
     * 加密、解密函数、变量等
     * 
     *************************************************************/

    public static final String DEFAULT_PASSWORD = "3588BC75C19C9120464A1F2932B6ED0A";

    public static byte[] hex2byte(String strhex) {
        if (strhex == null) {
            return null;
        }
        int l = strhex.length();
        if (l % 2 != 0) {
            return null;
        }
        byte[] b = new byte[l / 2];
        for (int i = 0; i != l / 2; i++) {
            b[i] = (byte) Integer.parseInt(strhex.substring(i * 2, i * 2 + 2), 16);
        }
        return b;
    }

    public static String byte2hex(byte[] b) {
        StringBuilder sb = new StringBuilder();
        for (int n = 0; n < b.length; n++) {
            String stmp = Integer.toHexString(b[n] & 0xff);
            if (stmp.length() == 1) {
                sb.append("0");
            }
            sb.append(stmp);
        }
        return sb.toString().toUpperCase();
    }

    public static Key getKey(String password) {
        byte[] bytes = hex2byte(password);
        return new SecretKeySpec(bytes, "AES");
    }

    /**
     * 加密一个字符串
     * 
     * @param input
     * @return
     * @throws Exception
     */
    public static String encrypt(String input) throws Exception {
        if (null == input)
            throw new Exception("输入为null");
        Cipher cp = Cipher.getInstance("AES");
        cp.init(Cipher.ENCRYPT_MODE, getKey(DEFAULT_PASSWORD));
        byte[] bytes = cp.doFinal(input.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(bytes);
    }

    /**
     * 解密一个字符串
     * 
     * @param input
     * @return
     * @throws Exception
     */
    public static String decrypt(String input) throws Exception {
        if (null == input)
            throw new Exception("输入为null");
        byte[] bytes = Base64.decodeBase64(input);
        Cipher cp = Cipher.getInstance("AES");
        cp.init(Cipher.DECRYPT_MODE, getKey(DEFAULT_PASSWORD));
        bytes = cp.doFinal(bytes);
        return new String(bytes, StandardCharsets.UTF_8);
    }

    /**
     * 本类提供的加密串获取函数
     */
    @SuppressWarnings("deprecation")
    public static void main(String[] args) {
        String END_FLAG = "End!!!";
        String ENTER = "\n";

        try {
            System.out.println("请输入明文, 一行上剪除空白符后等于 End!!! 表示输入结束:");
            StringBuffer buf = new StringBuffer();
            while (true) {
                DataInputStream dis = new DataInputStream(System.in);
                String line = dis.readLine();
                if (END_FLAG.equals(line.trim()))
                    break;
                buf.append(line).append(ENTER);
            }
            String input = buf.substring(0, buf.length() - ENTER.length());
            System.out.println("输入结束！你提供的有效明文是：");
            System.out.println("*********************************************************************");
            System.out.println(input);
            System.out.println("*********************************************************************");
            System.out.println("\n\n");

            String encryptStr = encrypt(input);
            System.out.println("加密结束！得到的密文是：");
            System.out.println("*********************************************************************");
            System.out.println(encryptStr);
            System.out.println("*********************************************************************");
            System.out.println("\n\n");

            String decryptStr = decrypt(encryptStr);
            System.out.println("解密结束！还原的明文是：");
            System.out.println("*********************************************************************");
            System.out.println(decryptStr);
            System.out.println("*********************************************************************");
            System.out.println("\n\n");

            System.out.println("加解密成功结束！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
