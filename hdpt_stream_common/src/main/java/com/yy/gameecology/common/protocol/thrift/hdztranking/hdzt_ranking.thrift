//namespace java com.yy.gameecology.common.protocol.thrift.hdztranking
namespace java com.yy.gameecology.common.protocol.thrift.hdztranking

//业务标识（可用于代替模板类型）
enum BusiId {
	HDZT				= 1, //活动中台
	HDZT_RANKING		= 2, //活动中台-榜单
	HDZT_AWARD			= 3, //活动中台-发奖

	YY_PAY_CENTER		= 100, //支付中心
	YY_BASE_PLATFORM	= 101, //礼物基础平台

	GAME_ECOLOGY		= 200, //游戏生态
	GAME_GUILD			= 300, //游戏公会

	GAME_BABY			= 400, //游戏宝贝
	MAKE_FRIEND			= 500, //交友
	YUE_ZHAN			= 600, //约战
	YU_LE				= 700, //娱乐
	ZHUI_WAN			= 800, //追玩
	PEI_WAN				= 900, //陪玩
	SKILL_CARD          = 810, // 技能卡
}

//角色类型（从所有业务中抽象出来的）
enum RoleType {
	ANY			= 0,   //通用角色

	USER		= 100, //用户
	ANCHOR		= 200, //主播
	WAITER	    = 201, //陪玩接待
	HOST		= 300, //主持
	GUILD		= 400, //公会
	HALL		= 401, //公会厅（子频道）
	PWTUAN		= 402, //陪玩团（公会和团的关系是 1:n）
}

//榜单类型(和成员角色组成有关联）, 中台会提供下面类型的相关功能的默认实现
enum RankingType {
	//1.通用类型, 榜单成员可含任意多个角色，角色成员可来自多个业务
	ANY = 0,	//通用榜

	//2.单角色榜，角色成员可来自多个业务
	ONE_USER		= 1000,	//用户榜
	ONE_ANCHOR		= 1001,	//主播榜
	ONE_HOST		= 1002,	//主持榜
	ONE_GUILD		= 1003,	//公会榜
	ONE_HALL		= 1004,	//公会厅榜

    //3.双角色 cp 榜，角色成员可来自多个业务，成员排列顺序同枚举名字
    TWO_USER_ANCHOR	= 2000,	//用户+主播榜
    TWO_USER_HOST	= 2001,	//用户+主持榜
    TWO_USER_GUILD	= 2002,	//用户+公会榜
    TWO_ANCHOR_GUILD= 2003,	//主播+公会榜
    TWO_HOST_GUILD	= 2004,	//主持+公会榜
    TWO_ANCHOR_HOST	= 2005,	//主播+主持榜
    TWO_USER_HALL	= 2006,	//用户+公会厅榜
    TWO_ANCHOR_HALL	= 2007,	//主播+公会厅榜
    TWO_HOST_HALL	= 2008,	//主持+公会厅榜
    TWO_GUILD_HALL	= 2009,	//公会+公会厅榜

	//4. 三角色以上暂不支持，可用ANY + 定制实现（后期看情况提供一些 3p 标准实现）
	// 若有三角色 cp 榜，其枚举值从 3000开始，四角色 cp 榜从 4000开始，以此类推，做到见值知意
}

struct SimpleResult
{
	1:i32 code,                 //结果码，0-成功，非零-失败或其他含义
	2:string reason,            //成功或失败的提示信息
	99:map<string, string> data, //成功时返回的数据，协商使用
}

struct Rank
{
	1:string member,//榜单成员
	2:i32 rank,     //排名（第一名为 1）
	3:i64 score,  	//分值
	4:string itemDesc,
}

struct QuerySingleRankingResult
{
	1:i32 code,			//结果码，0-成功，非零-失败或其他含义
	2:string reason,	//成功或失败的提示信息
	3:list<Rank> data,	//成功时返回的榜单数据， 第一层key：rankId，第二次key：zset key
	4:Rank pointedRank,		//返回 pointedMember 对应的榜单情况
}

/**
* 榜单查询业务参数
**/
struct QueryRankingRequest
{
    1:i64 actId,			//活动id
    2:i64 rankingId,		//榜单id
    3:i64 rankingCount,		//取前几名
    4:i64 phaseId,			//阶段id,0-代表不分阶段
    5:string dateStr,		//小时榜、日榜的时候才有值，yyyyMMdd或yyyyMMddHH,空的时候代表不分日期
    6:string findSrcMember,	//要查找的榜单贡献来源，例如要查主播送礼的土豪贡献，这里填主播id,rankType=3的时候要有值
    7:string rankType,		//1-当前比拼的榜 2-阶段晋级过来的初始名单（此时dateStr不生效） 3- 榜单贡献来源
	8:string pointedMember,	//指定的成员，用于获取指定成员的 积分 和 排名，非必填，空则忽略
    99:map<string, string> extData, //扩展参数
}

struct QueryMulRankingResult
{
	1:i32 code,			//结果码，0-成功，非零-失败或其他含义
	2:string reason,	//成功或失败的提示信息
	3:map<string, map<string, list<Rank>>> data, //成功时返回的榜单数据， 第一层key：rankId / hour key
}

/**
* 榜单查询业务参数
**/
struct QueryTimeRankingRequest
{
    1:i64 actId, //活动id
    2:i64 rankingId, //榜单id
    3:i64 rankingCount, //取前几名
    4:i64 phaseId, //阶段id,0-代表不分阶段
    99:map<string, string> extData //扩展参数
}

struct RankingConfigRequest
{
    1:i64 actId,			//活动id
    2:list<i64> rankingIds,	//榜单id rankingIds为空时，读取整个活动配置的榜单数据
    3:i64 currentDate,		//业务当前时间
    4:i64 curPhaseId,		//当前阶段ID，currentDate可以自动定位阶段，currentDate同时存在的时候，优先级curPhaseId>currentDate
	99:map<string, string> extData,	//扩展参数
}

//阶段信息
struct RankingPhaseInfo
{
    1:i64 phaseId,			//阶段id
    2:string phaseName,		//阶段名称
    3:i64 beginTime,		//阶段开始时间时间戳，毫秒
    4:i64 endTime,			//阶段结束时间时间戳，毫秒
    5:i64 passCount,		//阶段晋级人数,n进m中的m
    6:i64 totalCount,		//当前参赛人数,n进m中的n
    7:string phaseGroupCode,//阶段分组
    8:string phaseNameShow,	//阶段展示名称
    9:i64 showBeginTime,	//阶段展示开始时间时间戳，毫秒
    10:i64 showEndTime,		//阶段展示结束时间时间戳，毫秒
    11:string extJson, //扩展字段

    99:map<string, string> extData,	//扩展参数
}

//查询阶段信息
struct QueryRankingPhaseRequest
{
    1:i64 actId, //活动id
    99:map<string, string> extData,
}

struct QueryRankingPhaseResponse
{
    1:i32 code,			//结果码，0-成功，非零-失败或其他含义
    2:string reason,	//成功或失败的提示信息
    3:list<RankingPhaseInfo> data,
}

//计榜角色的详细信息
struct RoleDetail {
	1:i64 roleId,
	2:string roleName,
	3:i64 roleType,
	4:i64 roleBusiId,
	5:string roleExtjson,
	6:i64 roleHint,
	7:string roleHintExtjson,
}

//榜单的角色、采集项目配置
struct RoleItemConfig {
	1:list<list<RoleDetail>> roles,	//计榜角色
	2:string rolesExtjson,
	3:list<string> items,			//计榜项目
	4:string itemAlias,				//项目别称
	5:string itemsExtjson,
}

//榜单信息
struct RankingInfo
{
    1:i64 actId,							//活动id
    2:i64 rankingId,						//榜单id
    3:string rankingName,					//榜单名称，前端展示用
    4:RankingPhaseInfo currentPhase,		//榜单当前阶段信息
    5:map<i64,RankingPhaseInfo> phasesMap,	//榜单的阶段信息
    6:map<string, string> extData,		//扩展参数
    7:i64 showBeginTime,				//展示开始时间
    8:i64 showEndTime					//展示结束时间
    9:i64 calBeginTime,					//计榜开始时间
    10:i64 calEndTime,					//计榜结束时间
    11:i64 timeKey,						//时间分榜: 0-不按时间再分, 1-按日再分，2-按小时再分
    12:string timeKeyBegin,				//时间分榜累计开始时间, time_flag=1,2 时为 HH:mm:ss, 比如 07:08:09
    13:string timeKeyEnd,				//时间分榜累计结束时间, time_flag=1,2 时为 HH:mm:ss, 比如 12:34:56
	14:string rankingExtjson,			//榜单扩展属性字符串
	15:RoleItemConfig roleItemConfig,	//榜单成员、项目信息
	16:i64 rankingType,					//榜单类型
}

struct RankingConfigResponse
{
    1:i32 code, 		//结果码，0-成功，非零-失败或其他含义
    2:string reason,    //成功或失败的提示信息
    3:map<i64, RankingInfo> data,	//榜单配置信息
}

struct ActorQueryItem
{
    1:i64 rankingId,	//榜单id,(非必填，-1 智能读取当前成员所在的榜单)
    2:i64 phaseId,		//阶段id,必填
    3:string actorId,	//成员 id: 公会id,或者主播uid
    4:string dateStr,	//yyyyMMdd yyyyMMddHH 查时间分榜
    5:bool withStatus, //是否查询成员参赛状态
    6:i64 roleType, //角色类型
    7:i64 queryPhaseRank, //不查询赛制晋级的cotest榜单，查询阶段榜(非晋级相关)
    99:map<string, string> extData, //扩展参数
}

//参赛状态查询
struct ActorRankingInfoRequest
{
    1:i64 actId,
    2:list<ActorQueryItem> actorStatus,
    99:map<string, string> extData,
}

struct ActorInfoItem
{
    1:i64 rankingId,	//榜单id
    2:i64 phaseId,		//阶段id,如果留空或者传入0，则代表当前阶段
    3:string actorId,	//成员id uid或者公会id
    4:i32 status,		//对象状态 -1非参赛角色（没在分组名单） 0-正常状态（在晋级线以上） 1-代表有危险（在晋级线以下） 2-被淘汰
    5:i64 score,		//当前积分
    6:i64 rank,			//当前名次
    7:i64 preScore,		//上一名积分
    8:string dateStr,	//yyyyMMdd yyyyMMddHH 查时间分榜
    99:map<string, string> extData, //扩展参数
}

//参赛状态信息/积分信息/过关信息
struct ActorRankingInfoResponse
{
    1:i32 code,			//结果码，0-成功，非零-失败或其他含义
    2:string reason,	//成功或失败的提示信息
    3:list<ActorInfoItem> actorStatus,
    99:map<string, string> extData, //扩展参数
}

//榜单更新请求
struct UpdateRankingRequest
{
	1:i64 busiId,					//发起请求的业务标识
	2:i64 actId,					//活动标识
	3:string seq,					//请求seq，请保证在 busiId + actId 下是唯一的
	4:map<i64, string> actors,		//榜单参与者，key 是参与者角色， val 是具体的参与者标识值
	5:string itemId,				//发生事项标识（可以是礼物ID， 约定的动作ID等等。。。）
	6:i64 count,					//本次事件中 item 的次数
	7:i64 score,					//本次事件中 总积分值（这个必须由业务方算好，因中台并不知道怎么算积分）
	8:i64 timestamp,				//时间戳，精确到毫秒
	9:map<string, i64> roleCounts,	//指定角色组合累计时的项目数量，key是角色组合串如 role1&role2&...，val是数量，累榜发生匹配时优先级高于 count（非必须）
	10:map<string, i64> roleScores, //指定角色组合累计时的项目积分值，key是角色组合串如 role1&role2&...，val是积分值，累榜发生匹配时优先级高于 score（非必须）
	11:map<i64, i64> rankCounts,	//指定榜单累计时的项目数量，key是榜单ID，val是数量，累榜发生匹配时优先级高于 roleCounts（非必须）
	12:map<i64, i64> rankScores,	//指定榜单累计时的项目积分值，key是榜单ID，val是积分值，累榜发生匹配时优先级高于 roleScores（非必须）
	96:string ip,					//用户IP，统计、防刷、等使用（非必须）
	97:string mac,					//用户机器码，统计、防刷、等使用（非必须）
	98:i64 extLong,					//扩展长整形，协商使用
	99:map<string, string> extData, //扩展 map，协商使用
	100:string sign,				//请求参数签名（使用 busiId 和 actId 下的组合key，签名算法待定，目前尚未启用）
}

//榜单更新结果，目前定义的很简单，以后可能会扩充，另外有些结果可能是约定通过MQ返回的
struct UpdateRankingResult
{
	1:i32 code,                 //结果码，0-成功，非零-失败或其他含义
	2:string reason,            //成功或失败的提示信息
	99:map<string, string> data,//成功时返回的数据，协商使用
}


//活动配置信息
struct ActivityInfo{
    1:i64 actId,
    2:i64 busiId,
    3:string actName,
    4:i64 status,
    5:string actBgUrl,
    6:string detailUrl,
    7:i64 beginTime,
    8:i64 endTime,
    9:i64 actType,
    10:i64 rankEndTime,
    11:i64 beginTimeShow,
    12:i64 endTimeShow,
    99:map<string, string> extData,	//扩展 map，协商使用
}

struct ActivityInfoResult
{
	1:i32 code,			//结果码，0-成功，非零-失败或其他含义
	2:string reason,	//成功或失败的提示信息
    3:ActivityInfo data,
}

struct ActivityAttrResult
{
	1:i32 code,			//结果码，0-成功，非零-失败或其他含义
	2:string reason,	//成功或失败的提示信息
    3:map<string,string> data,
    99:map<string, string> extData,	//扩展 map，协商使用
}

struct ActivityInfosResult
{
	1:i32 code,			//结果码，0-成功，非零-失败或其他含义
	2:string reason,	//成功或失败的提示信息
    3:list<ActivityInfo> data,
    99:map<string, string> extData,	//扩展 map，协商使用
}

struct ActivityInfoRequest
{
	1:i64 actId,
    99:map<string, string> extData,	//扩展 map，协商使用
}

struct ActivityInfosRequest
{
    99:map<string, string> extData,	//扩展 map，协商使用
}

struct QueryRankIdItem
{
    1:string memberId,
    2:string memberType,			//非必填
    3:i64 phaseId,
    99:map<string,string> extData,	//扩展 map，协商使用
}

//查询榜单id
struct QueryRankIdRequest
{
   1:i64 actId,
   2:list<QueryRankIdItem> items,
   3:i64 queryType, // 0 -查询主赛程相关 1-查询淘汰赛制相关
   99:map<string, string> extData, //扩展 map，协商使用
}

//查询榜单id
struct QueryRankIdResponse
{
    1:i32 code,							//结果码，0-成功，非零-失败或其他含义
    2:string reason,					//成功或失败的提示信息
    3:map<string,list<i64>> rankIdMap,	//key uid/sid，value rankId
    99:map<string, string> extData,		//扩展 map，协商使用
}

struct EnrollmentInfo
{
    1:i64 actId, 			//活动ID
    2:string memberId,		//成员id
	3:i64 srcRoleId, 		//源角色ID
    4:i64 roleBusiId,		//源角色ID对应的业务ID
	5:i64 roleType,			//源角色ID对应的类型（公会、宝贝、主持 等）
	6:i64 destRoleId,		//报名分组角色ID
    7:i64 status,			//报名状态，1：有效， 0：无效
    8:i64 signSid,			//签约频道
    9:i64 signAsid,			//签约频道短号
    10:i64 startRankId,		//原始赛道榜单id
    11:i64 startPhaseId,	//原始赛道 阶段id
    99:map<string,string> extData, //扩展配置
}

struct QueryEnrollmentInfoRequest
{
	1:i64 actId,					//活动id
    2:list<string> memberIds,		//成员id，此字段有值时 page、pageSize 无效
    3:i32 page,						//非必填
    4:i32 pageSize,					//非必填
    5:i64 roleType,  //nocache = true 时这个参数才生效   200===主播、交友主持 400===公会 401==厅 402===陪玩团 201===陪玩客服接待
    6:bool nocache, //nocache = ture , 不带缓存的实时数据 用户榜单数据上报实时场景,nocache = ture时, roleType、memberIds必填
    99:map<string, string> extData,	//扩展 map，协商使用
}


struct QueryEnrollmentInfoResponse
{
    1:i32 code,                 //结果码，0-成功，非零-失败或其他含义
    2:string reason,            //成功或失败的提示信息
    3:list<EnrollmentInfo> items,
    99:map<string,string> extData,
}

struct QueryUserTaskRequest
{
    1:i64 actId,		//活动id
    2:i64 rankId,		//榜单id
    3:string memberId,	//成员id
    4:i64 phaseId,		//阶段id
    5:string dateStr,	//yyyyMMdd
    99:map<string, string> extData,	//扩展 map，协商使用
}

struct UserTaskItem
{
    1:i64 curTaskId,			//用户所处当前阶段关卡id
    2:i64 curTaskScore,			//用户当前阶段积分
    3:i64 curRound,				//已经过关轮次
    4:i64 curTaskScoreConfig,	//过当前轮次所需积分
    5:string itemId,
    6:string itemName,
    7:string itemUrl,
    8:i64 allTaskScoreConfig,		//单个礼物过当前任务所有关需要总积分
    9:i64 itemScore, //礼物累的积分
    10:i64 allTaskCount, //单个礼物关卡数量
    11:string taskName, //当前任务名称
    99:map<string, string> extData,	//扩展 map，协商使用
}

struct QueryUserTaskResponse
{
    1:i32 code,							//结果码，0-成功，非零-失败或其他含义
    2:string reason,					//成功或失败的提示信息
    3:map<string,UserTaskItem> items,	//key itemid
    4:i64 curRound,						//已经过关轮次
    5:i64 curTaskId,					//用户所处当前阶段关卡id
    99:map<string, string> extData,		//扩展 map，协商使用
}

struct QueryBatchRankingRequest
{

    1:map<string, QueryRankingRequest> batchQuestData //扩展参数
    99:map<string, string> extData, //key 调用方定义，扩展参数
}

struct BatchRankingItem
{
	1:list<Rank> data,	 //成功时返回的榜单数据
	2:Rank pointedRank,	 //返回  pointedMember 对应的榜单情况
	99:map<string, string> extData,		//扩展 map，协商使用
}


struct QueryBatchRankingResult
{
	1:i32 code,			//结果码，0-成功，非零-失败或其他含义
	2:string reason,	//成功或失败的提示信息
	3:map<string, BatchRankingItem> items, //成功时返回的榜单数据，key是调用方传过来的
	99:map<string, string> extData,		//扩展 map，协商使用
}

struct QueryPkInfoRequest
{
	1:i64 actId,		//活动id
    2:i64 srcRankId,    //来源榜单
    3:i64 phaseId,		//阶段id
    4:string dateStr,	//日期索引，yyyyMMdd,  00000000 是默认配置
    5:i32 returnData,  //返回的数据，0 只返回晋级赛排名（默认，性能最优），1 返回晋级赛排名、成员id和晋级赛分数，2 在1的基础上加上当前分数和排名
    6:string queryDateStr //查询当前排名和分数指定的时间，yyyyMMdd，returnData = 2 时选填
    99:map<string, string> extData,	//扩展 map，协商使用
}

struct GroupMemberItem  //分组成员
{
    1:i32 lastRank,     //晋级赛排名（第一名为 1）
    2:string memberId,  //成员id，非必填，指定返回时才会填充
    3:i64 lastScore,  	//晋级赛分值，非必填，指定返回时才会填充
    4:i32 rank,         //当前排名（第一名为 1）非必填，指定返回时才会填充
    5:i64 score,  	    //当前分值，非必填，指定返回时才会填充
    99:map<string,string> extData,	//扩展 map，协商使用
}

struct PkGroupItem       //pk分组
{
    1:string code,	    //分组编码
    2:string name,      //分组名称
    3:string day,       //日期索引，yyyyMMdd,  00000000 是默认配置
	4:list<list<GroupMemberItem>> memberPkItems,	//分组成员pk信息,list<GroupMemberItem> 是一对pk成员
    5:string remark,			    //配置说明
    99:map<string,string> extData,  //扩展配置
}

struct PkInfo                          //pk信息
{
    1:i64 actId, 			            //目标活动
    2:i64 destRankId,		            //目标榜单
    3:i64 phaseId, 		                //目标阶段
    4:i64 srcRankId, 		            //来源榜单
    5:list<i32> pkAward,		        //PK奖励，json形如： [5,1,2]， 表示胜5分，负1分，平2分
    6:list<i32> rankAward,		        //排名奖励，源阶段json形如[5,3,1]，第1名奖5分，第2名奖3分，以此类推
    7:string dateStr,	                //日期索引，yyyyMMdd,  00000000 是默认配置
    8:list<PkGroupItem> pkGroupItems,   //pk分组信息
    9:string remark,			        //配置说明
    99:map<string,string> extData,      //扩展配置
}

struct QueryPkInfoResponse
{
    1:i32 code,							//结果码，0-成功，非零-失败或其他含义
    2:string reason,					//成功或失败的提示信息
    3:PkInfo pkConfig,                  //PK信息
    99:map<string, string> extData,		//扩展 map，协商使用
}

struct QueryQualificationGroupRequest
{
	1:i64 actId,		//活动id
    2:i64 rankId,	    //榜单id
    3:i64 phaseId,		//阶段id
    4:i32 returnData,  //返回的数据，0 只返回晋级赛排名（默认，性能最优），1 返回晋级赛排名、成员id和晋级赛分数，2 在1的基础上加上当前分数和排名
    5:string queryDateStr //查询当前排名和分数指定的时间，yyyyMMdd，returnData = 2 时选填
    99:map<string, string> extData,	//扩展 map，协商使用
}

struct GroupItem
{
    1:string code,	    //分组编码
    2:string name,      //分组名称
    3:i32 count,        //
	4:list<GroupMemberItem> memberItems,	//分组成员信息
    5:string remark,			    //配置说明
    99:map<string,string> extData,  //扩展配置
}

struct MemberGroup
{
    1:i64 actId, 			        //活动id
    2:i64 rankId,		            //榜单id
    3:i64 phaseId, 		            //阶段id
    4:i32 passCount,		        //通过数量, 比拼分组中按排名可通过的人数，0是找不到晋级配置
    5:list<GroupItem> groupItems,    //分组信息，size=0说明没有分组信息，当passCount>0并且size=0说明只有一个分组，对象是所有成员
    6:string remark,			    //配置说明
    99:map<string,string> extData,  //扩展配置
}

struct QueryQualificationGroupResponse
{
    1:i32 code,							//结果码，0-成功，非零-失败或其他含义
    2:string reason,					//成功或失败的提示信息
    3:MemberGroup memberGroup,            //成员分组配置
    99:map<string, string> extData,		//扩展 map，协商使用
}

//角色定义
struct HdztActorInfo
{
    1:i64 role,
    2:i64 busiId,
    3:string name,
    4:i64 type,
    5:string remark,
    6:string extjson,
    7:i64 ctime,
    8:i64 utime,
    99:map<string, string> extData,		//扩展 map，协商使用
}

struct QueryHdztActorInfoResponse
{
    1:i32 code,							//结果码，0-成功，非零-失败或其他含义
    2:string reason,					//成功或失败的提示信息
    3:map<i64,HdztActorInfo> roles;
    99:map<string, string> extData,		//扩展 map，协商使用
}

struct QueryHdztActorInfoRequest
{
    99:map<string, string> extData,		//扩展 map，协商使用
}

struct SaveEnrollmentInfoRequest
{
	1:i64 actId,					  //活动id
	2:string seq,   //报名信息
    3:EnrollmentInfo enrollmentInfo,   //报名信息
    4:bool update,		               //是否更新覆盖，暂时不支持更新
    5:string remark,		         //备注
    99:map<string, string> extData,	//扩展 map，协商使用
}


struct SaveEnrollmentInfoResponse
{
    1:i32 code,                 //结果码，插入或更新成功返回0，重复更新返回1，频繁操作返回2
    2:string reason,            //成功或失败的提示信息
    99:map<string,string> extData,
}

struct ZsetQueryCondition
{
    1:string key,   // zset key
    2:i32 way,      // 查询方式，1：按分值降序取排名在[from,to]者，2：按分值升序取排名在[from,to]者，3：按分值取 from~to之间成员，若from>to结果是升序，若from<to结果是降序
    /**
    * thrift0.9 不允许from
    **/
	3:i64 fromName(java.field="from"),     // 起始的排名或分值，效果同 redis 原生命令
	4:i64 to,       // 结束的排名或分值，效果同 redis 原生命令
	5:i64 offset,   // 按分值范围查时的偏移量（按排名查被忽略），当小于等于0时被忽略
	6:i64 count,    // 按分值范围查时的获取数量（按排名查被忽略），当offset大于0时才有意义，此时count必须大于0
}

struct ZsetMember
{
	1:string member,  //榜单成员
	2:double score,   //分值
}

struct QueryZsetRawDataRequest
{
	1:i64 actId,    // 要查询的活动ID，若为0则查询公共redis库
    2:list<ZsetQueryCondition> conditions, //要查询榜单的集合, key是zset的键名， value是查询条件对象
    99:map<string,string> extData,
}

struct QueryZsetRawDataResponse
{
    1:i32 code,							 //结果码，0-成功，非零-失败或其他含义
    2:string reason,					 //成功或失败的提示信息
    3:list<list<ZsetMember>> rawDatas,   //结果数据，key的遍历访问顺序不保证和请求时一样，不存在的key也有空list，所以不能用来识别key不存在的情况
    99:map<string, string> extData,		 //扩展 map，协商使用
}

struct RankPhasePair
{
	1:i64 rankId,  //榜单id
	2:i64 phaseId,   //阶段id
}

//查询榜单id
struct QueryRankPhasePairResponse
{
    1:i32 code,							//结果码，0-成功，非零-失败或其他含义
    2:string reason,					//成功或失败的提示信息
    3:map<string,list<RankPhasePair>> rankIdMap,	//key uid/sid，value rankId
    99:map<string, string> extData,		//扩展 map，协商使用
}



service HdztRankingService
{
	/*
	* 连接测试
	*/
	void ping(),

	/*
	* 版本查询
	*/
	string version(),

	/**
	*当前服务器时间-可能是测试虚拟时间
    **/
	string currentTime();

    /**
    *根据活动ID，获取当前服务器时间-可能是测试虚拟时间
    **/
    string getCurrentTime(1:i64 actId);

   /**
    * 判断当前活动是否在灰度状态
	* code为 1：灰度中，2：不在灰度中，其它值表示出现某种错误
    */
	SimpleResult checkGrey(1:i64 actId),

    /**
     * 预留通用接口
     * busiId - 业务方标识
     * type - 调用类型，自定义自解释
     * data - 请求参数
     * sign - 请求参数签名（用 busiId 的 key）
     */
    SimpleResult invoke(1:i64 busiId, 2:i64 type, 3:map<string, string> data, 4:string sign);

    /**
     * 判断是否白名单成员（本函数主要用于线上测试）
     * actId - 活动标识
     * roleType - 角色类型（不要使用 RoleType.ANY，要使用明确的类型）
     * roleValue - 角色值
     * extData - 扩展数据
     */
    SimpleResult checkWhiteList(1:i64 actId, 2:RoleType roleType, 3:string roleValue, 4:map<string, string> extData);

    /**
     * 通用更新榜单接口
     * request - 更新请求对象
     */
	UpdateRankingResult updateRanking(1:UpdateRankingRequest request);

    /**
     * 查询指定的榜单
     * request - 查询榜单业务参数
     */
    QuerySingleRankingResult queryRanking(1:QueryRankingRequest request);

    /**
     * 查询指定的榜单
     * request - 查询榜单业务参数
     */
    QueryMulRankingResult queryTimeRank(1:QueryTimeRankingRequest request);

    /**
     * 查询榜单配置信息
     * request - 查询榜单配置信息业务请求参数
     */
    RankingConfigResponse queryRankingConfig(1:RankingConfigRequest request);

    /**
     * 查询参赛者状态信息以及当前积分
     * request - 查询榜单配置信息业务请求参数
     */
    ActorRankingInfoResponse queryActorRankingInfo(1:ActorRankingInfoRequest request);

    /**
     * 查询活动配置信息
     */
    ActivityInfoResult queryActInfo(1:ActivityInfoRequest request);

    /**
     * 查询活动配置扩展属性信息
     */
    ActivityAttrResult queryActAttr(1:ActivityInfoRequest request);

    /**
     * 查询活动配置信息
     */
    ActivityInfosResult queryActInfos(1:ActivityInfosRequest request);

     /**
       * 查询追玩组件活动配置信息
      */
    ActivityInfosResult queryZwActInfos(1:ActivityInfosRequest request);

    /**
     * 查询阶段配置信息
     */
    QueryRankingPhaseResponse queryRankingPhase(1:QueryRankingPhaseRequest request);

    /**
     * 查询成员当前所在的榜单id
     */
    QueryRankIdResponse queryRankId(1:QueryRankIdRequest request);

    /**
     * 查询成员当前所在的榜单阶段
     */
    QueryRankPhasePairResponse queryRankPhasePair(1:QueryRankIdRequest request);

    /**
     * 查询报名成员信息(新）
     * noCache,只支持指定角色类型和memberId，其余会返回空数组，不要大量加载
     */
     QueryEnrollmentInfoResponse queryEnrollment(1:QueryEnrollmentInfoRequest request);

     /**
      * 保存报名成员信息，不做参数校验，调用端要保证参数是准确的
      */
     SaveEnrollmentInfoResponse saveEnrollment(1:SaveEnrollmentInfoRequest request);

    /**
     * 查询成员闯关信息
     */
     QueryUserTaskResponse queryUserTaskInfo(1:QueryUserTaskRequest request);

     /**
     * 查询批量榜单信息
     */
     QueryBatchRankingResult queryBatchRanking(1:QueryBatchRankingRequest request);

    /**
     * 查询pk信息-可指定返回积分和排名，找不到配置会报错
     */
     QueryPkInfoResponse queryPkInfo(1:QueryPkInfoRequest request);

      /**
      * 查询晋级分组信息-可指定返回积分和排名
      */
     QueryQualificationGroupResponse queryQualificationGroup(1: QueryQualificationGroupRequest request);

      /**
      * 查询角色定义
      */
     QueryHdztActorInfoResponse queryHdztActorInfo(1: QueryHdztActorInfoRequest request);

       /**
       * 批量查询 zset 榜单数据, 返回原始数据，应尽量避免调用这个接口
       */
      QueryZsetRawDataResponse queryZsetRawData(1: QueryZsetRawDataRequest request);

}
