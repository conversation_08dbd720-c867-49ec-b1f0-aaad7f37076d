/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class QueryRankIdResponse implements org.apache.thrift.TBase<QueryRankIdResponse, QueryRankIdResponse._Fields>, java.io.Serializable, Cloneable, Comparable<QueryRankIdResponse> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryRankIdResponse");

  private static final org.apache.thrift.protocol.TField CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("code", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField REASON_FIELD_DESC = new org.apache.thrift.protocol.TField("reason", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField RANK_ID_MAP_FIELD_DESC = new org.apache.thrift.protocol.TField("rankIdMap", org.apache.thrift.protocol.TType.MAP, (short)3);
  private static final org.apache.thrift.protocol.TField EXT_DATA_FIELD_DESC = new org.apache.thrift.protocol.TField("extData", org.apache.thrift.protocol.TType.MAP, (short)99);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryRankIdResponseStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryRankIdResponseTupleSchemeFactory());
  }

  public int code; // required
  public String reason; // required
  public Map<String,List<Long>> rankIdMap; // required
  public Map<String,String> extData; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    CODE((short)1, "code"),
    REASON((short)2, "reason"),
    RANK_ID_MAP((short)3, "rankIdMap"),
    EXT_DATA((short)99, "extData");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CODE
          return CODE;
        case 2: // REASON
          return REASON;
        case 3: // RANK_ID_MAP
          return RANK_ID_MAP;
        case 99: // EXT_DATA
          return EXT_DATA;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __CODE_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CODE, new org.apache.thrift.meta_data.FieldMetaData("code", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.REASON, new org.apache.thrift.meta_data.FieldMetaData("reason", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.RANK_ID_MAP, new org.apache.thrift.meta_data.FieldMetaData("rankIdMap", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
                new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)))));
    tmpMap.put(_Fields.EXT_DATA, new org.apache.thrift.meta_data.FieldMetaData("extData", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryRankIdResponse.class, metaDataMap);
  }

  public QueryRankIdResponse() {
  }

  public QueryRankIdResponse(
    int code,
    String reason,
    Map<String,List<Long>> rankIdMap,
    Map<String,String> extData)
  {
    this();
    this.code = code;
    setCodeIsSet(true);
    this.reason = reason;
    this.rankIdMap = rankIdMap;
    this.extData = extData;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryRankIdResponse(QueryRankIdResponse other) {
    __isset_bitfield = other.__isset_bitfield;
    this.code = other.code;
    if (other.isSetReason()) {
      this.reason = other.reason;
    }
    if (other.isSetRankIdMap()) {
      Map<String,List<Long>> __this__rankIdMap = new HashMap<String,List<Long>>(other.rankIdMap.size());
      for (Map.Entry<String, List<Long>> other_element : other.rankIdMap.entrySet()) {

        String other_element_key = other_element.getKey();
        List<Long> other_element_value = other_element.getValue();

        String __this__rankIdMap_copy_key = other_element_key;

        List<Long> __this__rankIdMap_copy_value = new ArrayList<Long>(other_element_value);

        __this__rankIdMap.put(__this__rankIdMap_copy_key, __this__rankIdMap_copy_value);
      }
      this.rankIdMap = __this__rankIdMap;
    }
    if (other.isSetExtData()) {
      Map<String,String> __this__extData = new HashMap<String,String>(other.extData);
      this.extData = __this__extData;
    }
  }

  public QueryRankIdResponse deepCopy() {
    return new QueryRankIdResponse(this);
  }

  @Override
  public void clear() {
    setCodeIsSet(false);
    this.code = 0;
    this.reason = null;
    this.rankIdMap = null;
    this.extData = null;
  }

  public int getCode() {
    return this.code;
  }

  public QueryRankIdResponse setCode(int code) {
    this.code = code;
    setCodeIsSet(true);
    return this;
  }

  public void unsetCode() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __CODE_ISSET_ID);
  }

  /** Returns true if field code is set (has been assigned a value) and false otherwise */
  public boolean isSetCode() {
    return EncodingUtils.testBit(__isset_bitfield, __CODE_ISSET_ID);
  }

  public void setCodeIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __CODE_ISSET_ID, value);
  }

  public String getReason() {
    return this.reason;
  }

  public QueryRankIdResponse setReason(String reason) {
    this.reason = reason;
    return this;
  }

  public void unsetReason() {
    this.reason = null;
  }

  /** Returns true if field reason is set (has been assigned a value) and false otherwise */
  public boolean isSetReason() {
    return this.reason != null;
  }

  public void setReasonIsSet(boolean value) {
    if (!value) {
      this.reason = null;
    }
  }

  public int getRankIdMapSize() {
    return (this.rankIdMap == null) ? 0 : this.rankIdMap.size();
  }

  public void putToRankIdMap(String key, List<Long> val) {
    if (this.rankIdMap == null) {
      this.rankIdMap = new HashMap<String,List<Long>>();
    }
    this.rankIdMap.put(key, val);
  }

  public Map<String,List<Long>> getRankIdMap() {
    return this.rankIdMap;
  }

  public QueryRankIdResponse setRankIdMap(Map<String,List<Long>> rankIdMap) {
    this.rankIdMap = rankIdMap;
    return this;
  }

  public void unsetRankIdMap() {
    this.rankIdMap = null;
  }

  /** Returns true if field rankIdMap is set (has been assigned a value) and false otherwise */
  public boolean isSetRankIdMap() {
    return this.rankIdMap != null;
  }

  public void setRankIdMapIsSet(boolean value) {
    if (!value) {
      this.rankIdMap = null;
    }
  }

  public int getExtDataSize() {
    return (this.extData == null) ? 0 : this.extData.size();
  }

  public void putToExtData(String key, String val) {
    if (this.extData == null) {
      this.extData = new HashMap<String,String>();
    }
    this.extData.put(key, val);
  }

  public Map<String,String> getExtData() {
    return this.extData;
  }

  public QueryRankIdResponse setExtData(Map<String,String> extData) {
    this.extData = extData;
    return this;
  }

  public void unsetExtData() {
    this.extData = null;
  }

  /** Returns true if field extData is set (has been assigned a value) and false otherwise */
  public boolean isSetExtData() {
    return this.extData != null;
  }

  public void setExtDataIsSet(boolean value) {
    if (!value) {
      this.extData = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case CODE:
      if (value == null) {
        unsetCode();
      } else {
        setCode((Integer)value);
      }
      break;

    case REASON:
      if (value == null) {
        unsetReason();
      } else {
        setReason((String)value);
      }
      break;

    case RANK_ID_MAP:
      if (value == null) {
        unsetRankIdMap();
      } else {
        setRankIdMap((Map<String,List<Long>>)value);
      }
      break;

    case EXT_DATA:
      if (value == null) {
        unsetExtData();
      } else {
        setExtData((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case CODE:
      return getCode();

    case REASON:
      return getReason();

    case RANK_ID_MAP:
      return getRankIdMap();

    case EXT_DATA:
      return getExtData();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case CODE:
      return isSetCode();
    case REASON:
      return isSetReason();
    case RANK_ID_MAP:
      return isSetRankIdMap();
    case EXT_DATA:
      return isSetExtData();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryRankIdResponse)
      return this.equals((QueryRankIdResponse)that);
    return false;
  }

  public boolean equals(QueryRankIdResponse that) {
    if (that == null)
      return false;

    boolean this_present_code = true;
    boolean that_present_code = true;
    if (this_present_code || that_present_code) {
      if (!(this_present_code && that_present_code))
        return false;
      if (this.code != that.code)
        return false;
    }

    boolean this_present_reason = true && this.isSetReason();
    boolean that_present_reason = true && that.isSetReason();
    if (this_present_reason || that_present_reason) {
      if (!(this_present_reason && that_present_reason))
        return false;
      if (!this.reason.equals(that.reason))
        return false;
    }

    boolean this_present_rankIdMap = true && this.isSetRankIdMap();
    boolean that_present_rankIdMap = true && that.isSetRankIdMap();
    if (this_present_rankIdMap || that_present_rankIdMap) {
      if (!(this_present_rankIdMap && that_present_rankIdMap))
        return false;
      if (!this.rankIdMap.equals(that.rankIdMap))
        return false;
    }

    boolean this_present_extData = true && this.isSetExtData();
    boolean that_present_extData = true && that.isSetExtData();
    if (this_present_extData || that_present_extData) {
      if (!(this_present_extData && that_present_extData))
        return false;
      if (!this.extData.equals(that.extData))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_code = true;
    list.add(present_code);
    if (present_code)
      list.add(code);

    boolean present_reason = true && (isSetReason());
    list.add(present_reason);
    if (present_reason)
      list.add(reason);

    boolean present_rankIdMap = true && (isSetRankIdMap());
    list.add(present_rankIdMap);
    if (present_rankIdMap)
      list.add(rankIdMap);

    boolean present_extData = true && (isSetExtData());
    list.add(present_extData);
    if (present_extData)
      list.add(extData);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryRankIdResponse other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetCode()).compareTo(other.isSetCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.code, other.code);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetReason()).compareTo(other.isSetReason());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetReason()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.reason, other.reason);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetRankIdMap()).compareTo(other.isSetRankIdMap());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRankIdMap()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rankIdMap, other.rankIdMap);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExtData()).compareTo(other.isSetExtData());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExtData()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.extData, other.extData);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryRankIdResponse(");
    boolean first = true;

    sb.append("code:");
    sb.append(this.code);
    first = false;
    if (!first) sb.append(", ");
    sb.append("reason:");
    if (this.reason == null) {
      sb.append("null");
    } else {
      sb.append(this.reason);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("rankIdMap:");
    if (this.rankIdMap == null) {
      sb.append("null");
    } else {
      sb.append(this.rankIdMap);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("extData:");
    if (this.extData == null) {
      sb.append("null");
    } else {
      sb.append(this.extData);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryRankIdResponseStandardSchemeFactory implements SchemeFactory {
    public QueryRankIdResponseStandardScheme getScheme() {
      return new QueryRankIdResponseStandardScheme();
    }
  }

  private static class QueryRankIdResponseStandardScheme extends StandardScheme<QueryRankIdResponse> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryRankIdResponse struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.code = iprot.readI32();
              struct.setCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // REASON
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.reason = iprot.readString();
              struct.setReasonIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // RANK_ID_MAP
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map388 = iprot.readMapBegin();
                struct.rankIdMap = new HashMap<String,List<Long>>(2*_map388.size);
                String _key389;
                List<Long> _val390;
                for (int _i391 = 0; _i391 < _map388.size; ++_i391)
                {
                  _key389 = iprot.readString();
                  {
                    org.apache.thrift.protocol.TList _list392 = iprot.readListBegin();
                    _val390 = new ArrayList<Long>(_list392.size);
                    long _elem393;
                    for (int _i394 = 0; _i394 < _list392.size; ++_i394)
                    {
                      _elem393 = iprot.readI64();
                      _val390.add(_elem393);
                    }
                    iprot.readListEnd();
                  }
                  struct.rankIdMap.put(_key389, _val390);
                }
                iprot.readMapEnd();
              }
              struct.setRankIdMapIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 99: // EXT_DATA
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map395 = iprot.readMapBegin();
                struct.extData = new HashMap<String,String>(2*_map395.size);
                String _key396;
                String _val397;
                for (int _i398 = 0; _i398 < _map395.size; ++_i398)
                {
                  _key396 = iprot.readString();
                  _val397 = iprot.readString();
                  struct.extData.put(_key396, _val397);
                }
                iprot.readMapEnd();
              }
              struct.setExtDataIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryRankIdResponse struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(CODE_FIELD_DESC);
      oprot.writeI32(struct.code);
      oprot.writeFieldEnd();
      if (struct.reason != null) {
        oprot.writeFieldBegin(REASON_FIELD_DESC);
        oprot.writeString(struct.reason);
        oprot.writeFieldEnd();
      }
      if (struct.rankIdMap != null) {
        oprot.writeFieldBegin(RANK_ID_MAP_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.LIST, struct.rankIdMap.size()));
          for (Map.Entry<String, List<Long>> _iter399 : struct.rankIdMap.entrySet())
          {
            oprot.writeString(_iter399.getKey());
            {
              oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, _iter399.getValue().size()));
              for (long _iter400 : _iter399.getValue())
              {
                oprot.writeI64(_iter400);
              }
              oprot.writeListEnd();
            }
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.extData != null) {
        oprot.writeFieldBegin(EXT_DATA_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.extData.size()));
          for (Map.Entry<String, String> _iter401 : struct.extData.entrySet())
          {
            oprot.writeString(_iter401.getKey());
            oprot.writeString(_iter401.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryRankIdResponseTupleSchemeFactory implements SchemeFactory {
    public QueryRankIdResponseTupleScheme getScheme() {
      return new QueryRankIdResponseTupleScheme();
    }
  }

  private static class QueryRankIdResponseTupleScheme extends TupleScheme<QueryRankIdResponse> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryRankIdResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetCode()) {
        optionals.set(0);
      }
      if (struct.isSetReason()) {
        optionals.set(1);
      }
      if (struct.isSetRankIdMap()) {
        optionals.set(2);
      }
      if (struct.isSetExtData()) {
        optionals.set(3);
      }
      oprot.writeBitSet(optionals, 4);
      if (struct.isSetCode()) {
        oprot.writeI32(struct.code);
      }
      if (struct.isSetReason()) {
        oprot.writeString(struct.reason);
      }
      if (struct.isSetRankIdMap()) {
        {
          oprot.writeI32(struct.rankIdMap.size());
          for (Map.Entry<String, List<Long>> _iter402 : struct.rankIdMap.entrySet())
          {
            oprot.writeString(_iter402.getKey());
            {
              oprot.writeI32(_iter402.getValue().size());
              for (long _iter403 : _iter402.getValue())
              {
                oprot.writeI64(_iter403);
              }
            }
          }
        }
      }
      if (struct.isSetExtData()) {
        {
          oprot.writeI32(struct.extData.size());
          for (Map.Entry<String, String> _iter404 : struct.extData.entrySet())
          {
            oprot.writeString(_iter404.getKey());
            oprot.writeString(_iter404.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryRankIdResponse struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(4);
      if (incoming.get(0)) {
        struct.code = iprot.readI32();
        struct.setCodeIsSet(true);
      }
      if (incoming.get(1)) {
        struct.reason = iprot.readString();
        struct.setReasonIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map405 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.LIST, iprot.readI32());
          struct.rankIdMap = new HashMap<String,List<Long>>(2*_map405.size);
          String _key406;
          List<Long> _val407;
          for (int _i408 = 0; _i408 < _map405.size; ++_i408)
          {
            _key406 = iprot.readString();
            {
              org.apache.thrift.protocol.TList _list409 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
              _val407 = new ArrayList<Long>(_list409.size);
              long _elem410;
              for (int _i411 = 0; _i411 < _list409.size; ++_i411)
              {
                _elem410 = iprot.readI64();
                _val407.add(_elem410);
              }
            }
            struct.rankIdMap.put(_key406, _val407);
          }
        }
        struct.setRankIdMapIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TMap _map412 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.extData = new HashMap<String,String>(2*_map412.size);
          String _key413;
          String _val414;
          for (int _i415 = 0; _i415 < _map412.size; ++_i415)
          {
            _key413 = iprot.readString();
            _val414 = iprot.readString();
            struct.extData.put(_key413, _val414);
          }
        }
        struct.setExtDataIsSet(true);
      }
    }
  }

}

