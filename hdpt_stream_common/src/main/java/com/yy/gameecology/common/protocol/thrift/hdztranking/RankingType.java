/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.hdztranking;


import java.util.Map;
import java.util.HashMap;
import org.apache.thrift.TEnum;

public enum RankingType implements org.apache.thrift.TEnum {
  ANY(0),
  ONE_USER(1000),
  ONE_ANCHOR(1001),
  ONE_HOST(1002),
  ONE_GUILD(1003),
  ONE_HALL(1004),
  TWO_USER_ANCHOR(2000),
  TWO_USER_HOST(2001),
  TWO_USER_GUILD(2002),
  TWO_ANCHOR_GUILD(2003),
  TWO_HOST_GUILD(2004),
  TWO_ANCHOR_HOST(2005),
  TWO_USER_HALL(2006),
  TWO_ANCHOR_HALL(2007),
  TWO_HOST_HALL(2008),
  TWO_GUILD_HALL(2009);

  private final int value;

  private RankingType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  public static RankingType findByValue(int value) { 
    switch (value) {
      case 0:
        return ANY;
      case 1000:
        return ONE_USER;
      case 1001:
        return ONE_ANCHOR;
      case 1002:
        return ONE_HOST;
      case 1003:
        return ONE_GUILD;
      case 1004:
        return ONE_HALL;
      case 2000:
        return TWO_USER_ANCHOR;
      case 2001:
        return TWO_USER_HOST;
      case 2002:
        return TWO_USER_GUILD;
      case 2003:
        return TWO_ANCHOR_GUILD;
      case 2004:
        return TWO_HOST_GUILD;
      case 2005:
        return TWO_ANCHOR_HOST;
      case 2006:
        return TWO_USER_HALL;
      case 2007:
        return TWO_ANCHOR_HALL;
      case 2008:
        return TWO_HOST_HALL;
      case 2009:
        return TWO_GUILD_HALL;
      default:
        return null;
    }
  }
}
