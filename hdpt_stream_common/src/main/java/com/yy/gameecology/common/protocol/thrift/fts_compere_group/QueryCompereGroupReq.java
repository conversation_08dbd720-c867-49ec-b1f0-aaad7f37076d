/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_compere_group;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-08-06")
public class QueryCompereGroupReq implements org.apache.thrift.TBase<QueryCompereGroupReq, QueryCompereGroupReq._Fields>, java.io.Serializable, Cloneable, Comparable<QueryCompereGroupReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("QueryCompereGroupReq");

  private static final org.apache.thrift.protocol.TField R_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("r_id", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField U_ID_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("u_id_list", org.apache.thrift.protocol.TType.LIST, (short)2);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.MAP, (short)15);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new QueryCompereGroupReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new QueryCompereGroupReqTupleSchemeFactory());
  }

  public long r_id; // required
  public List<Long> u_id_list; // required
  public Map<String,String> expand; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    R_ID((short)1, "r_id"),
    U_ID_LIST((short)2, "u_id_list"),
    EXPAND((short)15, "expand");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // R_ID
          return R_ID;
        case 2: // U_ID_LIST
          return U_ID_LIST;
        case 15: // EXPAND
          return EXPAND;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __R_ID_ISSET_ID = 0;
  private byte __isset_bitfield = 0;
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.R_ID, new org.apache.thrift.meta_data.FieldMetaData("r_id", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.U_ID_LIST, new org.apache.thrift.meta_data.FieldMetaData("u_id_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64))));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.MapMetaData(org.apache.thrift.protocol.TType.MAP, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING), 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(QueryCompereGroupReq.class, metaDataMap);
  }

  public QueryCompereGroupReq() {
  }

  public QueryCompereGroupReq(
    long r_id,
    List<Long> u_id_list,
    Map<String,String> expand)
  {
    this();
    this.r_id = r_id;
    setR_idIsSet(true);
    this.u_id_list = u_id_list;
    this.expand = expand;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public QueryCompereGroupReq(QueryCompereGroupReq other) {
    __isset_bitfield = other.__isset_bitfield;
    this.r_id = other.r_id;
    if (other.isSetU_id_list()) {
      List<Long> __this__u_id_list = new ArrayList<Long>(other.u_id_list);
      this.u_id_list = __this__u_id_list;
    }
    if (other.isSetExpand()) {
      Map<String,String> __this__expand = new HashMap<String,String>(other.expand);
      this.expand = __this__expand;
    }
  }

  public QueryCompereGroupReq deepCopy() {
    return new QueryCompereGroupReq(this);
  }

  @Override
  public void clear() {
    setR_idIsSet(false);
    this.r_id = 0;
    this.u_id_list = null;
    this.expand = null;
  }

  public long getR_id() {
    return this.r_id;
  }

  public QueryCompereGroupReq setR_id(long r_id) {
    this.r_id = r_id;
    setR_idIsSet(true);
    return this;
  }

  public void unsetR_id() {
    __isset_bitfield = EncodingUtils.clearBit(__isset_bitfield, __R_ID_ISSET_ID);
  }

  /** Returns true if field r_id is set (has been assigned a value) and false otherwise */
  public boolean isSetR_id() {
    return EncodingUtils.testBit(__isset_bitfield, __R_ID_ISSET_ID);
  }

  public void setR_idIsSet(boolean value) {
    __isset_bitfield = EncodingUtils.setBit(__isset_bitfield, __R_ID_ISSET_ID, value);
  }

  public int getU_id_listSize() {
    return (this.u_id_list == null) ? 0 : this.u_id_list.size();
  }

  public java.util.Iterator<Long> getU_id_listIterator() {
    return (this.u_id_list == null) ? null : this.u_id_list.iterator();
  }

  public void addToU_id_list(long elem) {
    if (this.u_id_list == null) {
      this.u_id_list = new ArrayList<Long>();
    }
    this.u_id_list.add(elem);
  }

  public List<Long> getU_id_list() {
    return this.u_id_list;
  }

  public QueryCompereGroupReq setU_id_list(List<Long> u_id_list) {
    this.u_id_list = u_id_list;
    return this;
  }

  public void unsetU_id_list() {
    this.u_id_list = null;
  }

  /** Returns true if field u_id_list is set (has been assigned a value) and false otherwise */
  public boolean isSetU_id_list() {
    return this.u_id_list != null;
  }

  public void setU_id_listIsSet(boolean value) {
    if (!value) {
      this.u_id_list = null;
    }
  }

  public int getExpandSize() {
    return (this.expand == null) ? 0 : this.expand.size();
  }

  public void putToExpand(String key, String val) {
    if (this.expand == null) {
      this.expand = new HashMap<String,String>();
    }
    this.expand.put(key, val);
  }

  public Map<String,String> getExpand() {
    return this.expand;
  }

  public QueryCompereGroupReq setExpand(Map<String,String> expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case R_ID:
      if (value == null) {
        unsetR_id();
      } else {
        setR_id((Long)value);
      }
      break;

    case U_ID_LIST:
      if (value == null) {
        unsetU_id_list();
      } else {
        setU_id_list((List<Long>)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((Map<String,String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case R_ID:
      return getR_id();

    case U_ID_LIST:
      return getU_id_list();

    case EXPAND:
      return getExpand();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case R_ID:
      return isSetR_id();
    case U_ID_LIST:
      return isSetU_id_list();
    case EXPAND:
      return isSetExpand();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof QueryCompereGroupReq)
      return this.equals((QueryCompereGroupReq)that);
    return false;
  }

  public boolean equals(QueryCompereGroupReq that) {
    if (that == null)
      return false;

    boolean this_present_r_id = true;
    boolean that_present_r_id = true;
    if (this_present_r_id || that_present_r_id) {
      if (!(this_present_r_id && that_present_r_id))
        return false;
      if (this.r_id != that.r_id)
        return false;
    }

    boolean this_present_u_id_list = true && this.isSetU_id_list();
    boolean that_present_u_id_list = true && that.isSetU_id_list();
    if (this_present_u_id_list || that_present_u_id_list) {
      if (!(this_present_u_id_list && that_present_u_id_list))
        return false;
      if (!this.u_id_list.equals(that.u_id_list))
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_r_id = true;
    list.add(present_r_id);
    if (present_r_id)
      list.add(r_id);

    boolean present_u_id_list = true && (isSetU_id_list());
    list.add(present_u_id_list);
    if (present_u_id_list)
      list.add(u_id_list);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    return list.hashCode();
  }

  @Override
  public int compareTo(QueryCompereGroupReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetR_id()).compareTo(other.isSetR_id());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetR_id()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.r_id, other.r_id);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetU_id_list()).compareTo(other.isSetU_id_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetU_id_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.u_id_list, other.u_id_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("QueryCompereGroupReq(");
    boolean first = true;

    sb.append("r_id:");
    sb.append(this.r_id);
    first = false;
    if (!first) sb.append(", ");
    sb.append("u_id_list:");
    if (this.u_id_list == null) {
      sb.append("null");
    } else {
      sb.append(this.u_id_list);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class QueryCompereGroupReqStandardSchemeFactory implements SchemeFactory {
    public QueryCompereGroupReqStandardScheme getScheme() {
      return new QueryCompereGroupReqStandardScheme();
    }
  }

  private static class QueryCompereGroupReqStandardScheme extends StandardScheme<QueryCompereGroupReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, QueryCompereGroupReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // R_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.r_id = iprot.readI64();
              struct.setR_idIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // U_ID_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.u_id_list = new ArrayList<Long>(_list0.size);
                long _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = iprot.readI64();
                  struct.u_id_list.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setU_id_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 15: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.MAP) {
              {
                org.apache.thrift.protocol.TMap _map3 = iprot.readMapBegin();
                struct.expand = new HashMap<String,String>(2*_map3.size);
                String _key4;
                String _val5;
                for (int _i6 = 0; _i6 < _map3.size; ++_i6)
                {
                  _key4 = iprot.readString();
                  _val5 = iprot.readString();
                  struct.expand.put(_key4, _val5);
                }
                iprot.readMapEnd();
              }
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, QueryCompereGroupReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(R_ID_FIELD_DESC);
      oprot.writeI64(struct.r_id);
      oprot.writeFieldEnd();
      if (struct.u_id_list != null) {
        oprot.writeFieldBegin(U_ID_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, struct.u_id_list.size()));
          for (long _iter7 : struct.u_id_list)
          {
            oprot.writeI64(_iter7);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        {
          oprot.writeMapBegin(new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, struct.expand.size()));
          for (Map.Entry<String, String> _iter8 : struct.expand.entrySet())
          {
            oprot.writeString(_iter8.getKey());
            oprot.writeString(_iter8.getValue());
          }
          oprot.writeMapEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class QueryCompereGroupReqTupleSchemeFactory implements SchemeFactory {
    public QueryCompereGroupReqTupleScheme getScheme() {
      return new QueryCompereGroupReqTupleScheme();
    }
  }

  private static class QueryCompereGroupReqTupleScheme extends TupleScheme<QueryCompereGroupReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, QueryCompereGroupReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetR_id()) {
        optionals.set(0);
      }
      if (struct.isSetU_id_list()) {
        optionals.set(1);
      }
      if (struct.isSetExpand()) {
        optionals.set(2);
      }
      oprot.writeBitSet(optionals, 3);
      if (struct.isSetR_id()) {
        oprot.writeI64(struct.r_id);
      }
      if (struct.isSetU_id_list()) {
        {
          oprot.writeI32(struct.u_id_list.size());
          for (long _iter9 : struct.u_id_list)
          {
            oprot.writeI64(_iter9);
          }
        }
      }
      if (struct.isSetExpand()) {
        {
          oprot.writeI32(struct.expand.size());
          for (Map.Entry<String, String> _iter10 : struct.expand.entrySet())
          {
            oprot.writeString(_iter10.getKey());
            oprot.writeString(_iter10.getValue());
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, QueryCompereGroupReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(3);
      if (incoming.get(0)) {
        struct.r_id = iprot.readI64();
        struct.setR_idIsSet(true);
      }
      if (incoming.get(1)) {
        {
          org.apache.thrift.protocol.TList _list11 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.I64, iprot.readI32());
          struct.u_id_list = new ArrayList<Long>(_list11.size);
          long _elem12;
          for (int _i13 = 0; _i13 < _list11.size; ++_i13)
          {
            _elem12 = iprot.readI64();
            struct.u_id_list.add(_elem12);
          }
        }
        struct.setU_id_listIsSet(true);
      }
      if (incoming.get(2)) {
        {
          org.apache.thrift.protocol.TMap _map14 = new org.apache.thrift.protocol.TMap(org.apache.thrift.protocol.TType.STRING, org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.expand = new HashMap<String,String>(2*_map14.size);
          String _key15;
          String _val16;
          for (int _i17 = 0; _i17 < _map14.size; ++_i17)
          {
            _key15 = iprot.readString();
            _val16 = iprot.readString();
            struct.expand.put(_key15, _val16);
          }
        }
        struct.setExpandIsSet(true);
      }
    }
  }

}

