namespace java com.yy.gameecology.common.protocol.thrift.turnover_family
struct TFamily {
  /**
   * 家族ID
   */
  1: i64 id;

  /**
   * 家族名称
   */
  2: string name;

  /**
   * 会长UID
   */
  3: i64 uid;

  /**
   * 入驻日期
   */
  4: i64 signTime;

  /**
   * 状态, 0:申请中; 1:生效中; 2:已失效
   */
  5: i32 status;
}

struct TFamilyContract {
  /**
   * 业务id
   */
  1: i32 appid;

  /**
   * 家族id
   */
  2: i64 familyId;

  /**
   * 主持uid
   */
  3: i64 liveUid;

  /**
   * 分成比例
   */
  4: i32 weight;

  /**
   * 生效日期
   */
  5: i64 signTime;

  /**
   * 失效日期
   */
  6: i64 finishTime;

  /**
   * 扩展json字段 {"delete":1} 表示解约中
   */
  7: string expand;

  /**
   * 经纪人uid
  */
  8: i64 agentUid;
}

struct TFamilySsid {
  /**
   * 业务id
   */
  1: i32 appid;

  /**
   * 家族id
   */
  2: i64 familyId;

  /**
   * ssid
   */
  3: i64 ssid;

  /**
   * 扩展字段
   */
  4: string expand;

  /**
   * sid
   */
  5: i64 sid;
  /**
   * liveUid
   */
  6: i64 liveUid;

  /**
   * 创建时间
   */
  7: i64 createTime;

  /**
   * 更新时间
   */
  8: i64 updateTime;
}

struct TFamilySsidPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TFamilySsid> contents;
}

struct TFamilyContractPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TFamilyContract> contents;
}

exception TServiceException {
  1: i32 code;
  2: string message;
  3: string expand;
}
service TPingService {
  i64 ping(1: i64 seq);
  void ping2();
}

struct TFamilySsidDetail {
  /**
   * 业务id
   */
  1: i32 appid;

  /**
   * 家族id
   */
  2: i64 familyId;

  /**
   * ssid
   */
  3: i64 ssid;

  /**
   * 扩展字段
   */
  4: string expand;

  /**
   * sid
   */
  5: i64 sid;

  /**
   * 家族名称
   */
  6: string name;

  /**
   * 家族长uid
   */
  7: i64 uid;
}

struct TFamilySsidDetailPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TFamilySsidDetail> contents;
}

struct TFamilyUidPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<i64> contents;
}

enum TAppId {
  Finance=1, Dating=2, Hundred=3, FreeShow=4, GameGuild=5, Ktv=6, Blackjack=7, Spy=8, SlaveSales=9, ScratchOff=10, Niuniu=11, MedicalTreatment=12, Sport=13, VipPk=14, HelloApp=15, FinanceForceRelieveContract=16, GameSpot=17, Bilin=18, XunHuan=19, WeiFang=20, TinyTime=21, YoMall=22, GameTemplate=23, MEPlus=24, WerewolfKill=25, TinyVideo=26, MGameVoice=27, DianHu=28, ZhuiDu=29, ZhuiYa=30, Findyou=31, Nearby=33, PeopleGame=34, DatingHuabaRepay=35, Baby=36, PeiwanPaidan=37, Baidu=38, GameLive=39, Demo=40, BaiduTieba=41, HaokanShipin=42, QuanminXiaoshipin=43, YYLive=44, Baijiahao=45, YYChargeCenter=46, Bdgassist=47, PC=48, FanZhiShi=49, BaiduLite=50, BaiduHealth=51, ZfbApplets=52, BaiduXR=53, BaiduBZ=55, DatingYYLiveWithdraw=20001, DatingYYLiveWithdrawV3=20002, ShengDongWithdraw=190000, ShengLangWithdraw=190001, ZhuiWanGiftbagWithdraw=300001, ZhuiWanActWithdraw=300002, ZhuiWanGunKingWithdraw=300003, ZhuiWanGunKingV2Withdraw=300004, ZhuiWanYYFWithdraw=3401, SDKCourseWithdraw=3901
}

struct TRoomContract {
  1: i64 id;
  2: i32 appid;
  3: i64 sid;
  4: i64 ssid;
  5: i64 owUid;
  6: i64 roomMgrUid;
  7: i32 weight;
  8: i32 settleMode;
  9: string expand;
  10: i64 createTime;
  11: i64 updateTime;
  12: i64 signTime;
  13: i64 finishTime;
  14: i64 playSid;
}

struct TDatingIdentify {
  /**
   * 是否主持，1：是，0：否
   */
  1: i32 anchor;

  /**
   * 是否房管，1：是，0：否
   */
  2: i32 roomMgr;

  /**
   * 是否厅管，1：是，0：否
   */
  3: i32 tingMgr;
}

struct TStarlight {
  1: i32 appid;
  2: i64 sid;
  3: i64 ssid;
  4: i64 owUid;
  5: i64 roomMgrUid;
  6: i64 liveUid;
  7: string expand;
  8: i64 createTime;
  9: i64 updateTime;
}

struct TStarlightPageResult {
  1: i32 page;
  2: i32 pagesize;
  3: i32 total;
  4: i32 totalPage;
  5: list<TStarlight> contents;
}

service TFamilyService extends TPingService {


  /**
   * 查询家族
   * @param ids 家族ID
   */
  map<i64, TFamily> queryFamily(1: list<i64> ids) throws (1: TServiceException ex1);



  /**
     * 查询家族相关配置
     */
  map<string, string> queryFamilyConf() throws (1: TServiceException ex1);


  /**
   * 根据uid查询家族
   * @param uid 用户UID
   */
  list<TFamily> queryFamilyByUid(1: i64 uid) throws (1: TServiceException ex1);

  /**
   * 搜索家族
   * @param keys 搜索词
   */
  list<TFamily> searchFamilyByKeys(1: string keys) throws (1: TServiceException ex1);

  /**
   * 查询家族签约信息
   * @param familyId 家族id
   * @param page 1为第一页，默认1
   * @param pagesize 每页数量，默认50
   */
  TFamilyContractPageResult queryContractByFamilyId(1: i64 familyId, 2: i32 page, 3: i32 pageSize) throws (1: TServiceException ex1);

  /**
   * 查询家族已签约人数
   * @param familyId 家族id
   * @return MAX_CONTRACT_NUM：最大签约限制人数，EXIST_CONTRACT_NUM：已签约人数
   */
  map<string, i32> queryContractNumByFamilyId(1: i64 familyId) throws (1: TServiceException ex1);

  /**
   * 家族绑定ssid
   * @param familyId 家族id
   * @param ssid ssid
   * @param expand 扩展json字段
   * @param liveUid 主持uid
   */
  TFamilySsid addFamilySsid(1: i64 familyId, 2: i64 sid, 3: i64 ssid, 4: string expand, 5: i64 liveUid) throws (1: TServiceException ex1);




  /**
   * 查询家族绑定ssid
   * @param familyId 家族id
   */
  TFamilySsidPageResult queryFamilySsidByFamilyId(1: i64 familyId, 2: i32 page, 3: i32 pageSize) throws (1: TServiceException ex1);



  /**
     * 查询家族签约信息
     * @param liveUid 主持uid
     */
    TFamilyContract queryContractByLiveUid(1: i64 liveUid) throws (1: TServiceException ex1);


    /**
   * 批量查询家族签约信息
   * @param liveUid 主持uid
   */
    map<i64, TFamilyContract> batchQueryContractByLiveUids(1: list<i64> liveUids) throws (1: TServiceException ex1);

    /**
     * 统计频道临近到期人数
     * @param familyId 家族id
     * @param days x天内到期 必填
     * @return map<key,数量> key->total：总签约数，nearExpiration：临近过期数
     */
     map<string, i32> countFamilyNearExpiration(1: i64 familyId, 2: i32 days) throws (1: TServiceException ex1);


  /**
   * 查询家族签约信息
   * @param page 1为第一页，默认1
   * @param pagesize 每页数量，默认50
   * @param familyId 家族id
   * @param liveUids 主持uid
   * @param signTimeStart 签约生效时间
   * @param signTimeEnd 签约生效时间
   * @param sortType 排序类型 1：签约时间倒序，2：到期时间正序，默认 1 可选
   */
  TFamilyContractPageResult queryContract(1: i32 page, 2: i32 pageSize, 3: i64 familyId, 4: list<i64> liveUids, 5: i64 signTimeStart, 6: i64 signTimeEnd, 7: i32 sortType) throws (1: TServiceException ex1);

  /**
   * 查询家族最近签约的数量
   * @param familyId 家族id
   * @param timeStart 开始时间
   * @param timeEnd 结束时间
   * @param type 1：按签约时间，2：按创建时间，3：按更新时间
   * @return Map<yyyy-MM-dd,数量>
   */
  map<string, i32> countContractByTime(1: i64 familyId, 2: i64 timeStart, 3: i64 timeEnd, 4: i32 type) throws (1: TServiceException ex1);

/**
   * 查询家族下经纪人
   * @param familyId 家族id 必填
   * @param agentUid 经纪人uid 可选
   * @return <经纪人uid,主持数量>
   */
  map<i64, i64> queryAgentUidsByFamilyId(1: i64 familyId, 2: i64 agentUid) throws (1: TServiceException ex1);

    /**
     * 查询经纪人下主持uid
     * @param page 1为第一页，默认1
     * @param pagesize 每页数量，默认50
     * @param familyId 家族id 必填
     * @param agentUid 经纪人uid 必填
     * @param anchorUid 主持uid 可选
     */
    TFamilyUidPageResult queryAnchorUidsByAgentUid(1: i32 page, 2: i32 pageSize, 3: i64 familyId, 4: i64 agentUid, 5: i64 anchorUid) throws (1: TServiceException ex1);
}

service TRoomContractService {


  list<TRoomContract> queryRoomContractByAppId(1: TAppId appid) throws (1: TServiceException ex1);

  map<i64, TRoomContract> queryRoomContractByUid(1: TAppId appid, 2: list<i64> uidList) throws (1: TServiceException ex1);

  map<i64, TRoomContract> queryRoomContractBySsid(1: TAppId appid, 2: list<i64> ssidList) throws (1: TServiceException ex1);


  /**
   * 批量查询uid是否拥有 主持/房管/厅管 身份
   * @param appid 业务id
   * @param uids 用户uid
   */
  map<i64, TDatingIdentify> queryIdentify(1: TAppId appid, 2: list<i64> uids) throws (1: TServiceException ex1);


  /**
   * 查询sid下的房管数量
   * @param appid 业务id
   * @param sid 公会sid
   * @return MAX_ROOM_MGR_NUM：最大房管限制数量，EXIST_ROOM_MGR_NUM：已有房管数量
   */
  map<string, i32> queryRoomMgrNumBySid(1: TAppId appid, 2: i64 sid) throws (1: TServiceException ex1);

    /**
   * 房管签约
   * @param appid 业务id
   * @param sid sid
   * @param ssid ssid
   * @param owUid 公会uid
   * @param roomMgrUid 房管uid
   * @param weight 分成比例
   * @param settleMode 0.对私结算 1.对公结算
   * @param isPersonalRoom 0.天团厅 1.个播厅
   * @param signTime 签约时间 yyyy-MM-dd 毫秒
   * @param finishTime 到期时间 yyyy-MM-dd 毫秒
   * @param expand 扩展json
   */
  i32 addRoomContract(1: TAppId appid, 2: i64 sid, 3: i64 ssid, 4: i64 owUid, 5: i64 roomMgrUid, 6: i32 weight, 7: i32 settleMode, 8: i32 isPersonalRoom, 9: i64 signTime, 10: i64 finishTime, 11: string expand) throws (1: TServiceException ex1);


  /**
   * 添加星光主持
   * @param appid 业务id
   * @param ssid ssid
   * @param owUid 公会uid
   * @param roomMgrUid 房管uid
   * @param expand 扩展json信息，可选
   */
  i32 addStarlight(1: TAppId appid, 2: i64 ssid, 3: i64 roomMgrUid, 4: i64 liveUid, 5: string expand) throws (1: TServiceException ex1);


  /**
   * 删除星光主持
   * @param appid 业务id
   * @param ssid ssid
   * @param owUid 公会uid
   * @param roomMgrUid 房管uid
   * @param expand 扩展json信息，可选
   */
  i32 deleteStarlight(1: TAppId appid, 2: i64 ssid, 3: i64 roomMgrUid, 4: i64 liveUid, 5: string expand) throws (1: TServiceException ex1);


  /**
   * 星光主持分页查询
   * @param appid 业务id
   * @param sid sid
   * @param ssid ssid
   * @param owUid 公会uid
   * @param roomMgrUid 房管uid
   */
  TStarlightPageResult queryStarlight(1: i32 page, 2: i32 pageSize, 3: TAppId appid, 4: i64 owUid, 5: i64 sid, 6: i64 ssid, 7: i64 roomMgrUid) throws (1: TServiceException ex1);


  /**
   * 根据主持uid查询星光主持
   * @param appid 业务id
   * @param uids 主持uid
   */
  map<i64, TStarlight> queryStarlightByLiveUids(1: TAppId appid, 2: list<i64> uids) throws (1: TServiceException ex1);



  /**
   * 根据房管uid查询星光主持数量
   * @param appid 业务id
   * @param roomMgrUids 房管uid
   */
  map<i64, i32> queryStarlightNum(1: TAppId appid, 2: list<i64> roomMgrUids) throws (1: TServiceException ex1);
}

