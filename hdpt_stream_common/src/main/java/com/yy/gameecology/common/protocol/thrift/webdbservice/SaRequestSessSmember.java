/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.webdbservice;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
/**
 * 查询某个用户在一些频道的角色信息(批量)
 * @param     appkey           客户端的标识
 * @param     uid              用户id
 * @param     tids             频道id列表(列表最大长度为500)
 * @param     columns          需要查询的字段集合，批量接口只支持以下字段
 *                               - "tid",  频道id
 *                               - "type", 角色类型
 *                                   可能取值如下：
 *                                   取值   意义
 *                                   88     嘉宾（VIP），绿色马甲
 *                                   100    会员，蓝色马甲
 *                                   150    子频道管理员，红色马甲
 *                                   200    全频道（包括子频道）管理员，黄色马甲
 *                                   230    副会长，橙色马甲
 *                                   255    频道拥有者，紫色马甲
 *                                   300    客服，黑色马甲
 *                                   1000   超级管理员，黑色马甲
 *                               - "jifen", 原始的频道积分(或叫贡献),按分钟计算
 *                               - "add_time", 加入频道时间
 * @SaResponseSet              频道成员角色信息结果集，如果没有查到结果则dataSet为空
 */
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-07-13")
public class SaRequestSessSmember implements org.apache.thrift.TBase<SaRequestSessSmember, SaRequestSessSmember._Fields>, java.io.Serializable, Cloneable, Comparable<SaRequestSessSmember> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("SaRequestSessSmember");

  private static final org.apache.thrift.protocol.TField AUTH_MSG_FIELD_DESC = new org.apache.thrift.protocol.TField("authMsg", org.apache.thrift.protocol.TType.STRUCT, (short)1);
  private static final org.apache.thrift.protocol.TField APPKEY_FIELD_DESC = new org.apache.thrift.protocol.TField("appkey", org.apache.thrift.protocol.TType.STRING, (short)2);
  private static final org.apache.thrift.protocol.TField UID_FIELD_DESC = new org.apache.thrift.protocol.TField("uid", org.apache.thrift.protocol.TType.STRING, (short)3);
  private static final org.apache.thrift.protocol.TField TIDS_FIELD_DESC = new org.apache.thrift.protocol.TField("tids", org.apache.thrift.protocol.TType.LIST, (short)4);
  private static final org.apache.thrift.protocol.TField COLUMNS_FIELD_DESC = new org.apache.thrift.protocol.TField("columns", org.apache.thrift.protocol.TType.LIST, (short)5);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new SaRequestSessSmemberStandardSchemeFactory());
    schemes.put(TupleScheme.class, new SaRequestSessSmemberTupleSchemeFactory());
  }

  public AuthorizeMsg authMsg; // required
  public String appkey; // required
  public String uid; // required
  public List<String> tids; // required
  public List<String> columns; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    AUTH_MSG((short)1, "authMsg"),
    APPKEY((short)2, "appkey"),
    UID((short)3, "uid"),
    TIDS((short)4, "tids"),
    COLUMNS((short)5, "columns");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // AUTH_MSG
          return AUTH_MSG;
        case 2: // APPKEY
          return APPKEY;
        case 3: // UID
          return UID;
        case 4: // TIDS
          return TIDS;
        case 5: // COLUMNS
          return COLUMNS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.AUTH_MSG, new org.apache.thrift.meta_data.FieldMetaData("authMsg", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, AuthorizeMsg.class)));
    tmpMap.put(_Fields.APPKEY, new org.apache.thrift.meta_data.FieldMetaData("appkey", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.UID, new org.apache.thrift.meta_data.FieldMetaData("uid", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    tmpMap.put(_Fields.TIDS, new org.apache.thrift.meta_data.FieldMetaData("tids", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    tmpMap.put(_Fields.COLUMNS, new org.apache.thrift.meta_data.FieldMetaData("columns", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING))));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(SaRequestSessSmember.class, metaDataMap);
  }

  public SaRequestSessSmember() {
  }

  public SaRequestSessSmember(
    AuthorizeMsg authMsg,
    String appkey,
    String uid,
    List<String> tids,
    List<String> columns)
  {
    this();
    this.authMsg = authMsg;
    this.appkey = appkey;
    this.uid = uid;
    this.tids = tids;
    this.columns = columns;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public SaRequestSessSmember(SaRequestSessSmember other) {
    if (other.isSetAuthMsg()) {
      this.authMsg = new AuthorizeMsg(other.authMsg);
    }
    if (other.isSetAppkey()) {
      this.appkey = other.appkey;
    }
    if (other.isSetUid()) {
      this.uid = other.uid;
    }
    if (other.isSetTids()) {
      List<String> __this__tids = new ArrayList<String>(other.tids);
      this.tids = __this__tids;
    }
    if (other.isSetColumns()) {
      List<String> __this__columns = new ArrayList<String>(other.columns);
      this.columns = __this__columns;
    }
  }

  public SaRequestSessSmember deepCopy() {
    return new SaRequestSessSmember(this);
  }

  @Override
  public void clear() {
    this.authMsg = null;
    this.appkey = null;
    this.uid = null;
    this.tids = null;
    this.columns = null;
  }

  public AuthorizeMsg getAuthMsg() {
    return this.authMsg;
  }

  public SaRequestSessSmember setAuthMsg(AuthorizeMsg authMsg) {
    this.authMsg = authMsg;
    return this;
  }

  public void unsetAuthMsg() {
    this.authMsg = null;
  }

  /** Returns true if field authMsg is set (has been assigned a value) and false otherwise */
  public boolean isSetAuthMsg() {
    return this.authMsg != null;
  }

  public void setAuthMsgIsSet(boolean value) {
    if (!value) {
      this.authMsg = null;
    }
  }

  public String getAppkey() {
    return this.appkey;
  }

  public SaRequestSessSmember setAppkey(String appkey) {
    this.appkey = appkey;
    return this;
  }

  public void unsetAppkey() {
    this.appkey = null;
  }

  /** Returns true if field appkey is set (has been assigned a value) and false otherwise */
  public boolean isSetAppkey() {
    return this.appkey != null;
  }

  public void setAppkeyIsSet(boolean value) {
    if (!value) {
      this.appkey = null;
    }
  }

  public String getUid() {
    return this.uid;
  }

  public SaRequestSessSmember setUid(String uid) {
    this.uid = uid;
    return this;
  }

  public void unsetUid() {
    this.uid = null;
  }

  /** Returns true if field uid is set (has been assigned a value) and false otherwise */
  public boolean isSetUid() {
    return this.uid != null;
  }

  public void setUidIsSet(boolean value) {
    if (!value) {
      this.uid = null;
    }
  }

  public int getTidsSize() {
    return (this.tids == null) ? 0 : this.tids.size();
  }

  public java.util.Iterator<String> getTidsIterator() {
    return (this.tids == null) ? null : this.tids.iterator();
  }

  public void addToTids(String elem) {
    if (this.tids == null) {
      this.tids = new ArrayList<String>();
    }
    this.tids.add(elem);
  }

  public List<String> getTids() {
    return this.tids;
  }

  public SaRequestSessSmember setTids(List<String> tids) {
    this.tids = tids;
    return this;
  }

  public void unsetTids() {
    this.tids = null;
  }

  /** Returns true if field tids is set (has been assigned a value) and false otherwise */
  public boolean isSetTids() {
    return this.tids != null;
  }

  public void setTidsIsSet(boolean value) {
    if (!value) {
      this.tids = null;
    }
  }

  public int getColumnsSize() {
    return (this.columns == null) ? 0 : this.columns.size();
  }

  public java.util.Iterator<String> getColumnsIterator() {
    return (this.columns == null) ? null : this.columns.iterator();
  }

  public void addToColumns(String elem) {
    if (this.columns == null) {
      this.columns = new ArrayList<String>();
    }
    this.columns.add(elem);
  }

  public List<String> getColumns() {
    return this.columns;
  }

  public SaRequestSessSmember setColumns(List<String> columns) {
    this.columns = columns;
    return this;
  }

  public void unsetColumns() {
    this.columns = null;
  }

  /** Returns true if field columns is set (has been assigned a value) and false otherwise */
  public boolean isSetColumns() {
    return this.columns != null;
  }

  public void setColumnsIsSet(boolean value) {
    if (!value) {
      this.columns = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case AUTH_MSG:
      if (value == null) {
        unsetAuthMsg();
      } else {
        setAuthMsg((AuthorizeMsg)value);
      }
      break;

    case APPKEY:
      if (value == null) {
        unsetAppkey();
      } else {
        setAppkey((String)value);
      }
      break;

    case UID:
      if (value == null) {
        unsetUid();
      } else {
        setUid((String)value);
      }
      break;

    case TIDS:
      if (value == null) {
        unsetTids();
      } else {
        setTids((List<String>)value);
      }
      break;

    case COLUMNS:
      if (value == null) {
        unsetColumns();
      } else {
        setColumns((List<String>)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case AUTH_MSG:
      return getAuthMsg();

    case APPKEY:
      return getAppkey();

    case UID:
      return getUid();

    case TIDS:
      return getTids();

    case COLUMNS:
      return getColumns();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case AUTH_MSG:
      return isSetAuthMsg();
    case APPKEY:
      return isSetAppkey();
    case UID:
      return isSetUid();
    case TIDS:
      return isSetTids();
    case COLUMNS:
      return isSetColumns();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof SaRequestSessSmember)
      return this.equals((SaRequestSessSmember)that);
    return false;
  }

  public boolean equals(SaRequestSessSmember that) {
    if (that == null)
      return false;

    boolean this_present_authMsg = true && this.isSetAuthMsg();
    boolean that_present_authMsg = true && that.isSetAuthMsg();
    if (this_present_authMsg || that_present_authMsg) {
      if (!(this_present_authMsg && that_present_authMsg))
        return false;
      if (!this.authMsg.equals(that.authMsg))
        return false;
    }

    boolean this_present_appkey = true && this.isSetAppkey();
    boolean that_present_appkey = true && that.isSetAppkey();
    if (this_present_appkey || that_present_appkey) {
      if (!(this_present_appkey && that_present_appkey))
        return false;
      if (!this.appkey.equals(that.appkey))
        return false;
    }

    boolean this_present_uid = true && this.isSetUid();
    boolean that_present_uid = true && that.isSetUid();
    if (this_present_uid || that_present_uid) {
      if (!(this_present_uid && that_present_uid))
        return false;
      if (!this.uid.equals(that.uid))
        return false;
    }

    boolean this_present_tids = true && this.isSetTids();
    boolean that_present_tids = true && that.isSetTids();
    if (this_present_tids || that_present_tids) {
      if (!(this_present_tids && that_present_tids))
        return false;
      if (!this.tids.equals(that.tids))
        return false;
    }

    boolean this_present_columns = true && this.isSetColumns();
    boolean that_present_columns = true && that.isSetColumns();
    if (this_present_columns || that_present_columns) {
      if (!(this_present_columns && that_present_columns))
        return false;
      if (!this.columns.equals(that.columns))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_authMsg = true && (isSetAuthMsg());
    list.add(present_authMsg);
    if (present_authMsg)
      list.add(authMsg);

    boolean present_appkey = true && (isSetAppkey());
    list.add(present_appkey);
    if (present_appkey)
      list.add(appkey);

    boolean present_uid = true && (isSetUid());
    list.add(present_uid);
    if (present_uid)
      list.add(uid);

    boolean present_tids = true && (isSetTids());
    list.add(present_tids);
    if (present_tids)
      list.add(tids);

    boolean present_columns = true && (isSetColumns());
    list.add(present_columns);
    if (present_columns)
      list.add(columns);

    return list.hashCode();
  }

  @Override
  public int compareTo(SaRequestSessSmember other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetAuthMsg()).compareTo(other.isSetAuthMsg());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAuthMsg()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.authMsg, other.authMsg);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetAppkey()).compareTo(other.isSetAppkey());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAppkey()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.appkey, other.appkey);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetUid()).compareTo(other.isSetUid());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUid()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.uid, other.uid);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetTids()).compareTo(other.isSetTids());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetTids()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.tids, other.tids);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetColumns()).compareTo(other.isSetColumns());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetColumns()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.columns, other.columns);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("SaRequestSessSmember(");
    boolean first = true;

    sb.append("authMsg:");
    if (this.authMsg == null) {
      sb.append("null");
    } else {
      sb.append(this.authMsg);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("appkey:");
    if (this.appkey == null) {
      sb.append("null");
    } else {
      sb.append(this.appkey);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("uid:");
    if (this.uid == null) {
      sb.append("null");
    } else {
      sb.append(this.uid);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("tids:");
    if (this.tids == null) {
      sb.append("null");
    } else {
      sb.append(this.tids);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("columns:");
    if (this.columns == null) {
      sb.append("null");
    } else {
      sb.append(this.columns);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
    if (authMsg != null) {
      authMsg.validate();
    }
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class SaRequestSessSmemberStandardSchemeFactory implements SchemeFactory {
    public SaRequestSessSmemberStandardScheme getScheme() {
      return new SaRequestSessSmemberStandardScheme();
    }
  }

  private static class SaRequestSessSmemberStandardScheme extends StandardScheme<SaRequestSessSmember> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, SaRequestSessSmember struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // AUTH_MSG
            if (schemeField.type == org.apache.thrift.protocol.TType.STRUCT) {
              struct.authMsg = new AuthorizeMsg();
              struct.authMsg.read(iprot);
              struct.setAuthMsgIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // APPKEY
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.appkey = iprot.readString();
              struct.setAppkeyIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // UID
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.uid = iprot.readString();
              struct.setUidIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // TIDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list172 = iprot.readListBegin();
                struct.tids = new ArrayList<String>(_list172.size);
                String _elem173;
                for (int _i174 = 0; _i174 < _list172.size; ++_i174)
                {
                  _elem173 = iprot.readString();
                  struct.tids.add(_elem173);
                }
                iprot.readListEnd();
              }
              struct.setTidsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 5: // COLUMNS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list175 = iprot.readListBegin();
                struct.columns = new ArrayList<String>(_list175.size);
                String _elem176;
                for (int _i177 = 0; _i177 < _list175.size; ++_i177)
                {
                  _elem176 = iprot.readString();
                  struct.columns.add(_elem176);
                }
                iprot.readListEnd();
              }
              struct.setColumnsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, SaRequestSessSmember struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.authMsg != null) {
        oprot.writeFieldBegin(AUTH_MSG_FIELD_DESC);
        struct.authMsg.write(oprot);
        oprot.writeFieldEnd();
      }
      if (struct.appkey != null) {
        oprot.writeFieldBegin(APPKEY_FIELD_DESC);
        oprot.writeString(struct.appkey);
        oprot.writeFieldEnd();
      }
      if (struct.uid != null) {
        oprot.writeFieldBegin(UID_FIELD_DESC);
        oprot.writeString(struct.uid);
        oprot.writeFieldEnd();
      }
      if (struct.tids != null) {
        oprot.writeFieldBegin(TIDS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.tids.size()));
          for (String _iter178 : struct.tids)
          {
            oprot.writeString(_iter178);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.columns != null) {
        oprot.writeFieldBegin(COLUMNS_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, struct.columns.size()));
          for (String _iter179 : struct.columns)
          {
            oprot.writeString(_iter179);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class SaRequestSessSmemberTupleSchemeFactory implements SchemeFactory {
    public SaRequestSessSmemberTupleScheme getScheme() {
      return new SaRequestSessSmemberTupleScheme();
    }
  }

  private static class SaRequestSessSmemberTupleScheme extends TupleScheme<SaRequestSessSmember> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, SaRequestSessSmember struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetAuthMsg()) {
        optionals.set(0);
      }
      if (struct.isSetAppkey()) {
        optionals.set(1);
      }
      if (struct.isSetUid()) {
        optionals.set(2);
      }
      if (struct.isSetTids()) {
        optionals.set(3);
      }
      if (struct.isSetColumns()) {
        optionals.set(4);
      }
      oprot.writeBitSet(optionals, 5);
      if (struct.isSetAuthMsg()) {
        struct.authMsg.write(oprot);
      }
      if (struct.isSetAppkey()) {
        oprot.writeString(struct.appkey);
      }
      if (struct.isSetUid()) {
        oprot.writeString(struct.uid);
      }
      if (struct.isSetTids()) {
        {
          oprot.writeI32(struct.tids.size());
          for (String _iter180 : struct.tids)
          {
            oprot.writeString(_iter180);
          }
        }
      }
      if (struct.isSetColumns()) {
        {
          oprot.writeI32(struct.columns.size());
          for (String _iter181 : struct.columns)
          {
            oprot.writeString(_iter181);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, SaRequestSessSmember struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(5);
      if (incoming.get(0)) {
        struct.authMsg = new AuthorizeMsg();
        struct.authMsg.read(iprot);
        struct.setAuthMsgIsSet(true);
      }
      if (incoming.get(1)) {
        struct.appkey = iprot.readString();
        struct.setAppkeyIsSet(true);
      }
      if (incoming.get(2)) {
        struct.uid = iprot.readString();
        struct.setUidIsSet(true);
      }
      if (incoming.get(3)) {
        {
          org.apache.thrift.protocol.TList _list182 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.tids = new ArrayList<String>(_list182.size);
          String _elem183;
          for (int _i184 = 0; _i184 < _list182.size; ++_i184)
          {
            _elem183 = iprot.readString();
            struct.tids.add(_elem183);
          }
        }
        struct.setTidsIsSet(true);
      }
      if (incoming.get(4)) {
        {
          org.apache.thrift.protocol.TList _list185 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRING, iprot.readI32());
          struct.columns = new ArrayList<String>(_list185.size);
          String _elem186;
          for (int _i187 = 0; _i187 < _list185.size; ++_i187)
          {
            _elem186 = iprot.readString();
            struct.columns.add(_elem186);
          }
        }
        struct.setColumnsIsSet(true);
      }
    }
  }

}

