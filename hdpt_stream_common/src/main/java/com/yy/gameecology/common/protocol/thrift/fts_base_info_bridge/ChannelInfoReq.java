/**
 * Autogenerated by Thrift Compiler (0.9.3)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge;

import org.apache.thrift.scheme.IScheme;
import org.apache.thrift.scheme.SchemeFactory;
import org.apache.thrift.scheme.StandardScheme;

import org.apache.thrift.scheme.TupleScheme;
import org.apache.thrift.protocol.TTupleProtocol;
import org.apache.thrift.protocol.TProtocolException;
import org.apache.thrift.EncodingUtils;
import org.apache.thrift.TException;
import org.apache.thrift.async.AsyncMethodCallback;
import org.apache.thrift.server.AbstractNonblockingServer.*;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;
import java.util.EnumMap;
import java.util.Set;
import java.util.HashSet;
import java.util.EnumSet;
import java.util.Collections;
import java.util.BitSet;
import java.nio.ByteBuffer;
import java.util.Arrays;
import javax.annotation.Generated;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked"})
@Generated(value = "Autogenerated by Thrift Compiler (0.9.3)", date = "2021-09-08")
public class ChannelInfoReq implements org.apache.thrift.TBase<ChannelInfoReq, ChannelInfoReq._Fields>, java.io.Serializable, Cloneable, Comparable<ChannelInfoReq> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("ChannelInfoReq");

  private static final org.apache.thrift.protocol.TField CHANNEL_LIST_FIELD_DESC = new org.apache.thrift.protocol.TField("channel_list", org.apache.thrift.protocol.TType.LIST, (short)1);
  private static final org.apache.thrift.protocol.TField EXPAND_FIELD_DESC = new org.apache.thrift.protocol.TField("expand", org.apache.thrift.protocol.TType.STRING, (short)2);

  private static final Map<Class<? extends IScheme>, SchemeFactory> schemes = new HashMap<Class<? extends IScheme>, SchemeFactory>();
  static {
    schemes.put(StandardScheme.class, new ChannelInfoReqStandardSchemeFactory());
    schemes.put(TupleScheme.class, new ChannelInfoReqTupleSchemeFactory());
  }

  public List<ChannelId> channel_list; // required
  public String expand; // required

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    CHANNEL_LIST((short)1, "channel_list"),
    EXPAND((short)2, "expand");

    private static final Map<String, _Fields> byName = new HashMap<String, _Fields>();

    static {
      for (_Fields field : EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // CHANNEL_LIST
          return CHANNEL_LIST;
        case 2: // EXPAND
          return EXPAND;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    public static _Fields findByName(String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final String _fieldName;

    _Fields(short thriftId, String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  public static final Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.CHANNEL_LIST, new org.apache.thrift.meta_data.FieldMetaData("channel_list", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, ChannelId.class))));
    tmpMap.put(_Fields.EXPAND, new org.apache.thrift.meta_data.FieldMetaData("expand", org.apache.thrift.TFieldRequirementType.DEFAULT, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.STRING)));
    metaDataMap = Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(ChannelInfoReq.class, metaDataMap);
  }

  public ChannelInfoReq() {
  }

  public ChannelInfoReq(
    List<ChannelId> channel_list,
    String expand)
  {
    this();
    this.channel_list = channel_list;
    this.expand = expand;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public ChannelInfoReq(ChannelInfoReq other) {
    if (other.isSetChannel_list()) {
      List<ChannelId> __this__channel_list = new ArrayList<ChannelId>(other.channel_list.size());
      for (ChannelId other_element : other.channel_list) {
        __this__channel_list.add(new ChannelId(other_element));
      }
      this.channel_list = __this__channel_list;
    }
    if (other.isSetExpand()) {
      this.expand = other.expand;
    }
  }

  public ChannelInfoReq deepCopy() {
    return new ChannelInfoReq(this);
  }

  @Override
  public void clear() {
    this.channel_list = null;
    this.expand = null;
  }

  public int getChannel_listSize() {
    return (this.channel_list == null) ? 0 : this.channel_list.size();
  }

  public java.util.Iterator<ChannelId> getChannel_listIterator() {
    return (this.channel_list == null) ? null : this.channel_list.iterator();
  }

  public void addToChannel_list(ChannelId elem) {
    if (this.channel_list == null) {
      this.channel_list = new ArrayList<ChannelId>();
    }
    this.channel_list.add(elem);
  }

  public List<ChannelId> getChannel_list() {
    return this.channel_list;
  }

  public ChannelInfoReq setChannel_list(List<ChannelId> channel_list) {
    this.channel_list = channel_list;
    return this;
  }

  public void unsetChannel_list() {
    this.channel_list = null;
  }

  /** Returns true if field channel_list is set (has been assigned a value) and false otherwise */
  public boolean isSetChannel_list() {
    return this.channel_list != null;
  }

  public void setChannel_listIsSet(boolean value) {
    if (!value) {
      this.channel_list = null;
    }
  }

  public String getExpand() {
    return this.expand;
  }

  public ChannelInfoReq setExpand(String expand) {
    this.expand = expand;
    return this;
  }

  public void unsetExpand() {
    this.expand = null;
  }

  /** Returns true if field expand is set (has been assigned a value) and false otherwise */
  public boolean isSetExpand() {
    return this.expand != null;
  }

  public void setExpandIsSet(boolean value) {
    if (!value) {
      this.expand = null;
    }
  }

  public void setFieldValue(_Fields field, Object value) {
    switch (field) {
    case CHANNEL_LIST:
      if (value == null) {
        unsetChannel_list();
      } else {
        setChannel_list((List<ChannelId>)value);
      }
      break;

    case EXPAND:
      if (value == null) {
        unsetExpand();
      } else {
        setExpand((String)value);
      }
      break;

    }
  }

  public Object getFieldValue(_Fields field) {
    switch (field) {
    case CHANNEL_LIST:
      return getChannel_list();

    case EXPAND:
      return getExpand();

    }
    throw new IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new IllegalArgumentException();
    }

    switch (field) {
    case CHANNEL_LIST:
      return isSetChannel_list();
    case EXPAND:
      return isSetExpand();
    }
    throw new IllegalStateException();
  }

  @Override
  public boolean equals(Object that) {
    if (that == null)
      return false;
    if (that instanceof ChannelInfoReq)
      return this.equals((ChannelInfoReq)that);
    return false;
  }

  public boolean equals(ChannelInfoReq that) {
    if (that == null)
      return false;

    boolean this_present_channel_list = true && this.isSetChannel_list();
    boolean that_present_channel_list = true && that.isSetChannel_list();
    if (this_present_channel_list || that_present_channel_list) {
      if (!(this_present_channel_list && that_present_channel_list))
        return false;
      if (!this.channel_list.equals(that.channel_list))
        return false;
    }

    boolean this_present_expand = true && this.isSetExpand();
    boolean that_present_expand = true && that.isSetExpand();
    if (this_present_expand || that_present_expand) {
      if (!(this_present_expand && that_present_expand))
        return false;
      if (!this.expand.equals(that.expand))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    List<Object> list = new ArrayList<Object>();

    boolean present_channel_list = true && (isSetChannel_list());
    list.add(present_channel_list);
    if (present_channel_list)
      list.add(channel_list);

    boolean present_expand = true && (isSetExpand());
    list.add(present_expand);
    if (present_expand)
      list.add(expand);

    return list.hashCode();
  }

  @Override
  public int compareTo(ChannelInfoReq other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = Boolean.valueOf(isSetChannel_list()).compareTo(other.isSetChannel_list());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetChannel_list()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.channel_list, other.channel_list);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = Boolean.valueOf(isSetExpand()).compareTo(other.isSetExpand());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetExpand()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.expand, other.expand);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    schemes.get(iprot.getScheme()).getScheme().read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    schemes.get(oprot.getScheme()).getScheme().write(oprot, this);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder("ChannelInfoReq(");
    boolean first = true;

    sb.append("channel_list:");
    if (this.channel_list == null) {
      sb.append("null");
    } else {
      sb.append(this.channel_list);
    }
    first = false;
    if (!first) sb.append(", ");
    sb.append("expand:");
    if (this.expand == null) {
      sb.append("null");
    } else {
      sb.append(this.expand);
    }
    first = false;
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, ClassNotFoundException {
    try {
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class ChannelInfoReqStandardSchemeFactory implements SchemeFactory {
    public ChannelInfoReqStandardScheme getScheme() {
      return new ChannelInfoReqStandardScheme();
    }
  }

  private static class ChannelInfoReqStandardScheme extends StandardScheme<ChannelInfoReq> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, ChannelInfoReq struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // CHANNEL_LIST
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list62 = iprot.readListBegin();
                struct.channel_list = new ArrayList<ChannelId>(_list62.size);
                ChannelId _elem63;
                for (int _i64 = 0; _i64 < _list62.size; ++_i64)
                {
                  _elem63 = new ChannelId();
                  _elem63.read(iprot);
                  struct.channel_list.add(_elem63);
                }
                iprot.readListEnd();
              }
              struct.setChannel_listIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // EXPAND
            if (schemeField.type == org.apache.thrift.protocol.TType.STRING) {
              struct.expand = iprot.readString();
              struct.setExpandIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, ChannelInfoReq struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.channel_list != null) {
        oprot.writeFieldBegin(CHANNEL_LIST_FIELD_DESC);
        {
          oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.channel_list.size()));
          for (ChannelId _iter65 : struct.channel_list)
          {
            _iter65.write(oprot);
          }
          oprot.writeListEnd();
        }
        oprot.writeFieldEnd();
      }
      if (struct.expand != null) {
        oprot.writeFieldBegin(EXPAND_FIELD_DESC);
        oprot.writeString(struct.expand);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class ChannelInfoReqTupleSchemeFactory implements SchemeFactory {
    public ChannelInfoReqTupleScheme getScheme() {
      return new ChannelInfoReqTupleScheme();
    }
  }

  private static class ChannelInfoReqTupleScheme extends TupleScheme<ChannelInfoReq> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, ChannelInfoReq struct) throws org.apache.thrift.TException {
      TTupleProtocol oprot = (TTupleProtocol) prot;
      BitSet optionals = new BitSet();
      if (struct.isSetChannel_list()) {
        optionals.set(0);
      }
      if (struct.isSetExpand()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetChannel_list()) {
        {
          oprot.writeI32(struct.channel_list.size());
          for (ChannelId _iter66 : struct.channel_list)
          {
            _iter66.write(oprot);
          }
        }
      }
      if (struct.isSetExpand()) {
        oprot.writeString(struct.expand);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, ChannelInfoReq struct) throws org.apache.thrift.TException {
      TTupleProtocol iprot = (TTupleProtocol) prot;
      BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list67 = new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, iprot.readI32());
          struct.channel_list = new ArrayList<ChannelId>(_list67.size);
          ChannelId _elem68;
          for (int _i69 = 0; _i69 < _list67.size; ++_i69)
          {
            _elem68 = new ChannelId();
            _elem68.read(iprot);
            struct.channel_list.add(_elem68);
          }
        }
        struct.setChannel_listIsSet(true);
      }
      if (incoming.get(1)) {
        struct.expand = iprot.readString();
        struct.setExpandIsSet(true);
      }
    }
  }

}

