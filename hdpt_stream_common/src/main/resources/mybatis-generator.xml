<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- 执行命令：mvn mybatis-generator:generate -->
<generatorConfiguration>

    <context id="yy" targetRuntime="MyBatis3">

        <property name="javaFileEncoding" value="UTF-8"/>

        <commentGenerator>
            <property name="addRemarkComments" value="true"/>
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true" />
        </commentGenerator>

        <!--数据连接信息-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*******************************************"
                        userId="helloworld" password="JaZiIwRMR1XbW2GTi579ajKG">
        </jdbcConnection>

        <!--模型-->
        <javaModelGenerator targetPackage="com.yy.gameecology.common.db.model.gameecology"
                            targetProject="src\main\java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!--MapperInterface-->
        <sqlMapGenerator targetPackage="mapper"
                         targetProject="src\main\resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!--mapper.xml-->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.yy.gameecology.common.db.mapper.gameecology"
                             targetProject="src\main\java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--表格信息, 用完即注释-->
        <table tableName="re_rule_test_case"
               enableSelectByExample="false"
               enableCountByExample="false"
               enableDeleteByExample="false"
               enableUpdateByExample="false"
        />

    </context>


</generatorConfiguration>
