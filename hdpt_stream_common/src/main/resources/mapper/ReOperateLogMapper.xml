<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.gameecology.ReOperateLogMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.ReOperateLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="origin" jdbcType="VARCHAR" property="origin" />
    <result column="operate" jdbcType="VARCHAR" property="operate" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.yy.gameecology.common.db.model.gameecology.ReOperateLog">
    <result column="param" jdbcType="LONGVARCHAR" property="param" />
  </resultMap>
  <sql id="Base_Column_List">
    id, origin, operate, result, operator, create_time
  </sql>
  <sql id="Blob_Column_List">
    param
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from re_operate_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from re_operate_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.ReOperateLog">
    insert into re_operate_log (id, origin, operate, 
      result, operator, create_time, 
      param)
    values (#{id,jdbcType=BIGINT}, #{origin,jdbcType=VARCHAR}, #{operate,jdbcType=VARCHAR}, 
      #{result,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{param,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.gameecology.common.db.model.gameecology.ReOperateLog">
    insert into re_operate_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="origin != null">
        origin,
      </if>
      <if test="operate != null">
        operate,
      </if>
      <if test="result != null">
        result,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="param != null">
        param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="origin != null">
        #{origin,jdbcType=VARCHAR},
      </if>
      <if test="operate != null">
        #{operate,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        #{result,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null">
        #{param,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.ReOperateLog">
    update re_operate_log
    <set>
      <if test="origin != null">
        origin = #{origin,jdbcType=VARCHAR},
      </if>
      <if test="operate != null">
        operate = #{operate,jdbcType=VARCHAR},
      </if>
      <if test="result != null">
        result = #{result,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="param != null">
        param = #{param,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.yy.gameecology.common.db.model.gameecology.ReOperateLog">
    update re_operate_log
    set origin = #{origin,jdbcType=VARCHAR},
      operate = #{operate,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      param = #{param,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.gameecology.common.db.model.gameecology.ReOperateLog">
    update re_operate_log
    set origin = #{origin,jdbcType=VARCHAR},
      operate = #{operate,jdbcType=VARCHAR},
      result = #{result,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
