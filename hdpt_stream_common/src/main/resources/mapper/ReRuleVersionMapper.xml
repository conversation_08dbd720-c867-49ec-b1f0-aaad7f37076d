<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.gameecology.ReRuleVersionMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.ReRuleVersion">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_passport" jdbcType="VARCHAR" property="createPassport" />
    <result column="enable_time" jdbcType="TIMESTAMP" property="enableTime" />
    <result column="enable_passport" jdbcType="VARCHAR" property="enablePassport" />
    <result column="version_desc" jdbcType="VARCHAR" property="versionDesc" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.yy.gameecology.common.db.model.gameecology.ReRuleVersion">
    <result column="rule_config" jdbcType="LONGVARCHAR" property="ruleConfig" />
  </resultMap>
  <sql id="Base_Column_List">
    id, rule_id, status, create_time, create_passport, enable_time, enable_passport, version_desc
  </sql>
  <sql id="Blob_Column_List">
    rule_config
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from re_rule_version
    where id = #{id,jdbcType=BIGINT}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from re_rule_version
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleVersion">
    insert into re_rule_version (id, rule_id, status, 
      create_time, create_passport, enable_time, 
      enable_passport, rule_config, version_desc)
    values (#{id,jdbcType=BIGINT}, #{ruleId,jdbcType=BIGINT}, #{status,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createPassport,jdbcType=VARCHAR}, #{enableTime,jdbcType=TIMESTAMP}, 
      #{enablePassport,jdbcType=VARCHAR}, #{ruleConfig,jdbcType=LONGVARCHAR}, #{versionDesc})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleVersion">
    insert into re_rule_version
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createPassport != null">
        create_passport,
      </if>
      <if test="enableTime != null">
        enable_time,
      </if>
      <if test="enablePassport != null">
        enable_passport,
      </if>
      <if test="ruleConfig != null">
        rule_config,
      </if>
      <if test="versionDesc != null">
          version_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createPassport != null">
        #{createPassport,jdbcType=VARCHAR},
      </if>
      <if test="enableTime != null">
        #{enableTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enablePassport != null">
        #{enablePassport,jdbcType=VARCHAR},
      </if>
      <if test="ruleConfig != null">
        #{ruleConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="versionDesc != null">
        #{versionDesc},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleVersion">
    update re_rule_version
    <set>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createPassport != null">
        create_passport = #{createPassport,jdbcType=VARCHAR},
      </if>
      <if test="enableTime != null">
        enable_time = #{enableTime,jdbcType=TIMESTAMP},
      </if>
      <if test="enablePassport != null">
        enable_passport = #{enablePassport,jdbcType=VARCHAR},
      </if>
      <if test="ruleConfig != null">
        rule_config = #{ruleConfig,jdbcType=LONGVARCHAR},
      </if>
      <if test="versionDesc != null">
          version_desc = #{versionDesc},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleVersion">
    update re_rule_version
    set rule_id = #{ruleId,jdbcType=BIGINT},
      status = #{status,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_passport = #{createPassport,jdbcType=VARCHAR},
      enable_time = #{enableTime,jdbcType=TIMESTAMP},
      enable_passport = #{enablePassport,jdbcType=VARCHAR},
        version_desc = #{versionDesc},
      rule_config = #{ruleConfig,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleVersion">
    update re_rule_version
    set rule_id = #{ruleId,jdbcType=BIGINT},
      status = #{status,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_passport = #{createPassport,jdbcType=VARCHAR},
      enable_time = #{enableTime,jdbcType=TIMESTAMP},
      enable_passport = #{enablePassport,jdbcType=VARCHAR},
        version_desc = #{versionDesc}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <update id="enableRuleVersion">
        update re_rule_version set
            status = if(id = #{versionId}, 1, 0),
            enable_time = if(id = #{versionId}, now(), enable_time),
            enable_passport = if(id = #{versionId}, #{passport}, enable_passport)
        where rule_id = #{ruleId}
    </update>
    <update id="disableRuleVersion">
        update re_rule_version set status = 0 where id = #{id}
    </update>

    <select id="selectByRuleId" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from re_rule_version
        where rule_id = #{ruleId}
        order by id desc
    </select>
    <select id="selectEnabledVersion" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        from re_rule_version
        where rule_id = #{ruleId} and status = 1
    </select>
    <delete id="deleteByRuleId">
        delete from re_rule_version where rule_id = #{ruleId}
    </delete>
</mapper>
