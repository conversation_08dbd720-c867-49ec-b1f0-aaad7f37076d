<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.gameecology.ReRuleAppMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.ReRuleApp">
    <id column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleApp">
    insert into re_rule_app (app_id, app_name, create_time
      )
    values (#{appId,jdbcType=BIGINT}, #{appName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select app_id, app_name, create_time
    from re_rule_app
    where app_id = #{appId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    select app_id, app_name, create_time
    from re_rule_app
  </select>
</mapper>
