<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.gameecology.ReRuleScriptMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.ReRuleScript">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
    <result column="rule_script" jdbcType="LONGVARCHAR" property="ruleScript" />
    <result column="return_type" jdbcType="VARCHAR" property="returnType" />
    <result column="visual_config" jdbcType="LONGVARCHAR" property="visualConfig" />
    <result column="view_config" jdbcType="LONGVARCHAR" property="viewConfig" />
  </resultMap>
    <insert id="insertScript">
        insert into re_rule_script (rule_id, rule_type, rule_script, return_type
        <if test="visualConfig != null">
            ,visual_config
        </if>
        <if test="viewConfig != null">
            ,view_config
        </if>
        )
        values (#{ruleId}, #{ruleType}, #{ruleScript}, #{returnType}
        <if test="visualConfig != null">
            ,#{visualConfig}
        </if>
        <if test="viewConfig != null">
            ,#{viewConfig}
        </if>
        )
    </insert>
    <update id="updateRuleId">
        update re_rule_script set rule_id = #{ruleId} where rule_id = #{srcRuleId}
    </update>
    <update id="updateVisualConfig">
        update re_rule_script set visual_config = #{visualConfig}, view_config = #{viewConfig}, rule_script = #{ruleScript} where rule_id = #{ruleId}
    </update>
    <update id="updateScriptBaseInfo">
        update re_rule_script set rule_type = #{ruleType}, return_type = #{returnType} where rule_id = #{ruleId}
    </update>
    <delete id="deleteByRuleId">
        delete from re_rule_script where rule_id = #{ruleId}
    </delete>
    <select id="selectByRuleId" resultMap="BaseResultMap">
        select id, rule_id, rule_type, rule_script, return_type, visual_config, view_config
        from re_rule_script
        where rule_id = #{ruleId}
    </select>
</mapper>
