<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.gameecology.ReRuleMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.ReRule">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="rule_name" jdbcType="VARCHAR" property="ruleName" />
    <result column="rule_status" jdbcType="BIT" property="ruleStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_passport" jdbcType="VARCHAR" property="createPassport" />
    <result column="update_passport" jdbcType="VARCHAR" property="updatePassport" />
    <result column="config_type" jdbcType="SMALLINT" property="configType" />
    <result column="log_status" jdbcType="BIT" property="logStatus" />
    <result column="limit_second" jdbcType="BIT" property="limitSecond" />
  </resultMap>
    <sql id="Base_Column_List">
        id, app_id, rule_name, rule_status, create_time, update_time, create_passport, update_passport, config_type, log_status, limit_second
    </sql>
    <insert id="insertRule">
        insert into re_rule (id, app_id, rule_name, create_time, create_passport, update_time, update_passport, config_type)
        values (#{id}, #{appId}, #{ruleName}, now(), #{passport}, now(), #{passport}, #{configType})
    </insert>
    <update id="updateRule">
        update re_rule
        <set>
            <if test="id != null and id != srcId">
                id = #{id},
            </if>
            <if test="appId != null">
                app_id = #{appId},
            </if>
            <if test="ruleName != null">
                rule_name = #{ruleName},
            </if>
            <if test="ruleStatus != null">
                rule_status = #{ruleStatus},
            </if>
            <if test="configType != null">
                config_type = #{configType},
            </if>
            update_time = now(), update_passport = #{passport}
        </set>
        where id = #{srcId}
    </update>
    <delete id="deleteRule">
        delete from re_rule where id = #{id}
    </delete>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
      select <include refid="Base_Column_List"/>
      from re_rule
      where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectBy" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from re_rule
        <where>
            <if test="ruleId != null">
                and id = #{ruleId}
            </if>
            <if test="appId != null">
                and app_id = #{appId}
            </if>
            <if test="ruleName != null and ruleName != ''">
                and rule_name like concat('%', #{ruleName}, '%')
            </if>
            <if test="configType != null">
                and config_type = #{configType}
            </if>
            <if test="ruleStatus != null">
                and rule_status = #{ruleStatus}
            </if>
        </where>
        order by id desc
    </select>
    <select id="selectRuleByEventId" resultType="com.yy.gameecology.common.bean.vo.RuleEventInfo">
        select r.id ruleId, r.app_id appId, p.event_id eventId, p.event_act_id actId
        from re_rule r
        left join re_rule_param p on r.id = p.rule_id
        where r.rule_status = 1 and p.param_source = 3 and p.event_id = #{eventId}
    </select>

    <select id="selectAllActive" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from re_rule
        where rule_status = 1
    </select>

</mapper>
