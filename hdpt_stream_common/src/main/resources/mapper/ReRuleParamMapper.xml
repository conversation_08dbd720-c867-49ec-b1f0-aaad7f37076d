<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.gameecology.ReRuleParamMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.ReRuleParam">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="param_key" jdbcType="VARCHAR" property="paramKey" />
    <result column="param_type" jdbcType="VARCHAR" property="paramType" />
    <result column="param_default" jdbcType="VARCHAR" property="paramDefault" />
    <result column="param_desc" jdbcType="VARCHAR" property="paramDesc" />
    <result column="param_source" jdbcType="INTEGER" property="paramSource" />
    <result column="expression" jdbcType="VARCHAR" property="expression" />
    <result column="function_key" jdbcType="VARCHAR" property="functionKey" />
    <result column="function_param" jdbcType="VARCHAR" property="functionParam" />
    <result column="visual_status" jdbcType="BIT" property="visualStatus" />
    <result column="event_id" jdbcType="INTEGER" property="eventId" />
    <result column="event_act_id" jdbcType="BIGINT" property="eventActId" />
  </resultMap>
    <insert id="insertParam">
        insert into re_rule_param (rule_id, param_key, param_type, param_default, param_desc,
                                   param_source, expression, function_key,function_param, visual_status, event_id, event_act_id)
        values (#{ruleId}, #{paramKey}, #{paramType}, #{paramDefault}, #{paramDesc},
                #{paramSource}, #{expression}, #{functionKey}, #{functionParam}, #{visualStatus}, #{eventId}, #{eventActId})
    </insert>
    <update id="updateRuleId">
        update re_rule_param set rule_id = #{ruleId} where rule_id = #{srcRuleId}
    </update>
    <delete id="deleteByRuleId">
        delete from re_rule_param where rule_id = #{id}
    </delete>
    <select id="selectByRuleId" resultMap="BaseResultMap">
        select id, rule_id, param_key, param_type, param_default, param_desc, param_source,
               expression, function_key, function_param, visual_status, event_id, event_act_id
        from re_rule_param
        where rule_id = #{ruleId}
    </select>
</mapper>
