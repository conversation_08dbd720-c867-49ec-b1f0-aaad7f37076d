<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.gameecology.ReRuleResultMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.ReRuleResult">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="ret_value" jdbcType="VARCHAR" property="retValue" />
    <result column="ret_desc" jdbcType="VARCHAR" property="retDesc" />
  </resultMap>
  <sql id="Base_Column_List">
    id, rule_id, ret_value, ret_desc
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from re_rule_result
    where id = #{id,jdbcType=BIGINT}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from re_rule_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleResult">
    insert into re_rule_result (id, rule_id, ret_value, 
      ret_desc)
    values (#{id,jdbcType=BIGINT}, #{ruleId,jdbcType=BIGINT}, #{retValue,jdbcType=VARCHAR}, 
      #{retDesc,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleResult">
    insert into re_rule_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="retValue != null">
        ret_value,
      </if>
      <if test="retDesc != null">
        ret_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="retValue != null">
        #{retValue,jdbcType=VARCHAR},
      </if>
      <if test="retDesc != null">
        #{retDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleResult">
    update re_rule_result
    <set>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="retValue != null">
        ret_value = #{retValue,jdbcType=VARCHAR},
      </if>
      <if test="retDesc != null">
        ret_desc = #{retDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleResult">
    update re_rule_result
    set rule_id = #{ruleId,jdbcType=BIGINT},
      ret_value = #{retValue,jdbcType=VARCHAR},
      ret_desc = #{retDesc,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <delete id="deleteByRuleId">
        delete from re_rule_result where rule_id = #{ruleId}
    </delete>
    <select id="selectByRuleId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from re_rule_result
        where rule_id = #{ruleId}
    </select>

</mapper>
