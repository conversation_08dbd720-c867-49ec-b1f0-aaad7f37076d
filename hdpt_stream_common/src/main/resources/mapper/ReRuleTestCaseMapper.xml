<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.gameecology.ReRuleTestCaseMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.ReRuleTestCase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="edit_time" jdbcType="TIMESTAMP" property="editTime" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="test_param" jdbcType="LONGVARCHAR" property="testParam" />
  </resultMap>
  <sql id="Base_Column_List">
    id, rule_id, edit_time, status, test_param
  </sql>

  <select id="selectByRuleId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from re_rule_test_case
    where rule_id = #{ruleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from re_rule_test_case
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <update id="updateByPrimaryKeySelective" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleTestCase">
    update re_rule_test_case
    <set>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="editTime != null">
        edit_time = #{editTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="testParam != null">
        test_param = #{testParam,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertSelective" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleTestCase">
    insert into re_rule_test_case
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="editTime != null">
        edit_time,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="testParam != null">
        test_param,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=BIGINT},
      </if>
      <if test="editTime != null">
        #{editTime,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="testParam != null">
        #{testParam,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
    <insert id="insertOrUpdate">
        insert into re_rule_test_case (rule_id, edit_time, test_param)
        values (#{ruleId}, now(), #{testParam})
        on duplicate key update test_param = #{testParam}, edit_time = now()
    </insert>
</mapper>
