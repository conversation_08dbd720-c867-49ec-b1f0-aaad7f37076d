<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.gameecology.ReRuleEvalRecordMapper">
  <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.ReRuleEvalRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="app_id" jdbcType="BIGINT" property="appId" />
    <result column="rule_id" jdbcType="BIGINT" property="ruleId" />
    <result column="eval_result" jdbcType="INTEGER" property="evalResult" />
    <result column="eval_msg" jdbcType="VARCHAR" property="evalMsg" />
    <result column="eval_param" jdbcType="VARCHAR" property="evalParam" />
    <result column="eval_return" jdbcType="VARCHAR" property="evalReturn" />
    <result column="eval_time" jdbcType="TIMESTAMP" property="evalTime" />
  </resultMap>
  <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.ReRuleEvalRecord">
    insert into re_rule_eval_record_${@com.yy.gameecology.common.utils.TableNameUtil@yyyyMM(evalTime)}
        (id, app_id, rule_id,
        eval_result, eval_msg, eval_param,
        eval_return, eval_time)
    values (#{id,jdbcType=BIGINT}, #{appId,jdbcType=BIGINT}, #{ruleId,jdbcType=BIGINT}, 
      #{evalResult,jdbcType=INTEGER}, #{evalMsg,jdbcType=VARCHAR}, #{evalParam,jdbcType=VARCHAR}, 
      #{evalReturn,jdbcType=VARCHAR}, #{evalTime,jdbcType=TIMESTAMP})
  </insert>
</mapper>
