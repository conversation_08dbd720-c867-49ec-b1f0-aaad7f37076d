-- 要操作的成员
local field = ARGV[1]

-- 累加步长（可正、可负）
local step = tonumber(ARGV[2])

-- 限制值
local limit = tonumber(ARGV[3])

local canPartialAdd = tonumber(ARGV[4])

local partialAdd = 0;

-- 提取当前分值
local oldScore = tonumber(redis.call('HGET', KEYS[1], field) or 0)

if step >= 0 then
    if oldScore >= limit then
        return { -1, oldScore }
    -- 当 step为正时， incr 后不能超过 limit，
    elseif oldScore + step > limit and canPartialAdd == 0 then
        return { -1, oldScore }
    elseif oldScore + step > limit and canPartialAdd == 1 then
        -- 部分增加
        partialAdd = limit - oldScore;
    end
else
    if oldScore <= limit then
        return { -2, oldScore }
    -- 当 step 为负时 incr 后不能低于 limit
    elseif oldScore + step < limit and canPartialAdd == 0 then
        return { -2, oldScore }
    elseif oldScore + step < limit and canPartialAdd == 1 then
        -- 部分增加
        partialAdd = limit - oldScore;
    end
end

if (partialAdd == 0) then
    local newScore = tonumber(redis.call('HINCRBY', KEYS[1], field, step) or 0)
    return { 1, newScore }
else
    tonumber(redis.call('HINCRBY', KEYS[1], field, partialAdd) or 0)
    return { 2, partialAdd }
end
