
namespace java com.yy.gameecology.common.protocol.thrift.rule_engine

struct ExecuteRuleReq {
    1:i64 appId
    2:i64 ruleId
    3:string paramJSON
    4:string expand
}

struct ExecuteRuleResult {
    1:i32 code // 0-成功 其他失败 规则不存在或规则关闭时返回404
    2:string msg // 一般为错误提示
    3:string resultJSON
    4:string expand
}


service RuleEngineService {

    void ping()

    ExecuteRuleResult executeRule(1:ExecuteRuleReq req)

}

//s2sname
//生产: rule_engine_server
//测试: rule_engine_server_test
