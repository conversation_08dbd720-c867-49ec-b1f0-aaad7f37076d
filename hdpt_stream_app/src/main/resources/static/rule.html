<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>规则配置</title>

    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
    <script src="./js/moment.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">

    <script src="./js/codemirror/codemirror.js"></script>
    <link href="./js/codemirror/codemirror.css" rel="stylesheet">
    <script src="./js/codemirror/mode-javascript.js"></script>
    <link rel="stylesheet" href="./js/codemirror/theme-darcula.css">
    <link rel="stylesheet" href="./js/codemirror/theme-idea.css">
    <script src="./js/codemirror/addon-closebrackets.js"></script>
    <script src="./js/codemirror/addon-matchbrackets.js"></script>
    <script src="./js/codemirror/addon-active-line.js"></script>
    <script src="./js/codemirror/addon-foldcode.js"></script>
    <link rel="stylesheet" href="./js/codemirror/addon-foldgutter.css">
    <script src="./js/codemirror/addon-foldgutter.js"></script>
    <script src="./js/codemirror/addon-brace-fold.js"></script>
<!--    <script src="./js/codemirror/addon-show-hint.js"></script>-->
<!--    <link rel="stylesheet" href="./js/codemirror/addon-show-hint.css">-->
<!--    <script src="./js/codemirror/addon-javascript-hint.js"></script>-->
    <script src="./js/codemirror/addon-fullscreen.js"></script>
    <link rel="stylesheet" href="./js/codemirror/addon-fullscreen.css">
    <script src="./js/codemirror/addon-merge.js"></script>
    <link rel="stylesheet" href="./js/codemirror/addon-merge.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/diff_match_patch/20121119/diff_match_patch.js"></script>
    <script src="./js/codemirror/addon-searchcursor.js"></script>
    <script src="./js/codemirror/addon-match-highlighter.js"></script>
    <script src="./js/codemirror/addon-annotatescrollbar.js"></script>
    <script src="./js/codemirror/addon-matchesonscrollbar.js"></script>
    <link rel="stylesheet" href="./js/codemirror/addon-matchesonscrollbar.css">

    <style>
        .cm-matchhighlight {background-color: lightgreen;}
        .CodeMirror-selection-highlight-scrollbar {background-color: limegreen;}
    </style>

</head>
<body>

<div id="app">

    <el-container>

        <el-header height="120" style="padding-top: 10px">
            <el-row>
                <el-form :inline="true" :model="searchParam">
                    <el-button :type="this.isProd ? 'danger' : 'success'" round @click="onClickEnv">
                        {{this.isProd ? '生产环境' : '测试环境'}}
                    </el-button>
                    <el-form-item label="规则ID">
                        <el-input v-model.number="searchParam.ruleId" placeholder="规则id" @input="onSearch({page:1,delay:200})" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="AppId" v-if="!appId">
                        <el-select v-model="searchParam.appId" placeholder="AppId" @change="onSearch({page:1})" clearable>
                            <el-option v-for="item in appList" :label="`${item.appId}-${item.appName}`"
                                       :value="item.appId" :key="item.appId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="规则名称">
                        <el-input v-model="searchParam.ruleName" placeholder="规则名称" @input="onSearch({page:1,delay:200})" clearable></el-input>
                    </el-form-item>
                    <el-form-item label="状态">
                        <el-select v-model="searchParam.ruleStatus" placeholder="状态" @change="onSearch({page:1})" clearable>
                            <el-option :value="true" :key="1" label="开启"></el-option>
                            <el-option :value="false" :key="2" label="关闭"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-button type="primary" @click="onSearch" icon="el-icon-search" size="medium">查询</el-button>
                    <el-button @click="onReset" icon="el-icon-refresh-right" size="medium">清空</el-button>
                    <el-link href="/logout" :underline="false" style="float: right">退出登录</el-link>
                </el-form>
            </el-row>
            <el-row>
                <el-button type="primary" icon="el-icon-edit-outline" size="medium" @click="onEditRule">添加</el-button>
                <el-link type="primary" href="/file/rule_engine.thrift">thrift接口文档</el-link>
            </el-row>
        </el-header>

        <el-main>

            <el-table :data="tableData" border row-key="id" v-loading="loading">

                <el-table-column prop="rule.id" label="规则ID" align="center" width="150"></el-table-column>

                <el-table-column label="AppId" align="center" v-if="!appId" width="150">
                    <template slot-scope="scope">
                        <span>{{appInfo(scope.row.rule.appId)}}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="rule.ruleName" label="规则名称" align="center" width="200"></el-table-column>

                <el-table-column label="类型" align="center" width="150">
                    <template slot-scope="scope">
                        <span>{{scope.row.rule.configType == 0 ? '脚本' : '流程图'}}</span>
                    </template>
                </el-table-column>

                <el-table-column label="状态" align="center" width="150">
                    <template slot-scope="scope">
                        <el-popconfirm :title="`确定${scope.row.rule.ruleStatus?'关闭':'开启'}吗? 规则ID: ${scope.row.rule.id}`" @confirm="onSwitch(scope.row.rule)">
                            <el-button :type="scope.row.rule.ruleStatus?'success':'danger'"
                                       round slot="reference" size="mini">
                                {{scope.row.rule.ruleStatus?'开启':'关闭'}}
                            </el-button>
                        </el-popconfirm>
                    </template>
                </el-table-column>

                <el-table-column label="脚本" align="center" width="230">
                    <template slot-scope="scope">

                        <el-link type="primary" @click="onEditScript(scope.row.rule)">
                            修改配置
                        </el-link>

                        <el-link type="success" @click="onEditVersion(scope.row.rule)">
                            版本管理
                        </el-link>

                        <el-tooltip class="item" effect="dark" content="搜索yylive-gameecology-stream" placement="top">
                            <el-link type="info" target="re-admin-log" href="https://s.sysop.yy.com/commonComps/cloudLog">
                                日志
                            </el-link>
                        </el-tooltip>

                        <el-link type="primary" v-if="scope.row.rule.configType == 1"
                                 :target="`ruleFlow-${scope.row.rule.appId}`"
                                 :href="`https://${isProd?'web':'webtest'}.yy.com/ge_risk_admin/index.html?iframe=1&appId=${scope.row.rule.appId}#/riskRule`">
                            流程图
                        </el-link>
                    </template>
                </el-table-column>

                <el-table-column prop="createTime" label="创建时间" align="center" width="200">
                    <template slot-scope="scope">
                        <span>{{dateFormat(scope.row.rule.createTime)}}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="rule.createPassport" label="创建人" align="center" width="150"></el-table-column>

                <el-table-column prop="updateTime" label="更新时间" align="center" width="200">
                    <template slot-scope="scope">
                        <span>{{dateFormat(scope.row.rule.updateTime)}}</span>
                    </template>
                </el-table-column>

                <el-table-column prop="rule.updatePassport" label="更新人" align="center" width="150">

                </el-table-column>

                <el-table-column label="操作" width="200" fixed="right" align="center" width="150">
                    <template slot-scope="scope">
                        <el-link type="success" icon="el-icon-copy-document" @click="copyRule(scope.row.rule)">
                            复制
                        </el-link>
                        <el-link type="primary" icon="el-icon-edit-outline" @click="onEditRule(scope.row.rule)">
                            编辑
                        </el-link>
                        <el-link type="danger" icon="el-icon-delete" @click="onDeleteRule(scope.row.rule.id)">
                            删除
                        </el-link>
                    </template>
                </el-table-column>

            </el-table>

            <el-pagination
                    :page-sizes="[10,15,20,50,100]"
                    @size-change="onSearch"
                    @current-change="onSearch"
                    layout="->, total, prev, pager, next, sizes"
                    :current-page.sync="pageData.page"
                    :page-size.sync="pageData.size"
                    :total="pageData.total">
            </el-pagination>

            <el-dialog title="规则" :visible.sync="ruleFormVisible" width="40%"
                       :close-on-click-modal="false" @closed="onCloseRuleForm">

                <el-form :model="ruleFormData" label-width="150px">

                    <el-form-item label="复制规则ID" v-if="ruleFormData.copyId">
                        {{this.ruleFormData.copyId}}
                    </el-form-item>

                    <el-form-item label="规则ID" prop="ruleId">
                        <el-input v-model.number="ruleFormData.id" placeholder="规则ID" style="width: 50%"></el-input>
                    </el-form-item>
                    <el-form-item label="AppId" prop="appId" v-if="!appId">
                        <el-select v-model="ruleFormData.appId" placeholder="AppId" clearable style="width: 50%">
                            <el-option v-for="item in appList" :label="`${item.appId}-${item.appName}`"
                                       :value="item.appId" :key="item.appId"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="规则名称" prop="ruleName">
                        <el-input v-model="ruleFormData.ruleName" placeholder="规则名称" style="width: 50%"></el-input>
                    </el-form-item>
                    <el-form-item label="类型" prop="configType">
                        <el-select v-model="ruleFormData.configType" placeholder="类型" style="width: 50%">
                            <el-option label="脚本" :value="0"></el-option>
                            <el-option label="流程图" :value="1"></el-option>
                        </el-select>
                    </el-form-item>
                </el-form>

                <div slot="footer" class="dialog-footer">
                    <el-button @click="ruleFormVisible = false">取 消</el-button>
                    <el-button type="success" @click="onCopyRule" v-if="ruleFormData.copyId">复 制</el-button>
                    <el-button type="primary" @click="onSaveRule" v-else>保 存</el-button>
                </div>
            </el-dialog>

            <el-dialog :title="`版本管理：${versionFormData ? versionFormData.rule.ruleName : ''}`"
                       :visible.sync="versionFormVisible" width="80%" top="10vh"
                       :close-on-click-modal="false">

                <el-alert title="提示: 可以锁定执行版本，默认执行最新版本" type="success"></el-alert>

                <el-table :data="versionTableData" border row-key="id" v-loading="versionLoading">

                    <el-table-column prop="id" label="版本ID" align="center" ></el-table-column>

                    <el-table-column prop="versionDesc" label="版本说明" align="center" ></el-table-column>

                    <!--<el-table-column prop="ruleId" label="规则ID" align="center" ></el-table-column>-->

                    <el-table-column label="状态" align="center" >
                        <template slot-scope="scope">
                            <el-button :type="scope.row.status?'success':'info'" round size="mini" @click="enableVersion(scope.row)">
                                {{scope.row.status?'锁定':'未锁定'}}
                            </el-button>
                        </template>
                    </el-table-column>

                    <el-table-column prop="createTime" label="创建时间" align="center" >
                        <template slot-scope="scope">
                            <span>{{dateFormat(scope.row.createTime)}}</span>
                        </template>
                    </el-table-column>

                    <el-table-column prop="createPassport" label="创建人" align="center" ></el-table-column>

                    <el-table-column prop="enableTime" label="锁定时间" align="center" >
                        <template slot-scope="scope">
                            <span>{{dateFormat(scope.row.enableTime)}}</span>
                        </template>
                    </el-table-column>

                    <el-table-column prop="enablePassport" label="锁定人" align="center" ></el-table-column>

                    <el-table-column label="操作" align="center" fixed="right">
                        <template slot-scope="scope">
                            <el-tooltip class="item" effect="dark" content="测试" placement="top">
                                <el-link type="primary" @click="onEditScriptVersion(scope.row)">
                                    查看
                                </el-link>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" content="发送到生产" placement="top">
                                <el-link v-if="!isProd" type="danger" @click="onSyncRuleVersion(scope.row)">
                                    同步
                                </el-link>
                            </el-tooltip>
                            <el-dropdown size="small" @command="versionCommand">
                                <i class="el-icon-arrow-down el-icon--right"></i>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item :command="{type: 'delete', data: scope.row}">删除</el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                    </el-table-column>
                </el-table>

                <el-pagination
                        @size-change="onSearchVersion"
                        @current-change="onSearchVersion"
                        layout="->, total, prev, pager, next, sizes"
                        :current-page.sync="versionPageData.page"
                        :page-size.sync="versionPageData.size"
                        :total="versionPageData.total">
                </el-pagination>

                <div slot="footer" class="dialog-footer">
                    <el-button @click="versionFormVisible = false">关 闭</el-button>
                </div>
            </el-dialog>

            <el-dialog :title="scriptFormData.rule.ruleName + (scriptFormData.versionId ? ` (版本ID: ${scriptFormData.versionId})` : '')"
                       :visible.sync="scriptFormVisible" width="85%" top="7vh"
                       :close-on-click-modal="false" @closed="onCloseScriptForm">
                <div v-loading="scriptFormDataLoading">
                <el-row style="padding-bottom: 10px">
                    <el-col :span="1" v-if="scriptFormData.rule.configType == 1">
                        <span style="font-size: 14px; font-weight: bold;">流程图</span>
                    </el-col>
                    <el-col :span="3">
                        <span style="font-size: 14px; font-weight: bold; padding-left: 8px">参数名</span>
                    </el-col>
                    <el-col :span="3">
                        <span style="font-size: 14px; font-weight: bold; padding-left: 8px">类型</span>
                    </el-col>
                    <el-col :span="3">
                        <span style="font-size: 14px; font-weight: bold; padding-left: 8px">说明</span>
                    </el-col>
                    <el-col :span="3">
                        <span style="font-size: 14px; font-weight: bold; padding-left: 8px">来源</span>
                        <i class="el-icon-sort" @click="sortParam"></i>
                    </el-col>
                </el-row>
                <el-row v-for="param in scriptFormData.paramList" :key="param.id" style="padding-bottom: 10px">
                    <el-col :span="1" v-if="scriptFormData.rule.configType == 1">
                        <el-tooltip class="item" effect="dark" content="条件" placement="top">
                            <el-switch v-model="param.visualStatus" style="padding-top: 10px"></el-switch>
                        </el-tooltip>
                    </el-col>
                    <el-col :span="3">
                        <el-input v-model="param.paramKey" placeholder="参数" style="width: 90%"></el-input>
                    </el-col>
                    <el-col :span="3">
                        <el-select v-model="param.paramType" placeholder="类型" style="width: 90%">
                            <el-option label="数字" value="number"></el-option>
                            <el-option label="字符串" value="string"></el-option>
                            <el-option label="是否" value="boolean"></el-option>
                            <el-option label="JSON" value="json"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3">
                        <el-input v-model="param.paramDesc" placeholder="描述" style="width: 90%"></el-input>
                    </el-col>
                    <el-col :span="3">
                        <el-select v-model="param.paramSource" placeholder="来源" style="width: 90%">
                            <el-option label="传入" :value="0"></el-option>
                            <el-option label="表达式" :value="1"></el-option>
                            <el-option label="函数" :value="2"></el-option>
                            <el-option label="事件" :value="3" v-if="appId == null || appId == 200 || appId == 1"></el-option>
                            <el-option label="默认值" :value="4"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="6" v-if="param.paramSource == 1">
                        <el-input v-model="param.expression" placeholder="表达式" style="width: 90%"></el-input>
                    </el-col>
                    <el-col :span="3" v-if="param.paramSource == 2">
                        <el-select v-model="param.functionKey" placeholder="函数" style="width: 90%">
                            <el-option v-for="item in functionList" :label="item.functionDesc"
                                       :value="item.functionKey" :key="item.functionKey"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3" v-if="param.paramSource == 2">
                        <el-input v-for="(item, index) in getParamTypes(param.functionKey)" style="width: 90%" placeholder="函数参数"
                                  :value="param.functionParam && param.functionParam[index]" :key="param.index"
                                  @input="onChangeFunctionParam(param.id, index, this.event.target.value)"></el-input>
                    </el-col>
                    <el-col :span="3" v-if="param.paramSource == 3">
                        <el-select v-model="param.eventId" placeholder="事件" style="width: 90%">
                            <el-option v-for="item in eventList" :label="item.eventDesc"
                                       :value="item.eventId" :key="item.eventId"></el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="3" v-if="param.paramSource == 3">
                        <el-input v-model.number="param.eventActId" style="width: 90%" placeholder="活动ID(过滤)"></el-input>
                    </el-col>
                    <el-col :span="3" v-if="param.paramSource == 4">
                        <el-input v-model="param.paramDefault" style="width: 90%" placeholder="默认值"></el-input>
                    </el-col>
                    <el-col :span="1" v-if="!scriptFormData.view">
                        <el-button icon="el-icon-minus" circle type="danger" plain @click="removeParam(param)"></el-button>
                    </el-col>
                </el-row>
                <el-row v-if="!scriptFormData.view">
                    <el-button icon="el-icon-plus" circle plain type="primary" @click="addParam"></el-button>
                </el-row>

                <el-row style="padding-top: 10px">
                    <el-form :inline="true" label-width="80px">
                        <el-form-item label="脚本语言">
                            <el-select v-model="scriptFormData.ruleScript.ruleType" placeholder="脚本语言" style="width: 90%" >
                                <el-option label="JS" value="js"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="返回类型">
                            <el-select v-model="scriptFormData.ruleScript.returnType" placeholder="返回类型" style="width: 90%">
                                <el-option label="数字" value="number"></el-option>
                                <el-option label="字符串" value="string"></el-option>
                                <el-option label="是否" value="boolean"></el-option>
                                <!--<el-option label="JSON" value="json"></el-option>-->
                            </el-select>
                        </el-form-item>
                    </el-form>
                </el-row>
                <div v-if="scriptFormData.rule.configType == 1">
                    <el-row style="padding-bottom: 10px">
                        <el-col :span="3">
                            <span style="font-size: 14px; font-weight: bold; padding-left: 8px">返回值</span>
                        </el-col>
                        <el-col :span="3">
                            <span style="font-size: 14px; font-weight: bold; padding-left: 8px">说明</span>
                        </el-col>
                    </el-row>
                    <el-row v-for="result in scriptFormData.resultList" :key="result.id" style="padding-bottom: 10px">
                        <el-col :span="3">
                            <el-input v-model="result.retValue" placeholder="返回值" style="width: 90%"></el-input>
                        </el-col>
                        <el-col :span="3">
                            <el-input v-model="result.retDesc" placeholder="说明" style="width: 90%"></el-input>
                        </el-col>
                        <el-col :span="1" v-if="!scriptFormData.view">
                            <el-button icon="el-icon-minus" circle type="danger" plain @click="removeResult(result)"></el-button>
                        </el-col>
                    </el-row>
                    <el-row v-if="!scriptFormData.view">
                        <el-button icon="el-icon-plus" circle plain type="primary" @click="addResult"></el-button>
                    </el-row>
                </div>

                <el-row v-if="scriptFormData.view">
                    <el-switch v-model="scriptFormData.diff" active-text="对比" inactive-text="当前"></el-switch>
                    <el-alert title="提示: 此处只能查看, 右边为执行版本" type="success">
                    </el-alert>
                    <div v-if="scriptFormData.diff">
                        <my-code-mirror v-if="scriptFormVisible"
                                        v-model="scriptFormData.ruleScript.ruleScript"
                                        :script="scriptFormData.ruleScript.srcScript"
                                        :readonly="true">
                        </my-code-mirror>
                    </div>
                    <div v-else>
                        <code-mirror-textarea v-if="scriptFormVisible" v-model="scriptFormData.ruleScript.ruleScript" :readonly="true">
                        </code-mirror-textarea>
                    </div>
                </el-row>
                <el-row v-if="!scriptFormData.view && scriptFormData.rule.configType == 0">
                    <el-switch v-model="scriptFormData.diff" active-text="对比" inactive-text="当前"></el-switch>
                    <el-alert title="提示: 左边修改, 右边对比, F11进入全屏" type="success">
                    </el-alert>
                    <div v-if="scriptFormData.diff">
                        <my-code-mirror v-if="scriptFormVisible"
                                        v-model="scriptFormData.ruleScript.ruleScript || ''"
                                        :script="scriptFormData.ruleScript.srcScript || ''"
                                        @edit-code="editCode">
                        </my-code-mirror>
                    </div>
                    <div v-else>
                        <code-mirror-textarea v-if="scriptFormVisible" v-model="scriptFormData.ruleScript.ruleScript || ''" @edit-code="editCode">
                        </code-mirror-textarea>
                    </div>
                </el-row>

                <!--测试-->
                <el-collapse style="margin-top: 20px" v-if="scriptFormVisible">
                    <el-collapse-item name="1">
                        <template slot="title">
                            <h3><i class="el-icon-circle-check"></i> 测试</h3>
                        </template>
                        <el-form label-width="120px">
                            <el-form-item v-for="param in testParam(scriptFormData.paramList)"
                                          :label="param.paramDesc || param.paramKey" :key="param.id">
                                <el-input placeholder="参数" :style="{width: param.paramType == 'json'?'90%':'50%'}"
                                          :type="param.paramType == 'json'?'textarea':'text'" :autosize="{minRows: 3}"
                                          :value="testRuleData.params[param.paramKey]"
                                          @input="editTestParam(param.paramKey, this.event.target.value)" >
                                </el-input>
                                <el-tooltip class="item" effect="dark" content="格式化" placement="top" v-if="param.paramType == 'json'">
                                    <el-button circle icon="el-icon-magic-stick" size="medium"
                                               @click="formatJSONParam(param.paramKey)"></el-button>
                                </el-tooltip>
                                <el-tooltip class="item" effect="dark" content="默认值" placement="top" v-if="param.paramSource == 3">
                                    <el-button circle icon="el-icon-scissors" size="medium" type="success" plain
                                               @click="setEventDefaultJson(param)"></el-button>
                                </el-tooltip>
                            </el-form-item>
                        </el-form>

                        <el-row>
                            <el-col :span="16">
                                <el-result icon="success" title="执行成功" v-if="testResult && testResult.code == 0">
                                    <template slot="extra">
                                        <span style="font-size: 16px"><pre>脚本返回: {{testResult && JSON.stringify(testResult.data, null, 2)}}</pre></span>
                                    </template>
                                </el-result>
                                <el-result icon="error" title="执行错误" v-if="testResult && testResult.code != 0">
                                    <template slot="extra">
                                        <span style="font-size: 16px"><pre>异常: {{testResult.msg}}</pre></span>
                                    </template>
                                </el-result>
                                <div v-if="testResult && testResult.testLogList && testResult.testLogList.length > 0">
                                    <el-col :span="2" :offset="2">
                                        <span>脚本日志: </span>
                                    </el-col>
                                    <el-col :span="20">
                                        <el-row v-for="(testLog,index) in testResult.testLogList" :key="index">
                                            <span>{{index+1}}: {{testLog}}</span>
                                        </el-row>
                                    </el-col>
                                </div>
                            </el-col>
                        </el-row>

                        <el-row v-if="!scriptFormData.view">
                            <el-col :offset="20" :span="2">
                                <el-tooltip class="item" effect="dark" content="保存脚本/流程图时会执行测试用例" placement="top">
                                    <el-button type="success" @click="saveTestCase" plain>保存用例</el-button>
                                </el-tooltip>
                            </el-col>
                            <el-col :span="2" v-if="scriptFormData.rule.configType == 0">
                                <el-button type="success" @click="testRuleOnEdit" plain>测 试</el-button>
                            </el-col>
                        </el-row>
                        <el-row v-else>
                            <el-col :offset="22" :span="2">
                                <el-button type="success" @click="onTestRule" plain>测 试</el-button>
                            </el-col>
                        </el-row>

                    </el-collapse-item>
                </el-collapse>

                </div>

                <div slot="footer" class="dialog-footer" v-if="scriptFormData.view">
                    <el-button @click="scriptFormVisible = false">关 闭</el-button>
                </div>
                <div slot="footer" class="dialog-footer" v-else>
                    <el-form :inline="true" label-width="100px" v-if="scriptFormData.rule.configType == 0">
                        <el-form-item label="版本说明">
                            <el-input v-model="scriptFormData.versionDesc" ></el-input>
                        </el-form-item>
                    </el-form>
                    <el-button @click="scriptFormVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onSaveScript">保 存</el-button>
                </div>

            </el-dialog>

        </el-main>

        <el-footer>

        </el-footer>

    </el-container>
</div>

</body>

<script>

    let paramId = -1
    let resultId = -1

    const getQueryString = function (name) {
        const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i")
        const r = window.location.search.substr(1).match(reg)
        if (r != null) {
            return decodeURIComponent(r[2])
        }
        return null
    }

    const isProd = window.location.host === "re-admin.yy.com"
    document.title += isProd ? ' - 生产' : ' - 测试'

    Vue.component('my-code-mirror', {
        props: ['value', 'script', 'readonly'],
        template: `<div id="code"></div>`,
        created: function() {
            // this.originCode = this.value
        },
        mounted: function() {
            this.editor = CodeMirror.MergeView(document.getElementById("code"),{
                mode: "javascript",
                //view
                theme:"idea",
                lineNumbers: true,
                lineWrapping: true,
                //code
                indentUnit: 4,
                autoCloseBrackets: true,
                matchBrackets: true,
                readOnly: this.readonly,
                highlightSelectionMatches: {showToken: /\w/, annotateScrollbar: true},
                // styleActiveLine: true,
                //gutter
                foldGutter: true,
                gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
                //merge
                orig: this.script,
                value: this.value,
            })
            this.editor.edit.on('change', this.onChange)
            this.editor.edit.options.extraKeys = {
                "F11": function(cm) {
                    cm.setOption("fullScreen", !cm.getOption("fullScreen"));
                },
            }
        },
        methods: {
            onChange: function () {
                this.$emit('edit-code', this.editor.edit.getValue())
            }
        }
    })

    Vue.component('code-mirror-textarea', {
        props: ['value','readonly'],
        template: `<div><textarea id="code-text" v-model="value"></textarea></div>`,
        mounted: function() {
            this.editor = CodeMirror.fromTextArea(document.getElementById("code-text"),{
                mode: "javascript",
                //view
                theme:"idea",
                lineNumbers: true,
                lineWrapping: true,
                //code
                indentUnit: 4,
                autoCloseBrackets: true,
                matchBrackets: true,
                readOnly: this.readonly,
                highlightSelectionMatches: {showToken: /\w/, annotateScrollbar: true},
                //gutter
                foldGutter: true,
                gutters: ["CodeMirror-linenumbers", "CodeMirror-foldgutter"],
                extraKeys: {
                    "F11": function(cm) {
                        cm.setOption("fullScreen", !cm.getOption("fullScreen"));
                    },
                },
            })
            this.editor.on('change', this.onChange)
        },
        methods: {
            onChange: function () {
                this.$emit('edit-code', this.editor.getValue())
            }
        }
    })

    const app = new Vue({
        el: '#app',
        data: function () {
            return {
                isProd: isProd,
                appId: null,
                loading: false,
                versionLoading: false,
                searchParam: {},
                tableData: [],
                ruleFormVisible: false,
                ruleFormData: {},
                versionFormVisible: false,
                versionTableData: [],
                versionFormData: null,
                scriptFormVisible: false,
                scriptFormData: {
                    diff: false,
                    rule: {},
                    paramList:[],
                    ruleScript:{},
                    resultList: [],
                },
                scriptFormDataLoading: false,
                testRuleData: {
                    params: {},
                },
                testResult: undefined,
                appList: [],
                functionList: [],
                eventList: [],
                pageData: {
                    page: 1,
                    size: 15,
                    total: 0,
                },
                versionPageData: {
                    page: 1,
                    size: 10,
                    total: 0,
                },
                logVisible: false,
                logUrl: null,
                logRule: {},
            }
        },
        created: function () {
            const appId = getQueryString('appId')
            this.appId = appId
        },
        mounted: function () {
            this.loadAppData()
            this.onSearch()
            this.loadFunctionData()
            this.loadEventData()
        },
        methods: {
            dateFormat: function (time) {
                if (!time) {
                    return ''
                }
                return moment(time).format('YYYY-MM-DD HH:mm:ss')
            },
            loadAppData: function () {
                fetch('/ruleEngine/appList').then(res => res.json()).then(res => {
                    if (res.code === 0) {
                        this.appList = res.data
                    }
                })
            },
            loadFunctionData: function () {
                fetch('/ruleEngine/functionList').then(res => res.json()).then(res => {
                    if (res.code === 0) {
                        this.functionList = res.data
                    }
                })
            },
            loadEventData: function () {
                fetch('/ruleEngine/eventList').then(res => res.json()).then(res => {
                    if (res.code === 0) {
                        this.eventList = res.data
                    }
                })
            },
            appInfo: function (appId) {
                return this.appList.filter(item => item.appId == appId).map(item => item.appId + '-' + item.appName)[0]
            },
            getParamTypes: function (functionKey) {
                for (const func of this.functionList) {
                    if (func.functionKey == functionKey) {
                        return func.paramTypes
                    }
                }
                return []
            },
            onChangeFunctionParam: function(id, index, value) {
                for (const param of this.scriptFormData.paramList) {
                    if (param.id == id) {
                        if (!param.functionParam) {
                            param.functionParam = []
                        }
                        param.functionParam[index] = value
                    }
                }
                this.$forceUpdate()
            },
            onSearch: function (search) {

                if (search && search.page) {
                    this.pageData.page = search.page
                }
                if (search && search.delay) {

                }

                this.loading = true
                const param = Object.assign({appId: this.appId}, this.pageData, this.searchParam)
                fetch('/ruleEngine/ruleList', {
                    method: 'POST',
                    body: JSON.stringify(param),
                    headers: {'Content-Type': 'application/json'}
                }).then(res => res.json()).then(res => {
                    this.loading = false
                    if (res.code == 0) {
                        this.tableData = res.data
                        this.pageData.total = res.total
                    } else {
                        this.$message.error(res.msg)
                    }
                })
            },
            onReset: function () {
                console.log('onReset:')
                this.searchParam = {}
                this.pageData.page = 1
                this.onSearch()
            },
            onEditRule: function(item) {
                this.ruleFormVisible = true
                if (item) {
                    this.ruleFormData = Object.assign({srcId:item.id}, item)
                }
            },
            onEditScript: function (rule) {
                this.scriptFormVisible = true
                this.scriptFormData.rule = rule
                this.scriptFormDataLoading = true

                fetch('/ruleEngine/ruleDetail', {
                    method: 'POST',
                    body: JSON.stringify(rule.id),
                    headers: {'Content-Type': 'application/json'}
                }).then(res => res.json()).then(res => {
                    this.scriptFormDataLoading = false
                    if (res.code == 0) {
                        if (res.data.paramList) {
                            this.scriptFormData.paramList = res.data.paramList
                            for (const param of this.scriptFormData.paramList) {
                                if (param.functionParam) {
                                    param.functionParam = JSON.parse(param.functionParam)
                                }
                            }
                        }
                        if (res.data.ruleScript) {
                            this.scriptFormData.ruleScript = res.data.ruleScript
                            this.scriptFormData.ruleScript.srcScript = this.scriptFormData.ruleScript.ruleScript || ''
                            this.scriptFormData.diff = true
                        }
                        if (res.data.resultList) {
                            this.scriptFormData.resultList = res.data.resultList
                        }
                    } else {
                        this.$message.error(res.msg)
                    }
                })

                fetch('/ruleEngine/getTestCase?ruleId=' + rule.id)
                    .then(res=>res.json())
                    .then(res=>{
                        if (res.code == 0 && res.data) {
                            for (let key in res.data) {
                                if (typeof res.data[key] == 'object') {
                                    res.data[key] = JSON.stringify(res.data[key], null, 2)
                                }
                            }
                            this.testRuleData.params = res.data
                        }
                    })

                this.testRuleData = {
                    params: {},
                }
                this.testResult = null
            },
            onEditScriptVersion: function(item) {
                this.scriptFormVisible = true
                const ruleConfig = JSON.parse(item.ruleConfig)
                this.scriptFormData = {
                    rule: this.versionFormData.rule,
                    paramList: ruleConfig.ruleParamList,
                    ruleScript: ruleConfig.ruleScript,
                    resultList: ruleConfig.ruleResultList,
                    view: true,
                    diff: true,
                    versionId: item.id,
                }
                if (this.scriptFormData.ruleScript) {
                    this.scriptFormData.ruleScript.srcScript = this.versionFormData.enabledVersion
                        && this.versionFormData.enabledVersion.ruleScript
                        && this.versionFormData.enabledVersion.ruleScript.ruleScript || ''
                }
                for (const param of this.scriptFormData.paramList) {
                    if (param.functionParam) {
                        param.functionParam = JSON.parse(param.functionParam)
                    }
                }

                this.testRuleData = {
                    params: {},
                }
                this.testResult = null
            },
            onEditVersion: function(rule) {
                this.versionFormVisible = true
                this.versionFormData = {
                    rule: rule
                }
                this.onSearchVersion()
            },
            onSearchVersion: function() {
                this.versionLoading = true
                const param = Object.assign({ruleId: this.versionFormData.rule.id}, this.versionPageData)
                fetch('/ruleEngine/ruleVersionList', {
                    method: 'POST',
                    body: JSON.stringify(param),
                    headers: {'Content-Type': 'application/json'}
                }).then(res => res.json()).then(res => {
                    this.versionLoading = false
                    if (res.code == 0) {
                        this.versionTableData = res.data
                        this.versionPageData.total = res.total
                    } else {
                        this.$message.error(res.msg)
                    }
                })
                fetch('/ruleEngine/getEnabledVersion', {
                    method: 'POST',
                    body: this.versionFormData.rule.id,
                    headers: {'Content-Type': 'application/json'}
                }).then(res => res.json()).then(res => {
                    if (res.code == 0) {
                        this.versionFormData.enabledVersion = res.data
                    }
                })
            },
            enableVersion: function(ruleVersion) {
                this.$confirm('确定'+(ruleVersion.status?'解锁':'锁定')+'此版本? 版本ID: ' + ruleVersion.id, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    fetch('/ruleEngine/enableRuleVersion', {
                        method: 'POST',
                        body: JSON.stringify(ruleVersion.id),
                        headers: {'Content-Type': 'application/json'}
                    }).then(res => res.json()).then(res => {
                        if (res.code == 0) {
                            this.$message.success('操作成功')
                            this.onSearchVersion()
                        } else {
                            this.$message.error('操作失败: ' + res.msg)
                        }
                    })
                }).catch(()=>{})
            },
            onSyncRuleVersion: function(ruleVersion) {
                this.$confirm('同步此版本到生产? 规则ID: ' + ruleVersion.ruleId +'  版本ID: ' + ruleVersion.id + '<br>提示: 需先到生产配置规则ID', '同步', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                    dangerouslyUseHTMLString: true,
                }).then(() => {
                    fetch('/ruleEngine/syncRuleVersion', {
                        method: 'POST',
                        body: JSON.stringify(ruleVersion.id),
                        headers: {'Content-Type': 'application/json'}
                    }).then(res => res.json()).then(res => {
                        if (res.code == 0) {
                            this.$message.success('同步成功')
                        } else {
                            this.$message.error('同步失败: ' + res.msg)
                        }
                    })
                }).catch(()=>{})
            },
            testParam: function(paramList) {
                return paramList ? paramList.filter(item => item.paramSource == 0 || item.paramSource == 3) : []
            },
            editTestParam: function(key, value) {
                this.testRuleData.params[key] = value
                this.$forceUpdate()
            },
            onSwitch: function (rule) {
                console.log('onSwitch:',rule)
                this.saveRule({
                    srcId: rule.id,
                    ruleStatus: !rule.ruleStatus,
                })
            },
            onSaveRule: function() {
                console.log('onSaveRule:', this.ruleFormData)
                const rule = {
                    srcId: this.ruleFormData.srcId,
                    id: this.ruleFormData.id,
                    appId: this.appId || this.ruleFormData.appId,
                    ruleName: this.ruleFormData.ruleName,
                    configType: this.ruleFormData.configType,
                }
                if (!rule.id || !rule.appId || !rule.ruleName || rule.configType == undefined) {
                    this.$message.error('参数有误')
                    return
                }
                if (rule.srcId && rule.srcId != rule.id) {
                    this.$confirm('规则ID有修改 是否继续?', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.saveRule(rule)
                    }).catch(()=>{})
                } else {
                    this.saveRule(rule)
                }
            },
            saveRule: function(rule) {
                fetch('/ruleEngine/saveRule', {
                    method: 'POST',
                    body: JSON.stringify(rule),
                    headers: {'Content-Type': 'application/json'}
                }).then(res => res.json()).then(res => {
                    if (res.code == 0) {
                        this.ruleFormVisible = false
                        this.$message.success('操作成功')
                        this.onSearch()
                    } else {
                        this.$message.error('操作失败: ' + res.msg)
                    }
                })
            },
            onSaveScript: function () {
                //校验
                if (!this.checkSaveScript()) {
                    return
                }
                if (this.scriptFormData.rule.configType == 0 && !this.scriptFormData.versionDesc) {
                    this.$message.error('请填写版本说明')
                    return
                }

                const vo = {
                    ruleId: this.scriptFormData.rule.id,
                    ruleType: this.scriptFormData.ruleScript.ruleType,
                    returnType: this.scriptFormData.ruleScript.returnType,
                    ruleScript: this.scriptFormData.ruleScript.ruleScript,
                    paramList: this.scriptFormData.paramList,
                    resultList: this.scriptFormData.resultList,
                    versionDesc: this.scriptFormData.versionDesc,
                }
                console.log('saveScript:',vo)
                fetch('/ruleEngine/saveScript', {
                    method: 'POST',
                    body: JSON.stringify(vo),
                    headers: {'Content-Type': 'application/json'}
                }).then(res => res.json()).then(res => {
                    if (res.code == 0) {
                        this.scriptFormVisible = false
                        this.$message.success('保存成功')
                        this.onSearch()
                    } else {
                        this.$message.error('保存失败: ' + res.msg)
                    }
                })
            },
            checkSaveScript: function () {
                for (let param of this.scriptFormData.paramList) {
                    if (!param.paramKey || !param.paramType) {
                        this.$message.error('参数名/类型为空')
                        return false
                    }
                    if (param.paramSource == 1 && !param.expression) {
                        this.$message.error('表达式为空')
                        return false
                    }
                    if (param.paramSource == 2 && !param.functionKey) {
                        this.$message.error('函数为空')
                        return false
                    }
                    if (param.paramSource == 3 && !param.eventId) {
                        this.$message.error('事件为空')
                        return false
                    }
                }
                if (!this.scriptFormData.ruleScript.ruleType
                    || !this.scriptFormData.ruleScript.returnType
                ) {
                    this.$message.error('语言/返回有误')
                    return false
                }
                if (this.scriptFormData.rule.configType == 1) {
                    if (!this.scriptFormData.resultList.length) {
                        this.$message.error('缺少返回值')
                        return false
                    }
                    for (let result of this.scriptFormData.resultList) {
                        if (!result.retValue || !result.retDesc) {
                            this.$message.error('返回值有误')
                            return false
                        }
                    }

                } else if (this.scriptFormData.rule.configType == 0) {
                    if (!this.scriptFormData.ruleScript.ruleScript) {
                        this.$message.error('脚本有误')
                        return false
                    }
                }
                return true
            },
            onDeleteRule: function(id) {
                console.log('onDeleteRule:',id)
                this.$confirm('此操作将永久删除该规则 是否继续? 规则ID: ' + id, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    fetch('/ruleEngine/deleteRule', {
                        method: 'POST',
                        body: JSON.stringify(id),
                        headers: {'Content-Type': 'application/json'}
                    }).then(res => res.json()).then(res => {
                        if (res.code == 0) {
                            this.$message.success('删除成功')
                            this.onSearch()
                        } else {
                            this.$message.error('删除失败')
                        }
                    })
                }).catch(()=> {})
            },
            onTestRule: function() {
                const params = this.getTestParams()
                if (!params) {
                    return
                }
                const req = {
                    versionId: this.scriptFormData.versionId,
                    params: params
                }
                console.log('onTestRule:',req)

                fetch('/ruleEngine/testRule', {
                    method: 'POST',
                    body: JSON.stringify(req),
                    headers: {'Content-Type': 'application/json'}
                }).then(res => res.json()).then(res => {
                    this.testResult = res
                    if (res.code == 0) {
                        this.$message.success('执行成功')
                    } else {
                        this.$message.error(`执行失败.[${res.code}]${res.msg}`)
                    }
                })
            },
            getTestParams: function() {
                const params = {}
                const paramTypeMap = {}
                for (const param of this.scriptFormData.paramList) {
                    paramTypeMap[param.paramKey] = param.paramType
                }

                for (const key in this.testRuleData.params) {
                    let value = this.testRuleData.params[key]
                    if (paramTypeMap[key] == 'json') {
                        try {
                            value = JSON.parse(this.testRuleData.params[key])
                        } catch (e) {
                            this.$message.error('JSON有误. ' + e)
                            return null
                        }
                    }
                    params[key] = value
                }
                return params
            },
            saveTestCase: function() {
                const params = this.getTestParams()
                if (!params) {
                    return
                }
                const req = {
                    ruleId: this.scriptFormData.rule.id,
                    params: params,
                }
                console.log('saveTestCase:', req);

                fetch('/ruleEngine/saveTestCase', {
                    method: 'POST',
                    body: JSON.stringify(req),
                    headers: {'Content-Type': 'application/json'}
                }).then(res => res.json()).then(res => {
                    if (res.code == 0) {
                        this.$message.success('保存成功')
                    } else {
                        this.$message.error('保存失败')
                    }
                })
            },
            testRuleOnEdit: function() {
                if (!this.checkSaveScript()) {
                    return
                }
                const params = this.getTestParams()
                if (!params) {
                    return
                }
                const req = {
                    ruleId: this.scriptFormData.rule.id,
                    params: params,
                    paramList: this.scriptFormData.paramList,
                    ruleScript: this.scriptFormData.ruleScript,
                }
                console.log('testRuleOnEdit:',req)

                fetch('/ruleEngine/testRuleOnEdit', {
                    method: 'POST',
                    body: JSON.stringify(req),
                    headers: {'Content-Type': 'application/json'}
                }).then(res => res.json()).then(res => {
                    this.testResult = res
                    if (res.code == 0) {
                        this.$message.success('执行成功')
                    } else {
                        this.$message.error(`执行失败.[${res.code}]${res.msg}`)
                    }
                })
            },
            onCloseRuleForm: function () {
                this.ruleFormData = {}
            },
            onCloseScriptForm: function () {
                this.scriptFormData = {
                    diff: false,
                    rule: {},
                    paramList:[],
                    ruleScript:{},
                    resultList: [],
                }
            },
            editCode: function(value) {
                // console.log('editCode:',value)
                this.scriptFormData.ruleScript.ruleScript = value
            },
            addParam: function() {
                this.scriptFormData.paramList.push({
                    id: paramId--,
                    paramSource: 0,
                })
            },
            removeParam: function(param) {
                this.scriptFormData.paramList = this.scriptFormData.paramList.filter(item => item.id != param.id)
            },
            addResult: function() {
                this.scriptFormData.resultList.push({
                    id: resultId--,
                })
            },
            removeResult: function(result) {
                this.scriptFormData.resultList = this.scriptFormData.resultList.filter(item => item.id != result.id)
            },
            sortParam: function() {
                if (this.scriptFormData.paramList) {
                    this.scriptFormData.paramList.sort((a, b) => a.paramSource <= b.paramSource ? -1 : 1)
                }
            },
            formatJSONParam: function(key) {
                try {
                    let json = JSON.parse(this.testRuleData.params[key])
                    this.testRuleData.params[key] = JSON.stringify(json, null, 2)
                    this.$forceUpdate()
                } catch (e) {
                    this.$message.error('JSON有误. ' + e)
                }
            },
            setEventDefaultJson: function(param) {
                for (const event of this.eventList) {
                    if (event.eventId == param.eventId) {
                        this.testRuleData.params[param.paramKey] = JSON.stringify(event.eventJson, null, 2)
                        this.$forceUpdate()
                        return
                    }
                }
            },
            ruleCommand: function(command) {
                switch (command.type) {
                    case 'copy':
                        this.ruleFormVisible = true
                        this.ruleFormData = {
                            copyId: command.data.rule.id,
                            appId: command.data.rule.appId,
                            ruleName: command.data.rule.ruleName,
                            configType: command.data.rule.configType,
                        }
                        console.log('ruleFormData:',this.ruleFormData)
                        break
                    default:
                        break
                }
            },
            copyRule: function(rule) {
                this.ruleFormVisible = true
                this.ruleFormData = {
                    copyId: rule.id,
                    appId: rule.appId,
                    ruleName: rule.ruleName,
                    configType: rule.configType,
                }
                console.log('copyRule:',this.ruleFormData)
            },
            onCopyRule: function() {
                console.log('onCopyRule:',this.ruleFormData)
                const rule = {
                    copyId: this.ruleFormData.copyId,
                    id: this.ruleFormData.id,
                    appId: this.appId || this.ruleFormData.appId,
                    ruleName: this.ruleFormData.ruleName,
                    configType: this.ruleFormData.configType,
                }
                if (!rule.id || !rule.appId || !rule.ruleName || rule.configType == undefined) {
                    this.$message.error('参数有误')
                    return
                }
                this.saveRule(rule)
            },
            onClickEnv: function() {
                if (this.isProd) {
                    this.$message.error('生产环境')
                } else {
                    this.$message.success('测试环境')
                }
            },
            versionCommand: function(command) {
                const type = command.type
                switch (type) {
                    case 'delete': this.deleteVersion(command.data)
                }
            },
            deleteVersion: function(row) {
                console.log('onRemoveVersion:', row)
                this.$confirm('此操作将永久删除该版本 是否继续? 版本ID: ' + row.id, '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(() => {
                    fetch('/ruleEngine/deleteVersion', {
                        method: 'POST',
                        body: JSON.stringify(row.id),
                        headers: {'Content-Type': 'application/json'}
                    }).then(res => res.json()).then(res => {
                        if (res.code == 0) {
                            this.$message.success('删除成功')
                            this.onSearchVersion()
                        } else {
                            this.$message.error('删除失败')
                        }
                    })
                }).catch(()=> {})
            }
        }
    })
</script>

</html>
