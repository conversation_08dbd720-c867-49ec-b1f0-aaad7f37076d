<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>规则引擎</title>
    <link rel="stylesheet" href="https://unpkg.com/element-ui@2.15.14/lib/theme-chalk/index.css">
    <script src="https://unpkg.com/vue/dist/vue.js"></script>
    <script src="https://unpkg.com/element-ui@2.15.14/lib/index.js"></script>
    <script src="./js/echarts.min.js"></script>
</head>
<body>

<div id="app">

    <el-container>

        <el-main>


            <el-dialog title="节点配置" :visible.sync="formVisible" width="50%"
                       :close-on-click-modal="false" @close="onFormClose">

                <el-form :model="formData" label-width="150px" >

                    <el-form-item label="节点名称" prop="nodeName">
                        <el-input v-model="formData.nodeName" placeholder="节点名称" style="width: 50%"></el-input>
                    </el-form-item>

                    <el-form-item label="节点类型" prop="type">
                        <el-radio-group v-model="formData.type">
                            <el-radio label="BOOL">判断</el-radio>
                            <el-radio label="RETURN">返回</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <div v-if="formData.type === 'BOOL'">
                        <el-form-item label="" :inline="true">
                            <el-select v-model="nodeData.param" placeholder="参数" style="width: 200px">
                                <el-option label="YY等级" value="level"></el-option>
                                <el-option label="充值金额" value="amount"></el-option>
                            </el-select>
                            <el-select v-model="nodeData.logic" placeholder="逻辑" style="width: 100px">
                                <el-option label="大于等于" value="≥"></el-option>
                                <el-option label="大于" value=">"></el-option>
                                <el-option label="等于" value="="></el-option>
                                <el-option label="小于" value="<"></el-option>
                                <el-option label="小于等于" value="≤"></el-option>
                            </el-select>
                            <el-input v-model="nodeData.value" placeholder="值" style="width: 200px"></el-input>
                            <!--开关-->
                        </el-form-item>
                    </div>
                    <div v-else-if="formData.type === 'RETURN'">
                        <el-form-item label="" :inline="true">
                            <el-input v-model="nodeData.value" placeholder="值" style="width: 200px"></el-input>
                        </el-form-item>
                        <!--返回下拉框-->
                    </div>

                </el-form>

                <div slot="footer" class="dialog-footer">
                    <el-button @click="formVisible = false">取 消</el-button>
                    <el-button type="primary" @click="onFormSave">保 存</el-button>
                </div>
            </el-dialog>

        </el-main>

    </el-container>


</div>

<div id="main" style="width: 80%; height: 800px;"></div>

</body>



<script>

    const myChart = echarts.init(document.getElementById('main'));

    const app = new Vue({
        el: '#app',
        data: function () {

            return {
                formVisible: false,
                formData: {},
                nodeData: {},
                formIndex: null,
                title: '规则',
                nodeConfig: {
                    beginNode: 'node_1',
                    node_1: {
                        nodeName: '是否帅哥',
                        type: 'BOOL',
                        data: {
                            conditions: {
                                link: 'and', //'or'
                                items: [{
                                    link: 'and', //'or'
                                    items: [{
                                        param: '',
                                        logic: '',
                                        value: '',
                                    }],
                                }],
                            },
                            yesNode: 'node_2',
                            noNode: 'node_3',
                        }
                    },
                    node_2: {
                        nodeName: '返回0',
                        type: 'RETURN',
                        data: {
                            value: '',
                        }
                    },
                },
                paramList: []
            }
        },
        created: function () {
            myChart.on('click', this.onClick)
            this.onUpdate()
        },
        methods: {
            onUpdate: function () {

                const root = {
                    name: '开始',
                    x: 100,
                    y: 50,
                    node: 'root'
                };
                const data = [root]

                const links = []

                const nodeConfig = this.nodeConfig;
                if (!nodeConfig.beginNode) {
                    const nodeIndex = this.addNode()
                    nodeConfig.beginNode = nodeIndex
                }

                const nodeInfo = nodeConfig[nodeConfig.beginNode];
                links.push({
                    source: '开始',
                    target: nodeInfo.nodeName,
                })
                this.parseNodeConfig(root.x, root.y + 100, nodeConfig.beginNode, nodeInfo, data, links)

                const chartOption = {
                    title: {
                        text: this.title
                    },
                    series: [{
                        type: 'graph',
                        symbolSize: 50,
                        roam: true,
                        label: {
                            show: true
                        },
                        edgeSymbol: ['circle', 'arrow'],
                        edgeSymbolSize: [4, 10],
                        edgeLabel: {
                            fontSize: 14
                        },
                        lineStyle: {
                            opacity: 1,
                            width: 2,
                            curveness: 0
                        },
                        data: data,
                        links: links,
                    },]
                }
                myChart.setOption(chartOption)
            },
            parseNodeConfig: function (x, y, nodeIndex, nodeInfo, data, links) {
                switch (nodeInfo.type) {
                    case 'BOOL':
                        data.push({
                            name: nodeInfo.nodeName,
                            x: x,
                            y: y,
                            symbol: 'diamond',
                            symbolSize: 100,
                            node: nodeIndex,
                        })
                        const boolNode = nodeInfo.data;
                        if (boolNode.yesNode) {
                            const yesNodeInfo = this.nodeConfig[boolNode.yesNode];
                            links.push({
                                source: nodeInfo.nodeName,
                                target: yesNodeInfo.nodeName,
                                label: {
                                    show: true,
                                    formatter: '是'
                                },
                                sourceIndex: nodeIndex,
                            })
                            this.parseNodeConfig(boolNode.yesDown ? x : x + 100, boolNode.yesDown ? y + 100 : y, boolNode.yesNode, yesNodeInfo, data,  links)
                        }
                        if (boolNode.noNode) {
                            const noNodeInfo = this.nodeConfig[boolNode.noNode];
                            links.push({
                                source: nodeInfo.nodeName,
                                target: noNodeInfo.nodeName,
                                label: {
                                    show: true,
                                    formatter: '否'
                                },
                                sourceIndex: nodeIndex,
                            })
                            this.parseNodeConfig(boolNode.yesDown ? x + 100 : x, boolNode.yesDown ? y : y + 100, boolNode.noNode, noNodeInfo, data,  links)
                        }
                        break;
                    case 'RETURN':
                        data.push({
                            name: nodeInfo.nodeName,
                            x: x,
                            y: y,
                            node: nodeIndex,
                        })
                        break;
                    default:
                        //未定义
                        data.push({
                            name: nodeInfo.nodeName,
                            x: x,
                            y: y,
                            node: nodeIndex,
                        })
                        break;
                }
            },
            onClick: function (param) {
                console.log('param:',param)
                if (param.dataType === 'node') {
                    const index = param.data.node;
                    if (index !== 'root') {
                        const formData = Object.assign({}, this.nodeConfig[index], );
                        if (formData.nodeName === index) {
                            formData.nodeName = null
                        }
                        const nodeData = Object.assign({}, formData.data);
                        this.formData = formData
                        this.nodeData = nodeData
                        this.formIndex = index
                        this.formVisible = true
                    }
                } else if (param.dataType === 'edge') {
                    if (param.data.sourceIndex) {
                        this.nodeConfig[param.data.sourceIndex].data.yesDown = !this.nodeConfig[param.data.sourceIndex].data.yesDown;
                        this.onUpdate()
                    }
                }
            },
            onFormSave: function () {
                console.log('onFormSave:', this.formData, this.nodeData)
                if (!this.formData.nodeName) {
                    this.$message.error('请填写节点名称')
                    return
                }
                if (!this.formData.type) {
                    this.$message.error('请选择节点类型')
                    return
                }
                const nodeInfo = Object.assign({}, this.formData, {data: Object.assign({}, this.nodeData)})
                const srcNodeInfo = this.nodeConfig[this.formIndex];
                if (srcNodeInfo.type !== nodeInfo.type) {
                    switch (nodeInfo.type) {
                        case 'BOOL':
                            const yesIndex = this.addNode()
                            const noIndex = this.addNode()
                            nodeInfo.data.yesNode = yesIndex
                            nodeInfo.data.noNode = noIndex
                            break;
                        case 'RETURN':

                            break;
                        default:
                                break;
                    }
                    //TODO: 撤回, 删掉无用节点
                }
                // TODO: 重名问题
                this.nodeConfig[this.formIndex] = nodeInfo
                this.formVisible = false
                this.onUpdate()
            },
            onFormClose: function () {
                // this.formData = {}
            },
            addNode: function () {
                const nodeIndex = 'node_' + this.nodeConfig.nextId++;
                this.nodeConfig[nodeIndex] = {
                    nodeName: nodeIndex
                }
                return nodeIndex
            }
        }
    });



    const option = {
        title: {
            text: '陪玩风控'
        },
        tooltip: {},
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: [
            {
                type: 'graph',
                layout: 'none',
                symbolSize: 50,
                roam: true,
                label: {
                    show: true
                },
                edgeSymbol: ['circle', 'arrow'],
                edgeSymbolSize: [4, 10],
                edgeLabel: {
                    fontSize: 20
                },

                data: [{
                    name: '开始',
                    x: 100,
                    y: 50,
                }, {
                    name: '充值帐号YY等级≤A',
                    x: 100,
                    y: 150,
                    symbol: 'diamond',
                    symbolSize: 100,
                }, {
                    name: '充值帐号YY等级≥B',
                    x: 100,
                    y: 250,
                    symbol: 'diamond',
                    symbolSize: 100,
                }, {
                    name: '返回1',
                    x: 200,
                    y: 150,
                }, {
                    name: '返回0',
                    x: 200,
                    y: 250,
                }, {
                    name: '返回2',
                    x: 100,
                    y: 350,
                    node: 'node_1',
                }],
                // links: [],
                links: [{
                    source: '开始',
                    target: '充值帐号YY等级≤A',
                }, {
                    source: '充值帐号YY等级≤A',
                    target: '充值帐号YY等级≥B',
                    label: {
                        show: true,
                        formatter: '否'
                    },
                }, {
                    source: '充值帐号YY等级≤A',
                    target: '返回1',
                    label: {
                        show: true,
                        formatter: '是'
                    },
                }, {
                    source: '充值帐号YY等级≥B',
                    target: '返回0',
                    label: {
                        show: true,
                        formatter: '是'
                    },
                }, {
                    source: '充值帐号YY等级≥B',
                    target: '返回2',
                    label: {
                        show: true,
                        formatter: '否'
                    },
                }],
                lineStyle: {
                    opacity: 1,
                    width: 2,
                    curveness: 0
                }
            }
        ]
    };


</script>

</html>
