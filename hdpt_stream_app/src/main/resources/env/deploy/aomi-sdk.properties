# \u88AB\u76D1\u63A7\u8FDB\u7A0B\u540D\uFF0C\u5EFA\u8BAE\u4EE5\u201C\u4E1A\u52A1\u540D.\u8FDB\u7A0B\u540D\u201D\u7684\u65B9\u5F0F\u8FDB\u884C\u547D\u540D
aomi.sdk.processname=gameecology_stream

#\u662F\u5426\u542F\u7528sdk\u529F\u80FD\uFF0C\u9ED8\u8BA4\u662Ftrue
aomi.sdk.init=true

#\u662F\u5426\u6253\u5370\u4E0A\u62A5\u6570\u636E\uFF0C\u9ED8\u8BA4\u662Ffalse
aomi.sdk.report.print=true

# \u8FDB\u7A0B\u5BF9\u5916\u670D\u52A1\u7AEF\u53E3\uFF0C\u8BF7\u4FDD\u8BC1\u4E0E\u4F60\u771F\u6B63\u8FDB\u7A0B\u5BF9\u5916\u670D\u52A1\u7684\u76D1\u542C\u7AEF\u53E3\u4FDD\u6301\u4E00\u81F4
aomi.sdk.portlist=7732

#\u81EA\u52A8\u83B7\u53D6\uFF0C\u4E00\u822C\u4E0D\u7528\u8BBE\u7F6E\uFF0C\u591A\u4E2A\u7528,\u53F7\u9694\u5F00
#aomi.sdk.iplist=xx.xx.xx.xx

#\u670D\u52A1\u8FDB\u7A0B\u6240\u5728\u57DF\uFF0C\u4E00\u822C\u7528\u4E8E\u76F4\u63A5\u5BF9\u63A5\u5BA2\u6237\u7AEF\u7684\u670D\u52A1\uFF08\u5982\uFF1AmobSrv\uFF09
#aomi.sdk.domain=appid.60273

#aop\u4EE3\u7406\u53C2\u6570\uFF0C\u4E00\u822C\u65E0\u987B\u914D\u7F6E
#aomi.sdk.agent.args

#\u81EA\u5B9A\u4E49\u4E0A\u62A5http url - \u6B63\u5F0F\u73AF\u5883
aomi.sdk.reportUrl=http://clt.aomi.yy.com/collector/reportModelData.action
