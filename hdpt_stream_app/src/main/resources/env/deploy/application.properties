
server.port=7733
logging.config=classpath:log4j2-prod.xml
# spring
spring.task.execution.pool.core-size=100
spring.task.execution.pool.max-size=200
spring.task.execution.pool.queue-capacity=1000
spring.task.scheduling.pool.size=50

# thrift server
thrift.server.rule_engine.port=7732

# mysql datasource for db gameecology
mysql.gameecology.jdbcUrl=************************************************************************************************************************************************************
mysql.gameecology.username=udb_ma_rw@gamebaby
mysql.gameecology.driver-class-name=com.mysql.jdbc.Driver
mysql.gameecology.maxPoolSize=100

mybatis.mapper-locations=classpath:mapper/**/*.xml

# redis
redis.gameecology-stream.sentinel.master=ge_stream_redis_001
redis.gameecology-stream.sentinel.nodes=group1013-bj-sentinel.yy.com:20095,group1013-sz-sentinel.yy.com:20095,group1013-wx-sentinel.yy.com:20095

# mongodb
spring.data.mongodb.uri=mongodb://ge_stream:<EMAIL>:10223,gestreammongoprod2.mongo.self.int.yy.com:10223,gestreammongoprod3.mongo.self.int.yy.com:10223/ge_stream?replicaSet=gestreammongoprod1

# thrift client
thrift.client.hdzt_ranking.s2sname=hdzt_ranking1
thrift.client.hdzt_ranking2.s2sname=hdzt_ranking2
thrift.client.hdzt_ranking3.s2sname=hdzt_ranking3
thrift.client.hdzt_ranking4.s2sname=hdzt_ranking4
thrift.client.hdzt_ranking5.s2sname=hdzt_ranking5
thrift.client.hdzt_ranking6.s2sname=hdzt_ranking6
thrift.client.hdzt_ranking7.s2sname=hdzt_ranking7
thrift.client.compere_group.s2sname=fts_compere_group
thrift.client.group_center.s2sname=fts_group_center
thrift.client.compere_group.urls=thrift://*************:14330;thrift://*************:14330
thrift.client.turnover.s2sname=to_service
thrift.client.turnover.family.s2sname=to_contract
thrift.client.pk_thrift_read.s2sname=vippk_thrift_read
thrift.client.contract.s2sname=to_contract
thrift.client.fts_room_manager.s2sname=fts_room_manager

thrift.service-agent-new.url=thrift://${HOST_IP}:12500;thrift://${HOST_IP}:12500;thrift://${HOST_IP}:12500
thrift.service-agent.url=nythrift://${MY_HOST_IP}:12500

ftsBaseInfoBridge_client_s2sname=fts_base_info_bridge


# todo: idc properties

# kafka (gamebaby_core_kafka)
kafka.baby-wx.consumer.bootstrap-servers=kafkawx012-core003.yy.com:8103,kafkawx012-core002.yy.com:8103,kafkawx012-core001.yy.com:8103
kafka.baby-wx.consumer.auto-offset-reset=latest

# kafka jiaoyou (fts_sz_cross_bs) (fts_wx_cross_bs)
kafka.jiaoyou-wx.consumer.bootstrap-servers=kafkawx006-core001.yy.com:8104,kafkawx006-core002.yy.com:8104,kafkawx006-core003.yy.com:8104
kafka.jiaoyou-wx.consumer.auto-offset-reset=latest

kafka.jiaoyou-sz.consumer.bootstrap-servers=kafkasz004-core001.yy.com:8107,kafkasz004-core002.yy.com:8107,kafkasz004-core003.yy.com:8107
kafka.jiaoyou-sz.consumer.auto-offset-reset=latest

# kafka hdpt (yxst_hdpt_kafka_wx) (yxst_hdpt_kafka_wx_xdc)
kafka.hdpt-wx.consumer.bootstrap-servers=kafkawx012-core001.yy.com:8106,kafkawx012-core002.yy.com:8106,kafkawx012-core003.yy.com:8106
kafka.hdpt-wx.consumer.auto-offset-reset=latest
kafka.hdpt-wx.producer.bootstrap-servers=kafkawx012-core001.yy.com:8106,kafkawx012-core002.yy.com:8106,kafkawx012-core003.yy.com:8106
kafka.hdpt-wx.producer.acks=all

kafka.hdpt-wx-xdc.consumer.bootstrap-servers=kafkasz012-core001.yy.com:8101,kafkasz012-core002.yy.com:8101,kafkasz012-core003.yy.com:8101
kafka.hdpt-wx-xdc.consumer.auto-offset-reset=latest
kafka.hdpt-wx-xdc.producer.bootstrap-servers=kafkasz012-core001.yy.com:8101,kafkasz012-core002.yy.com:8101,kafkasz012-core003.yy.com:8101
kafka.hdpt-wx-xdc.producer.acks=all

# kafka hdzt (hdzt_wx_kafka_prod) (hdzt_sz_kafka_prod)
kafka.hdzt-wx.producer.bootstrap-servers=kafkawx017-core001.yy.com:8104,kafkawx017-core002.yy.com:8104,kafkawx017-core003.yy.com:8104
kafka.hdzt-wx.producer.acks=all

kafka.hdzt-sz.producer.bootstrap-servers=hdzt-sz-kafka-prod-3.dbms-kafka.self.int.yy.com:8163,hdzt-sz-kafka-prod-4.dbms-kafka.self.int.yy.com:8163,hdzt-sz-kafka-prod-5.dbms-kafka.self.int.yy.com:8163
kafka.hdzt-sz.producer.acks=all

kafka.hdzt-wx7.producer.bootstrap-servers=kafkawx018-core001.yy.com:8104,kafkawx018-core002.yy.com:8104,kafkawx018-core003.yy.com:8104
kafka.hdzt-wx7.producer.acks=all

kafka.hdzt-sz7.producer.bootstrap-servers=kafkasz018-core002.yy.com:8101,kafkasz018-core003.yy.com:8101,kafkasz018-core001.yy.com:8101
kafka.hdzt-sz7.producer.acks=all

# kafka turnover (turnover_kafka)
kafka.turnover-wx.consumer.bootstrap-servers=kafkawx012-core001.yy.com:8105,kafkawx012-core002.yy.com:8105,kafkawx012-core003.yy.com:8105
kafka.turnover-wx.consumer.auto-offset-reset=latest

# kafka zhuiwan (zhuiwan_kafka_prod)
kafka.zhuiwan-wx.consumer.bootstrap-servers=kafkawx006-core001.yy.com:8112,kafkawx006-core002.yy.com:8112,kafkawx006-core003.yy.com:8112
kafka.zhuiwan-wx.consumer.auto-offset-reset=latest

# kafka yule
kafka.yule-wx.consumer.bootstrap-servers=ent-present-mq-2.dbms-kafka.self.int.yy.com:8184,ent-present-mq-0.dbms-kafka.self.int.yy.com:8184,ent-present-mq-1.dbms-kafka.self.int.yy.com:8184
kafka.yule-wx.consumer.auto-offset-reset=latest

# kafka webdb
kafka.webdb.consumer.bootstrap-servers=dmq-wuxi.yy.com:9099
kafka.webdb.consumer.auto-offset-reset=latest
kafka.webdb.consumer.group-id=gameecology_stream

#kafka data collect
kafka.data-collect-wx.consumer.bootstrap-servers=kafkawx012-core001.yy.com:8106,kafkawx012-core002.yy.com:8106,kafkawx012-core003.yy.com:8106
kafka.data-collect-wx.consumer.auto-offset-reset=latest
kafka.data-collect-sz.consumer.bootstrap-servers=kafkasz012-core001.yy.com:8101,kafkasz012-core002.yy.com:8101,kafkasz012-core003.yy.com:8101
kafka.data-collect-sz.consumer.auto-offset-reset=latest


# kafka openplatform_game
kafka.openplatform-game-wx.consumer.bootstrap-servers=kafkawx012-core001.yy.com:8107,kafkawx012-core002.yy.com:8107,kafkawx012-core003.yy.com:8107
kafka.openplatform-game-wx.consumer.auto-offset-reset=latest

# topic
kafka.topic.dead-message=stream_dead_message_topic

## define yrpc application name and scan base package
## https://git.yy.com/midwares/yrpc/yrpc-java-new/-/blob/master/dubbo-docs/v2/demo.nythrift.boot.md
dubbo.application.name=HDZKStream
dubbo.scan.base-packages=com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream,com.yy.gameecology.stream.worker.server,com.yy.gameecology.stream.client

#define yrpc registry: yyp-reg
dubbo.registries.yyp-reg.id=yyp-reg
dubbo.registries.yyp-reg.address=s2s://meta.yy.com
dubbo.registries.yyp-reg.username=HDZKStream
dubbo.registries.yyp-reg.parameters.s2stype=4096

#define yrpc registry: rule-reg
dubbo.registries.rule-reg.id=rule-reg
dubbo.registries.rule-reg.address=s2s://meta.yy.com
dubbo.registries.rule-reg.username=rule_engine_server
dubbo.registries.rule-reg.parameters.s2stype=4096
dubbo.protocols.attach_nythrift.name=attach_nythrift
dubbo.protocols.attach_nythrift.port=7732

#define yrpc provider parameters
dubbo.provider.parameters.eagle.appname=HDZKStream_provider
dubbo.provider.filter=eagle

#define yrpc yyp protocol service export
dubbo.protocols.yyp.name=yyp
dubbo.protocols.yyp.port=-1

#define yrpc consumer parameters
dubbo.consumer.parameters.eagle.appname=HDZKStream_consumer
dubbo.consumer.parameters.eagle.progress=yrpc-client-prod
dubbo.consumer.filter=eagle
dubbo.consumer.loadbalance=smart
dubbo.consumer.lbrouters=room,city,isp
dubbo.registries.consumer-reg.id=consumer-reg
dubbo.registries.consumer-reg.address=s2s://meta.yy.com
dubbo.registries.consumer-reg.username=ge_access_s2s_acct

#micrometer config
management.metrics.tags.application=hdpt_stream
management.metrics.tags.env=prod
#management.server.port=8081
#management.endpoints.web.exposure.include=prometheus
#management.endpoints.enabled-by-default=false
#management.endpoint.prometheus.enabled=true

thrift_zhuiya_client_s2s_name=zhuiya_server_thrift

thrift_skillcard_client_s2s_name=skillcard_server_thrift



########## zhuiwan_prize_issue config start###########
thrift_zhuiwan_prize_issue_client_s2s_name=zhuiya_hdzt_thrift
########### zhuiwan_prize_issue config end###########
hdzk.host.url=hdzk.yy.com

shenshu.udb.appid=9999
shenshu.udb.app-key=2C7B9D99572FAC91CB62352CAEE4F05A
shenshu.udb.cookie-domain=lgn.yy.com
shenshu.udb.index-uri=/rule.html
shenshu.udb.anon-path=/ruleEngine/receiveRuleVersion,/**/*.ico,/**/*.css,/**/*.js
shenshu.udb.udb-path=/ruleEngine/ruleVersionList
shenshu.udb.uri-perm-path=/**
shenshu.cache.redis.sentinel.nodes=group1013-bj-sentinel.yy.com:20095,group1013-sz-sentinel.yy.com:20095,group1013-wx-sentinel.yy.com:20095
shenshu.cache.redis.sentinel.master=ge_stream_redis_001
shenshu.auth.access-key=hdpt-stream
shenshu.auth.perm-url=https://zhuiya.yy.com/web/permission/ambiguous/getMyPermissions
shenshu.auth.apply-url=https://zhuiya.yy.com/admin-static/adminset.html