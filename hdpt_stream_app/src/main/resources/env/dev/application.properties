
server.port=7733

logging.config=classpath:log4j2-test.xml

# spring
spring.task.execution.pool.core-size=10
spring.task.execution.pool.max-size=20
spring.task.execution.pool.queue-capacity=100
spring.task.scheduling.pool.size=10

# thrift server
thrift.server.rule_engine.port=7732

# mysql datasource for db gameecology
mysql.gameecology.jdbcUrl=*************************************************************************************************************************************************************************
mysql.gameecology.username=udb_data@zwyz_mysql_test
mysql.gameecology.driver-class-name=com.mysql.jdbc.Driver
mysql.gameecology.maxPoolSize=10

mybatis.mapper-locations=classpath:mapper/**/*.xml

# redis
redis.gameecology-stream.sentinel.master=ge_activity_redis_test_001
redis.gameecology-stream.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20013,fstst-group0001-002-sentinel.yy.com:20013,fstst-group0001-003-sentinel.yy.com:20013
redis.gameecology-stream.database=8

# mongodb
spring.data.mongodb.uri=mongodb://ge_stream:<EMAIL>:10220,gestreammongotest2.mongo.self.int.yy.com:10220,gestreammongotest3.mongo.self.int.yy.com:10220/ge_stream?replicaSet=gestreammongotest1

# thrift client
thrift.client.hdzt_ranking.s2sname=hdzt_ranking1_test
thrift.client.hdzt_ranking2.s2sname=hdzt_ranking2_test
thrift.client.hdzt_ranking3.s2sname=hdzt_ranking3_test
thrift.client.hdzt_ranking4.s2sname=hdzt_ranking4_test
thrift.client.hdzt_ranking5.s2sname=hdzt_ranking5_test
thrift.client.hdzt_ranking6.s2sname=hdzt_ranking6_test
thrift.client.hdzt_ranking7.s2sname=hdzt_ranking7_test
thrift.client.compere_group.s2sname=fts_compere_group_test
thrift.client.group_center.s2sname=fts_group_center_test
thrift.client.compere_group.urls=thrift://***********:14330;thrift://***********:14330
thrift.client.turnover.s2sname=to_service_pre
thrift.client.turnover.family.s2sname=to_contract_pre
thrift.client.pk_thrift_read.s2sname=vippk_thrift_read_test
thrift.client.contract.s2sname=to_contract_pre
thrift.client.fts_room_manager.s2sname=fts_room_manager_test

thrift.service-agent-new.url=thrift://${HOST_IP}:12500;thrift://${HOST_IP}:12500;thrift://${HOST_IP}:12500
thrift.service-agent.url=nythrift://${MY_HOST_IP}:12500

thrift.miniest.url=thrift://*************:9995

ftsBaseInfoBridge_client_s2sname=fts_base_info_bridge_test


# kafka baby
kafka.baby-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.baby-wx.consumer.auto-offset-reset=latest

# kafka jiaoyou
kafka.jiaoyou-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou-wx.consumer.auto-offset-reset=latest

kafka.jiaoyou-sz.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8103,kafkafs0014-test002.yy.com:8103,kafkafs0014-test003.yy.com:8103,kafkafs0014-test004.yy.com:8103,kafkafs0014-test005.yy.com:8103
kafka.jiaoyou-sz.consumer.auto-offset-reset=latest

# kafka hdpt
kafka.hdpt-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt-wx.consumer.auto-offset-reset=latest
kafka.hdpt-wx.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt-wx.producer.acks=all

kafka.hdpt-wx-xdc.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt-wx-xdc.consumer.auto-offset-reset=latest
kafka.hdpt-wx-xdc.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdpt-wx-xdc.producer.acks=all

# kafka hdzt
kafka.hdzt-wx.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-wx.producer.acks=all

kafka.hdzt-sz.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-sz.producer.acks=all

kafka.hdzt-wx7.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-wx7.producer.acks=all

kafka.hdzt-sz7.producer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.hdzt-sz7.producer.acks=all

# kafka turnover
kafka.turnover-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.turnover-wx.consumer.auto-offset-reset=latest

# kafka zhuiwan
kafka.zhuiwan-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.zhuiwan-wx.consumer.auto-offset-reset=latest

# kafka yule
kafka.yule-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.yule-wx.consumer.auto-offset-reset=latest

# kafka webdb
kafka.webdb.consumer.bootstrap-servers=dmq-shenzhen.yy.com:9099
kafka.webdb.consumer.auto-offset-reset=latest
kafka.webdb.consumer.group-id=gameecology_stream_test

#kafka data collect
kafka.data-collect-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.data-collect-wx.consumer.auto-offset-reset=latest
kafka.data-collect-sz.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102,kafkafs0014-test002.yy.com:8102,kafkafs0014-test003.yy.com:8102,kafkafs0014-test004.yy.com:8102,kafkafs0014-test005.yy.com:8102
kafka.data-collect-sz.consumer.auto-offset-reset=latest

# kafka openplatform_game
kafka.openplatform-game-wx.consumer.bootstrap-servers=kafkafs0014-test001.yy.com:8102
kafka.openplatform-game-wx.consumer.auto-offset-reset=latest

kafka.topic.dead-message=stream_dead_message_topic

## define yrpc application name and scan base package
## https://git.yy.com/midwares/yrpc/yrpc-java-new/-/blob/master/dubbo-docs/v2/demo.nythrift.boot.md
dubbo.application.name=HDZKStream_test
dubbo.scan.base-packages=com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream,com.yy.gameecology.stream.worker.server,com.yy.gameecology.stream.client

#define yrpc registry: yyp-reg
dubbo.registries.yyp-reg.id=yyp-reg
dubbo.registries.yyp-reg.address=s2s://test-wudang-meta.yy.com
dubbo.registries.yyp-reg.username=HDZKStream_test
dubbo.registries.yyp-reg.parameters.s2stype=4096

#define yrpc registry: rule-reg
dubbo.registries.rule-reg.id=rule-reg
dubbo.registries.rule-reg.address=s2s://test-wudang-meta.yy.com
dubbo.registries.rule-reg.username=rule_engine_server_test
dubbo.registries.rule-reg.parameters.s2stype=4096
dubbo.protocols.attach_nythrift.name=attach_nythrift
dubbo.protocols.attach_nythrift.port=7732

#define yrpc provider parameters
dubbo.provider.parameters.eagle.appname=HDZKStream_provider_test
dubbo.provider.filter=eagle

#define yrpc yyp protocol service export
dubbo.protocols.yyp.name=yyp
dubbo.protocols.yyp.port=-1

#define yrpc consumer parameters
dubbo.consumer.parameters.eagle.appname=HDZKStream_consumer_test
dubbo.consumer.parameters.eagle.progress=yrpc-client-test
dubbo.consumer.filter=eagle
dubbo.consumer.loadbalance=smart
dubbo.consumer.lbrouters=room,city,isp
dubbo.registries.consumer-reg.id=consumer-reg
dubbo.registries.consumer-reg.address=s2s://test-wudang-meta.yy.com
dubbo.registries.consumer-reg.username=ge_access_s2s_acct

#micrometer config
management.metrics.tags.application=hdpt_stream
management.metrics.tags.env=test
#management.server.port=8081
#management.endpoints.web.exposure.include=prometheus
#management.endpoints.enabled-by-default=false
#management.endpoint.prometheus.enabled=true

thrift_zhuiya_client_s2s_name=zhuiya_server_thrift_test

thrift_skillcard_client_s2s_name=skillcard_server_thrift_test

########## zhuiwan_prize_issue config start###########
thrift_zhuiwan_prize_issue_client_s2s_name=zhuiya_hdzt_thrift_test
########### zhuiwan_prize_issue config end###########
hdzk.host.url=test-hdzk.yy.com

shenshu.udb.appid=9999
shenshu.udb.app-key=2C7B9D99572FAC91CB62352CAEE4F05A
shenshu.udb.cookie-domain=lgn.yy.com
shenshu.udb.index-uri=/rule.html
shenshu.udb.anon-path=/ruleEngine/receiveRuleVersion,/test/listRoomInfoBySsid,/**/*.ico,/**/*.css,/**/*.js
shenshu.udb.udb-path=/ruleEngine/ruleVersionList
shenshu.udb.uri-perm-path=/**
shenshu.cache.redis.sentinel.nodes=fstst-group0001-001-sentinel.yy.com:20013,fstst-group0001-002-sentinel.yy.com:20013,fstst-group0001-003-sentinel.yy.com:20013
shenshu.cache.redis.sentinel.master=ge_activity_redis_test_001
shenshu.auth.access-key=hdpt-stream
shenshu.auth.perm-url=https://zhuiya-test.yy.com/web/permission/ambiguous/getMyPermissions
shenshu.auth.apply-url=https://zhuiya-test.yy.com/admin-static/adminset.html