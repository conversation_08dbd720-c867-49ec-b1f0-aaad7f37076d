<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" monitorInterval="60" packages="com.yy.zhuiya.log4j.appender,com.yy.gameecology.stream.service.stream.script.context">
    <Properties>
        <Property name="basePath">/data/weblog/java</Property>
        <Property name="application">${env:MY_PROJECT_NAME:-currency}</Property>
        <Property name="logFile">all.log2</Property>
        <Property name="fileName">${basePath}/${application}/${logFile}</Property>
        <!--供阿里云和loki采集-->
        <Property name="jsonLogFile">json.log</Property>
        <Property name="jsonFileName">${basePath}/${application}/${jsonLogFile}</Property>
        <Property name="pattern">%d{DEFAULT} %p [%X{origin} %X{trace_id}] [%t] %-c{1}:%L - %m%n</Property>
    </Properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT"/>
            <PatternLayout pattern="${pattern}" />
        </Console>

        <TextLog name="TextLog">
            <PatternLayout pattern="${pattern}" />
        </TextLog>

        <RollingRandomAccessFile name="AllFile" immediateFlush="true"
                                 fileName="/data/weblog/business/stream.gameecology.yy.com/all.log2"
                                 filePattern="/data/weblog/business/stream.gameecology.yy.com/all.log2.%d{yyyy-MM-dd-HH}.%i.gz" ignoreExceptions="false" append="true">
            <ThresholdFilter level="INFO" onMatch="ACCEPT"/>
            <PatternLayout pattern="${pattern}" />
            <Policies>
                <!--                <TimeBasedTriggeringPolicy interval="4" modulate="true"/>-->
                <!--不要太大,避免系统文件滚动-->
                <SizeBasedTriggeringPolicy size="300MB"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="/data/weblog/business/stream.gameecology.yy.com/" maxDepth="1" testMode="false">
                    <!-- 只删除文件前缀为zhuiya.log.的文件 -->
                    <IfFileName glob="${logFile}.*.*.gz" >
                        <IfAny>
                            <!-- 日志保存时间 -->
                            <IfLastModified age="1d" />
                            <IfAccumulatedFileSize exceeds="10 GB" />
                            <IfAccumulatedFileCount exceeds="15" />
                        </IfAny>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>


        <RollingRandomAccessFile name="JsonFile" immediateFlush="true"
                                 fileName="${jsonFileName}"
                                 filePattern="${jsonFileName}.%d{yyyy-MM-dd-HH}.%i.gz" ignoreExceptions="false" append="true">
            <ThresholdFilter level="INFO" onMatch="ACCEPT"/>
            <JsonLayout compact="true" eventEol="true" properties="true" complete="false" locationInfo="true" includeStacktrace="true"
                        stacktraceAsString="true" objectMessageAsJsonObject="false" includeTimeMillis="true">
                <KeyValuePair key="traceId" value="$${ctx:trace_id:-}"/>
                <KeyValuePair key="timestamp" value="$${date:yyyy-MM-dd'T'HH:mm:ss.SSSZZ}" />
            </JsonLayout>
            <Policies>
                <!--                <TimeBasedTriggeringPolicy interval="4" modulate="true"/>-->
                <!--不要太大,避免系统文件滚动-->
                <SizeBasedTriggeringPolicy size="300MB"/>
            </Policies>
            <DefaultRolloverStrategy>
                <Delete basePath="${basePath}/${application}/" maxDepth="1" testMode="false">
                    <!-- 只删除文件前缀为zhuiya.log.的文件 -->
                    <IfFileName glob="${jsonLogFile}.*.*.gz" >
                        <IfAny>
                            <!-- 日志保存时间 -->
                            <IfLastModified age="1d" />
                            <IfAccumulatedFileSize exceeds="2 GB" />
                            <IfAccumulatedFileCount exceeds="2" />
                        </IfAny>
                    </IfFileName>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <Monitor name="Monitor" srvname="java.yymusic.${application}-test">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT"/>
            <PatternLayout pattern="${pattern}" />
        </Monitor>
    </Appenders>
    <Loggers>
        <logger name="com.yy.gameecology.stream.service.stream.script.context.ScriptLog" level="INFO" additivity="true">
            <AppenderRef ref="TextLog"/>
        </logger>
        <logger name="com.yy.ent.clients.s2s" level="ERROR" includeLocation="true">
            <AppenderRef ref="Console"/>
        </logger>
        <logger name="cn.yy.ent.external.service.yyp" level="ERROR" includeLocation="true" additivity="false">
            <AppenderRef ref="Console"/>
        </logger>
        <logger name="org.apache.catalina.core" level="ERROR" additivity="false">
            <AppenderRef ref="Console"/>
        </logger>
        <Root level="INFO" includeLocation="true">
            <AppenderRef ref="AllFile"/>
            <AppenderRef ref="JsonFile"/>
            <AppenderRef ref="Monitor"/>
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>