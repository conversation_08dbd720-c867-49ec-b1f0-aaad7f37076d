<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="INFO" monitorInterval="60" packages="com.yy.zhuiya.log4j.appender,com.yy.gameecology.stream.service.stream.script.context">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{DEFAULT} %p [%X{origin} %X{trace_id}] [%t] %-c{1}:%L - %m%n" />
        </Console>
        <TextLog name="TextLog">
            <PatternLayout pattern="%d{DEFAULT} %p [%X{origin} %X{trace_id}] [%t] %-c{1}:%L - %m%n" />
        </TextLog>
    </Appenders>
    <Loggers>
        <logger name="com.yy.gameecology.stream.service.stream.script.context.ScriptLog" level="INFO" additivity="true">
            <AppenderRef ref="TextLog"/>
        </logger>
        <Root level="INFO" includeLocation="true">
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>
