package com.duowan.sysop.hawk.metrics.client2;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.math.BigDecimal;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Properties;

class Utils {
	private static Logger log = LoggerFactory.getLogger(Utils.class);

	public static final String OS_NAME = getSystemProperty("os.name");
	
	private static String getSystemProperty(String property) {
		try {
			return System.getProperty(property);
		} catch (SecurityException ex) {
			log.info("getSystemProperty error ="+ex.getMessage());
			System.err
					.println("Caught a SecurityException reading the system property '"
							+ property
							+ "'; the SystemUtils property value will default to null.");
		}
		return null;
	}
	
	static boolean isLinux() {
		return OS_NAME != null && OS_NAME.toLowerCase().startsWith("linux");
	}
	
	static InetAddress getAddress(String ip) {
		try {
			return InetAddress.getByName(ip);
		} catch (UnknownHostException e) {
			log.info("getAddress error ="+e.getMessage());
		}
		return null;
	}
	
	static NIOConnection getUplinkConn(NIOConnection upLink, Config conf, IODriver iodrive, NIOReadWrite rwAction) {
		return getConn(upLink, conf.getUplink(), iodrive, rwAction);
	}
	
	static NIOConnection getBypassConn(NIOConnection bypass, Config conf, IODriver iodrive, NIOReadWrite rwAction) {
		return getConn(bypass, conf.getBypass(), iodrive, rwAction);
	}
	
	static NIOConnection getServConn(String ip, int port, int timeout, NIOAcceptAction acception) {
		ServSocketConnection conn = new ServSocketConnection(getAddress(ip), port, timeout);
		conn.setAcception(acception);
		return conn;
	}
	
	static NIOConnection getConn(NIOConnection conn, Conn endpoint, IODriver iodrive, NIOReadWrite rwAction) {
		if (endpoint == null) return conn;
		boolean drop = endpoint.isDropIfChanged(); // consume the flag
		if (conn != null && isEndpointChanged(conn, endpoint)) {
			if ((conn.isUdp() && endpoint.isUseTcp()) || drop) {
				conn.drop();
				conn = null;
			}
		}
		if (endpoint.isEnabled() && conn == null) {
			conn = NIOConnection.open(endpoint.isUseTcp(), endpoint.getIp(), endpoint.getPort(), endpoint.getTimeout());
			if (conn != null) {
				conn.setReadWriteAction(rwAction);
				iodrive.add(conn);
			}
		}
		return conn;
	}
	
	static boolean isEndpointChanged(NIOConnection conn, Conn endpoint) {
		if (conn.isUdp() == endpoint.isUseUdp()) {
			return endpoint.isChanged(conn.getIp(), conn.getPort());
		}
		return true;
	}
	
	static NIOConnection getTestConn(NIOConnection old, NIOConnection conn, IODriver iodrive) {
		if (old == null && conn != null) {
			iodrive.add(conn);
			return conn;
		}
		else if (old != null && conn == null) {
			old.drop();
			return null;
		}
		return old;
	}
	
	static volatile int pid = -1;
	static int getPid() {
		if (pid != -1) return pid;
		String os = System.getProperty("os.name").toLowerCase();
		boolean isWindows = os.contains("win");

		String pidStr = "-1";
		if (!isWindows) {
			byte[] bo = new byte[100];
			String[] cmd = {"bash", "-c", "echo $PPID"};
			Process p;
			try {
				p = Runtime.getRuntime().exec(cmd);
				p.getInputStream().read(bo);
				pidStr = new String(bo);
			} catch (IOException e) {
				isWindows = true;
			}
		}
		if (isWindows) {
			RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
			String jvmName = runtimeBean.getName();
			pidStr = jvmName.split("@")[0];
		}

		try {
			int id = Integer.parseInt(pidStr.trim());
			if(id < 0) {
				id = -1;
				log.error("can not get pid");
			}
			pid = id;
			return pid;
		}
		catch (Exception ex) {
			log.warn("can not get pid {}", ex.getMessage());
		}
		return -1;
	}
	
	static Properties loadFileAsProperties(String file){
		InputStream input = null;
		Properties hostinfo_prop = null;
		 
		try {
			input = new FileInputStream(file);
			hostinfo_prop = new Properties();
			hostinfo_prop.load(input);
			return hostinfo_prop;
		} catch (FileNotFoundException e) {
			log.error("not found file:"+file,e);
		} catch (IOException e) {
			log.error("read file:"+file+" failed!",e);
		}
		finally{
			if(input != null){
				try {
					input.close();
				} catch (IOException e) {
					log.info("loadFileAsProperties error ="+e.getMessage());
				}
			}
		}
		return null;
	}
	
	static int checkConnType(int type) {
		if (type != Conn.TCP && type != Conn.UDP)
			type = 0;
		return type;
	}
	
	static int checkTimeout(int timeout) {
		if (timeout < 0 || timeout > 15000)
			timeout = 5000;
		return timeout;
	}
	
	static int checkPort(int port) {
		if (port < 0 || port > 65535)
			port = 0;
		return port;
	}
	
	static String checkIp(String ip, String validIP) {
		ip = trim(ip, "");
		boolean b1 = "".equals(ip) || "127.0.0.1".equals(ip) || validIP.equals(ip);
		if (b1 || (ip.matches("^\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}$") && getAddress(ip) != null)) {
			return ip;
		}
		return "";
	}
	
	static String trim(String s, String def) {
		if (s == null) return def;
		return s.trim();
	}
	
	static int parseInt(String str) {
		if(str == null) return -1;
		try {
			return Integer.parseInt(str);
		}
		catch (NumberFormatException e) {
			log.debug("parseInt error ="+e.getMessage());
		}
		return -1;
	}
	
	static int parseInt(Properties prop, String key) {
		try {
			Object o = prop.getProperty(key);
			if (o instanceof Number) return ((Number)o).intValue();
			return Integer.parseInt((String)o);
		}
		catch (NumberFormatException e) {
			log.info("parseInt error ="+e.getMessage());
		}
		catch (NullPointerException e) {
			log.debug("parseInt error ="+e.getMessage());
		}
		return -1;
	}

	static int parseDouble2Int(Properties prop, String key) {
		try {
			String s = prop.getProperty(key);
			if (s != null) s = s.replaceAll("\\.", "");
			BigDecimal d = new BigDecimal(s);
			//d.setScale(2);
			//d = d.multiply(new BigDecimal(100));
			return d.intValue();
		}
		catch (Exception e) {
			log.debug("get key error="+e.getMessage()+" key="+key);
		}
		return 0;
	}

	
	static String getString(Properties prop, String key) {
		if (prop != null) return prop.getProperty(key);
		return "";
	}
	
	static Properties loadProperties(String file){
    	InputStream is = null;
    	Properties prop = new Properties();
    	try {
    		is = Utils.class.getResourceAsStream(file);
        	if(is == null ){
        		//log.error("not found "+file+" in metrics jar!");
        		return null;
        	}
			prop.load(is);
			return prop;
		} catch (IOException e) {
			log.info("loadProperties error ="+e.getMessage());
			//log.error("redad "+file+" file failed!",e);
		}
    	finally{
    		try {
				if (is != null) is.close();
			} catch (IOException e) {
				log.info("loadProperties error ="+e.getMessage());
			}
    	}
    	return null;
	}
	
	static String readLine(BufferedReader reader) {
		try {
			if (reader != null) {
				return reader.readLine();
			}
		} catch (IOException e) {
			log.info("readLine error ="+e.getMessage());
		}
		return null;
	}
	
	static BufferedReader open(String file) {
		BufferedReader reader =null;
		try {
			File f = new File(file);
			if (f.exists() && f.isFile()) {
				reader = new BufferedReader(new FileReader(file));
			}
		} catch (Exception e) {
			log.info("open error ="+e.getMessage());
		}
		return reader;
	}
	
	static long getLastModified(String file) {
		try {
			File f = new File(file);
			if (f.exists() && f.isFile()) {
				return f.lastModified();
			}
		} catch (Exception e) {
			log.info("getLastModified error ="+e.getMessage());
		}
		return -1;
	}
	
	static boolean createdAfter(String file, long startup) {
		long m = getLastModified(file);
		return (m > startup && m < System.currentTimeMillis());
	}
	
	static void close(Closeable cl) {
		if(cl != null){
			try {
				cl.close();
			} catch (Exception e) {
				log.info("close error ="+e.getMessage());
			}
		}
	}
	
	static void flush(Flushable f) {
		try {
			if (f != null) f.flush();
		} catch (Exception e) {
			log.info("flush error ="+e.getMessage());
		}
	}
	
	static String parseISPList(String str) {
		StringBuilder sb = new StringBuilder(24);
		java.util.Set<String> set = new java.util.HashSet<String>();
		if (str != null) {
			String [] isps = str.split(",");
			for (String ips : isps) {
				String [] ss = ips.split(":");
				if (ss.length > 1) {
					set.add(ss[1].trim());
				}
			}
		}
		for (String s: set)
			sb.append(s).append(':');
		return sb.toString();
	}
	
	static String substring(String str, String f, int start, boolean tail) {
		int end = str.indexOf(f, start);
		if (end < 0 && tail) return str.substring(start);
		return str.substring(start, end);
	}
	
	static boolean sleep(long t) {
		try {
			Thread.sleep(t);
		} catch (InterruptedException e) {
			e.printStackTrace();
			Thread.currentThread().interrupt();
			return false;
		}
		return true;
	}
	
	static String timestampString() {
		return new java.sql.Timestamp(System.currentTimeMillis()).toString().substring(0, 19).replaceAll("[ \\-\\:]", "");
	}
	
	static int getMtu() {
		try {
			NetworkInterface itf = NetworkInterface.getByName("lo");
			if (itf != null) {
				return itf.getMTU();
			}
		} catch (SocketException e) {
			log.info("getMtu error ="+e.getMessage());
		}
		return 0;
	}

	//exc env
	private static Properties getEnvServiceInfo(){
		String[] cmd = {"bash", "-c", "env"};
		Process p;
		Properties env = new Properties();
		try {
			p = Runtime.getRuntime().exec(cmd);
			env.load(p.getInputStream());
			return env;
		} catch (Exception e) {
			log.info("load env error ="+e.getMessage());
		}
		return null;
	}


	//复用这个字段，用于上报服务的信息，用作服务关联。
	//获取ISP，逻辑如下：
	//1、判断进程是否在容器内，通过判断环境
	//2、判断服务是否潜龙，通过启动参数判断
	//包忽略
	//获取服务的ServiceKey
	static String getServiceKey(){
		Properties p = getEnvServiceInfo();
		if(p==null){
			return null;
		}
		//判断是否容器
		if(p.getProperty("MY_PRODUCT_NAME")!=null){
			return "##3@"+p.getProperty("MY_PRODUCT_NAME")+"@"+p.getProperty("MY_PACKAGE_NAME");
		}else{
			//物理机 潜龙
			String service = System.getProperty("dragon.bizName.projName","");
			if(service.split("____").length==2){
				return "##2@"+service.split("____")[0]+"@"+service.split("____")[1];
			}else{
				return null;
			}
		}
	}

}
