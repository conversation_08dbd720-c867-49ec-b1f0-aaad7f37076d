package com.yy.gameecology.stream.service;

import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.common.consts.Const;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;

/**
 * <AUTHOR> 2021/4/25
 */
@Service
public class InnerService {

    private static final Logger log = LoggerFactory.getLogger(InnerService.class);

    private RestTemplate restTemplate = new RestTemplate();

    @Value("${hdzk.host.url}")
    private String hdzkHost;


    @Async
    public void sendFreeLimitTips(long actId, long busId, long sid, long ssid, long uid, long limit) {
        try {
            URI uri = UriComponentsBuilder.newInstance()
                    .scheme("http")
                    .host(selectHost(actId))
                    .path("/inner/act/sendFreeLimitTips")
                    .queryParam("actId", actId)
                    .queryParam("busId", busId)
                    .queryParam("sid", sid)
                    .queryParam("ssid", ssid)
                    .queryParam("uid", uid)
                    .queryParam("limit", limit)
                    .queryParam("sign", getSign(actId, busId, sid, ssid, uid, limit))
                    .build().toUri();
            String resp = restTemplate.getForObject(uri, String.class);
            log.info("sendFreeLimitTips actId:{} sid:{} ssid:{} uid:{} resp:{}", actId, sid, ssid, uid, resp);
        } catch (Exception e) {
            log.warn("sendFreeLimitTips err:{}", e.getMessage(), e);
        }
    }


    public int getMemberStatus(long actId, String memberId, String statusType, String roleType, String extJson) {
        try {
            URI uri = UriComponentsBuilder.newInstance()
                    .scheme("http")
                    .host(selectHost(actId))
                    .path("/inner/act/getMemberStatus")
                    .queryParam("actId", actId)
                    .queryParam("memberId", memberId)
                    .queryParam("statusType", statusType)
                    .queryParam("roleType", roleType)
                    .queryParam("extJson", extJson)
                    .build().toUri();
            JSONObject resp = restTemplate.getForObject(uri, JSONObject.class);
            log.info("getMemberStatus actId:{}  memberId:{} resp:{}", actId, memberId, resp);
            return resp.getIntValue("data");
        } catch (Exception e) {
            log.error("getMemberStatus actId:{} memberId:{},statusType:{},roleType:{},extJson:{}, err:{}", actId, memberId, statusType, roleType, extJson, e.getMessage(), e);
            throw e;
        }
    }

    private String getSign(long actId, long busId, long sid, long ssid, long uid, long limit) {
        String data = actId + busId + sid + ssid + uid + limit + "*&o21i34jDo1HIOU1832&%*(*&!L";
        return DigestUtils.md5DigestAsHex(data.getBytes());
    }

    private String selectHost(long actId) {
        return getHostByActId(actId);
    }

    private String getHostByActId(long actId) {
        switch (Const.getActRoute(actId)) {
            case 2:
            case 1:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
                return hdzkHost + "/" + actId;
            default:
                throw new IllegalArgumentException("活动ID找不到对应服务! actId: " + actId);
        }
    }

    public long getGameBabySignedSid(long actId, long busId, long uid) {
        try {
            URI uri = UriComponentsBuilder.newInstance()
                    .scheme("https")
                    .host(selectHost(actId))
                    .path("/inner/act/getGameBabySignedSid")
                    .queryParam("actId", actId)
                    .queryParam("busId", busId)
                    .queryParam("uid", uid)
                    .queryParam("sign", getSign(actId, busId, uid))
                    .build().toUri();
            JSONObject jsonObject = restTemplate.getForObject(uri, JSONObject.class);
            log.info("getGameBabySignedSid actId:{}  uid:{} resp:{}", actId, uid, jsonObject.toJSONString());

            return jsonObject.getLongValue("data");
        } catch (Exception ex) {
            log.warn("getGameBabySignedSid error,ex={}", ex.getMessage(), ex);
        }
        return 0;
    }

    private String getSign(long actId, long busId, long uid) {
        String data = actId + busId + uid + "*&o21i34jDo1HIOU1832&%*(*&!L";
        return DigestUtils.md5DigestAsHex(data.getBytes());
    }
}
