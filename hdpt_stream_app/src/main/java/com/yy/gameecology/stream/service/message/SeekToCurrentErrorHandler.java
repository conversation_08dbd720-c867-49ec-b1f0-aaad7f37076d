package com.yy.gameecology.stream.service.message;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.TopicPartition;
import org.springframework.kafka.listener.ContainerAwareErrorHandler;
import org.springframework.kafka.listener.MessageListenerContainer;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiPredicate;

/**
 * 失败重试, 重试上限 -> 死信处理
 *
 * <AUTHOR> 2021/7/12
 */
public class SeekToCurrentErrorHandler implements ContainerAwareErrorHandler {

    private static final Log log = LogFactory.getLog(SeekToCurrentErrorHandler.class);

    private final ThreadLocal<FailedRecord> failures = new ThreadLocal<>();

    private KafkaDeadMessageHandler deadMessageHandler;

    public SeekToCurrentErrorHandler(KafkaDeadMessageHandler deadMessageHandler) {
        this.deadMessageHandler = deadMessageHandler;
    }

    @Override
    public void handle(Exception thrownException, List<ConsumerRecord<?, ?>> records,
                       Consumer<?, ?> consumer, MessageListenerContainer container) {
        boolean doSeeks = doSeeks(records, consumer, thrownException);
        if (!doSeeks) {
            log.warn("handle fail, will try again.", thrownException);
        }
    }

    /**
     * kafka consumer
     * topic-group-partition: commit-offset / seek-offset
     */
    @Override
    public boolean isAckAfterHandle() {
        return false;
    }

    @Override
    public void clearThreadState() {
        this.failures.remove();
    }

    private boolean doSeeks(List<ConsumerRecord<?, ?>> records, Consumer<?, ?> consumer, Exception exception) {

        Map<TopicPartition, Long> partitions = new LinkedHashMap<>();
        AtomicBoolean first = new AtomicBoolean(true);
        AtomicBoolean skipped = new AtomicBoolean();
        records.forEach(record ->  {
            if (first.get()) {
                skipped.set(skip(record, exception));
            }
            if (!first.get() || !skipped.get()) {
                partitions.putIfAbsent(new TopicPartition(record.topic(), record.partition()), record.offset());
            }
            first.set(false);
        });
        partitions.forEach((topicPartition, offset) -> {
            try {
                consumer.seek(topicPartition, offset);
            } catch (Exception e) {
                log.error("Failed to seek " + topicPartition + " to " + offset, e);
            }
        });
        return skipped.get();
    }

    private boolean skip(ConsumerRecord<?, ?> record, Exception exception) {
        if (this.deadMessageHandler.maxFailures() <= 1) {
            this.deadMessageHandler.onDeadMessage(record, exception);
            return true;
        }
        FailedRecord failedRecord = this.failures.get();
        if (newFailure(record, failedRecord)) {
            this.failures.set(new FailedRecord(record.topic(), record.partition(), record.offset()));
            return false;
        } else if (failedRecord.incrementAndGet() >= this.deadMessageHandler.maxFailures()) {
            this.deadMessageHandler.onDeadMessage(record, exception);
            return true;
        } else {
            return false;
        }
    }

    private boolean newFailure(ConsumerRecord<?, ?> record, FailedRecord failedRecord) {
        return failedRecord == null
                || !failedRecord.getTopic().equals(record.topic())
                || failedRecord.getPartition() != record.partition()
                || failedRecord.getOffset() != record.offset();
    }

    private static final class FailedRecord {

        private final String topic;

        private final int partition;

        private final long offset;

        private int count;

        FailedRecord(String topic, int partition, long offset) {
            this.topic = topic;
            this.partition = partition;
            this.offset = offset;
            this.count = 1;
        }

        private String getTopic() {
            return this.topic;
        }

        private int getPartition() {
            return this.partition;
        }

        private long getOffset() {
            return this.offset;
        }

        private int incrementAndGet() {
            return ++this.count;
        }

    }

}
