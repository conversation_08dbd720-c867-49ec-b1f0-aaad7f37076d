package com.yy.gameecology.stream.service.message;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.AnalyzeAndCallerRunsPolicy;
import com.yy.gameecology.common.support.MDCTaskDecorator;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.MDCUtils;
import com.yy.gameecology.common.utils.ShutdownHolder;
import com.yy.gameecology.stream.bean.MessageEvent;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.ParameterizedType;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2021/7/8
 */
public abstract class KafkaMessageHandlerSupport<T> implements InitializingBean, DisposableBean, ApplicationRunner, KafkaMessageHandler {

    protected final Logger log = LoggerFactory.getLogger(getClass());

    public static final ConcurrentMap<String, KafkaMessageHandlerSupport> HANDLER_MAP = new ConcurrentHashMap<>();

    private final ThreadLocal<FailedRecord> failedRecords = new ThreadLocal<>();

    /**
     * 保存降级
     */
    private boolean saveFailBack;

    private long saveFailBackFinishTime;

    /**
     * 建议用 {@link Document} 指定表名
     */
    private String tableName;

    private Class<T> messageType;

    @Autowired
    private MessageDao messageDao;

    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private ConcurrentMap<TopicPartition, ExecutorService> executorMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() {
        ParameterizedType parameterizedType = (ParameterizedType) getClass().getGenericSuperclass();
        this.messageType = (Class<T>) parameterizedType.getActualTypeArguments()[0];
        this.tableName = messageDao.tableName(this.messageType);
        Assert.isNull(HANDLER_MAP.put(tableName(), this), "table name repeat! table: " + tableName());
    }

    @Override
    public void destroy() throws Exception {
        executorMap.values().forEach(executorService -> {
            try {
                executorService.shutdown();
                executorService.awaitTermination(10, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("destroy err:{}", e.getMessage(), e);
            }
        });
        failedRecords.remove();
    }

    /**
     * 消息保存, 限流, 重试
     */
    @Override
    public void handleMessage(ConsumerRecord<String, String> message) {

        logFirst(message);
        MessageEvent<T> messageEvent = toMessageEvent(message);

        //降级时同步处理
        if (saveFailBack) {
            handleMessageInternal(messageEvent);
            return;
        }

        //消息保存开关
        boolean saveSwitch = getSaveSwitch(messageEvent);
        if (saveSwitch) {
            saveMessage(messageEvent);
            //log.info("handleMessage save. messageId:{}", messageEvent.getMessageId());
        }

        Executor executor = getExecutor(messageEvent);
        if (saveSwitch) {
            executor.execute(() -> handleMessage(messageEvent));
        } else {
            executor.execute(() -> handleMessageInternal(messageEvent));
        }
    }

    @Override
    public void handleMessageList(List<ConsumerRecord<String, String>> messageList) {

        List<MessageEvent> messageEventList = messageList.stream().map(message -> {
            logFirst(message);
            return toMessageEvent(message);
        }).collect(Collectors.toList());

        MessageEvent<T> first = messageEventList.get(0);
        boolean saveSwitch = getSaveSwitch(first);
        if (saveSwitch) {
            batchSaveMessage(messageEventList);
            //log.info("handleMessages save. messageSize:{}", messageEventList.size());
        }

        messageEventList.forEach(messageEvent -> {
            Executor executor = getExecutor(messageEvent);
            if (saveSwitch) {
                executor.execute(() -> handleMessage(messageEvent));
            } else {
                executor.execute(() -> handleMessageInternal(messageEvent));
            }
        });

    }

    private void handleMessage(MessageEvent<T> messageEvent) {
        boolean handle = handleMessageInternal(messageEvent);
        if (handle) {
            doneMessage(messageEvent);
            //log.info("handleMessage done.");
        } else {
            retryMessage(messageEvent);
            log.info("handleMessage retry.");
        }
    }

    private boolean handleMessageInternal(MessageEvent<T> messageEvent) {
        boolean handle = false;
        try {
            //处理入口
            T message = messageEvent.getData();
            if (checkMessage(message)) {
                doHandleMessage(message);
                //log.info("handleMessage deal.");
            } else {
                log.info("handleMessage skip.");
            }
            handle = true;
        } catch (Exception e) {
            log.error("handleMessage fail. err:{} message:{}", e.getMessage(), messageEvent, e);
            handleFail(messageEvent);
        }
        return handle;
    }

    private void handleFail(MessageEvent<T> messageEvent) {
        try {
            handleFail(messageEvent.getData());
        } catch (Exception e) {
            log.error("handleFail err:{} messageId:{}", e.getMessage(), messageEvent.getMessageId(), e);
        }
    }

    /**
     * 消息检查, 返回true表示继续处理, 返回false表示跳过
     *
     * @return 消息是否正常
     */
    protected boolean checkMessage(T message) {
        return message != null;
    }

    /**
     * 消息处理
     */
    protected abstract void doHandleMessage(T message);

    /**
     * 失败时
     */
    protected void handleFail(T message) { }


    private Executor getExecutor(MessageEvent<T> messageEvent) {
        return executorMap.computeIfAbsent(new TopicPartition(messageEvent.getTopic(), messageEvent.getPartition()), partition -> {
            return new ThreadPoolExecutor(getExecutorCoreSize(), getExecutorCoreSize() * 5, 1, TimeUnit.MINUTES,
                    new LinkedBlockingQueue<>(getExecutorQueueSize()),
                    new CustomizableThreadFactory(partition.topic() + "-" + partition.partition() + "-"),
                    new AnalyzeAndCallerRunsPolicy()) {
                @Override
                public void execute(Runnable command) {
                    super.execute(MDCTaskDecorator.makeMDCTask(command));
                }
            };
        });
    }

    protected int getExecutorCoreSize() {
        int coreSize = Const.GEPM.getParamValueToInt("stream_executor_core_size", 10);
        log.info("getExecutorCoreSize:{}", coreSize);
        return coreSize;
    }

    protected int getExecutorQueueSize() {
        return 1000;
    }

    private void logFirst(ConsumerRecord<String, String> message) {
        log.info("handleMessage begin. partition:{} offset:{} key:{} value:{}",
                message.partition(), message.offset(), message.key(), message.value());
    }

    private MessageEvent<T> toMessageEvent(ConsumerRecord<String, String> message) {
        T data = parseMessage(message);
        String messageId = getMessageId(data, message);
        long timestamp = getTimestamp(data, message);

        MessageEvent<T> messageEvent = new MessageEvent<>();
        messageEvent.setMessageId(messageId);
        messageEvent.setTopic(message.topic());
        messageEvent.setPartition(message.partition());
        messageEvent.setOffset(message.offset());
        messageEvent.setTimestamp(new Date(timestamp));
        messageEvent.setData(data);
        return messageEvent;
    }

    private boolean getSaveSwitch(MessageEvent<T> messageEvent) {
        return messagePersistent()
                && Const.GEPM.getParamValueToBoolean("kafka_message_save_switch", true);
    }

    protected T parseMessage(ConsumerRecord<String, String> message) {
        return JSON.parseObject(message.value(), messageType());
    }

    protected boolean messagePersistent() {
        return true;
    }

    protected String tableName() {
        return tableName;
    }

    protected Class<T> messageType() {
        return messageType;
    }

    private boolean saveMessage(MessageEvent messageEvent) {
        try {
            messageDao.saveMessage(messageEvent, tableName());
            // TODO: 耗时超1s, failback
            return true;
        } catch (DuplicateKeyException e) {
            log.info("saveMessage DuplicateKey. message:{}", messageEvent);
            return false;
        } catch (Exception e) {
            FailedRecord failedRecord = this.failedRecords.get();
            if (failedRecord == null
                    || !failedRecord.getTopic().equals(messageEvent.getTopic())
                    || failedRecord.getPartition() != messageEvent.getPartition()
                    || failedRecord.getOffset() != messageEvent.getOffset()
            ) {
                this.failedRecords.set(new FailedRecord(messageEvent.getTopic(), messageEvent.getPartition(), messageEvent.getOffset()));
            } else if (failedRecord.incrementAndGet() >= 3) {
                //降级1分钟
                this.saveFailBackFinishTime = System.currentTimeMillis() + DateUtils.MILLIS_PER_MINUTE;
                this.saveFailBack = true;
                log.error("saveFailBack.");
            }
            throw e;
        }
    }

    private boolean batchSaveMessage(List<MessageEvent> messageEventList) {
        try {
            messageDao.batchSaveMessage(messageEventList, tableName());
            return true;
        } catch (DuplicateKeyException e) {
            //TODO: test
            log.info("batchSaveMessage DuplicateKey. messageSize:{}", messageEventList.size());
            return false;
        }
    }

    private void doneMessage(MessageEvent messageEvent) {
        messageDao.doneMessage(messageEvent.getMessageId(), tableName());
    }

    private void retryMessage(MessageEvent messageEvent) {
        int retryAfterSeconds = (int) Math.pow(2, messageEvent.getRetryTimes());
        Date retryTime = DateUtils.addSeconds(messageEvent.getTimestamp(), retryAfterSeconds);
        messageDao.retryMessage(messageEvent.getMessageId(), retryTime, tableName());
    }

    protected String getMessageId(T message, ConsumerRecord<String, String> consumerRecord) {
        return UUID.randomUUID().toString();
    }

    protected long getTimestamp(T message, ConsumerRecord<String, String> consumerRecord) {
        return consumerRecord.timestamp() > 0 ? consumerRecord.timestamp() : System.currentTimeMillis();
    }

    @Scheduled(cron = "0/5 * * * * ?")
    public void retryHandle() {

        if (SysEvHelper.isLocal()) {
            return;
        }

        if (ShutdownHolder.isShuttingDown()) {
            log.info("shutdown retryHandle scheduled");
            return;
        }

        this.saveFailBack = System.currentTimeMillis() <= this.saveFailBackFinishTime;

        String timerKey = "retryHandle:" + tableName();
        if (!stringRedisTemplate.opsForValue().setIfAbsent(timerKey, "1", 1, TimeUnit.MINUTES)) {
            return;
        }

        // receive -> retry
        messageDao.retryDeadMessage(tableName());

        // 重试数据量一般不多, 暂不考虑分片
        List<MessageEvent> messageEventList = messageDao.getRetryMessage(tableName());
        if (!CollectionUtils.isEmpty(messageEventList)) {
            CountDownLatch countDownLatch = new CountDownLatch(messageEventList.size());
            for (MessageEvent messageEvent : messageEventList) {
                taskExecutor.execute(() -> {
                    MDCUtils.putContext("timer=" + tableName());
                    log.info("retryHandle start. messageId:{}", messageEvent.getMessageId());
                    try {
                        handleMessage(messageEvent);
                    } finally {
                        MDCUtils.clearContext();
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                //超时避免死锁
                countDownLatch.await(1 , TimeUnit.MINUTES);
            } catch (InterruptedException e) {
                log.error("retryHandle err:{}", e.getMessage(), e);
            }
        }

        messageDao.deleteHistoryData(tableName, expireDays());
        
        stringRedisTemplate.delete(timerKey);
    }
    
    protected int expireDays() {
        return 30;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        try {
            if (SysEvHelper.isLocal()) {
                this.saveFailBack = true;
                this.saveFailBackFinishTime = Long.MAX_VALUE;
                return;
            }
            messageDao.initTable(tableName());
        } catch (Exception e) {
            log.info("initTable table:{} msg:{}", tableName(), e.getMessage());
        }
    }

    private static final class FailedRecord {

        private final String topic;

        private final int partition;

        private final long offset;

        private int count;

        FailedRecord(String topic, int partition, long offset) {
            this.topic = topic;
            this.partition = partition;
            this.offset = offset;
            this.count = 1;
        }

        private String getTopic() {
            return this.topic;
        }

        private int getPartition() {
            return this.partition;
        }

        private long getOffset() {
            return this.offset;
        }

        private int incrementAndGet() {
            return ++this.count;
        }

    }

}
