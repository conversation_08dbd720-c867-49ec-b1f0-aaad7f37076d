package com.yy.gameecology.stream.exception;

/**
 * <AUTHOR> 2021/8/16
 */
public class RuleException extends RuntimeException {

    private int code;

    public RuleException(int code, String message) {
        super(message);
        this.code = code;
    }

    public RuleException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
