package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.FriendSealEvent;
import com.yy.gameecology.stream.bean.GiftSourceChannel;
import com.yy.gameecology.stream.service.ActFriendService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.CommonService;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> 2021/7/13
 */
@Service
public class FriendSealHandler extends KafkaMessageHandlerSupport<FriendSealEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Autowired
    private CommonService commonService;

    @Override
    protected boolean checkMessage(FriendSealEvent message) {
        return message != null && !CollectionUtils.isEmpty(message.getGiftList());
    }

    @Override
    protected void doHandleMessage(FriendSealEvent message) {
        //追玩低版本app 用sid放的asid这里做临时兼容-陈侠专（2021-11-18）
        GiftSourceChannel giftSourceChannel = GiftSourceChannel.findByValue(message);
        if (giftSourceChannel == GiftSourceChannel.ZHUIWAN) {
            long sid = commonService.getSid(message.getSid());
            if (message.getSid() != sid) {
                log.info("FriendSealHandler adjust event sid@seq:{} old:{} newSid:{}", message.getSeqID(), message.getSid(), sid);
                message.setSid(sid);
            }
        }
        for (ActFriendService actFriendService : actServiceManager.getActFriendServiceList()) {
            actFriendService.onSealEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.FRIEND_SEAL_EVENT.getEventId());
    }
}
