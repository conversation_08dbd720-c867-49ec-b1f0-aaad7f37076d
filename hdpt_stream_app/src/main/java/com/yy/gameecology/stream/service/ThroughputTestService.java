package com.yy.gameecology.stream.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.concurrent.*;
import java.util.concurrent.atomic.LongAdder;

/**
 * <AUTHOR> 2021/7/7
 */
@Service
public class ThroughputTestService {

    private static final Logger log = LoggerFactory.getLogger(ThroughputTestService.class);

    public void testThroughput(int core, int size, Callable<Boolean> task) throws Exception {

        ThreadPoolExecutor executor = new ThreadPoolExecutor(core, core, 1, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

        long s = System.currentTimeMillis();
        CountDownLatch countDownLatch = new CountDownLatch(core);
        LongAdder totalCost = new LongAdder();
        LongAdder success = new LongAdder();

        for (int i = 0; i < core; i++) {
            executor.execute(()->{
                try {
                    long b = System.currentTimeMillis();
                    for (int j = 0; j < size; j++) {
                        if (task.call()) {
                            success.add(1);
                        }
                    }
                    totalCost.add(System.currentTimeMillis() - b);
                } catch (Exception e){
                    log.error("testThroughput err:{}", e.getMessage(), e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        countDownLatch.await();

        long cost = System.currentTimeMillis() - s;
        int times = core * size;
        long throughput = times * 1000 / cost;
        double avg = totalCost.doubleValue() / times;
        double successRate = success.doubleValue() / times * 100;

        log.info("testThroughput thread:{} cost:{}ms totalCount:{} throughput:{}/s avg:{}ms successRate:{}%", core, cost, times, throughput, avg, successRate);

        executor.shutdown();

    }

    /**
     * mongo单连接极限在3k左右, 应该是根据线程缓存了连接
     *
     * @param core
     * @param size
     * @param task
     * @throws Exception
     */
    public void testThroughputPar(int core, int size, Callable<Boolean> task) throws Exception {

        ThreadPoolExecutor executor = new ThreadPoolExecutor(core, core, 1, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

        long s = System.currentTimeMillis();
        CountDownLatch countDownLatch = new CountDownLatch(size);
        LongAdder totalCost = new LongAdder();
        LongAdder success = new LongAdder();

        for (int j = 0; j < size; j++) {
            executor.execute(()->{
                try {
                    long b = System.currentTimeMillis();
                    if (task.call()) {
                        success.add(1);
                    }
                    totalCost.add(System.currentTimeMillis() - b);
                } catch (Exception e){
                    log.error("testThroughput err:{}", e.getMessage(), e);
                } finally {
                    countDownLatch.countDown();
                }
            });
        }

        countDownLatch.await();

        long cost = System.currentTimeMillis() - s;
        int times = size;
        long throughput = times * 1000 / cost;
        double avg = totalCost.doubleValue() / times;
        double successRate = success.doubleValue() / times * 100;

        log.info("testThroughput thread:{} cost:{}ms totalCount:{} throughput:{}/s avg:{}ms successRate:{}%", core, cost, times, throughput, avg, successRate);

        executor.shutdown();

    }

}
