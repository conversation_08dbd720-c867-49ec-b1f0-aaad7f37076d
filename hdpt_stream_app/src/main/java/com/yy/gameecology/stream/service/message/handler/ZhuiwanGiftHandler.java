package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.ZhuiwanGiftEvent;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.ActZhuiwanService;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2021/8/10
 */
@Component
public class ZhuiwanGiftHandler extends KafkaMessageHandlerSupport<ZhuiwanGiftEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(ZhuiwanGiftEvent message) {
        for (ActZhuiwanService actZhuiwanService : actServiceManager.getActZhuiwanServiceList()) {
            actZhuiwanService.onGiftEvent(message);
        }
        eventRuleStreamService.executeEventRule(message, EventID.ZHUIWAN_GIFT_EVENT.getEventId());
    }
}
