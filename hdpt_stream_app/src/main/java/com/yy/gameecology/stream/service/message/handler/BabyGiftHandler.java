package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.BabyGiftEvent;
import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.service.ActBabyService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2021/7/8
 */
@Service
public class BabyGiftHandler extends KafkaMessageHandlerSupport<BabyGiftEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected boolean checkMessage(BabyGiftEvent message) {
        return message != null
                && message.getTransaction() != null
                && message.getTransaction().getDebitUid() != null
                //宝贝特殊逻辑, 忽略以下送礼uid段
                && (message.getTransaction().getDebitUid() < 2118428900
                    || message.getTransaction().getDebitUid() >= 2118429900);
    }

    @Override
    protected void doHandleMessage(BabyGiftEvent message) {
        for (ActBabyService actPkService : actServiceManager.getActBabyServiceList()) {
            actPkService.onGiftEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.BABY_GIFT_EVENT.getEventId());
    }

}
