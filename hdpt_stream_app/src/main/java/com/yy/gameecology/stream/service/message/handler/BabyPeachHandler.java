package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.BabyPeachEvent;
import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.service.ActBabyService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: 郭立平
 * @Desciption: 宝贝桃花签
 * @Date: 2022/04/18 10:00
 * @Modified:
 */
@Service
public class BabyPeachHandler extends KafkaMessageHandlerSupport<BabyPeachEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(BabyPeachEvent message) {
        for (ActBabyService actBabyService : actServiceManager.getActBabyServiceList()) {
            actBabyService.onBabyPeachEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.BABY_PEACH_EVENT.getEventId());
    }
}
