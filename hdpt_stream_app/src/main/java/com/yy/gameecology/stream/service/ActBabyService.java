package com.yy.gameecology.stream.service;

import com.yy.gameecology.common.protocol.thrift.hdztranking.BusiId;
import com.yy.gameecology.stream.bean.BabyGiftEvent;
import com.yy.gameecology.stream.bean.BabyPeachEvent;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> 2021/4/19
 */
public interface ActBabyService extends ActService {

    void onGiftEvent(BabyGiftEvent babyGiftEvent);

    @Override
    default long getBusId() {
        return BusiId.GAME_BABY.getValue();
    }

    default boolean isFromZhuiwan(String mac) {
        return "05".equals(mac) || "06".equals(mac);
    }

    ConcurrentMap<Long, ActBabyService> ACT_ID_MAP = new ConcurrentHashMap<>();

    /**
     * 宝贝桃花签事件
     *
     * @param babyPeachEvent
     */
    default void onBabyPeachEvent(BabyPeachEvent babyPeachEvent) {}

    @PostConstruct
    default void initServiceMap() {
        Assert.isNull(ACT_ID_MAP.put(getActId(), this), "actService repeat! actId:{}" + getActId());
    }
}
