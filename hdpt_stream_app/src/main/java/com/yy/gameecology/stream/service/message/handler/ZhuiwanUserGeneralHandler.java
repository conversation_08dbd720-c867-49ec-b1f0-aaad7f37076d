package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.ZhuiwanUserGeneralEvent;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ZhuiwanUserGeneralHandler extends KafkaMessageHandlerSupport<ZhuiwanUserGeneralEvent> {

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected boolean messagePersistent() {
        return false;
    }

    @Override
    protected void doHandleMessage(ZhuiwanUserGeneralEvent message) {
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.ZHUIWAN_USER_GENERAL_EVENT.getEventId());
    }
}
