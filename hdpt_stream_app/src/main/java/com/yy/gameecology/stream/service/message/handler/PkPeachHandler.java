package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.PkPeachEvent;
import com.yy.gameecology.stream.service.ActPkService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: CXZ
 * @Desciption: 约战桃花签
 * @Date: 2021/10/20 12:40
 * @Modified:
 */
@Service
public class PkPeachHandler extends KafkaMessageHandlerSupport<PkPeachEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(PkPeachEvent message) {
        for (ActPkService actPkService : actServiceManager.getActPkServiceList()) {
            actPkService.onPkPeachEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.YZ_PEACH_EVENT.getEventId());
    }
}
