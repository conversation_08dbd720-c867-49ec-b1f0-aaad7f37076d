package com.yy.gameecology.stream.service.stream;

import com.alibaba.fastjson.JSON;

import java.util.List;

/**
 * <AUTHOR> 2021/4/25
 */
public class ScriptEvalResult<T> {

    public static final int SUCCESS = 0;

    public static final int EVAL_FAIL = -1;

    public static final int LIMIT = -2;

    public static final int NOT_FOUND = 404;

    private int code;

    private String msg;

    private T data;

    public T getData() {
        return (T) data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    private List<String> testLogList;

    public List<String> getTestLogList() {
        return testLogList;
    }

    public void setTestLogList(List<String> testLogList) {
        this.testLogList = testLogList;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public static <T> ScriptEvalResult<T> success(T data) {
        ScriptEvalResult<T> result = new ScriptEvalResult<>();
        result.code = SUCCESS;
        result.data = data;
        return result;
    }

    public static ScriptEvalResult fail(int code, String msg) {
        ScriptEvalResult result = new ScriptEvalResult();
        result.code = code;
        result.msg = msg;
        return result;
    }
}
