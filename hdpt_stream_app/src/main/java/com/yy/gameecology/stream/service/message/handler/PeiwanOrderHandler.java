package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.PeiwanOrderEvent;
import com.yy.gameecology.stream.service.ActPeiwanService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2021/7/13
 */
@Service
public class PeiwanOrderHandler extends KafkaMessageHandlerSupport<PeiwanOrderEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(PeiwanOrderEvent message) {
        for (ActPeiwanService actPeiwanService : actServiceManager.getActPeiwanServiceList()) {
            actPeiwanService.onOrderEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.PEIWAN_ORDER_EVENT.getEventId());
    }
}
