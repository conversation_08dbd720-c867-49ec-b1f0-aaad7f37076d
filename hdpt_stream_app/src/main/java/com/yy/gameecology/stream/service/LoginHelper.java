package com.yy.gameecology.stream.service;

import com.yy.gameecology.stream.bean.UserInfo;

/**
 * <AUTHOR> 2021/8/11
 */
public class LoginHelper {

    private static final ThreadLocal<UserInfo> USER_INFO_THREAD_LOCAL = new ThreadLocal<>();

    public static final void setUserInfo(UserInfo userInfo) {
        USER_INFO_THREAD_LOCAL.set(userInfo);
    }

    public static final UserInfo getUserInfo() {
        return USER_INFO_THREAD_LOCAL.get();
    }

    public static final void removeUserInfo() {
        USER_INFO_THREAD_LOCAL.remove();
    }

}
