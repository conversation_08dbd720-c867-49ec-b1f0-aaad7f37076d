package com.yy.gameecology.stream.service.message;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.utils.SystemUtil;
import com.yy.gameecology.stream.bean.KafkaDeadMessage;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;

/**
 * <AUTHOR> 2021/7/12
 */
public class ConfirmDeadMessageHandler implements KafkaDeadMessageHandler<String, String> {

    private static final Logger log = LoggerFactory.getLogger(ConfirmDeadMessageHandler.class);

    @Value("${kafka.topic.dead-message.max-failures:5}")
    private int maxFailures;

    @Value("${kafka.topic.dead-message}")
    private String deadMessageTopic;

    @Autowired
    private KafkaTemplate<String, String> hdptWxKafkaTemplate;

    @Autowired
    private KafkaTemplate<String, String> hdptWxXdcKafkaTemplate;

    @Override
    public int maxFailures() {
        return maxFailures;
    }

    @Override
    public void onDeadMessage(ConsumerRecord<String, String> consumerRecord, Exception exception) {

        log.error("Max failures (" + maxFailures() + ") reached for: " + consumerRecord, exception);

        KafkaDeadMessage kafkaDeadMessage = buildDeadMessage(consumerRecord, exception);
        String key = consumerRecord.topic() + "#" + consumerRecord.partition() + "#" + consumerRecord.offset();
        String value = JSON.toJSONString(kafkaDeadMessage);

        hdptWxKafkaTemplate.send(deadMessageTopic, key, value)
                .addCallback(
                        result -> log.info("onDeadMessage success. key:{} partition:{} offset", key),
                        ex1 -> hdptWxXdcKafkaTemplate.send(deadMessageTopic, key, value)
                                .addCallback(
                                        result -> log.info("onDeadMessage success. key:{} ex1:{}", key, ex1.getMessage()),
                                        ex2 -> log.error("onDeadMessage err:{} record:{}", ex2.getMessage(), consumerRecord, ex2)
                                )
                );
    }

    private KafkaDeadMessage buildDeadMessage(ConsumerRecord<String, String> consumerRecord, Exception exception) {
        KafkaDeadMessage deadMessage = new KafkaDeadMessage();
        deadMessage.setTopic(consumerRecord.topic());
        deadMessage.setPartition(consumerRecord.partition());
        deadMessage.setOffset(consumerRecord.offset());
        deadMessage.setTimestamp(consumerRecord.timestamp());
        deadMessage.setKey(consumerRecord.key());
        deadMessage.setValue(consumerRecord.value());
        deadMessage.setException(exception.getMessage());
        deadMessage.setIp(SystemUtil.getIp());
        return deadMessage;
    }
}
