package com.yy.gameecology.stream.service.stream;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.protocol.thrift.rule_engine.ExecuteRuleReq;
import com.yy.gameecology.common.protocol.thrift.rule_engine.ExecuteRuleResult;
import com.yy.gameecology.common.protocol.thrift.rule_engine.RuleEngineService;
import com.yy.gameecology.stream.exception.RuleException;
import org.apache.thrift.TException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR> 2021/4/26
 */
public class RuleEngineServiceImpl implements RuleEngineService.Iface {

    private static final Logger log = LoggerFactory.getLogger(RuleEngineServiceImpl.class);

    @Autowired
    private RuleEngineCoreService ruleEngineCoreService;

    @Override
    public void ping() throws TException {}

    /**
     * TODO: 调用鉴权
     *
     * @param req
     * @return
     * @throws TException
     */
    @Override
    public ExecuteRuleResult executeRule(ExecuteRuleReq req) throws TException {
        try {
            log.info("executeRule req:{}", req);
            Map<String, Object> params = new HashMap<>();
            Optional.ofNullable(JSON.parseObject(req.getParamJSON())).ifPresent(params::putAll);
            ScriptEvalResult evalResult = ruleEngineCoreService.executeRule(req.getAppId(), req.getRuleId(), params);
            ExecuteRuleResult result = new ExecuteRuleResult()
                    .setCode(evalResult.getCode())
                    .setMsg(evalResult.getMsg())
                    .setResultJSON(JSON.toJSONString(evalResult.getData()));
            log.info("executeRule result:{}", result);
            return result;
        } catch (RuleException e) {
            log.warn("executeRule code:{} err:{}", e.getCode(), e.getMessage());
            return new ExecuteRuleResult()
                    .setCode(e.getCode())
                    .setMsg(e.getMessage());
        } catch (Exception e) {
            log.error("executeRule err:{}", e.getMessage(), e);
            return new ExecuteRuleResult()
                    .setCode(-500)
                    .setMsg("系统错误");
        }
    }
}
