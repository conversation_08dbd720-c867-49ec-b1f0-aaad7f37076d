package com.yy.gameecology.stream.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 2021/4/23
 */
@Service
public class EventDistinctService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public boolean eventRepeat(String key) {
        return !stringRedisTemplate.opsForValue().setIfAbsent("event:" + key, "1", 2, TimeUnit.DAYS);
    }

}
