
package com.yy.gameecology.stream.support;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class RedisSupport {

	private static Logger log = LoggerFactory.getLogger(RedisSupport.class);

	private static final Map<String, String> SCRIPT_MAP = new ConcurrentHashMap<String, String>();

	public static String getScript(String name) {
		String script = SCRIPT_MAP.get(name);
		if (script == null) {
			PathMatchingResourcePatternResolver prpr = new PathMatchingResourcePatternResolver();
			String scriptLocation = "classpath:/lua/";
			Resource resource = prpr.getResource(scriptLocation + name);
			InputStream is = null;
			try {
				if (resource != null) {
					is = resource.getInputStream();
					script = IOUtils.toString(is);
					SCRIPT_MAP.put(name, script);
					return script;
				}
			} catch (IOException e) {
				log.error("read script file error,script file name:" + name, e);
			} finally {
				IOUtils.closeQuietly(is);
			}
		}
		return script;
	}

	/**
	 * lua脚本执行封装函数
	 */
	public static <T> T executeLua(StringRedisTemplate stringRedisTemplate, String scriptLuaName, Class<T> returnClz, List<String> keys, List<String> argv) {
		DefaultRedisScript<T> script = new DefaultRedisScript<>();
		script.setScriptText(RedisSupport.getScript(scriptLuaName));
		script.setResultType(returnClz);
		return stringRedisTemplate.execute(script, keys, argv.toArray());
	}
}
