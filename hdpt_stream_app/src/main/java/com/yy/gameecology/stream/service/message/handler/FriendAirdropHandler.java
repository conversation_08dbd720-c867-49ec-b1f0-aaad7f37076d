package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.AirdropEvent;
import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.service.ActFriendService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> 2021/7/13
 */
@Service
public class FriendAirdropHandler extends KafkaMessageHandlerSupport<AirdropEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected boolean checkMessage(AirdropEvent message) {
        return message != null && !CollectionUtils.isEmpty(message.getDropList());
    }

    @Override
    protected void doHandleMessage(AirdropEvent message) {
        for (ActFriendService actFriendService : actServiceManager.getActFriendServiceList()) {
            actFriendService.onAirdropEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.AIRDROP_EVENT.getEventId());
    }
}
