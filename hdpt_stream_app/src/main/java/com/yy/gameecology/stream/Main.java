package com.yy.gameecology.stream;

import com.yy.aomi.sdk.AomiSdk;
import com.yy.gameecology.common.support.SysEvHelper;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR> 2021/4/15
 */
@EnableDubbo
@EnableKafka
@EnableRabbit
@EnableScheduling
@EnableAsync(proxyTargetClass = true)
@MapperScan(basePackages = "com.yy.gameecology.common.db.mapper.gameecology")
@SpringBootApplication(scanBasePackages = {"com.yy.gameecology"}, exclude = {RedisAutoConfiguration.class, RabbitAutoConfiguration.class, KafkaAutoConfiguration.class, DataSourceAutoConfiguration.class})
public class Main {

    private static final Logger log = LoggerFactory.getLogger(Main.class);

    public static void main(String[] args) {
        String desc = SysEvHelper.getEnvDesc();
        String appcfg = SysEvHelper.getAppConfig();
        String aomiCfg = SysEvHelper.getAomiConfig();

        // 必须在IOC框架以及相关被监控框架初始化前完成对AomiSdk的初始化
        // 所以请将初始化代码放在main函数入口最开头
        //去除Aomi

        SpringApplication app = new SpringApplication(Main.class);
        app.setDefaultProperties(SysEvHelper.properties);

        log.info("########################################################################################");
        log.info("============>> 系统启动，使用配置： 【" + desc + "】 -> " + appcfg);
        log.info("########################################################################################");
        app.run(args);

    }

}
