package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.FriendGiftEvent;
import com.yy.gameecology.stream.bean.GiftSourceChannel;
import com.yy.gameecology.stream.service.ActFriendService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.CommonService;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR> 2021/7/13
 */
@Service
public class FriendGiftHandler extends KafkaMessageHandlerSupport<FriendGiftEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Autowired
    private CommonService commonService;

    @Override
    protected boolean checkMessage(FriendGiftEvent message) {
        return message != null && !CollectionUtils.isEmpty(message.getGiftList());
    }

    @Override
    protected void doHandleMessage(FriendGiftEvent message) {
        //追玩低版本app 用sid放的asid这里做临时兼容-陈侠专（2021-11-18）
        GiftSourceChannel giftSourceChannel = GiftSourceChannel.findByValue(message);
        if (giftSourceChannel == GiftSourceChannel.ZHUIWAN) {
            long sid = commonService.getSid(message.getSid());
            if (message.getSid() != sid) {
                log.info("FriendGiftHandler adjust event sid@seq:{} old:{} newSid:{}", message.getSeqID(), message.getSid(), sid);
                message.setSid(sid);
            }
        }

        for (ActFriendService actFriendService : actServiceManager.getActFriendServiceList()) {
            actFriendService.onGiftEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.FRIEND_GIFT_EVENT.getEventId());
    }
}
