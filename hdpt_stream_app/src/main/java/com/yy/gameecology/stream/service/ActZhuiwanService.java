package com.yy.gameecology.stream.service;

import com.yy.gameecology.common.protocol.thrift.hdztranking.BusiId;
import com.yy.gameecology.stream.bean.*;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> 2021/4/23
 */
public interface ActZhuiwanService extends ActService {

    default void onGiftEvent(ZhuiwanGiftEvent zhuiwanGiftEvent) {}

    default void onGiftComboEvent(ZhuiwanGiftComboEvent zhuiwanGiftComboEvent) {}

    default void onTaskEvent(ZhuiwanTaskEvent zhuiwanTaskEvent){}

    default void onLoginEvent(ZhuiwanLoginEvent zhuiwanLoginEvent){}

    default void onRoomHeartEvent(ZhuiWanRoomHeartEvent event){}

    default void onRechargeEvent(ZhuiWanRechargeEvent event){}

    @Override
    default long getBusId() {
        return BusiId.ZHUI_WAN.getValue();
    }

    ConcurrentMap<Long, ActZhuiwanService> ACT_ID_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    default void initServiceMap() {
        Assert.isNull(ACT_ID_MAP.put(getActId(), this), "actService repeat! actId:{}" + getActId());
    }
}
