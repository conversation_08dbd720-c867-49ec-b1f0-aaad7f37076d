package com.yy.gameecology.stream.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.protocol.thrift.miniest.TextChatMsg;
import com.yy.gameecology.common.utils.XmlUtil;
import com.yy.gameecology.stream.bean.ChannelMessageEvent;
import com.yy.gameecology.stream.bean.RuleApp;
import com.yy.gameecology.stream.client.MiniestClient;
import com.yy.gameecology.stream.client.UserInfoClient;
import com.yy.gameecology.stream.exception.RuleException;
import com.yy.gameecology.stream.service.stream.RuleEngineCoreService;
import com.yy.gameecology.stream.service.stream.ScriptEvalResult;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.NodeList;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2021/8/7
 */
@Service
public class ChannelMessageService {

    private static final Logger log = LoggerFactory.getLogger(ChannelMessageService.class);

    public static final String ZHUIWAN_APPKEY = "1325544243";

    public static final String ZHUIWAN_APPSECRET = "f4c4699cfe86414395deca409171079f";

    @Autowired
    private RuleEngineCoreService ruleEngineCoreService;

    @Autowired
    private UserInfoClient userInfoClient;

    @Autowired
    private MiniestClient miniestClient;

    /**
     * 转换公屏发言消息, 推送到极小包
     *
     * 文档: https://doc.yy.com/pages/viewpage.action?pageId=169427289
     *
     * @param consumerRecordList
     */
    public void onChannelMessageEvents(List<ConsumerRecord<String, String>> consumerRecordList) {

        if (!Const.GEPM.getParamValueToBoolean("channel_message_to_mini_sdk", true)) {
            return;
        }

        //转换
        List<JSONObject> msgList = new ArrayList<>();
        consumerRecordList.forEach(consumerRecord -> {
            ChannelMessageEvent event = JSON.parseObject(consumerRecord.value(), ChannelMessageEvent.class);
            //语音房模版ID
            String templateId = "33554530";
            if (!templateId.equals(event.getTemplateId())) {
                return;
            }
            //提取xml
            xmlChat(event);
            //过滤飞机票/表情
            filterChat(event);
            try {
                Map<String, Object> params = new HashMap<>();
                params.put("event", event);
                ScriptEvalResult<String> evalResult = ruleEngineCoreService.executeRule(RuleApp.ZHUIWAN, 80003L, params);
                if (evalResult.getCode() == 0 && StringUtils.hasLength(evalResult.getData())) {
                    JSONObject msg = JSON.parseObject(evalResult.getData());
                    if (msg != null) {
                        msgList.add(msg);
                    }
                }
            } catch (RuleException e) {
                log.info("onChannelMessageEvents code:{} err:{}", e.getCode(), e.getMessage());
            } catch (Exception e) {
                log.error("executeRule err:{}", e.getMessage(), e);
            }
        });
        if (msgList.isEmpty()) {
            return;
        }

        //批量设置昵称
        List<String> uidList = msgList.stream().map(message -> message.getString("uid")).distinct().collect(Collectors.toList());
        JSONObject userNickExtMap = userInfoClient.batchGetUserNickExt(uidList);
        msgList.forEach(message -> {
            JSONObject nickExt = userNickExtMap.getJSONObject(message.getString("uid"));
            message.getJSONObject("msg_detail").put("nick", nickExt);
        });

        //推送
        List<TextChatMsg> list = msgList.stream().map(msg -> {
            TextChatMsg textChatMsg = new TextChatMsg();
            textChatMsg.setTopsid(msg.getLongValue("topsid"));
            textChatMsg.setSubsid(msg.getLongValue("subsid"));
            textChatMsg.setTemplateid(msg.getLongValue("templateId"));
            textChatMsg.setUid(msg.getLongValue("uid"));
            textChatMsg.setMsg_send_time(msg.getLongValue("msg_send_time"));
            String msgDetail = msg.getJSONObject("msg_detail").toJSONString();
            textChatMsg.setMsg_detail(msgDetail.getBytes(StandardCharsets.UTF_8));
            return textChatMsg;
        }).collect(Collectors.toList());
        miniestClient.historyTextChatWrite(list);

    }

    private void filterChat(ChannelMessageEvent event) {
        event.setChat(filter(event.getChat()));
    }

    private void xmlChat(ChannelMessageEvent event) {
        try {
            if (event.getChat().startsWith("<?xml")) {
                Document document = XmlUtil.getDocument(event.getChat());
                NodeList txt = document.getElementsByTagName("txt");
                NamedNodeMap attributes = txt.item(0).getAttributes();
                String chat = attributes.getNamedItem("data").getNodeValue();
                event.setChat(chat);
            }
        } catch (Exception e) {
            log.warn("xmlChat err:{} chat:{}", e.getMessage(), event.getChat(), e);
        }
    }

    public static String getSignature(String secretKey, String timestamp, String nonce) {
        try {
            String algorithm = "HmacSHA256";
            Mac mac1 = Mac.getInstance(algorithm);
            mac1.init(new SecretKeySpec(secretKey.getBytes(), algorithm));
            byte[] result = mac1.doFinal(timestamp.getBytes());

            Mac mac2 = Mac.getInstance(algorithm);
            mac2.init(new SecretKeySpec(result, algorithm));
            result = mac2.doFinal(nonce.getBytes());

            Mac mac3 = Mac.getInstance(algorithm);
            mac3.init(new SecretKeySpec(result, algorithm));
            result = mac3.doFinal((timestamp + '/' + nonce).getBytes());

            return byteToHex(result);
        } catch (Exception e) {
            log.error("getSignature key:{} time:{} nonce:{} err:{}", secretKey, timestamp, nonce, e.getMessage(), e);
        }
        return "";
    }

    private static String byteToHex(byte[] b) {
        StringBuilder hs = new StringBuilder();
        for (int n = 0; n < b.length; n++) {
            String s = Integer.toHexString(b[n] & 0XFF);
            if (s.length() == 1) {
                hs.append('0');
            }
            hs.append(s);
        }
        return hs.toString();
    }

    public static final Pattern PATTERN_1 = Pattern.compile("(((?i)yy)://(\\d+))", Pattern.CASE_INSENSITIVE);
    public static final Pattern PATTERN_2 = Pattern.compile("((((?i)yy)://pd-)((\\[([^\\].]+)\\])|(\\w+))(/\\[([^\\]]+)\\])?)", Pattern.CASE_INSENSITIVE);
    public static final Pattern PATTERN_3 = Pattern.compile("(/\\{wx)|(/\\{dx)|(/\\{tp)|(/\\{jy)|(/\\{pz)|(/\\{fn)|(/\\{ng)|(/\\{hk)|(/\\{kz)|(/\\{ot)|(/\\{se)|(/\\{tx)|(/\\{ka)|(/\\{by)|(/\\{am)|(/\\{kun)|(/\\{hp)|(/\\{lh)|(/\\{kx)|(/\\{cy)|(/\\{ll)|(/\\{fd)|(/\\{yw)|(/\\{xu)|(/\\{yun)|(/\\{zs)|(/\\{kl)|(/\\{qd)|(/\\{88)|(/\\{dy)|(/\\{zt)|(/\\{bz)|(/\\{yb)|(/\\{dai)|(/\\{sj)|(/\\{hx)|(/\\{gz)|(/\\{kb)|(/\\{kel)|(/\\{qq)|(/\\{wq)|(/\\{yx)|(/\\{zk)|(/\\{bs)|(/\\{bq)|(/\\{ok)|(/\\{zan)|(/\\{ruo)|(/\\{ws)|(/\\{sl)|(/\\{mg)|(/\\{kw)|(/\\{wen)|(/\\{xd)|(/\\{xs)|(/\\{lw)|(/\\{sd)|(/\\{zd)|(/\\{dao)|(/\\{cc)");


    public static final String filter(String chat) {
        String result = PATTERN_1.matcher(chat).replaceAll("");
        result = PATTERN_2.matcher(result).replaceAll("");
        result = PATTERN_3.matcher(result).replaceAll("");
        //log.info("chatFilter chat:{} result:{}", chat, result);
        return result;
    }

    public static void main(String[] args) throws Exception {
        String s = "yy://pd-[sid=565&subid=2786368476]";
        System.out.println(PATTERN_1.matcher(s).find());
        System.out.println(PATTERN_2.matcher(s).find());
        String b = "/{lw/{mg不是吧/{dx笑死/{wx/{wx";
        Matcher matcher = PATTERN_3.matcher(b);
        System.out.println(matcher.replaceAll(""));
    }

}
