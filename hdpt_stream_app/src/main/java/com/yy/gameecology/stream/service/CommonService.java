package com.yy.gameecology.stream.service;

import com.yy.gameecology.cache.annotation.LocalCache;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.stream.client.WebdbSinfoClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> 2019/8/26
 */
@Component
public class CommonService {

    @Autowired
    private WebdbSinfoClient webdbSinfoClient;

    @LocalCache(ttl = 300)
    public long getSid(long asid) {
        Map<String, String> chanelInfo = webdbSinfoClient.getChannelInfoByAsId(asid);
        return Convert.toLong(chanelInfo.get("sid"), asid);
    }

}
