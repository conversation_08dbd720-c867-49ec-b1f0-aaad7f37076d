package com.yy.gameecology.stream.service.stream;

import com.yy.gameecology.common.db.mapper.gameecology.ReRuleEvalRecordMapper;
import com.yy.gameecology.common.db.model.gameecology.ReRuleEvalRecord;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.ShutdownHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 2021/4/25
 */
@Service
public class RuleEvalRecordService {

    private static final Logger log = LoggerFactory.getLogger(RuleEvalRecordService.class);

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ReRuleEvalRecordMapper reRuleEvalRecordMapper;

    @Async
    public void addEvalRecord(Long appId, Long ruleId, Map<String, Object> params, ScriptEvalResult result) {
        try {
            ReRuleEvalRecord reRuleEvalRecord = new ReRuleEvalRecord();
            reRuleEvalRecord.setAppId(appId);
            reRuleEvalRecord.setRuleId(ruleId);
            reRuleEvalRecord.setEvalResult(result.getCode());
            reRuleEvalRecord.setEvalMsg(subStr(result.getMsg()));
            reRuleEvalRecord.setEvalParam(subStr(params));
            reRuleEvalRecord.setEvalReturn(subStr(result.getData()));
            reRuleEvalRecord.setEvalTime(new Date());
            reRuleEvalRecordMapper.insert(reRuleEvalRecord);
        } catch (Exception e) {
            log.warn("addEvalRecord err:{}", e.getMessage(), e);
        }
    }

    private String subStr(Object obj) {
        if (obj == null) {
            return null;
        }
        String str = String.valueOf(obj);
        return str.substring(0, Math.min(str.length(), 100));
    }

    @Scheduled(cron = "0 0 10 * * ?")
    public void createTable() {
        if (ShutdownHolder.isShuttingDown()) {
            log.info("shutdown createTable scheduled");
            return;
        }
        if (stringRedisTemplate.opsForValue().setIfAbsent("create_table:re_rule_eval_record", "1", 1, TimeUnit.HOURS)) {
            String nextMonth = LocalDate.now().plusMonths(1).format(DateUtil.yyyyMM);
            reRuleEvalRecordMapper.createTable(nextMonth);
        }
    }

}
