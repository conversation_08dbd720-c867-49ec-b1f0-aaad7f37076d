package com.yy.gameecology.stream.service.stream.visual;

import java.util.List;

/**
 * <AUTHOR> 2021/6/2
 */
public class BoolNode implements Node {

    private Condition conditions;

    private String yesNode;

    private String noNode;

    public String getYesNode() {
        return yesNode;
    }

    public void setYesNode(String yesNode) {
        this.yesNode = yesNode;
    }

    public String getNoNode() {
        return noNode;
    }

    public void setNoNode(String noNode) {
        this.noNode = noNode;
    }

    public Condition getConditions() {
        return conditions;
    }

    public void setConditions(Condition conditions) {
        this.conditions = conditions;
    }

    public static class Condition {
        private String link;
        private List<Condition> items;
        private String param;
        private String logic;
        private String value;

        public String getLink() {
            return link;
        }

        public void setLink(String link) {
            this.link = link;
        }

        public List<Condition> getItems() {
            return items;
        }

        public void setItems(List<Condition> items) {
            this.items = items;
        }

        public String getParam() {
            return param;
        }

        public void setParam(String param) {
            this.param = param;
        }

        public String getLogic() {
            return logic;
        }

        public void setLogic(String logic) {
            this.logic = logic;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }
    }
}
