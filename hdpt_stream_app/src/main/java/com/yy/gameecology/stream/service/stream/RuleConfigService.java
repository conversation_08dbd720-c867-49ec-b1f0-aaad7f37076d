package com.yy.gameecology.stream.service.stream;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.cache.annotation.LocalCache;
import com.yy.gameecology.common.bean.vo.RuleConfig;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleParamMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleScriptMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleVersionMapper;
import com.yy.gameecology.common.db.model.gameecology.ReRule;
import com.yy.gameecology.common.db.model.gameecology.ReRuleParam;
import com.yy.gameecology.common.db.model.gameecology.ReRuleScript;
import com.yy.gameecology.common.db.model.gameecology.ReRuleVersion;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> 2021/7/7
 */
@Service
public class RuleConfigService {

    @Resource
    private ReRuleMapper reRuleMapper;

    @Resource
    private ReRuleParamMapper reRuleParamMapper;

    @Resource
    private ReRuleScriptMapper reRuleScriptMapper;

    @Resource
    private ReRuleVersionMapper reRuleVersionMapper;

    @LocalCache
    public ReRule getRule(Long ruleId) {
        return reRuleMapper.selectByPrimaryKey(ruleId);
    }

    @LocalCache
    public List<ReRuleParam> getRuleParam(Long ruleId) {
        ReRuleVersion ruleVersion = reRuleVersionMapper.selectEnabledVersion(ruleId);
        if (ruleVersion != null) {
            RuleConfig ruleConfig = JSON.parseObject(ruleVersion.getRuleConfig(), RuleConfig.class);
            return ruleConfig.getRuleParamList();
        }
        return reRuleParamMapper.selectByRuleId(ruleId);
    }

    @LocalCache
    public ReRuleScript getRuleScript(Long ruleId) {
        ReRuleVersion ruleVersion = reRuleVersionMapper.selectEnabledVersion(ruleId);
        if (ruleVersion != null) {
            RuleConfig ruleConfig = JSON.parseObject(ruleVersion.getRuleConfig(), RuleConfig.class);
            return ruleConfig.getRuleScript();
        }
        return reRuleScriptMapper.selectByRuleId(ruleId);
    }

}
