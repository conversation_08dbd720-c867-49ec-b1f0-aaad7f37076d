package com.yy.gameecology.stream.service.message;

import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.stream.bean.MessageEvent;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.Index;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * mongo为主库落地,从库异步同步,可靠性与redis差不多
 *
 * <AUTHOR> 2021/7/8
 */
@Component
public class MessageDao {

    @Autowired
    private MongoTemplate mongoTemplate;

    public String tableName(Class<?> clz) {
        return mongoTemplate.getCollectionName(clz);
    }

    public void saveMessage(MessageEvent<?> message, String tableName) {
        message.setStatus(MessageEvent.RECEIVE);
        mongoTemplate.insert(message, tableName);
    }

    public void batchSaveMessage(List<MessageEvent> messageList, String tableName) {
        mongoTemplate.insert(messageList, tableName);
    }

    public void doneMessage(String messageId, String tableName) {
        mongoTemplate.updateFirst(
                Query.query(Criteria.where("messageId").is(messageId)),
                Update.update("status", MessageEvent.DONE).currentDate("doneTime"),
                tableName);
    }

    public void retryMessage(String messageId, Date retryTime, String tableName) {
        mongoTemplate.updateFirst(
                Query.query(Criteria.where("messageId").is(messageId).and("status").ne(MessageEvent.DONE)),
                Update.update("status", MessageEvent.RETRY).inc("retryTimes", 1).set("retryTime", retryTime),
                tableName);
    }

    public List<MessageEvent> getRetryMessage(String tableName) {
        int maxRetryTimes = 10;
        int limit = SysEvHelper.isDeploy() ? 1000 : 100;
        List<MessageEvent> list = mongoTemplate.find(
                Query.query(Criteria.where("status").is(MessageEvent.RETRY).and("retryTimes").lte(maxRetryTimes)
                        .and("retryTime").lte(new Date())).limit(limit),
                MessageEvent.class, tableName);
        return list;
    }

    public void retryDeadMessage(String tableName) {
        Date deadTime = DateUtils.addSeconds(new Date(), -30);
        mongoTemplate.updateMulti(Query.query(Criteria.where("status").is(MessageEvent.RECEIVE).and("timestamp").lt(deadTime)),
                Update.update("status", MessageEvent.RETRY).set("retryTime", new Date()),
                tableName);
    }

    public void initTable(String tableName) {
        IndexOperations indexOperations = mongoTemplate.indexOps(tableName);
        indexOperations.ensureIndex(new Index().on("messageId", Sort.Direction.ASC).unique());
        indexOperations.ensureIndex(new Index().on("timestamp", Sort.Direction.ASC).expire(100, TimeUnit.DAYS));
        indexOperations.ensureIndex(new Index().on("status", Sort.Direction.ASC).on("retryTimes", Sort.Direction.ASC).on("retryTime", Sort.Direction.ASC));
    }
    
    public void deleteHistoryData(String tableName, int days) {
        Date deadTime = DateUtils.addDays(new Date(), -days);
        int limit = Const.GEPM.getParamValueToInt("kafka_message_delete_batch_size", 100000);
        mongoTemplate.remove(
                Query.query(Criteria.where("timestamp").lt(deadTime)).limit(limit),
                MessageEvent.class, tableName);
    }
}
