package com.yy.gameecology.stream.service.message.handler;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.TurnoverRechargeEvent;
import com.yy.gameecology.stream.bean.ZhuiWanRechargeEvent;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.ActTurnoverService;
import com.yy.gameecology.stream.service.ActZhuiwanService;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
* @author: yuli<PERSON><PERSON>hu
* @date: 2023.02.24
* @description:
*/
@Component
public class TurnoverRechargeHandler extends KafkaMessageHandlerSupport<TurnoverRechargeEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(TurnoverRechargeEvent message) {
        log.info("doHandleMessage TurnoverRechargeEvent: event:{}", JSON.toJSONString(message));
        for (ActTurnoverService actTurnoverService : actServiceManager.getActTurnoverServices()) {
            actTurnoverService.onRechargeEvent(message);
        }
        eventRuleStreamService.executeEventRule(message, EventID.TURNOVER_RECHARGE_EVENT.getEventId());
    }
}
