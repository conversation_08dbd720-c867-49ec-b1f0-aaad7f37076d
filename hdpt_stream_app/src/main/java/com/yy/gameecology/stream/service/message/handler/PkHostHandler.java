package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.PkHostScoreEvent;
import com.yy.gameecology.stream.service.ActPkService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2021/7/12
 */
@Service
public class PkHostHandler extends KafkaMessageHandlerSupport<PkHostScoreEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(PkHostScoreEvent message) {
        for (ActPkService actPkService : actServiceManager.getActPkServiceList()) {
            actPkService.onHostScoreEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.PK_HOST_SCORE_EVENT.getEventId());
    }
}
