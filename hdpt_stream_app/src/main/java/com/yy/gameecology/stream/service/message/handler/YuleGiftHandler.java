package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.yule.YuleGiftEvent;
import com.yy.gameecology.stream.bean.yule.YulePresent;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 2022/2/14
 */
@Component
public class YuleGiftHandler extends KafkaMessageHandlerSupport<YuleGiftEvent> {
    @Override
    protected int getExecutorCoreSize() {
        return 100;
    }

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(YuleGiftEvent message) {
        YulePresent yulePresent = YulePresent.buildFromGift(message);
        eventRuleStreamService.executeEventRule(yulePresent, EventID.YULE_GIFT_EVENT.getEventId());
    }
    
    @Override
    protected boolean messagePersistent() {
        return false;
    }
}
