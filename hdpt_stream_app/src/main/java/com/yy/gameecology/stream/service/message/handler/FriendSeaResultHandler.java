package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.SeaResultEvent;
import com.yy.gameecology.stream.service.ActFriendService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 海底秘境
 *
 * <AUTHOR> 2024/01/11
 */
@Service
public class FriendSeaResultHandler extends KafkaMessageHandlerSupport<SeaResultEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected boolean checkMessage(SeaResultEvent message) {
        return message != null && !CollectionUtils.isEmpty(message.getRecvList());
    }

    @Override
    protected void doHandleMessage(SeaResultEvent message) {
        for (ActFriendService actFriendService : actServiceManager.getActFriendServiceList()) {
            actFriendService.onSeaResultEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.SEA_RESULT_EVENT.getEventId());
    }
}
