package com.yy.gameecology.stream.service.stream;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleTestCaseMapper;
import com.yy.gameecology.common.db.model.gameecology.ReRule;
import com.yy.gameecology.common.db.model.gameecology.ReRuleParam;
import com.yy.gameecology.common.db.model.gameecology.ReRuleScript;
import com.yy.gameecology.common.db.model.gameecology.ReRuleTestCase;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.exception.RuleException;
import com.yy.gameecology.stream.service.stream.script.ParamSource;
import com.yy.gameecology.stream.service.stream.script.RuleScriptHandler;
import com.yy.gameecology.stream.service.stream.script.RuleType;
import com.yy.gameecology.stream.service.stream.script.context.TextLogAppender;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 2021/4/23
 */
@Service
public class RuleEngineCoreService {
    
    private static final Logger log = LoggerFactory.getLogger(RuleEngineCoreService.class);

    @Autowired
    private RuleConfigService ruleConfigService;

    @Autowired
    private RuleEvalRecordService ruleEvalRecordService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ReRuleMapper reRuleMapper;

    @Resource
    private ReRuleTestCaseMapper reRuleTestCaseMapper;

    /**
     * 执行规则
     *
     * @param ruleId
     * @param params
     * @return
     * @throws RuleException 规则执行异常
     */
    public <T> ScriptEvalResult<T> executeRule(Long appId, Long ruleId, Map<String, Object> params) throws RuleException {
        ReRule rule = ruleConfigService.getRule(ruleId);
        ReRuleScript ruleScript = ruleConfigService.getRuleScript(ruleId);
        List<ReRuleParam> ruleParamList = ruleConfigService.getRuleParam(ruleId);
        ScriptEvalResult checkResult = checkRuleConfig(rule, ruleScript);
        if (checkResult != null) {
            return checkResult;
        }

        return executeRule(rule, ruleScript, ruleParamList, params);
    }

    private ScriptEvalResult executeRule(ReRule rule, ReRuleScript ruleScript, List<ReRuleParam> ruleParamList, Map<String, Object> params) {
        RuleType ruleType = RuleType.valueOf(ruleScript.getRuleType().toUpperCase());
        RuleScriptHandler scriptHandler = RuleScriptHandler.MAP.get(ruleType);

        //执行
        ScriptEvalResult result = scriptHandler.eval(rule, ruleScript, ruleParamList, params);

        //log
        if (rule.getLogStatus()) {
            log.info("executeRule ruleId:{} params:{} result:{}", rule.getId(), params, result);
            //ruleEvalRecordService.addEvalRecord(rule.getAppId(), rule.getId(), params, result);
        }

        return result;
    }

    public <T> ScriptEvalResult<T> testRule(ReRule rule,  ReRuleScript ruleScript, List<ReRuleParam> paramList, Map<String, Object> params) {
        ScriptEvalResult result = null;
        TextLogAppender.TEST_LOG_THREAD_LOCAL.set(new ArrayList<>());
        try {
            convertEventParam(params, paramList);
            result = executeRule(rule, ruleScript, paramList, params);
        } catch (Throwable e) {
            result = ScriptEvalResult.fail(ScriptEvalResult.EVAL_FAIL, ExceptionUtils.getRootCauseMessage(e));
        } finally {
            if (result != null) {
                result.setTestLogList(TextLogAppender.TEST_LOG_THREAD_LOCAL.get());
            }
            TextLogAppender.TEST_LOG_THREAD_LOCAL.remove();
        }
        return result;
    }

    private void convertEventParam(Map<String, Object> params, List<ReRuleParam> ruleParamList) {
        for (ReRuleParam ruleParam : ruleParamList) {
            if (ruleParam.getParamSource() == ParamSource.EVENT.source) {
                String eventJson = JSON.toJSONString(params.get(ruleParam.getParamKey()));
                Class eventClz = EventID.findByEventId(ruleParam.getEventId());
                Object event = JSON.parseObject(eventJson, eventClz);
                params.put(ruleParam.getParamKey(), event);
            }
        }
    }

    private ScriptEvalResult checkRuleConfig(ReRule rule, ReRuleScript ruleScript) {
        if (rule == null || !rule.getRuleStatus()) {
            log.info("executeRule rule not found. ruleId:{}", rule.getId());
            return ScriptEvalResult.fail(ScriptEvalResult.NOT_FOUND, "规则不存在");
        }
        if (ruleScript == null) {
            log.info("executeRule script not found. ruleId:{}", rule.getId());
            return ScriptEvalResult.fail(ScriptEvalResult.NOT_FOUND, "脚本不存在");
        }
        //限流
        if (rule.getLimitSecond() != null) {
            String key = "limit_second:" + rule.getId() + ":" + DateUtil.getSeconds();
            Long times = stringRedisTemplate.opsForValue().increment(key, 1);
            if (times == 1) {
                stringRedisTemplate.expire(key, 1, TimeUnit.MINUTES);
            }
            if (times > rule.getLimitSecond()) {
                log.info("checkRuleConfig limit. ruleId:{} limit:{} times:{}", rule.getId(), rule.getLimitSecond(), times);
                throw new RuleException(ScriptEvalResult.LIMIT, "限流(秒): " + rule.getLimitSecond());
            }
        }
        return null;
    }

    @EventListener(ContextRefreshedEvent.class)
    public void initScript() {
        if (SysEvHelper.isLocal()) {
            return;
        }
        Clock clock = new Clock();
        List<ReRule> ruleList = reRuleMapper.selectAllActive();
        ruleList.parallelStream().forEach(rule -> {
            try {
                ReRuleScript ruleScript = ruleConfigService.getRuleScript(rule.getId());
                List<ReRuleParam> ruleParamList = ruleConfigService.getRuleParam(rule.getId());
                ReRuleTestCase testCase = reRuleTestCaseMapper.selectByRuleId(rule.getId());

                RuleType ruleType = RuleType.valueOf(ruleScript.getRuleType().toUpperCase());
                RuleScriptHandler scriptHandler = RuleScriptHandler.MAP.get(ruleType);

                if (testCase != null && testCase.getStatus()) {
                    scriptHandler.eval(rule, ruleScript, ruleParamList, JSON.parseObject(testCase.getTestParam()));
                } else {
                    scriptHandler.initScript(rule, ruleScript, ruleParamList, Collections.emptyMap());
                }
            } catch (Exception e) {
                log.warn("initScript rule:{} err:{}", rule.getId(), e.getMessage(), e);
            }
        });
        log.info("initScript {}", clock.tag());
    }

}
