package com.yy.gameecology.stream.service;

import com.google.common.collect.Lists;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge.TTingBind;
import com.yy.gameecology.common.protocol.thrift.hdztranking.BusiId;
import com.yy.gameecology.common.protocol.thrift.turnover.TAppId;
import com.yy.gameecology.common.protocol.thrift.turnover.TContract;
import com.yy.gameecology.common.protocol.thrift.turnover_family.TRoomContract;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.stream.client.FtsBaseInfoBridgeClient;
import com.yy.gameecology.stream.client.TurnoverServiceClient;
import com.yy.gameecology.stream.service.stream.script.context.TurnoverFamilyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR> 2021/4/19
 */
@Service
public class SignedService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private TurnoverServiceClient turnoverServiceClient;

    @Autowired
    private TurnoverFamilyService turnoverFamilyService;

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;
    @Autowired
    private InnerService innerService;

    /**
     * 活动期间, 以第一次送礼签约频道为准
     *
     * @param uid                  主播uid
     * @param actId                活动id
     * @param busId                业务id
     * @param currentSignedChannel 当前签约频道
     * @return
     */
    public long queryActSignedChannel(long uid, long actId, long busId, long currentSignedChannel) {
        String key = Const.addActivityPrefix(actId, "signed_channel_" + busId);
        long cache = Convert.toLong(stringRedisTemplate.opsForHash().get(key, String.valueOf(uid)));
        if (cache > 0) {
            return cache;
        }
        if (currentSignedChannel == 0) {
            if (busId == BusiId.YUE_ZHAN.getValue()) {
                currentSignedChannel = getSignedChanelFromTurnover(uid, TAppId.VipPk);
            } else if (busId == BusiId.MAKE_FRIEND.getValue()) {
                currentSignedChannel = getSignedChanelFromTurnover(uid, TAppId.Dating);
            } else if (busId == BusiId.GAME_BABY.getValue()) {
                //TODO: 宝贝/陪玩签约信息, 目前流水自带
                currentSignedChannel = innerService.getGameBabySignedSid(actId, busId, uid);
            } else if (busId == BusiId.PEI_WAN.getValue()) {

            }
        }
        //特殊逻辑
        if (busId == BusiId.MAKE_FRIEND.getValue()) {
            if (currentSignedChannel == 26150465 || currentSignedChannel == 78997865) {
                currentSignedChannel = 69595230;
            }
        }
        if (currentSignedChannel > 0) {
            if (stringRedisTemplate.opsForHash().putIfAbsent(key, String.valueOf(uid), String.valueOf(currentSignedChannel))) {
                return currentSignedChannel;
            }
        }
        return Convert.toLong(stringRedisTemplate.opsForHash().get(key, String.valueOf(uid)));
    }


    public long queryJyRoomContract(long actId, long sid, long ssid) {
        String key = Const.addActivityPrefix(actId, "jy_signed_manager_channel_" + BusiId.MAKE_FRIEND.getValue());
        long cache = Convert.toLong(stringRedisTemplate.opsForHash().get(key, String.valueOf(ssid)));
        if (cache > 0) {
            return cache;
        }
        Map<Long, TRoomContract> roomContractMap = turnoverFamilyService.queryRoomContract();
        if(roomContractMap == null || roomContractMap.isEmpty()) {
            return sid;
        }
        long roomContractSid = 0;
        if(roomContractMap.containsKey(ssid)) {
            TRoomContract tRoomContract = roomContractMap.get(ssid);
            if(tRoomContract.getSid() == 0) {
                roomContractSid = sid;
            } else {
                roomContractSid = tRoomContract.getSid();
            }
            stringRedisTemplate.opsForHash().putIfAbsent(key, String.valueOf(ssid), String.valueOf(roomContractSid));
            return roomContractSid;
        }
        return sid;
    }

    // 从营收获取签约的公会id
    public long getSignedChanelFromTurnover(long uid, TAppId appId) {
        TContract tContract = turnoverServiceClient.queryContractByAnchor(uid, appId);
        if (tContract != null && tContract.getLiveUid() == uid) {
            return tContract.getSid();
        }

        return 0;
    }


    /**
     * 签约团，目前只有陪玩有
     * 活动期间, 以第一次送礼签约频道为准
     *
     * @param uid               主播uid
     * @param actId             活动id
     * @param busId             业务id
     * @param currentSignedDept 当前签约团
     * @return
     */
    public long queryActSignedDeptId(long uid, long actId, long busId, long currentSignedDept) {
        //目前除了陪玩，其他业务没有签约团
        if (busId != BusiId.PEI_WAN.getValue()) {
            return 0L;
        }
        String key = Const.addActivityPrefix(actId, "signed_dept_" + busId);
        long cache = Convert.toLong(stringRedisTemplate.opsForHash().get(key, String.valueOf(uid)));
        if (cache > 0) {
            return cache;
        }

        if (currentSignedDept > 0) {
            if (stringRedisTemplate.opsForHash().putIfAbsent(key, String.valueOf(uid), String.valueOf(currentSignedDept))) {
                return currentSignedDept;
            }
        }
        return Convert.toLong(stringRedisTemplate.opsForHash().get(key, String.valueOf(uid)));
    }

    /**
     * 查询厅信息,
     * 活动期间,以第一次为准
     *
     * @param uid
     * @return
     */
    public Long queryTingBindByUid(long actId, long busi, Long uid) {
        String key = Const.addActivityPrefix(actId, "signed_ting_" + busi);
        long cache = Convert.toLong(stringRedisTemplate.opsForHash().get(key, String.valueOf(uid)));
        if (cache != 0) {
            return cache;
        }
        long tingId = 0L;
        Map<Long, TTingBind> tTingBindMap = ftsBaseInfoBridgeClient.queryTingBindByUids(Lists.newArrayList(uid));
        if (tTingBindMap != null && tTingBindMap.get(uid) != null) {
            tingId = tTingBindMap.get(uid).getTingMgrUid();
        }

        if (tingId > 0) {
            if (stringRedisTemplate.opsForHash().putIfAbsent(key, String.valueOf(uid), String.valueOf(tingId))) {
                return tingId;
            } else {
                tingId = Convert.toLong(stringRedisTemplate.opsForHash().get(key, String.valueOf(uid)));
            }
        }
        return tingId;
    }


    /**
     * 查询主播对应的厅管,
     * 活动期间,以第一次为准
     *
     * @param uid
     * @return
     */
    public Long queryTingBindMgrUidByUid(long actId, long busi, Long uid) {
        String key = Const.addActivityPrefix(actId, "signed_ting_mgr_" + busi);
        long cache = Convert.toLong(stringRedisTemplate.opsForHash().get(key, String.valueOf(uid)));
        if (cache != 0) {
            return cache;
        }
        long tingMgrUid = 0L;
        Map<Long, TTingBind> tTingBindMap = ftsBaseInfoBridgeClient.queryTingBindByUids(Lists.newArrayList(uid));
        if (tTingBindMap != null && tTingBindMap.get(uid) != null) {
            tingMgrUid = tTingBindMap.get(uid).getTingMgrUid();
        }

        if (tingMgrUid > 0) {
            if (stringRedisTemplate.opsForHash().putIfAbsent(key, String.valueOf(uid), String.valueOf(tingMgrUid))) {
                return tingMgrUid;
            } else {
                tingMgrUid = Convert.toLong(stringRedisTemplate.opsForHash().get(key, String.valueOf(uid)));
            }
        }
        return tingMgrUid;
    }

}
