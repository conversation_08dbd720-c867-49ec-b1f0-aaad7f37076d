package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.ZhuiwanGiftComboEvent;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.ActZhuiwanService;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ZhuiwanGiftComboHandler extends KafkaMessageHandlerSupport<ZhuiwanGiftComboEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;


    @Override
    protected void doHandleMessage(ZhuiwanGiftComboEvent message) {
        for (ActZhuiwanService actZhuiwanService : actServiceManager.getActZhuiwanServiceList()) {
            actZhuiwanService.onGiftComboEvent(message);
        }
        eventRuleStreamService.executeEventRule(message, EventID.ZHUIWAN_GIFT_COMBO_EVENT.getEventId());
    }
}
