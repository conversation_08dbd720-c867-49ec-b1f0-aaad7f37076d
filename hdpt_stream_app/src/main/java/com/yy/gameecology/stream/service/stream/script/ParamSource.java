package com.yy.gameecology.stream.service.stream.script;

/**
 * <AUTHOR> 2021/8/11
 */
public enum ParamSource {

    EXTERNAL(0, "传入"),
    EXPRESSION(1, "表达式"),
    FUNCTION(2, "函数"),
    EVENT(3, "事件"),
    DEFAULT(4, "默认值"),
    ;

    ParamSource(int source, String desc) {
        this.source = source;
        this.desc = desc;
    }

    public int source;
    public String desc;

    public static ParamSource valueOfSource(int source) {
        for (ParamSource value : values()) {
            if (value.source == source) {
                return value;
            }
        }
        return null;
    }

}
