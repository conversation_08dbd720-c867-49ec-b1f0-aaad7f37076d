package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.OnlineTimeEvent;
import com.yy.gameecology.stream.service.ActFriendService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2021.12.23 16:59
 */
@Service
public class OnlineTimeHandler  extends KafkaMessageHandlerSupport<OnlineTimeEvent> {
    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected boolean messagePersistent() {
        return false;
    }

    @Override
    protected void doHandleMessage(OnlineTimeEvent message) {
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.ONLINE_TIME_EVENT.getEventId());
    }
}
