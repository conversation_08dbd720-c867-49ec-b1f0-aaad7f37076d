package com.yy.gameecology.stream.service;

import com.yy.gameecology.common.protocol.thrift.hdztranking.BusiId;
import com.yy.gameecology.stream.bean.TurnoverBabyGiftEvent;
import com.yy.gameecology.stream.bean.TurnoverRechargeEvent;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
* @author: yuli<PERSON><PERSON><PERSON>
* @date: 2023.02.24
* @description:
*/
public interface ActTurnoverService extends ActService {

    default void onRechargeEvent(TurnoverRechargeEvent event){}

    default void onGiftEvent(TurnoverBabyGiftEvent event){}

    @Override
    default long getBusId() {
        return BusiId.ZHUI_WAN.getValue();
    }

    ConcurrentMap<Long, ActTurnoverService> ACT_ID_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    default void initServiceMap() {
        Assert.isNull(ACT_ID_MAP.put(getActId(), this), "actService repeat! actId:{}" + getActId());
    }
}
