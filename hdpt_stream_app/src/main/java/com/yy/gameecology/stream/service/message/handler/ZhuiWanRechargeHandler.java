package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.ZhuiWanRechargeEvent;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.ActZhuiwanService;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/8/3
 */
@Component
public class ZhuiWanRechargeHandler extends KafkaMessageHandlerSupport<ZhuiWanRechargeEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(ZhuiWanRechargeEvent message) {
        for (ActZhuiwanService actZhuiwanService : actServiceManager.getActZhuiwanServiceList()) {
            actZhuiwanService.onRechargeEvent(message);
        }
        eventRuleStreamService.executeEventRule(message, EventID.ZHUIWAN_RECHARGE_EVENT.getEventId());
    }
}
