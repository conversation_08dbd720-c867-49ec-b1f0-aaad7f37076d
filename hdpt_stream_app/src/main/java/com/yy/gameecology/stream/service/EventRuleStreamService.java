package com.yy.gameecology.stream.service;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.bean.vo.RuleConfig;
import com.yy.gameecology.common.bean.vo.RuleEventInfo;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleParamMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleVersionMapper;
import com.yy.gameecology.common.db.model.gameecology.ReRule;
import com.yy.gameecology.common.db.model.gameecology.ReRuleParam;
import com.yy.gameecology.common.db.model.gameecology.ReRuleVersion;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.ShutdownHolder;
import com.yy.gameecology.stream.bean.Event;
import com.yy.gameecology.stream.service.stream.RuleEngineCoreService;
import com.yy.gameecology.stream.service.stream.script.ParamSource;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2021/7/14
 */
@Service
public class EventRuleStreamService {

    private static final Logger log = LoggerFactory.getLogger(EventRuleStreamService.class);

    @Autowired
    private RuleEngineCoreService ruleEngineCoreService;

    @Resource
    private ReRuleMapper reRuleMapper;

    @Resource
    private ReRuleVersionMapper reRuleVersionMapper;

    @Resource
    private ReRuleParamMapper reRuleParamMapper;

    @Autowired
    private HdztService hdztService;


    /**
     * 执行事件规则流
     */
    public void executeEventRule(Object event, int eventId) {
        List<RuleEventInfo> ruleList = getRuleListByEventId(eventId);
        if (!CollectionUtils.isEmpty(ruleList)) {
            for (RuleEventInfo rule : ruleList) {
                if (rule.getActId() != null && event instanceof Event) {
                    if (!hdztService.isRunning(rule.getActId(), ((Event) event).eventTime())) {
                        continue;
                    }
                }
                Map<String, Object> param = new HashMap<>();
                param.put(rule.getParamKey(), event);
                if (rule.getActId() != null) {
                    param.put("event_act_id", rule.getActId());
                }
                ruleEngineCoreService.executeRule(rule.getAppId(), rule.getRuleId(), param);
            }
        }
    }

    private List<RuleEventInfo> getRuleListByEventId(int eventId) {
        if (eventRuleListMap == null) {
            synchronized (this) {
                if (eventRuleListMap == null) {
                    refreshEventRuleList();
                }
            }
        }
        return eventRuleListMap.get(eventId);
    }

    private volatile Map<Integer, List<RuleEventInfo>> eventRuleListMap;

    @Scheduled(fixedDelay = 60000)
    public synchronized void refreshEventRuleList() {
        try {
            if (ShutdownHolder.isShuttingDown()) {
                log.info("shutdown refreshEventRuleList scheduled");
                return;
            }
            Clock clock = new Clock();
            Map<Integer, List<RuleEventInfo>> eventRuleListMap = new HashMap<>();
            List<ReRule> ruleList = reRuleMapper.selectAllActive();
            for (ReRule rule : ruleList) {
                ReRuleVersion ruleVersion = reRuleVersionMapper.selectEnabledVersion(rule.getId());
                List<ReRuleParam> ruleParamList;
                if (ruleVersion != null) {
                    RuleConfig ruleConfig = JSON.parseObject(ruleVersion.getRuleConfig(), RuleConfig.class);
                    ruleParamList = ruleConfig.getRuleParamList();
                } else {
                    ruleParamList = reRuleParamMapper.selectByRuleId(rule.getId());
                }
                for (ReRuleParam ruleParam : ruleParamList) {
                    if (ShutdownHolder.isShuttingDown()) {
                        log.info("shutdown refreshEventRuleList scheduled on {}", ruleParam);
                        return;
                    }
                    if (ruleParam.getParamSource() == ParamSource.EVENT.source) {
                        if (ruleParam.getEventActId() != null) {
                            if (!hdztService.eventInActTime(ruleParam.getEventActId(), DateUtils.MILLIS_PER_HOUR)) {
                                break;
                            }
                        }
                        eventRuleListMap.compute(ruleParam.getEventId(), (eventId, ruleEventInfos) -> {
                            if (ruleEventInfos == null) {
                                ruleEventInfos = new ArrayList<>();
                            }
                            RuleEventInfo ruleEventInfo = new RuleEventInfo();
                            ruleEventInfo.setRuleId(rule.getId());
                            ruleEventInfo.setAppId(rule.getAppId());
                            ruleEventInfo.setEventId(eventId);
                            ruleEventInfo.setActId(ruleParam.getEventActId());
                            ruleEventInfo.setParamKey(ruleParam.getParamKey());
                            ruleEventInfos.add(ruleEventInfo);
                            return ruleEventInfos;
                        });
                        break;
                    }
                }
            }
            this.eventRuleListMap = eventRuleListMap;
            log.info("refreshEventRuleList {}", clock.tag());
        } catch (Exception ex) {
            log.error("refreshEventRuleList error", ex);
        }
    }

}
