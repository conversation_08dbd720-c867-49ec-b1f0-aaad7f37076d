package com.yy.gameecology.stream.service;

import com.yy.gameecology.common.protocol.thrift.hdztranking.BusiId;
import com.yy.gameecology.stream.bean.*;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> 2021/4/19
 */
public interface ActFriendService extends ActService {

    void onGiftEvent(FriendGiftEvent friendGiftEvent);

    void onSealEvent(FriendSealEvent friendGiftEvent);

    default void onAirdropEvent(AirdropEvent airdropEvent) {}

    default void onAirdropHelpEvent(AirdropHelpEvent airdropHelpEvent) {}

    default void onPeachEvent(PeachEvent peachEvent) {}

    default void onComboEndEvent(FriendComboEndEvent friendComboEndEvent) {}

    default void onSeaResultEvent(SeaResultEvent seaResultEvent) {}

    default void onCupidResultEvent(CupidResultEvent cupidResultEvent) {}

    @Override
    default long getBusId() {
        return BusiId.MAKE_FRIEND.getValue();
    }

    default boolean isFromZhuiwan(int channel) {
        return channel == 77 || channel == 78;
    }

    ConcurrentMap<Long, ActFriendService> ACT_ID_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    default void initServiceMap() {
        Assert.isNull(ACT_ID_MAP.put(getActId(), this), "actService repeat! actId:{}" + getActId());
    }
}
