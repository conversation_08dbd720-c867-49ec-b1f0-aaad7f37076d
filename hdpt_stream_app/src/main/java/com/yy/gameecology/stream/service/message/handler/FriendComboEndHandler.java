package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.FriendComboEndEvent;
import com.yy.gameecology.stream.service.ActFriendService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2021/7/13
 */
@Service
public class FriendComboEndHandler extends KafkaMessageHandlerSupport<FriendComboEndEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(FriendComboEndEvent message) {
        for (ActFriendService actFriendService : actServiceManager.getActFriendServiceList()) {
            actFriendService.onComboEndEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.FRIEND_COMBO_END_EVENT.getEventId());
    }
}
