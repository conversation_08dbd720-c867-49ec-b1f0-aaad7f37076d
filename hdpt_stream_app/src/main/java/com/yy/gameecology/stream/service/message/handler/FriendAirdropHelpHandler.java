package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.AirdropHelpEvent;
import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.service.ActFriendService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2021/8/27
 */
@Service
public class FriendAirdropHelpHandler extends KafkaMessageHandlerSupport<AirdropHelpEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;


    @Override
    protected void doHandleMessage(AirdropHelpEvent message) {
        for (ActFriendService actFriendService : actServiceManager.getActFriendServiceList()) {
            actFriendService.onAirdropHelpEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.AIRDROP_HELP_EVENT.getEventId());
    }
}
