package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.ZhuiWanRoomHeartEvent;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.ActZhuiwanService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021/8/3
 */
@Component
public class Zhu<PERSON><PERSON>anRoomHeartHandler extends KafkaMessageHandlerSupport<ZhuiWanRoomHeartEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Override
    protected boolean messagePersistent() {
        return false;
    }

    @Override
    protected void doHandleMessage(ZhuiWanRoomHeartEvent message) {
        for (ActZhuiwanService actZhuiwanService : actServiceManager.getActZhuiwanServiceList()) {
            actZhuiwanService.onRoomHeartEvent(message);
        }
    }
}
