package com.yy.gameecology.stream.service;

import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.common.utils.ShutdownHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR> 2019/8/29
 */
@Component
@DependsOn("springBeanAwareFactory")
public class CacheService {

    private static final Logger log = LoggerFactory.getLogger(CacheService.class);

    private long count = 0;

    @PostConstruct
    public void init() {
        refresh();
    }

    @Scheduled(cron = "0/20 * * * * ?")
    public void refresh() {
        if (ShutdownHolder.isShuttingDown()) {
            log.info("shutdown CacheService.refresh scheduled");
            return;
        }
        Clock clock = new Clock();
        try {
            count++;
            // 让测试环境加快更新， 生产环境为20秒 x 3 = 60秒 = 1分钟
            if (SysEvHelper.isDeploy() && (count % 3 != 1)) {
                return;
            }

            try {
                Const.GEPM.reloadAll();
            } catch (Exception e) {
                e.printStackTrace();
            }

            log.info("refresh ok@counter:{} {}", count, clock.tag());
        } catch (Throwable t) {
            log.error("refresh exception@counter:{}, err:{} {}", count, t.getMessage(), clock.tag());
        }
    }

}
