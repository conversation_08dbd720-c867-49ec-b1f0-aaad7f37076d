package com.yy.gameecology.stream.service.message;

import com.yy.gameecology.common.consts.Const;
import org.apache.kafka.clients.consumer.ConsumerRebalanceListener;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.concurrent.*;

/**
 * 消费限流, 避免压垮可靠缓冲层
 *
 * <AUTHOR> 2021/8/2
 */
public class ConsumerBufferListener implements ConsumerRebalanceListener {

    private static final Logger log = LoggerFactory.getLogger(ConsumerBufferListener.class);

    private final ConcurrentMap<TopicPartition, BlockingQueue<Long>> bufferMap = new ConcurrentHashMap<>();

    @Override
    public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
        log.info("partitions revoked: " + partitions);
    }

    @Override
    public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
        log.info("partitions assigned: " + partitions);
        partitions.forEach(partition -> {
            int capacity = Const.GEPM.getParamValueToInt("topic_partition_buffer_capacity:" + partition.topic(), 10000);
            BlockingQueue<Long> old = bufferMap.put(partition, new LinkedBlockingQueue<>(capacity));
            log.info("onPartitionsAssigned partition:{} old:{}", partition, old);
        });
    }

    public void enqueue(ConsumerRecord consumerRecord) {
        TopicPartition partition = new TopicPartition(consumerRecord.topic(), consumerRecord.partition());
        BlockingQueue<Long> blockingQueue = bufferMap.get(partition);
        //选择了offer, 阻塞超时可告警
        if (blockingQueue != null) {
            try {
                while (true) {
                    boolean offer = blockingQueue.offer(consumerRecord.offset(), 1, TimeUnit.MINUTES);
                    log.info("enqueue partition:{} offset:{} offer:{}", partition, consumerRecord.offset(), offer);
                    if (offer) {
                        break;
                    }
                    log.error("enqueue timeout. partition:{}", partition);
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public void dequeue(ConsumerRecord consumerRecord) {
        TopicPartition partition = new TopicPartition(consumerRecord.topic(), consumerRecord.partition());
        BlockingQueue<Long> blockingQueue = bufferMap.get(partition);
        //remove需要全遍历, 选择了性能更好的poll
        if (blockingQueue != null) {
            try {
                while (true) {
                    Long offset = blockingQueue.poll(1, TimeUnit.MINUTES);
                    log.info("dequeue partition:{} offset:{} poll:{}", partition, consumerRecord.offset(), offset);
                    if (offset != null) {
                        break;
                    }
                    log.error("dequeue timeout. partition:{}", partition);
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
