package com.yy.gameecology.stream.service;

import com.yy.gameecology.common.protocol.thrift.hdztranking.BusiId;
import com.yy.gameecology.stream.bean.PkGiftEvent;
import com.yy.gameecology.stream.bean.PkHostScoreEvent;
import com.yy.gameecology.stream.bean.PkPeachEvent;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> 2021/4/19
 */
public interface ActPkService extends ActService {

    void onGiftEvent(PkGiftEvent pkGiftEvent);

    void onHostScoreEvent(PkHostScoreEvent pkHostScoreEvent);

    /**
     * 约战桃花签事件
     *
     * @param pkPeachEvent
     */
    default void onPkPeachEvent(PkPeachEvent pkPeachEvent) {}


    @Override
    default long getBusId() {
        return BusiId.YUE_ZHAN.getValue();
    }

    default boolean isFromZhuiwan(int channel) {
        return channel == 77 || channel == 78;
    }

    ConcurrentMap<Long, ActPkService> ACT_ID_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    default void initServiceMap() {
        Assert.isNull(ACT_ID_MAP.put(getActId(), this), "actService repeat! actId:{}" + getActId());
    }
}
