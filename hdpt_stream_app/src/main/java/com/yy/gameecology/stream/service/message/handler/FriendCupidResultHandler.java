package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.CupidResultEvent;
import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.service.ActFriendService;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 交友丘比特乐园
 *
 * @Author: CXZ
 * @Desciption:
 * @Date: 2024/7/15 16:20
 * @Modified:
 */
@Service
public class FriendCupidResultHandler extends KafkaMessageHandlerSupport<CupidResultEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected boolean checkMessage(CupidResultEvent message) {
        return message != null && !CollectionUtils.isEmpty(message.getRecvList());
    }

    @Override
    protected void doHandleMessage(CupidResultEvent message) {
        for (ActFriendService actFriendService : actServiceManager.getActFriendServiceList()) {
            actFriendService.onCupidResultEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.CUPID_RESULT_EVENT.getEventId());
    }
}
