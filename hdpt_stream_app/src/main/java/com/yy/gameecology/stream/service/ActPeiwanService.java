package com.yy.gameecology.stream.service;

import com.yy.gameecology.common.protocol.thrift.hdztranking.BusiId;
import com.yy.gameecology.stream.bean.PeiwanOrderEvent;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> 2021/4/21
 */
public interface ActPeiwanService extends ActService {

    void onOrderEvent(PeiwanOrderEvent peiwanOrderEvent);

    @Override
    default long getBusId() {
        return BusiId.PEI_WAN.getValue();
    }

    default boolean isFromZhuiwan(String source) {
        return "zhuiwan".equals(source);
    }

    ConcurrentMap<Long, ActPeiwanService> ACT_ID_MAP = new ConcurrentHashMap<>();

    @PostConstruct
    default void initServiceMap() {
        Assert.isNull(ACT_ID_MAP.put(getActId(), this), "actService repeat! actId:{}" + getActId());
    }
}
