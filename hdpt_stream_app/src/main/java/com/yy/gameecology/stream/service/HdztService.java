package com.yy.gameecology.stream.service;

import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.yy.gameecology.common.protocol.thrift.hdztranking.ActivityInfo;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.ShutdownHolder;
import com.yy.gameecology.stream.client.HdztRankingThriftClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> 2021/4/20
 */
@Service
@Slf4j
public class HdztService {

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    private LoadingCache<Long, Optional<ActivityInfo>> activityInfoCache;

    private LoadingCache<Long, Optional<Boolean>> activityGrepCache;

    @PostConstruct
    public void init() {
        activityInfoCache = CacheBuilder.newBuilder()
                .refreshAfterWrite(1, TimeUnit.MINUTES)
                .build(new CacheLoader<Long, Optional<ActivityInfo>>() {
                    @Override
                    public Optional<ActivityInfo> load(Long key) throws Exception {
                        try {
                            return Optional.ofNullable(hdztRankingThriftClient.queryActivityInfo(key));
                        } catch (Exception e) {
                            if (ShutdownHolder.isShuttingDown()) {
                                log.info("activityInfoCache load when shutdown {} error:{}", key, e.getMessage());
                                return Optional.empty();
                            }
                            throw e;
                        }
                    }
                });

        activityGrepCache = CacheBuilder.newBuilder()
                .refreshAfterWrite(1, TimeUnit.MINUTES)
                .build(new CacheLoader<Long, Optional<Boolean>>() {
                    @Override
                    public Optional<Boolean> load(Long key) throws Exception {
                        try {
                            return Optional.ofNullable(hdztRankingThriftClient.queryGreyStatus(key));
                        } catch (Exception e) {
                            if (ShutdownHolder.isShuttingDown()) {
                                log.info("activityGrepCache load when shutdown {} error:{}", key, e.getMessage());
                                return Optional.empty();
                            }
                            throw e;
                        }
                    }
                });


    }


    public boolean isRunning(long actId, Date time) {
        Optional<ActivityInfo> optional = activityInfoCache.getUnchecked(actId);
        if (optional.isPresent()) {
            ActivityInfo activityInfo = optional.get();
            long now = DateUtil.getTimeStripMilliseconds(now(actId, time));
            return now >= activityInfo.getBeginTime() && now <= activityInfo.getEndTime();
        }
        return false;
    }

    public Date now(long actId, Date time) {
        if (!SysEvHelper.isDeploy() || isGrey(actId)) {
            return hdztRankingThriftClient.queryNow(actId);
        }
        return time;
    }

    /**
     * 活动是否是灰度，默认是灰度
     *
     * @param actId
     * @return
     */
    public boolean isGrey(long actId) {
        return activityGrepCache.getUnchecked(actId).orElse(true);
    }

    public Optional<ActivityInfo> getActInfo(long actId) {
        return activityInfoCache.getUnchecked(actId);
    }

    /**
     * 事件在活动时间内, 传入偏移量, 即活动开始前/结束后多少毫秒内都算
     *
     * @param actId
     * @param offset 活动前后时间迁移量(毫秒)
     * @return
     */
    public boolean eventInActTime(long actId, long offset) {
        Optional<ActivityInfo> actInfo = getActInfo(actId);
        if (!actInfo.isPresent()) {
            return false;
        }
        Date now = now(actId, new Date());
        long currentTime = now.getTime();
        long beginTime = actInfo.get().getBeginTime();
        long endTime = actInfo.get().getEndTime();
        return currentTime > beginTime - offset && currentTime < endTime + offset;
    }
}
