package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.ZhuiwanLoginEvent;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.ActZhuiwanService;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import com.yy.gameecology.stream.worker.web.TestController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2021/7/13
 */
@Service
public class ZhuiwanLoginHandler extends KafkaMessageHandlerSupport<ZhuiwanLoginEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected boolean messagePersistent() {
        return false;
    }

    @Override
    protected void doHandleMessage(ZhuiwanLoginEvent message) {
        for (ActZhuiwanService actZhuiwanService : actServiceManager.getActZhuiwanServiceList()) {
            actZhuiwanService.onLoginEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.ZHUIWAN_LOGIN_EVENT.getEventId());

        if (TestController.consumeCompareLatch.getCount() > 0) {
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            TestController.consumeCompareLatch.countDown();
        }
    }
}
