package com.yy.gameecology.stream.service;

import com.yy.gameecology.common.protocol.thrift.zhuiwan_blacklist.BlackListItem;
import com.yy.gameecology.stream.client.ZhuiwanBlacklistClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ZhuiwanBlacklistService {

    private static Optional<Set<Long>> BLACKLIST_ROOM_IDS = Optional.empty();

    @Autowired
    private ZhuiwanBlacklistClient zhuiwanBlacklistClient;

    @Scheduled(initialDelay = 3000, fixedDelay = 30000)
    public void freshBlacklistRoomIds() {
        var blacklist = zhuiwanBlacklistClient.getRoomBlacklist();
        if (blacklist != null) {
            BLACKLIST_ROOM_IDS = Optional.of(blacklist.stream().map(BlackListItem::getRoomId).collect(Collectors.toSet()));
        }
    }

    public boolean inBlacklist(long roomId) {
        if (BLACKLIST_ROOM_IDS.isEmpty()) {
            var blacklist = zhuiwanBlacklistClient.getRoomBlacklist();
            if (blacklist != null) {
                BLACKLIST_ROOM_IDS = Optional.of(blacklist.stream().map(BlackListItem::getRoomId).collect(Collectors.toSet()));
            }
        }
        return BLACKLIST_ROOM_IDS.stream().anyMatch(id -> id.contains(roomId));
    }
}
