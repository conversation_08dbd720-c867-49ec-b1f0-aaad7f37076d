package com.yy.gameecology.stream.service;

import com.yy.gameecology.common.utils.ShutdownHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2021/6/30
 */
@Service
@Slf4j
public class ActServiceManager {

    @Autowired
    private HdztService hdztService;

    private List<ActBabyService> actBabyServiceList;
    private List<ActPkService> actPkServiceList;
    private List<ActFriendService> actFriendServiceList;
    private List<ActPeiwanService> actPeiwanServiceList;
    private List<ActZhuiwanService> actZhuiwanServiceList;
    private List<ActTurnoverService> actTurnoverServices;



    @Scheduled(fixedDelay = 60000)
    public synchronized void refresh() {
        if (ShutdownHolder.isShuttingDown()) {
            log.info("shutdown ActServiceManager.refresh scheduled");
            return;
        }
        this.actBabyServiceList = ActBabyService.ACT_ID_MAP.values().stream().filter(this::actInWorkTime).collect(Collectors.toList());
        this.actPkServiceList = ActPkService.ACT_ID_MAP.values().stream().filter(this::actInWorkTime).collect(Collectors.toList());
        this.actFriendServiceList = ActFriendService.ACT_ID_MAP.values().stream().filter(this::actInWorkTime).collect(Collectors.toList());
        this.actPeiwanServiceList = ActPeiwanService.ACT_ID_MAP.values().stream().filter(this::actInWorkTime).collect(Collectors.toList());
        this.actZhuiwanServiceList = ActZhuiwanService.ACT_ID_MAP.values().stream().filter(this::actInWorkTime).collect(Collectors.toList());
        this.actTurnoverServices = ActTurnoverService.ACT_ID_MAP.values().stream().filter(this::actInWorkTime).collect(Collectors.toList());
    }

    private boolean actInWorkTime(ActService actService) {
        return hdztService.eventInActTime(actService.getActId(), DateUtils.MILLIS_PER_HOUR);
    }

    public List<ActBabyService> getActBabyServiceList() {
        if (actBabyServiceList == null) {
            refresh();
        }
        return actBabyServiceList;
    }

    public List<ActPkService> getActPkServiceList() {
        if (actPkServiceList == null) {
            refresh();
        }
        return actPkServiceList;
    }

    public List<ActFriendService> getActFriendServiceList() {
        if (actFriendServiceList == null) {
            refresh();
        }
        return actFriendServiceList;
    }

    public List<ActPeiwanService> getActPeiwanServiceList() {
        if (actPeiwanServiceList == null) {
            refresh();
        }
        return actPeiwanServiceList;
    }

    public List<ActZhuiwanService> getActZhuiwanServiceList() {
        if (actZhuiwanServiceList == null) {
            refresh();
        }
        return actZhuiwanServiceList;
    }

    public List<ActTurnoverService> getActTurnoverServices() {
        if (actTurnoverServices == null) {
            refresh();
        }
        return actTurnoverServices;
    }

}
