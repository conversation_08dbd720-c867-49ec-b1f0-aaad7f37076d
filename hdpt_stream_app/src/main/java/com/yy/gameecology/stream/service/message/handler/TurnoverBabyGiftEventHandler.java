package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.TurnoverBabyGiftEvent;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.ActTurnoverService;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
* @author: yulianzhu
* @date: 2023.02.24
* @description:
*/
@Component
public class TurnoverBabyGiftEventHandler extends KafkaMessageHandlerSupport<TurnoverBabyGiftEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(TurnoverBabyGiftEvent message) {
        for (ActTurnoverService actTurnoverService : actServiceManager.getActTurnoverServices()) {
            actTurnoverService.onGiftEvent(message);
        }
        eventRuleStreamService.executeEventRule(message, EventID.TURNOVER_BABY_GIFT_EVENT.getEventId());
    }
}
