package com.yy.gameecology.stream.service.message.handler;


import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.platformgame.*;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;


@Service
public class PlatformGameHandler extends KafkaMessageHandlerSupport<PlatformGameEvent> {

    private static final String GAME_HOST = "HUDONG";

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected boolean checkMessage(PlatformGameEvent message) {
        boolean legalInfo = message != null && message.getGameHost().equals(GAME_HOST) && !StringUtil.isBlank(message.getOriginMsg());
        log.info("PlatformGameHandler checkMessage message:{},legalInfo:{}", JSON.toJSON(message),legalInfo);
        return legalInfo;
    }
    
    @Override
    protected boolean messagePersistent() {
        return false;
    }
    
    @Override
    protected int expireDays() {
        return 5;
    }
    
    @Override
    protected void doHandleMessage(PlatformGameEvent message) {
        log.info("PlatformGameHandler doHandleMessage message:{}",JSON.toJSON(message));
        if(message.getMsgType() == MsgType.LIVE_COMMENT) {
            List<DanmakuTextchat> msgList = JSON.parseArray(message.getOriginMsg(), DanmakuTextchat.class);
            for(DanmakuTextchat value : msgList) {
                DanmakuGameEvent event = convertToGameEvent(message,value);
                eventRuleStreamService.executeEventRule(event, EventID.DANMAKU_CHAT.getEventId());
            }
        }else if(message.getMsgType() == MsgType.LIVE_LIKE){
            List<DanmakuClick> msgList = JSON.parseArray(message.getOriginMsg(), DanmakuClick.class);
            for(DanmakuClick value : msgList) {
                DanmakuGameEvent event = convertToGameEvent(message,value);
                eventRuleStreamService.executeEventRule(event, EventID.DANMAKU_CLICK.getEventId());
            }

        }else if(message.getMsgType() == MsgType.LIVE_GIFT){
            List<DanmakuGift> msgList = JSON.parseArray(message.getOriginMsg(), DanmakuGift.class);
            for(DanmakuGift value : msgList) {
                DanmakuGameEvent event = convertToGameEvent(message,value);
                eventRuleStreamService.executeEventRule(event, EventID.DANMAKU_GIFT.getEventId());
            }
        }else{
            log.warn("PlatformGameHandler doHandleMessage message:{},MsgType:{}",JSON.toJSON(message),message.getMsgType());
        }
    }

    private DanmakuGameEvent convertToGameEvent(PlatformGameEvent message, DanmakuTextchat  info) {
        String seq = UUID.randomUUID().toString();
        DanmakuGameEvent gameEvent = new DanmakuGameEvent();
        BeanUtils.copyProperties(message,gameEvent);
        gameEvent.setTimestamp(info.getTimestamp());
        gameEvent.setSeqId(seq);
        gameEvent.setTexkInfo(info);

        return gameEvent;

    }

    private DanmakuGameEvent convertToGameEvent(PlatformGameEvent message, DanmakuClick info) {
        DanmakuGameEvent gameEvent = new DanmakuGameEvent();
        BeanUtils.copyProperties(message,gameEvent);
        gameEvent.setTimestamp(info.getTimestamp());
        gameEvent.setSeqId(info.getSeqId());
        gameEvent.setClickInfo(info);

        return gameEvent;
    }

    private DanmakuGameEvent convertToGameEvent(PlatformGameEvent message, DanmakuGift  info) {
        DanmakuGameEvent gameEvent = new DanmakuGameEvent();
        BeanUtils.copyProperties(message,gameEvent);
        gameEvent.setTimestamp(info.getUsedTime());
        gameEvent.setSeqId(String.valueOf(info.getId()));
        gameEvent.setGiftInfo(info);
        return gameEvent;

    }

}
