package com.yy.gameecology.stream.service.stream.script;

import com.yy.gameecology.common.db.model.gameecology.ReRule;
import com.yy.gameecology.common.db.model.gameecology.ReRuleParam;
import com.yy.gameecology.common.db.model.gameecology.ReRuleScript;
import com.yy.gameecology.stream.exception.RuleException;
import com.yy.gameecology.stream.service.stream.ScriptEvalResult;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> 2021/4/23
 */
public interface RuleScriptHandler {

    RuleType ruleType();

    /**
     * 执行脚本, 返回结果
     *
     * @param rule
     * @param ruleScript
     * @param ruleParamList
     * @param params
     * @return
     * @throws RuleException 规则执行异常
     */
    ScriptEvalResult eval(ReRule rule, ReRuleScript ruleScript, List<ReRuleParam> ruleParamList, Map<String, Object> params) throws RuleException;

    /**
     * 解析配置, 生成脚本
     *
     * @param config
     * @return
     */
    String parse(ReRuleScript ruleScript, List<ReRuleParam> ruleParamList, String config);

    ConcurrentMap<RuleType, RuleScriptHandler> MAP = new ConcurrentHashMap<>();

    @PostConstruct
    default void initMap() {
        MAP.put(ruleType(), this);
    }

    void initScript(ReRule rule, ReRuleScript ruleScript, List<ReRuleParam> ruleParamList, Map<String, Object> params) throws Exception;
}
