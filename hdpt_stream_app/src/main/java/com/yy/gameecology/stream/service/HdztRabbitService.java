package com.yy.gameecology.stream.service;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.common.utils.Clock;
import com.yy.gameecology.stream.bean.RankDataEvent;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.KafkaException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR> 2020/7/30
 */
@Service
public class HdztRabbitService {

    private static final Logger log = LoggerFactory.getLogger(HdztRabbitService.class);


    @Autowired
    private KafkaTemplate<String, String> hdztWxKafkaTemplate;

    @Autowired
    private KafkaTemplate<String, String> hdztSzKafkaTemplate;

    @Autowired
    private KafkaTemplate<String, String> hdztWx7KafkaTemplate;

    @Autowired
    private KafkaTemplate<String, String> hdztSz7KafkaTemplate;


    public void updateRanking(RankDataEvent rankDataEvent) {
        if (SysEvHelper.isLocal() || SysEvHelper.isYuFa()) {
            log.error("updateRanking env err. message:{}", rankDataEvent);
            return;
        }
        Clock clock = new Clock();
        String msg = "1|" + JSON.toJSONString(rankDataEvent);

        String rankingTopic = getRankingTopic(rankDataEvent.getActId());
        try {
            KafkaTemplate<String, String> privateTemplate = selectPrivateKafkaTemplate(rankDataEvent.getActId());
            privateTemplate.send(rankingTopic, rankDataEvent.getSeq(), msg).get(2, TimeUnit.SECONDS);
            log.info("updateRanking wx done rankingTopic:{} message:{} {}",rankingTopic, msg, clock.tag());
        } catch (Exception wxe) {
            log.error("updateRanking wx err seq:{} err:{} {}", rankDataEvent.getSeq(), wxe.getMessage(), clock.tag(), wxe);
            try {
                KafkaTemplate<String, String> backupTemplate = selectBackupKafkaTemplate(rankDataEvent.getActId());
                backupTemplate.send(rankingTopic, rankDataEvent.getSeq(), msg).get(1, TimeUnit.SECONDS);
                log.info("updateRanking sz done rankingTopic:{} message:{} {}", rankingTopic, msg, clock.tag());
            } catch (Exception sze) {
                log.error("updateRanking sz err seq:{} err:{} {}", rankDataEvent.getSeq(), sze.getMessage(), clock.tag(), sze);
                throw new KafkaException("send rank data fail.", ExceptionUtils.getRootCause(sze.getCause()));
            }
        }
    }

    private KafkaTemplate<String, String> selectPrivateKafkaTemplate(long actId) {
        int select = Const.getActRoute(actId);
        switch (select) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
                return hdztWxKafkaTemplate;
            case 7:
                return hdztWx7KafkaTemplate;
            default:
                throw new IllegalArgumentException("活动ID找不到对应服务! actId: " + actId);
        }
    }

    private KafkaTemplate<String, String> selectBackupKafkaTemplate(long actId) {
        int select = Const.getActRoute(actId);
        switch (select) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
                return hdztSzKafkaTemplate;
            case 7:
                return hdztSz7KafkaTemplate;
            default:
                throw new IllegalArgumentException("活动ID找不到对应服务! actId: " + actId);
        }
    }

    private String getRankingTopic(long actId) {
        int select = Const.getActRoute(actId);
        switch (select) {
            case 1: return "hdzt1_ranking_updating_topic";
            case 2: return "hdzt2_ranking_updating_topic";
            case 3: return "hdzt3_ranking_updating_topic";
            case 4: return "hdzt4_ranking_updating_topic";
            case 5: return "hdzt5_ranking_updating_topic";
            case 6: return "hdzt6_ranking_updating_topic";
            case 7: return "hdzt7_ranking_updating_topic";
            default: throw new IllegalArgumentException("活动ID找不到对应服务! actId: " + actId);
        }
    }


}
