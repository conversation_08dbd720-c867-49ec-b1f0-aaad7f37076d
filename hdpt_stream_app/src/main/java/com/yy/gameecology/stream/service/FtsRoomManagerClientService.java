package com.yy.gameecology.stream.service;

import com.duowan.udb.util.GsonUtil;
import com.yy.gameecology.common.protocol.thrift.fts_room_manager.AntiPoachingInfo;
import com.yy.gameecology.common.utils.Convert;
import com.yy.gameecology.common.utils.StringUtil;
import com.yy.gameecology.stream.bean.RankDataEvent;
import com.yy.gameecology.stream.client.FtsRoomManagerClient;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class FtsRoomManagerClientService {

    private static Logger log = LoggerFactory.getLogger(FtsRoomManagerClient.class);

    private Set<String> antiPoachingSet = new HashSet<>();

    @Autowired
    private FtsRoomManagerClient ftsRoomManagerClient;

    @Scheduled(cron = "0/20 * * * * ? ")
    public void fresh() {
        Map<Long, Long> tmp = new HashMap<>();
        Set<String> set = new HashSet<>();
         List<AntiPoachingInfo> list = ftsRoomManagerClient.getAntiPoachingList();
        if(list != null) {
            for (AntiPoachingInfo antiPoachingInfo : list) {
                if(antiPoachingInfo.isBillboard) {
                    String ting = antiPoachingInfo.getSid() + "_" + antiPoachingInfo.getSsid();
                    set.add(ting);
                }
            }
            log.info("fresh cache oldMapSize:{}, newMapSize:{}", antiPoachingSet.size(), set.size());
            antiPoachingSet = set;
        } else {
            log.info("fresh cache antiPoachingInfos is null");
        }
    }

    public void actorsFilter(RankDataEvent rankDataEvent) {
        List<Long> filterKeys = new ArrayList<>();
        Map<Long, String> actors = rankDataEvent.getActors();
        for (Map.Entry<Long, String> entry : actors.entrySet()) {
            String memberId = entry.getValue();
            if(StringUtils.contains(memberId, "_") && antiPoachingSet.contains(memberId)) {
                filterKeys.add(entry.getKey());
            }
        }
        if(!filterKeys.isEmpty()) {
            log.info("filter keys:{}, actors:{}", GsonUtil.toJson(filterKeys), GsonUtil.toJson(actors));
        }
        log.info("filter keys:{}, actors:{}", filterKeys, actors);

        Map<String, Long> roleScores = rankDataEvent.getRoleScores();
        if (roleScores == null) {
            roleScores = new HashMap<>(filterKeys.size());
            rankDataEvent.setRoleScores(roleScores);
        }
        for (long filterKey : filterKeys) {
            roleScores.put(String.valueOf(filterKey), 0L);
        }
    }

    public boolean inAntiPoachingList(String memberId) {
        return StringUtils.contains(memberId, "_") && antiPoachingSet.contains(memberId);
    }

}
