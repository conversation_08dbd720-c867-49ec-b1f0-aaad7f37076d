package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.DataCollectHeartBeatEvent;
import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.OnlineTimeEvent;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class DataCollectHeartBeatHandler extends KafkaMessageHandlerSupport<DataCollectHeartBeatEvent> {

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected boolean messagePersistent() {
        return false;
    }

    @Override
    protected void doHandleMessage(DataCollectHeartBeatEvent message) {
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.DATA_COLLECT_HEART_BEAT_EVENT.getEventId());
    }
}
