package com.yy.gameecology.stream.service.message.handler;

import com.yy.gameecology.stream.bean.EventID;
import com.yy.gameecology.stream.bean.ZhuiwanTaskEvent;
import com.yy.gameecology.stream.service.ActServiceManager;
import com.yy.gameecology.stream.service.ActZhuiwanService;
import com.yy.gameecology.stream.service.EventRuleStreamService;
import com.yy.gameecology.stream.service.message.KafkaMessageHandlerSupport;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> 2021/7/13
 */
@Service
public class ZhuiwanTaskHandler extends KafkaMessageHandlerSupport<ZhuiwanTaskEvent> {

    @Autowired
    private ActServiceManager actServiceManager;

    @Autowired
    private EventRuleStreamService eventRuleStreamService;

    @Override
    protected void doHandleMessage(ZhuiwanTaskEvent message) {
        for (ActZhuiwanService actZhuiwanService : actServiceManager.getActZhuiwanServiceList()) {
            actZhuiwanService.onTaskEvent(message);
        }
        //事件规则流
        eventRuleStreamService.executeEventRule(message, EventID.ZHUIWAN_TASK_EVENT.getEventId());
    }
}
