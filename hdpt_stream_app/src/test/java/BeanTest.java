import com.yy.gameecology.stream.Main;
import com.yy.gameecology.stream.bean.Response;
import com.yy.gameecology.stream.service.HdztService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.HashMap;

/**
 * <AUTHOR> 2021/4/20
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(value = {"classpath:env/local/application.properties"})
public class BeanTest {

    private static final Logger log = LoggerFactory.getLogger(BeanTest.class);

    @Autowired
    private HdztService hdztService;

    @Autowired
    private RestTemplate restTemplate;

    @Test
    public void testHdztCache() throws Exception {
        boolean running = hdztService.isRunning(2021053001, new Date());
    }

    @Test
    public void test404() throws Exception {
        try {
            ResponseEntity<Response> entity = restTemplate.postForEntity("https://re-admin-test.yy.com/ruleEngine/appList", new HashMap<>(), Response.class);
        } catch (RestClientException e) {
            e.printStackTrace();
        }
    }

}
