import com.google.common.collect.Lists;
import com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge.FtsBaseInfoBridgeService;
import com.yy.gameecology.common.protocol.thrift.fts_base_info_bridge.TTingBind;
import com.yy.gameecology.stream.Main;
import com.yy.gameecology.stream.client.FtsBaseInfoBridgeClient;
import com.yy.gameecology.stream.service.stream.script.context.ContractService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.09.08 15:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(value = {"classpath:env/local/application.properties"})
public class TingServiceTest {
    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private ContractService contractService;

    @Test
    public void test() {

       // Map<Long, TTingBind> tTingBindMap = ftsBaseInfoBridgeClient.queryTingBindByUids(Lists.newArrayList(1620548616L, 2717088677L, 2500576758L, 1620532038L));
      //  System.out.println(tTingBindMap);

        Long aLong = contractService.queryTingIdByUid(1620548616L, 2021083002, 500);
        System.out.println(aLong);
    }
}
