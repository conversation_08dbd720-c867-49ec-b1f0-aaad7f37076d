import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.bean.vo.ReceiveRuleVersionReq;
import com.yy.gameecology.common.bean.vo.RuleConfig;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleParamMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleResultMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleScriptMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleTestCaseMapper;
import com.yy.gameecology.common.db.mapper.gameecology.ReRuleVersionMapper;
import com.yy.gameecology.common.db.model.gameecology.ReRule;
import com.yy.gameecology.common.db.model.gameecology.ReRuleParam;
import com.yy.gameecology.common.db.model.gameecology.ReRuleScript;
import com.yy.gameecology.common.db.model.gameecology.ReRuleTestCase;
import com.yy.gameecology.common.db.model.gameecology.ReRuleVersion;
import com.yy.gameecology.common.support.SysEvHelper;
import com.yy.gameecology.stream.Main;
import com.yy.gameecology.stream.bean.Response;
import com.yy.gameecology.stream.bean.UserInfo;
import com.yy.gameecology.stream.client.HdztRankingThriftClient;
import com.yy.gameecology.stream.service.LoginHelper;
import com.yy.gameecology.stream.service.stream.RuleConfigService;
import com.yy.gameecology.stream.service.stream.RuleEngineCoreService;
import com.yy.gameecology.stream.service.stream.ScriptEvalResult;
import com.yy.gameecology.stream.service.stream.script.context.RankingService;
import org.openjdk.nashorn.api.scripting.NashornScriptEngine;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.script.ScriptEngineManager;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 2021/6/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(value = {"classpath:env/local/application.properties"})
public class RuleEngineTest {

    @Autowired
    private RuleEngineCoreService ruleEngineCoreService;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private RankingService rankingService;
    
    @Test
    public void testContext() throws Exception {

        String script = "function foo(actId) {\n" +
                "    var activityInfo = hdzt.queryActivityInfo(actId);\n" +
                "    var uid = JSON.parse('{\"uid\":3464561328321}').uid\n" +
                "    rankingService.updateRanking({'actors':{'90000':uid}});\n" +
                "    return activityInfo.actName\n" +
                "}";

        ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
        NashornScriptEngine scriptEngine = (NashornScriptEngine) scriptEngineManager.getEngineByName("js");
        scriptEngine.eval(script);
        scriptEngine.put("hdzt", hdztRankingThriftClient);
        scriptEngine.put("rankingService", rankingService);

        Object ret = scriptEngine.invokeFunction("foo", 2021083001);
        System.out.println(ret);

    }

    @Test
    public void testParam() throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("age", 20);
        params.put("uid", 2464561821L);
        ruleEngineCoreService.executeRule(1L, 1L, params);
    }

    @Resource
    private ReRuleVersionMapper reRuleVersionMapper;

    @Resource
    private ReRuleResultMapper reRuleResultMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RuleConfigService ruleConfigService;

    @Resource
    private ReRuleParamMapper reRuleParamMapper;


    @Resource
    private ReRuleTestCaseMapper reRuleTestCaseMapper;

    @Resource
    private ReRuleScriptMapper reRuleScriptMapper;

    @Resource
    private ReRuleMapper reRuleMapper;

    @Test
    public void syncRuleVersion() {
        long versionId = 665;
        //UserInfo userInfo = LoginHelper.getUserInfo();
        //log.info("syncRuleVersion versionId:{} userInfo:{}", versionId, userInfo);
       // if (SysEvHelper.isDeploy()) {
       //     return Response.success();
       // }
        ReRuleVersion ruleVersion = reRuleVersionMapper.selectByPrimaryKey(versionId);
        //String url = "https://re-admin.yy.com/ruleEngine/receiveRuleVersion";
        ReceiveRuleVersionReq req = new ReceiveRuleVersionReq();
        req.setRuleId(ruleVersion.getRuleId());
        req.setUser("abc");
        req.setVersionDesc(ruleVersion.getVersionDesc() + "(同步版本ID: " + versionId + ")");
        RuleConfig ruleConfig = JSON.parseObject(ruleVersion.getRuleConfig(), RuleConfig.class);
        req.setRuleConfig(ruleConfig);
        //return restTemplate.postForObject(url, req, Response.class);
        req.setRuleId(20073L);
        receiveRuleVersion(req);
    }


    public Response receiveRuleVersion(ReceiveRuleVersionReq req) {
        //log.info("receiveRuleVersion req:{} user:{}", req, req.getUser());
        ReRule rule = reRuleMapper.selectByPrimaryKey(req.getRuleId());
        if (rule == null) {
            return Response.fail(-1, "线上不存在规则ID: " + req.getRuleId());
        }

        RuleConfig ruleConfig = req.getRuleConfig();

        Response testResult = checkTestCase(rule, ruleConfig.getRuleScript(), ruleConfig.getRuleParamList());
        if (testResult != null) return testResult;

        ReRuleVersion ruleVersion = new ReRuleVersion();
        ruleVersion.setRuleId(req.getRuleId());
        ruleVersion.setCreateTime(new Date());
        ruleVersion.setCreatePassport(req.getUser());
        ruleVersion.setRuleConfig(ruleConfig.toString());
        ruleVersion.setVersionDesc(req.getVersionDesc());
        reRuleVersionMapper.insertSelective(ruleVersion);

        reRuleParamMapper.deleteByRuleId(req.getRuleId());
        ruleConfig.getRuleParamList().forEach(ruleParam -> {
            reRuleParamMapper.insertParam(ruleParam);
        });

        reRuleScriptMapper.deleteByRuleId(req.getRuleId());
        reRuleScriptMapper.insertScript(ruleConfig.getRuleScript());

        if (ruleConfig.getRuleResultList() != null) {
            reRuleResultMapper.deleteByRuleId(req.getRuleId());
            ruleConfig.getRuleResultList().forEach(ruleResult -> {
                reRuleResultMapper.insertSelective(ruleResult);
            });
        }

        return Response.success();
    }

    private Response checkTestCase(ReRule rule, ReRuleScript ruleScript, List<ReRuleParam> ruleParamList) {
        ReRuleTestCase testCase = reRuleTestCaseMapper.selectByRuleId(rule.getId());
        if (testCase != null && testCase.getStatus()) {
            ScriptEvalResult testResult = ruleEngineCoreService.testRule(rule, ruleScript, ruleParamList, JSON.parseObject(testCase.getTestParam()));
            if (testResult.getCode() != 0) {
                return Response.fail(testResult.getCode(), "测试用例执行失败: " + testResult.getMsg());
            }
        }
        return null;
    }

}
