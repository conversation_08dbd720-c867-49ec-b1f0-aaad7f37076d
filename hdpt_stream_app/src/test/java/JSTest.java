import com.alibaba.fastjson.JSON;
import org.openjdk.nashorn.api.scripting.NashornScriptEngine;
import org.openjdk.nashorn.api.scripting.NashornScriptEngineFactory;

import javax.script.ScriptEngineManager;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * <AUTHOR> 2021/7/13
 */
public class JSTest {

    public static void main(String[] args) throws Exception {
        String script = "function foo(person) {\n" +
                "    print(person.getName())\n" +
                "}";

        ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
        NashornScriptEngine scriptEngine = (NashornScriptEngine) scriptEngineManager.getEngineByName("js");
        scriptEngine.eval(script);

        Person person = new Person();
        person.setName("lwz");
        person.setAge(22);
        scriptEngine.invokeFunction("foo", person);

        NashornScriptEngineFactory factory = new NashornScriptEngineFactory();
        NashornScriptEngine engine = (NashornScriptEngine)factory.getScriptEngine("--language=es6");
        engine.eval("function foo(data) {" +
                "   const name = 'lwz'\n" +
                "   print(name)\n" +
                "   const clock = function () {\n" +
                "    const begin = new Date().getTime()\n" +
                "    let last = begin\n" +
                "    let costLog = ''\n" +
                "    return {\n" +
                "        tag: function () {\n" +
                "            const curr = new Date().getTime();\n" +
                "            costLog = costLog + '|' + (curr - last) + 'ms'\n" +
                "            last = curr\n" +
                "            return '@[clock:' + (curr - begin) + 'ms' + costLog + ']'\n" +
                "        }\n" +
                "    }\n" +
                "   }()\n" +
                "   print(clock.tag())\n" +
                //"   print(list.stream().mapToLong(function (a) { return a }).sum())\n" +
                //"   print(list.reduce(function (a,b){ return a+b }))" +
                "   print(Object.prototype.toString.call(data.list))\n" +
                //"   const person = {[name]:22,java:true}\n" +
                "   const array = [1,2,3]\n"+
                "   for (let key in array) { print(key) }\n" +
                "   print(array.indexOf(1))\n" +
                "   print(array.indexOf(4))\n" +
                "}");
        ArrayList<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(2);
        list.add(3);
        HashMap<String, Object> data = new HashMap<>();
        data.put("list", list);
        engine.invokeFunction("foo", data);
        Object obj = engine.eval(String.format("Java.asJSONCompatible(%s)", JSON.toJSONString(list)));
    }

    public static final class Person {

        private String name;
        private int age;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getAge() {
            return age;
        }

        public void setAge(int age) {
            this.age = age;
        }
    }

}
