package com.yy.gameecology.stream;

import com.alibaba.fastjson.JSON;
import com.yy.gameecology.common.protocol.thrift.turnover.TAppId;
import com.yy.gameecology.common.protocol.thrift.turnover.TContract;
import com.yy.gameecology.stream.client.TurnoverServiceClient;
import com.yy.gameecology.stream.service.stream.script.context.ContractService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(value = {"classpath:env/local/application.properties"})
public class ContractServiceTest {

    @Autowired
    private ContractService contractService;

    @Test
    public void isSdkAnchor() {
        boolean ret1 = contractService.isSdkAnchor(1713373250L); // sdk uid
        boolean ret2 = contractService.isSdkAnchor(1713388015L); // 非 sdk uid
        System.out.println(ret1 + ", " + ret2);
    }

    @Autowired
    private TurnoverServiceClient turnoverServiceClient;

    @Test
    public void queryContractByAnchorTest() {
        long uid = 2837714190L;
        TContract contract = turnoverServiceClient.queryContractByAnchor(uid, TAppId.PeopleGame);
        if (contract != null) {
            System.out.println(JSON.toJSONString(contract));
        }
    }
}
