package com.yy.gameecology.stream.client;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStream;
import com.yy.gameecology.common.protocol.yypyrpc.hdzk_stream.HDZKStreamService;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = {HDZKStreamClientTest.class})
@EnableDubbo
@TestPropertySource(value = {"classpath:env/local/application.properties"})
public class HDZKStreamClientTest {

	private static final Logger log = LoggerFactory.getLogger(HDZKStreamClientTest.class);

	@Reference(protocol = "yyp", registry = "consumer-reg", owner = "HDZKStream_local",interfaceClass = HDZKStreamService.class,interfaceName = "yypHDZKStreamService")
	private HDZKStreamService service;

  	@Test
	public void test(){
  		HDZKStream.SetBufferRequest req = makeRequest(2022047001L, 400,"2022-05-17 10:00:00", "2022-05-18 10:00:00", 0);
		HDZKStream.SetBufferResponse resp = service.setBuffer(req);
		log.info("setBuffer done@req:{}, resp:{}", req, resp);

		req = makeRequest2(2022047001L, 200,"2022-05-17 10:00:00", "2022-05-18 10:00:00", 0);
		resp = service.setBuffer(req);
		log.info("setBuffer done@req:{}, resp:{}", req, resp);

		HDZKStream.Ping ping = HDZKStream.Ping.newBuilder().build();
		HDZKStream.Pong pong = service.ping(ping);
		log.info("ping done@req:{}, resp:{}", ping, pong);
	}

	private HDZKStream.SetBufferRequest makeRequest(long actId, int ratio, String begin, String end, int policy) {
		HDZKStream.SetBufferRequest.Builder builder = HDZKStream.SetBufferRequest.newBuilder();
		builder.setActId(actId);
		builder.setSeq(java.util.UUID.randomUUID().toString());
		builder.setRatio(ratio);
		builder.setBeginTime(begin);
		builder.setEndTime(end);
		builder.addAllRoles(Lists.newArrayList("70001&70003"));
		builder.addAllRankings(Lists.newArrayList(2));
		List<Long> ssids1 = Lists.newArrayList(39626143L);
		HDZKStream.Channel channel1 = HDZKStream.Channel.newBuilder().setSid(39626143L).addAllSsids(ssids1).build();
		HDZKStream.MemberFilter memberFilter = HDZKStream.MemberFilter.newBuilder().addAllChannels(Lists.newArrayList(channel1)).
				addAllRecvUids(Lists.newArrayList(248697624L)).addAllSendUids(Lists.newArrayList(248699782L)).build();
		builder.setMemberFilter(memberFilter);
		return builder.build();
	}

	private HDZKStream.SetBufferRequest makeRequest2(long actId, int ratio, String begin, String end, int policy) {
		HDZKStream.SetBufferRequest.Builder builder = HDZKStream.SetBufferRequest.newBuilder();
		builder.setActId(actId);
		builder.setSeq(java.util.UUID.randomUUID().toString());
		builder.setRatio(ratio);
		builder.setBeginTime(begin);
		builder.setEndTime(end);
		builder.addAllRoles(Lists.newArrayList("70001&70003"));
		builder.addAllRankings(Lists.newArrayList(2));
		HDZKStream.Channel channel1 = HDZKStream.Channel.newBuilder().setSid(39626143L).build();
		HDZKStream.MemberFilter memberFilter = HDZKStream.MemberFilter.newBuilder().addAllChannels(Lists.newArrayList(channel1)).
				addAllRecvUids(Lists.newArrayList(248697624L)).build();
		builder.setMemberFilter(memberFilter);
		return builder.build();
	}

	private HDZKStream.SetBufferRequest makeRequest3(long actId, int ratio, String begin, String end, int policy) {
		HDZKStream.SetBufferRequest.Builder builder = HDZKStream.SetBufferRequest.newBuilder();
		builder.setActId(actId);
		builder.setSeq(java.util.UUID.randomUUID().toString());
		builder.setRatio(ratio);
//		builder.setRoundDown(true);
		builder.setBeginTime(begin);
		builder.setEndTime(end);
//		builder.setPolicy(policy);
//		builder.setGlobal(false);
		builder.addAllRoles(Lists.newArrayList("40001", "40003&40013"));
		builder.addAllRankings(Lists.newArrayList(2,4));

		List<Long> ssids1 = Lists.newArrayList(1780012L, 28800031L);
		HDZKStream.Channel channel1 = HDZKStream.Channel.newBuilder().setSid(39626143).addAllSsids(ssids1).build();
		List<Long> ssids2 = Lists.newArrayList(3780112L, 26807031L);
		HDZKStream.Channel channel2 = HDZKStream.Channel.newBuilder().setSid(2771295).addAllSsids(ssids2).build();
		HDZKStream.Channel channel3 = HDZKStream.Channel.newBuilder().setSid(197708).build();
		HDZKStream.MemberFilter memberFilter = HDZKStream.MemberFilter.newBuilder().addAllChannels(Lists.newArrayList(channel1, channel2, channel3)).
				addAllRecvUids(Lists.newArrayList(2487712L, 258881L)).addAllGiftIds(Lists.newArrayList("BBT", "MG")).build();

		builder.setMemberFilter(memberFilter);
		builder.putAllExtMap(ImmutableMap.of("a", "1", "b", "2"));

		return builder.build();
	}
}