import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yy.gameecology.common.protocol.thrift.fts_group_center.QueryType;
import com.yy.gameecology.common.protocol.thrift.fts_group_center.Source;
import com.yy.gameecology.common.protocol.thrift.fts_group_center.Visibility;
import com.yy.gameecology.stream.Main;
import com.yy.gameecology.stream.client.*;
import com.yy.gameecology.stream.service.stream.script.context.DanmuService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 * @since 2024/3/7 18:24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {
        Main.class,
})
@TestPropertySource(value = {
        "classpath:env/local/application.properties",
        "classpath:env/local/application-inner.properties",
})
public class YrpcConsumerTest {

    @Autowired
    private ContractServiceClient contractServiceClient;

    @Autowired
    private FtsBaseInfoBridgeClient ftsBaseInfoBridgeClient;

    @Autowired
    private FtsGroupCenterThriftClient ftsGroupCenterThriftClient;

    @Autowired
    private HdztRankingThriftClient hdztAwardThriftClient;

    @Autowired
    private MiniestClient miniestClient;

    @Autowired
    private TurnoverFamilyClient turnoverFamilyClient;

    @Autowired
    private UserInfoClient userInfoClient;

    @Autowired
    private WebdbSinfoClient webdbSinfoClient;

    @Autowired
    private WebdbUinfoClient webdbUinfoClient;

    @Autowired
    private ZhuiWanPrizeIssueServiceClient zhuiWanPrizeIssueServiceClient;

    @Autowired
    private ZhuiwanRoomInfoClient zhuiwanRoomInfoClient;

    @Autowired
    private DanmuService danmuService;

    @Test
    public void testDanmuService() throws Exception {
        String ret = danmuService.queryDanmuGameAppid(87814665L,87814665L, 50048391L);
        System.out.println(ret);
    }

    @Test
    public void testServiceDiscovery() {
        Assert.assertNotNull(hdztAwardThriftClient);
        Assert.assertNotNull(ftsBaseInfoBridgeClient);
        Assert.assertNotNull(ftsGroupCenterThriftClient);
        Assert.assertNotNull(miniestClient);
        Assert.assertNotNull(turnoverFamilyClient);
        Assert.assertNotNull(userInfoClient);
        Assert.assertNotNull(webdbSinfoClient);
        Assert.assertNotNull(webdbUinfoClient);
        Assert.assertNotNull(zhuiWanPrizeIssueServiceClient);
    }

    @Test
    public void testZhuiwanRoomInfoClient() {
        long ssid = 2793959425L;
        var ret = zhuiwanRoomInfoClient.roomInfoBySsid(ssid);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void testContractServiceClient() {
        var ret = contractServiceClient.isSdkAnchor(2856862693L);
        System.out.println(ret);
    }

    @Test
    public void testHdztAwardThriftClient() throws Exception {
        int[] actIds = new int[]{10001000, 10002000, 10003000, 10004000, 10005000, 10006000, 10007000};
        CountDownLatch latch = new CountDownLatch(10);
        for (int i = 0; i < 10; i++) {
            new Thread(() -> {
                try {
                    for (var actId : actIds) {
                        var ret = hdztAwardThriftClient.queryNow(actId);
                        System.out.println("actId:" + actId + ", ret:" + ret);
                    }
                } finally {
                    latch.countDown();
                }
            }).start();
        }
        latch.await();
        System.out.println("end");
    }

    @Test
    public void testFtsBaseInfoBridgeClient() {
        var ret = ftsBaseInfoBridgeClient.getFtsSign(Lists.newArrayList(2856862693L));
        System.out.println(ret);
    }

    @Test
    public void testFtsGroupCenterThriftClient() {
        var ret = ftsGroupCenterThriftClient.queryMemberGroup(2024021001,
                Lists.newArrayList(String.valueOf(2902244192L)),
                QueryType.TypeCompere,
                Source.SourceJY,
                Visibility.Normal);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void testMiniestClient() {
        miniestClient.historyTextChatWrite(Lists.newArrayList());
    }

    @Test
    public void testTurnoverFamilyClient() {
        var ret = turnoverFamilyClient.queryContractFamilyId(2856862693L);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void testUserInfoClient() {
        var ret3 = userInfoClient.checkIDInfo(2856862693L);
        System.out.println(JSON.toJSONString(ret3));

        var ret = userInfoClient.batchGetUserNickExt(Lists.newArrayList(String.valueOf(2856862693L)));
        System.out.println(JSON.toJSONString(ret));

        var ret2 = userInfoClient.getMobileMask(2856862693L);
        System.out.println(JSON.toJSONString(ret2));
    }

    @Test
    public void testWebdbSinfoClient() {
        var ret = webdbSinfoClient.getChannelInfo(1454962223);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void testWebdbUinfoClient() {
        var ret = webdbUinfoClient.getUserInfo(2856862693L);
        System.out.println(JSON.toJSONString(ret));
    }

    @Test
    public void testZhuiWanPrizeIssueServiceClient() {
        var ret = zhuiWanPrizeIssueServiceClient.checkSingleUserNew(2856862693L);
        System.out.println(JSON.toJSONString(ret));
    }
}
