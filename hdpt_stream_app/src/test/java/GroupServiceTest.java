import com.google.common.collect.Maps;
import com.yy.gameecology.common.protocol.thrift.fts_group_center.FtsGroupService;
import com.yy.gameecology.common.protocol.thrift.fts_group_center.QueryType;
import com.yy.gameecology.stream.Main;
import com.yy.gameecology.stream.client.FtsBaseInfoBridgeClient;
import com.yy.gameecology.stream.service.stream.script.context.ContractService;
import com.yy.gameecology.stream.service.stream.script.context.GroupService;
import com.yy.gameecology.stream.service.stream.script.context.ScoreLimitService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021.09.08 15:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Main.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@TestPropertySource(value = {"classpath:env/local/application.properties"})
public class GroupServiceTest {
    @Autowired
    private GroupService groupService;


    @Autowired
    private ScoreLimitService scoreLimitService;

    @Test
    public void test() {

        Map<String, String> queryMap = Maps.newHashMap();

//        queryMap.put(QueryType.TypeGuild.name(), "10148430");
        queryMap.put(QueryType.TypeCompere.name(), "50018033");
//        queryMap.put(QueryType.TypeTing.name(), "10081");
        //queryMap.put(QueryType.TypeFamily.name(), "10088");
        Map<String, String> resultMap = groupService.queryGroupV2(2023111001, queryMap, 500, 2023111001);
        System.out.println(resultMap);
    }

    @Test
    public void testScoreLimit() {
        System.out.println(scoreLimitService.incrAndLimit(2022073004L, 500L, "TypeUser", "50018033", 150, 500));
    }
}
